import json
import logging
import os
import zipfile
from typing import Set, Dict

from caasm_config.config import caasm_config
from caasm_enforcement.handlers.basic.create_query_template import CreateQueryTemplateHandler
from caasm_meta_data.constants import Category
from caasm_meta_data.manager import MetaDataManager
from caasm_service.runtime import entity_service
from caasm_vul.consts import (
    CVSS3_AV,
    CVSS3_UI,
    CVSS3_PR,
    CVSS3_AC,
    CVSS3_S,
    CVSS3_IMPACT,
    CVSS2_IMPACT,
    CVSS2_Au,
    CVSS2_AC,
    CVSS2_AV,
)

log = logging.getLogger()

_KEYWORD_MAPPER = {
    "远程代码执行": {
        "keywords": [
            "RCE",
            "Remote Code Execution",
            "远程代码执行",
            "远程执行代码",
        ],
        "children": {"代码执行": {"keywords": ["代码执行", "execute arbitrary code"]}},
    },
    "提权": {
        "keywords": [
            "提权",
            "越权",
            "权限提升",
            "privilege escalation",
            "Privilege Escalation",
        ]
    },
    "XSS": {
        "keywords": [
            "XSS",
            "xss",
            "Cross Site Scripting",
            "cross site scripting",
            "跨站脚本攻击",
            "跨站脚本",
        ]
    },
    "CSRF": {"keywords": ["CSRF", "csrf"]},
    "SSRF": {"keywords": ["SSRF", "ssrf"]},
    "XXE": {"keywords": ["XXE", "xxe"]},
    "路径穿越": {
        "keywords": [
            "目录遍历",
            "路径遍历",
            "目录穿越",
            "路径穿越",
            "Path Transversal",
            "path transversal",
        ]
    },
    "未授权访问": {"keywords": ["未授权", "unauthenticated"]},
    "拒绝服务攻击": {
        "keywords": [
            "拒绝服务攻击",
            "拒绝服务",
            "DoS",
            "denial of service",
            "Denial of Service",
        ]
    },
    "权限绕过": {
        "keywords": [
            "权限绕过",
            "authorization bypass",
            "Authorization Bypass",
        ]
    },
    "SQL注入": {
        "keywords": ["SQL注入", "sql注入"],
        "children": {
            "注入": {
                "keywords": [
                    "注入",
                    "代码注入",
                    "命令注入",
                    "command injection",
                    "Command Injection",
                ]
            }
        },
    },
    "缓冲区溢出": {"keywords": ["缓冲区溢出"]},
    "反序列化": {"keywords": ["反序列化"]},
}
_CATEGORY = Category.VUL
_MODEL_NAME = Category.VUL


def _parse_vector(vector):
    vector_split = vector.split("/")
    vectors = dict()
    for segment in vector_split:
        segment_split = segment.split(":")
        vectors[segment_split[0]] = segment_split[1]
    return vectors


def _extract_tags_from_keywords(text: str, tag: str, keywords_def: Dict, tags: Set[str]):
    keywords = keywords_def["keywords"]
    for keyword in keywords:
        if keyword in text:
            tags.add(tag)
    if not tags and "children" in keywords_def:
        for child_tag, child_keywords_def in keywords_def["children"].items():
            _extract_tags_from_keywords(text, child_tag, child_keywords_def, tags)


def _extract_tags_from_text(text: str, tags: Set[str]):
    for tag, keyword_def in _KEYWORD_MAPPER.items():
        _extract_tags_from_keywords(text, tag, keyword_def, tags)


def _extract_tags(vulnerability):
    tags = set()
    if "name" in vulnerability:
        _extract_tags_from_text(vulnerability["name"], tags)
    if "description" in vulnerability:
        _extract_tags_from_text(vulnerability["description"], tags)
    if "description_en" in vulnerability:
        _extract_tags_from_text(vulnerability["description_en"], tags)
    return list(tags)


def _format_vulnerability(vulnerability_data):
    cve_id = vulnerability_data.get("vul_id")

    _published = vulnerability_data.get("published")
    _published = None if not _published else _published.replace("T", " ")

    _last_modified = vulnerability_data.get("last_modified")
    _last_modified = None if not _last_modified else _last_modified.replace("T", " ")

    vulnerability_data.update({"published": _published, "last_modified": _last_modified})

    vul_detail = {
        "base": {
            "entity_id": cve_id,
            "asset_type": Category.VUL,
        },
        "vul": vulnerability_data,
    }
    return vul_detail


def _clean_vulnerability(data):
    meta_data_manager = MetaDataManager()
    meta_data_manager.initialize()
    response = meta_data_manager.transform_test(_CATEGORY, _MODEL_NAME, data)
    if not response.code:
        log.warning(f"Clean vulnerability error({response.message})")
        return

    data = response.data
    failed_data = data.get("failed")
    if failed_data:
        log.warning(f"Clean vulnerability return failed data({failed_data})")
        return
    return data.get("adapter")


def _flush_vulnerability(buffer, data=None, size=1000, force=False, refresh=False):
    buffer.append(data) if data else ...
    if not (buffer and (len(buffer) == size or force)):
        return

    entity_service.save_multi_direct(buffer, table=entity_service.get_table(Category.VUL))
    buffer.clear()
    if refresh:
        entity_service.refresh_entity(Category.VUL, None)


def init_vulnerability(buffer_size=1000):
    CreateQueryTemplateHandler(Category.VUL).execute()
    entity_service.drop_entity_table(Category.VUL, None)
    path = caasm_config.data_path / "vul" / "cves_vps.jsonl.zip"
    z = zipfile.ZipFile(path)
    z.extractall()

    _category = Category.VUL
    _model_name = Category.VUL
    _pk_field = "base.entity_id"
    _buffer = []

    with open("cves_vps.jsonl") as fp:
        lines = fp.readlines()
        for line in lines:
            vulnerability_dict = json.loads(line)
            if "cve_id" in vulnerability_dict:
                vulnerability_dict["vul_id"] = vulnerability_dict["cve_id"]
            cvss_score = None
            if "cvss_3" in vulnerability_dict:
                cvss_3 = vulnerability_dict["cvss_3"]
                cvss_score = cvss_3.get("base_score")
                vector = cvss_3["vector"]
                vectors = _parse_vector(vector)
                cvss_3["av"] = CVSS3_AV[vectors["AV"]]
                cvss_3["ac"] = CVSS3_AC[vectors["AC"]]
                cvss_3["pr"] = CVSS3_PR[vectors["PR"]]
                cvss_3["ui"] = CVSS3_UI[vectors["UI"]]
                cvss_3["s"] = CVSS3_S[vectors["S"]]
                cvss_3["c_impact"] = CVSS3_IMPACT[vectors["C"]]
                cvss_3["i_impact"] = CVSS3_IMPACT[vectors["I"]]
                cvss_3["a_impact"] = CVSS3_IMPACT[vectors["A"]]
                cvss_3["i_score"] = cvss_3["impact_score"]
                cvss_3["e_score"] = cvss_3["exploitability_score"]
            if "cvss_2" in vulnerability_dict:
                cvss_2 = vulnerability_dict["cvss_2"]
                if not cvss_score:
                    cvss_score = cvss_2.get("base_score")
                vector = cvss_2["vector"]
                vectors = _parse_vector(vector)
                cvss_2["av"] = CVSS2_AV[vectors["AV"]]
                cvss_2["ac"] = CVSS2_AC[vectors["AC"]]
                cvss_2["au"] = CVSS2_Au[vectors["Au"]]
                cvss_2["c_impact"] = CVSS2_IMPACT[vectors["C"]]
                cvss_2["i_impact"] = CVSS2_IMPACT[vectors["I"]]
                cvss_2["a_impact"] = CVSS2_IMPACT[vectors["A"]]
                cvss_2["i_score"] = cvss_2["impact_score"]
                cvss_2["e_score"] = cvss_2["exploitability_score"]
            if cvss_score is None:
                severity = 1
            else:
                if cvss_score == 0:
                    severity = 5
                    severity_text = "无"
                elif cvss_score < 4:
                    severity = 4
                    severity_text = "低危"
                elif cvss_score < 7:
                    severity = 3
                    severity_text = "中危"
                elif cvss_score < 9:
                    severity = 2
                    severity_text = "高危"
                elif cvss_score <= 10:
                    severity = 1
                    severity_text = "严重"
                else:
                    severity = 1
                    severity_text = "无"
                vulnerability_dict["severity"] = severity
            exploits = list()
            for ref in vulnerability_dict.get("refs", []):
                tags = ref.get("tags", [])
                url = ref.get("url")
                if "Vendor Advisory" in tags:
                    vulnerability_dict["vendor_advisory"] = url
                if "Exploit" in tags:
                    exploit = {
                        "source": "nvd",
                        "url": url,
                    }
                    exploits.append(exploit)
            if exploits:
                if "exploits" not in vulnerability_dict:
                    vulnerability_dict["exploits"] = list()
                vulnerability_dict["exploits"].extend(exploits)
            tags = _extract_tags(vulnerability_dict)
            vulnerability_dict["traits"] = tags

            single_data = _format_vulnerability(vulnerability_dict)
            cleaned_data = _clean_vulnerability(single_data)

            _flush_vulnerability(_buffer, cleaned_data, size=buffer_size)
    _flush_vulnerability(_buffer, force=True)
    os.remove("cves_vps.jsonl")


if __name__ == "__main__":
    from datetime import datetime

    print(datetime.now())
    init_vulnerability()
    print(datetime.now())
