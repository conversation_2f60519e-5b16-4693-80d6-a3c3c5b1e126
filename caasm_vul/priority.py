import logging

from caasm_aql.tool import parse_aql
from caasm_businesses.enums import GradeProtectionLevel
from caasm_meta_data.constants import Category
from caasm_persistence.handler.runtime import es_handler
from caasm_render.runtime import render_manager
from caasm_service.entity.vul_priority import (
    VulPrioritySetting,
    VulPriority,
)
from caasm_service.runtime import entity_service, vul_priority_service
from caasm_tool.util import extract, deduplicate

_LOG = logging.getLogger()

_PK_FIELD = "base.entity_id"
_ASSET_TYPE_NAME_FIELD = "base.asset_type_display_name"

_ASSET_PORT_FIELD = "network.ports"
_ASSET_DB_FIELD = "computer.dbs"
_ASSET_WEBSITE_FIELD = "computer.websites"
_ASSET_ADAPTER_PROPERTIES = "asset_base.adapter_properties"
_ASSET_OWNER_FIELD = "asset_base.owners"
_ASSET_BUSINESS_FIELD = "asset_base.businesses"

_ASSET_VUL_INSTANCE_DISTINCT_FIELD = "asset_vul_instance.asset.rel_id"
_ASSET_VUL_INSTANCE_ASSET_ID_FIELD = "asset_vul_instance.asset_id"
_ASSET_VUL_INSTANCE_CVE_ID = "asset_vul_instance.cve_id"
_ASSET_VUL_INSTANCE_PORT_FIELD = "asset_vul_instance.port"
_ASSET_VUL_INSTANCE_VUL_RISK_LEVEL = "asset_vul_instance.vul_risk_level"
_ASSET_VUL_INSTANCE_ASSET_SEVERITY_FIELD = "asset_vul_instance.asset_severity"
_ASSET_VUL_INSTANCE_REMEDIATION_SEVERITY_FIELD = "asset_vul_instance.remediation_severity"

_BUSINESS_NAME_FIELD = "business.full_name"
_BUSINESS_GRADE_PROTECTION_LEVEL_FIELD = "business.grade_protection_level.value"

_VUL_TRAITS_FIELD = "vul.traits"
_VUL_CVSS_3_FIELD = "vul.cvss_3"
_VUL_CVSS_2_FIELD = "vul.cvss_3"
_VUL_EXPLOITS_FIELD = "vul.exploits"

important_cwes = {
    "CWE-787",
    "CWE-79",
    "CWE-89",
    "CWE-20",
    "CWE-125",
    "CWE-78",
    "CWE-416",
    "CWE-22",
    "CWE-352",
    "CWE-434",
    "CWE-476",
    "CWE-502",
    "CWE-190",
    "CWE-287",
    "CWE-798",
    "CWE-862",
    "CWE-77",
    "CWE-306",
    "CWE-119",
    "CWE-276",
    "CWE-918",
    "CWE-362",
    "CWE-400",
    "CWE-611",
    "CWE-94",
}

_IMPORTANT_TAGS = ["远程代码执行", "提权", "XSS"]
_PROTECTION_COVERAGES = {
    "主机": [
        ("HIDS", "HIDS"),
        ("CMDB", "CMDB"),
        ("OSM", "堡垒机"),
    ],
    "终端": [("EDR", "EDR"), ("防病毒", "防病毒"), ("终端准入", "终端准入")],
}

web_protection_coverages = [("WAF", "WAF"), ("RASP", "RASP"), ("AST", "AST")]

db_protection_coverages = [("数据库审计", "数据库审计")]

data_coverages = {
    "主机": [
        ("HIDS", "HIDS"),
        ("VA", "漏洞扫描"),
        ("CMDB", "CMDB"),
    ],
    "终端": [
        ("EDR", "EDR"),
        ("VA", "漏洞扫描"),
    ],
}

business_level_mapping = {
    GradeProtectionLevel.UNDEFINED: (0, "none", "无等级"),
    GradeProtectionLevel.UNKNOWN: (0, "none", "无等级"),
    GradeProtectionLevel.LEVEL1: (1, "low", "一级"),
    GradeProtectionLevel.LEVEL2: (2, "medium", "二级"),
    GradeProtectionLevel.LEVEL3: (3, "high", "三级"),
    GradeProtectionLevel.LEVEL4: (4, "critical", "四级"),
    GradeProtectionLevel.LEVEL5: (5, "critical", "五级"),
}

_businesses = {}

r_severity_mapping = {
    1: {1: 1, 2: 2, 3: 3},
    2: {1: 2, 2: 3, 3: 4},
    3: {1: 3, 2: 4, 3: 5},
}


def _find_asset_vul_instance(asset_id, date):
    condition = {"bool": {"must": [{"term": {_ASSET_VUL_INSTANCE_ASSET_ID_FIELD: asset_id}}]}}
    return entity_service.find_entity_loop(Category.ASSET_VUL_INSTANCE, date, condition)


def _add_business_entity(asset_business, date, result):
    business_name = asset_business.get("name")
    if not business_name:
        return
    if business_name in _businesses:
        business_mapper = _businesses[business_name]
    else:
        business_mapper = entity_service.get_entity(Category.BUSINESS, _BUSINESS_NAME_FIELD, business_name, date=date)
        if business_mapper:
            _businesses[business_name] = business_mapper
    if business_mapper:
        result.append(business_mapper)


def _get_business_entity(asset_entity, date):
    result = []
    asset_businesses = extract(asset_entity, _ASSET_BUSINESS_FIELD)
    if not asset_businesses:
        return result

    if len(asset_businesses) == 1:
        _add_business_entity(asset_businesses[0], date, result)
    else:
        for asset_business in asset_businesses:
            if asset_business.get("direct"):
                _add_business_entity(asset_business, date, result)
    return result


def calc(asset_vul, asset, vul, businesses):
    if not vul:
        vul = {}
    is_vul_important = False
    _traits = extract(vul, _VUL_TRAITS_FIELD) or []
    for tag in _traits:
        if tag in _IMPORTANT_TAGS:
            is_vul_important = True
            break

    cvss = {}
    cvss_2 = extract(vul, _VUL_CVSS_2_FIELD)
    if cvss_2:
        cvss = cvss_2
    cvss_3 = extract(vul, _VUL_CVSS_3_FIELD)
    if cvss_3:
        cvss = cvss_3

    vectors = {}
    if cvss:
        vector_split = cvss.get("vector").split("/")
        for vector_segment in vector_split:
            vector_segment_split = vector_segment.split(":")
            vectors[vector_segment_split[0]] = vector_segment_split[1]

    is_network_av = vectors.get("AV") == "N"
    is_impact_important = not (vectors.get("C") == "N" and vectors.get("I") == "N")
    has_exp = bool(extract(vul, _VUL_EXPLOITS_FIELD))

    asset_vul_port = extract(asset_vul, _ASSET_VUL_INSTANCE_PORT_FIELD)

    #   外网
    exposed = False
    asset_ports = extract(asset, _ASSET_PORT_FIELD) or []
    for port in asset_ports:
        if asset_vul_port is not None and port.get("number") != asset_vul_port:
            continue
        if extract(port, "exposure.value") == 3:
            exposed = True
            break

    #   重要资产
    has_db = bool(extract(asset, _ASSET_DB_FIELD))
    has_web = bool(extract(asset, _ASSET_WEBSITE_FIELD))
    has_businesses = bool(businesses)
    is_asset_important = has_db or has_web or has_businesses

    if is_network_av:
        if is_vul_important:
            vul_risk_level = 1
        else:
            vul_risk_level = 2
    else:
        vul_risk_level = 3

    if exposed:
        asset_severity = 1
    else:
        if is_asset_important:
            asset_severity = 2
        else:
            asset_severity = 3
    r_severity = r_severity_mapping[asset_severity][vul_risk_level]

    #   计算分数
    #   1 是否容易被利用，是否容易失陷
    total = 10
    protection_score = cvss.get("base_score") or cvss.get("temporal_score") or 0
    #   1.1 是否已有利用脚本
    if has_exp:
        protection_score += 1
    total += 1
    #   1.2 是否有安全防护措施
    adapter_properties = deduplicate(extract(asset, _ASSET_ADAPTER_PROPERTIES) or [])
    asset_type = extract(asset, _ASSET_TYPE_NAME_FIELD)
    if asset_type in _PROTECTION_COVERAGES:
        protection_coverages_of_asset = _PROTECTION_COVERAGES[asset_type]
        total += 3
        for prop, name in protection_coverages_of_asset:
            if prop not in adapter_properties:
                protection_score += 3 / len(protection_coverages_of_asset)
    #   1.3 数据库防护措施
    if has_db:
        db_impact_score = 2
        total += 2
        for prop, name in db_protection_coverages:
            if prop not in adapter_properties:
                db_impact_score -= 2 / len(db_protection_coverages)
        protection_score += db_impact_score

    #   1.4 Web防护措施
    if has_web:
        web_impact_score = 2
        total += 2
        for prop, name in web_protection_coverages:
            if prop not in adapter_properties:
                web_impact_score -= 2 / len(web_protection_coverages)
        protection_score += web_impact_score

    #   1.3 防护用分
    protection_score = protection_score / total * 5

    #   2 影响
    total = 1
    #   2.1 漏洞情报影响
    if is_impact_important:
        impact_score = 1
    else:
        impact_score = 0

    #   2.2 数据库
    total += 1
    if has_db:
        impact_score += 1

    #   2.3 Web应用
    total += 1
    if has_web:
        impact_score += 1

    #   2.4 业务
    total += 3
    business_score = 0
    for business in businesses:
        _grade_value = extract(business, _BUSINESS_GRADE_PROTECTION_LEVEL_FIELD)
        if not _grade_value:
            continue
        business_score, severity, level = business_level_mapping[_grade_value]
        business_score += business_score
    if business_score > 3:
        business_score = 3
    impact_score += business_score

    #   影响得分
    impact_score = impact_score / total * 3

    #   3 失陷后响应
    response_score = 0

    #   3.1 责任人
    total = 1
    _owners = extract(asset, _ASSET_OWNER_FIELD)
    if not _owners:
        response_score += 1

    #   3.2 采集能力
    if asset_type in data_coverages:
        total += 3
        data_coverages_of_asset_type = data_coverages[asset_type]
        for prop, name in data_coverages_of_asset_type:
            if prop not in adapter_properties:
                response_score += 3 / len(data_coverages_of_asset_type)

    response_score = response_score / total * 2

    priority_score = protection_score + impact_score + response_score

    return r_severity, asset_severity, vul_risk_level, priority_score


class MemoryCacheDict:
    def __init__(self):
        self.cache = {}
        self.keys = []
        self.max_size = 200

    def add(self, key, value):
        if len(self.cache) >= self.max_size:
            key = self.keys.pop(0)
            del self.cache[key]
        self.cache[key] = value
        self.keys.append(key)

    def get(self, key):
        return self.cache.get(key)

    def has(self, key):
        return key in self.cache


cves = MemoryCacheDict()
cves.max_size = 100000


def _get_asset_vul_instance_enum_value(value, meta_field_mapper, field_full_name):
    result = {"value": value}
    meta_field = meta_field_mapper.get(field_full_name)
    if not meta_field:
        return result
    _rules = meta_field.rules

    _rule_mapper = {}
    for _rule in _rules:
        _rule_mapper[_rule.name] = _rule

    _enum_rule = _rule_mapper.get("enum")
    if not _enum_rule:
        return result

    _enum_setting = _enum_rule.setting

    for text, val in _enum_setting.items():
        if val == value:
            result["text"] = text
            return result
    _LOG.warning(f"Not found {field_full_name} {value} enum text")
    return result


def _find_total_asset_id(date):
    _asset_ids = set()
    _search_after = None
    _sort_fields = [{"_id": {"order": "desc"}}]
    _fields = [_ASSET_VUL_INSTANCE_ASSET_ID_FIELD]
    while True:
        records = es_handler.find_direct(
            fields=_fields,
            table=entity_service.get_table(Category.ASSET_VUL_INSTANCE, date),
            limit=100,
            search_after=_search_after,
            need_ori_response=True,
            sort_fields=_sort_fields,
        )
        if not records:
            break

        for record in records:
            _search_after = record["sort"]
            _content = record["_source"]
            _asset_id = extract(_content, _ASSET_VUL_INSTANCE_ASSET_ID_FIELD)
            _asset_ids.add(_asset_id)

    return list(_asset_ids)


def get_priority_level(priority_score):
    if priority_score < 4.5:
        return "低"
    elif priority_score < 7.5:
        return "中"
    else:
        return "高"


def calc_priorities(date):
    index = 0
    category = Category.ASSET_VUL_INSTANCE

    asset_vul_instance_field_mapper = render_manager.default_query.find_field_to_mapper(category, date=date)

    asset_ids = _find_total_asset_id(date)
    updates = list()
    for asset_id in asset_ids:
        asset_dict = entity_service.get_entity(Category.ASSET, _PK_FIELD, asset_id, date=date)
        businesses = _get_business_entity(asset_dict, date)
        asset_vul_list = _find_asset_vul_instance(asset_id, date)

        for asset_vul_dict in asset_vul_list:
            index += 1

            cve_id = extract(asset_vul_dict, _ASSET_VUL_INSTANCE_CVE_ID)
            if cve_id:
                if cves.has(cve_id):
                    cve = cves.get(cve_id)
                else:
                    cve_dict = entity_service.get_entity(Category.VUL, "base.entity_id", cve_id)
                    if cve_dict:
                        cve = cve_dict
                        cves.add(cve_id, cve)
                    else:
                        cve = None
            else:
                cve = None
            r = calc(asset_vul_dict, asset_dict, cve, businesses)
            r_severity, asset_severity, vul_risk_level, priority_score = r
            priority_score = round(priority_score, 2)

            _vul_risk_level_value = _get_asset_vul_instance_enum_value(
                vul_risk_level, asset_vul_instance_field_mapper, _ASSET_VUL_INSTANCE_VUL_RISK_LEVEL
            )
            _asset_severity_value = _get_asset_vul_instance_enum_value(
                asset_severity,
                asset_vul_instance_field_mapper,
                _ASSET_VUL_INSTANCE_ASSET_SEVERITY_FIELD,
            )
            _r_severity_value = _get_asset_vul_instance_enum_value(
                r_severity, asset_vul_instance_field_mapper, _ASSET_VUL_INSTANCE_REMEDIATION_SEVERITY_FIELD
            )
            priority = get_priority_level(priority_score)
            _tmp_update = {
                # "remediation_severity": _r_severity_value,
                # "asset_severity": _asset_severity_value,
                # "vul_risk_level": _vul_risk_level_value,
                # "priority_score": priority_score,
                "priority": priority,
            }
            if businesses:
                _tmp_update["business_name"] = extract(businesses[0], "business.full_name")
                print(_tmp_update["business_name"])
            print(_tmp_update)
            print({"_id": asset_vul_dict["_id"], "asset_vul_instance": _tmp_update})
            updates.append({"_id": asset_vul_dict["_id"], "asset_vul_instance": _tmp_update})
            if len(updates) >= 1000:
                entity_service.update_stream_direct(updates, table=f"{Category.ASSET_VUL_INSTANCE}.{date}")
                updates.clear()

    if len(updates):
        entity_service.update_stream_direct(updates, table=f"{Category.ASSET_VUL_INSTANCE}.{date}")
        updates.clear()
    entity_service.refresh_entity(Category.ASSET_VUL_INSTANCE, date)


def calc_priority_2():
    entity_service.update_multi_direct(
        None,
        {"vul_instance_unique.priority": ""},
        table=entity_service.get_table(Category.VUL_INSTANCE_UNIQUE),
    )
    #   似乎有版本控制问题，只能强制刷新
    entity_service.refresh_entity(Category.VUL_INSTANCE_UNIQUE, None)
    for priority in vul_priority_service.list_priorities():
        priority: VulPriority = priority
        count = vul_priority_service.count_of_settings(priority.id)
        if count != 0:

            def get_ids(category, condition, field):
                result = []
                offset = 0
                entity_query = parse_aql(condition, category)
                while True:
                    entity_ids = entity_service.find_entity(
                        category, condition=entity_query, fields=[field], limit=1000, offset=offset
                    )
                    if not entity_ids:
                        break
                    result.extend([extract(entity, field) for entity in entity_ids])
                    offset += 1000
                return result

            settings = vul_priority_service.list_settings(priority.id)
            for setting in settings:
                setting: VulPrioritySetting = setting
                if setting.asset_condition:
                    asset_ids = get_ids(Category.ASSET, setting.asset_condition, "base.entity_id")
                else:
                    asset_ids = None
                if setting.vul_condition:
                    vul_ids = get_ids(Category.VUL, setting.vul_condition, "vul.cve_id")
                else:
                    vul_ids = None
                max_terms = 65534
                asset_offset = 0
                vul_offset = 0
                should_break = False
                while True:
                    must = {}
                    if asset_ids is None:
                        if "assets" in must:
                            del must["assets"]
                    else:
                        asset_ids_slice = asset_ids[asset_offset : max_terms + asset_offset]
                        if not asset_ids_slice:
                            break
                        must["assets"] = {"terms": {"vul_instance_unique.asset.rel_id": asset_ids_slice}}
                    while True:
                        if vul_ids is None:
                            if "vuls" in must:
                                del must["vuls"]
                        else:
                            vul_ids_slice = vul_ids[vul_offset : max_terms + vul_offset]
                            if not vul_ids_slice:
                                break
                            must["vuls"] = {"terms": {"vul_instance_unique.vulnerability.rel_id": vul_ids_slice}}
                        if must:
                            entity_service.update_multi_direct(
                                {"bool": {"must": list(must.values())}},
                                {
                                    "vul_instance_unique.priority": priority.name,
                                    "vul_instance_unique.priority_hits": [setting.name],
                                },
                                table=entity_service.get_table(Category.VUL_INSTANCE_UNIQUE),
                            )
                            entity_service.refresh_entity(Category.VUL_INSTANCE_UNIQUE, None)
                        else:
                            r = entity_service.update_multi_direct(
                                None,
                                {
                                    "vul_instance_unique.priority": priority.name,
                                    "vul_instance_unique.priority_hits": [setting.name],
                                },
                                table=entity_service.get_table(Category.VUL_INSTANCE_UNIQUE),
                            )
                            entity_service.refresh_entity(Category.VUL_INSTANCE_UNIQUE, None)
                            should_break = True
                            break
                        vul_offset += max_terms
                        if vul_ids is None:
                            break
                    if should_break:
                        break
                    if asset_ids is None:
                        break
                    asset_offset += max_terms


if __name__ == "__main__":
    # dd = entity_service.get_entity(Category.ASSET, 'base.entity_id', "e4a5f84316c3d630511fcf1983fb5e65")
    # aa = entity_service.get_entity(Category.VUL_INSTANCE_UNIQUE, 'vul_instance_unique.asset.rel_id',
    #                                '1723de6a6c1848adbadb7244a7fa94cd')
    #
    # q = parse_aql('$.base.entity_id.in(["1", "2", "3"]) and $.network.priority_addr.in(["3", "2", "1"])',
    #               Category.ASSET)

    # aa = entity_service.get_entity(Category.VUL_INSTANCE_UNIQUE, "vul_instance_unique.remediation_severity.value", 1)
    aa = entity_service.get_entity(Category.VUL_INSTANCE_UNIQUE, "base.entity_id", "7e924bc63c269a0e7144d24639aca934")

    bb = entity_service.get_entity(Category.VUL, "vul.cve_id", extract(aa, "vul_instance_unique.vulnerability.rel_id"))

    calc_priority_2()
