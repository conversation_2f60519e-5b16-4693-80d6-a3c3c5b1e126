from copy import deepcopy
from datetime import datetime
from typing import List
from uuid import uuid4

from bson import ObjectId

from caasm_aql.tool import parse_aql
from caasm_enforcement.handlers.basic.create_query_template import CreateQ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>andler
from caasm_meta_data.constants import Category
from caasm_service.entity.change import ChangeDetail, ChangeType, ChangeStatus, CHANGE_STATUS_MAPPING
from caasm_service.entity.entity_change import SourceType
from caasm_service.runtime import (
    snapshot_record_service,
    entity_service,
    unique_service,
    change_service,
    unique_index_service,
    entity_change_record_service,
    entity_change_service,
)
from caasm_service.schema.runtime import entity_change_detail_schema
from caasm_tool.config import log
from caasm_tool.constants import DATETIME_FORMAT
from caasm_tool.util import extract, compute_md5, restore
from caasm_vul.enums import VulStatus, VUL_STATUS_MAPPING, VulResponseStatus, VUL_RESPONSE_STATUS_MAPPING
from caasm_vul.id_maker import make_vul_id


class VulMigrator:
    _ids = set()

    _vul_exists_aql = "$.vulnerability.vulners.exists()"

    _asset_vul_instance_model = "asset_vul_instance"

    # 基础字段
    _entity_id_field = "base.entity_id"
    _vul_field = "vulnerability.vulners"
    _vul_parent_field = "vulnerability"
    _priority_addr_field = "network.priority_addr"
    _hostname_field = "computer.host_name"
    _asset_name_field = "asset_base.name"
    _first_seen_field = "base.first_seen"
    _last_seen_field = "base.last_seen"
    _asset_type_field = "base.asset_type"

    # 端口相关字段
    _port_field = "network.ports"
    _port_number_field = "number"
    _port_ip_field = "ip"

    # 漏洞子字段
    _vul_child_name_field = "name"
    _vul_child_type_field = "type"
    _vul_child_package_field = "package"
    _vul_child_package_name_field = "package.name"
    _vul_child_cvss_score_field = "cvss_score"
    _vul_child_cve_id_field = "cve_id"
    _vul_child_cnvd_id_field = "cnvd_id"
    _vul_child_cnnvd_id_field = "cnnvd_id"
    _vul_child_cncve_id_field = "cncve_id"
    _vul_child_service_field = "service"
    _vul_child_service_port_field = "service.port"
    _vul_child_service_protocol_field = "service.protocol"
    _vul_child_categories_field = "categories"
    _vul_child_description_field = "description"
    _vul_child_solution_field = "solution"
    _vul_child_adapters_field = "adapters"
    _vul_child_summary_field = "summary"
    _vul_child_asset_id_field = "asset_id"
    _vul_child_url_field = "url"

    # 查询相关
    _query_fields = [_vul_parent_field, "base", "network", "computer.host_name", "asset_base.name"]
    _sort_fields = [{"_id": {"order": "desc"}}]

    def init(self, force=False):
        CreateQueryTemplateHandler(Category.VUL_INSTANCE_UNIQUE).execute()
        count = entity_service.get_count(Category.VUL_INSTANCE_UNIQUE, None)
        if count > 0:
            if force:
                #   删除所有生成的变化记录
                change_service.delete_change_details(Category.ASSET, self._vul_field)
                entity_change_service.delete_change_details(Category.VUL_INSTANCE_UNIQUE)
            else:
                return
        unique_index_service.begin_index(Category.VUL_INSTANCE_UNIQUE, False)
        #   从最后一个快照开始初始化，然后从当前日期和快照日期的变化生成当前的结果
        latest_date = snapshot_record_service.get_latest_useful_date_of_category(Category.ASSET)
        if not latest_date:
            return
        vul_uniques = []
        max_fetch = 100
        query_condition = parse_aql(self._vul_exists_aql, Category.ASSET, latest_date)
        total = entity_service.get_count(Category.ASSET, latest_date, query_condition)
        if isinstance(latest_date, str):
            first_date_ = datetime.fromisoformat(latest_date)
        else:
            first_date_ = latest_date

        count = 0
        search_after = None
        while True:
            search_results = entity_service.find_direct(
                condition=query_condition,
                limit=max_fetch,
                fields=self._query_fields,
                sort_fields=self._sort_fields,
                need_ori_response=True,
                search_after=search_after,
                table=entity_service.get_table(Category.ASSET, latest_date),
            )

            if not search_results:
                break

            for search_result in search_results:
                count += 1
                search_after = search_result.get("sort")
                asset_dict = search_result.get("_source", {})
                asset_dict["_id"] = search_result.get("_id")

                asset_id = extract(asset_dict, self._entity_id_field)
                vulners = extract(asset_dict, self._vul_field) or []
                asset_name = self._get_asset_name(asset_dict)
                last_seen = extract(asset_dict, self._last_seen_field)
                first_seen = extract(asset_dict, self._first_seen_field) or last_seen

                for vul in vulners:
                    changed_datetime = datetime.combine(first_date_, datetime.min.time()).strftime(DATETIME_FORMAT)
                    vul_instance_unique = self.create_vul_instance_unique(
                        vul, asset_id, asset_name, first_seen, last_seen, changed_datetime
                    )
                    if vul_instance_unique:
                        vul_uniques.append(vul_instance_unique)
                    self._create_vul_uniques(vul_uniques)
                if count % 1000 == 0:
                    log.info("Migrating vul unique: %d/%d", count, total)
        self._create_vul_uniques(vul_uniques, True)

        #   定制：迁移登记漏洞
        offset = 0
        limit = 1000
        while True:
            manual_vulners = entity_service.find_entity(
                Category.VUL_INSTANCE_UNIQUE,
                {
                    "bool": {
                        "should": [
                            {"bool": {"must_not": {"exists": {"field": "base.adapters"}}}},
                            {
                                "bool": {
                                    "must": {
                                        "script": {
                                            "script": {"source": "doc['base.adapters'].size() == 0", "lang": "painless"}
                                        }
                                    }
                                }
                            },
                        ]
                    }
                },
                limit=limit,
                offset=offset,
            )
            if not manual_vulners:
                break
            offset += limit
            for manual_vulner in manual_vulners:
                if "_id" in manual_vulner:
                    del manual_vulner["_id"]
            entity_service.save_multi_direct(
                manual_vulners, entity_service.get_table(Category.VUL_INSTANCE_UNIQUE, unique_finished=False)
            )

        unique_index_service.finish_index(Category.VUL_INSTANCE_UNIQUE)
        unique_service.save_unique(Category.VUL_INSTANCE_UNIQUE, latest_date)

    def _get_asset_name(self, asset_dict):
        asset_name = (
            extract(asset_dict, self._priority_addr_field)
            or extract(asset_dict, self._hostname_field)
            or extract(asset_dict, self._asset_name_field)
        )
        if not asset_name:
            asset_name = "资产"
        return asset_name

    @staticmethod
    def _create_vul_uniques(vul_uniques, force=False):
        if vul_uniques:
            if not force and len(vul_uniques) < 1000:
                return
            entity_service.save_multi_direct(
                vul_uniques, entity_service.get_table(Category.VUL_INSTANCE_UNIQUE, unique_finished=False)
            )
            vul_uniques.clear()

    @classmethod
    def make_vul_id(cls, vul, can_be_the_same=False):
        port = extract(vul, cls._vul_child_service_port_field)
        protocol = extract(vul, cls._vul_child_service_protocol_field)
        package_name = extract(vul, cls._vul_child_package_name_field)
        asset_id = extract(vul, cls._vul_child_asset_id_field)
        cve_id = extract(vul, cls._vul_child_cve_id_field)
        cnvd_id = extract(vul, cls._vul_child_cnvd_id_field)
        cnnvd_id = extract(vul, cls._vul_child_cnnvd_id_field)
        cncve_id = extract(vul, cls._vul_child_cncve_id_field)
        name = extract(vul, cls._vul_child_name_field)
        summary = extract(vul, cls._vul_child_summary_field)
        url = extract(vul, cls._vul_child_url_field)

        vul_id = make_vul_id(
            {
                "port": port,
                "protocol": protocol,
                "package_name": package_name,
                "asset_id": asset_id,
                "cve_id": cve_id,
                "cnvd_id": cnvd_id,
                "cnnvd_id": cnnvd_id,
                "cncve_id": cncve_id,
                "name": name,
                "summary": summary,
                "url": url,
            }
        )
        if not can_be_the_same:
            if vul_id in cls._ids:
                vul_id = compute_md5(str(uuid4()))
            cls._ids.add(vul_id)
        return vul_id

    def create_vul_instance_unique(self, vul_dict, asset_id, asset_name, first_seen, last_seen, changed_datetime):
        asset_vul_instance = self.to_asset_vul_instance(vul_dict, asset_id, asset_name, first_seen, last_seen)
        vul_instance_unique = self._to_vul_instance_unique(asset_vul_instance, last_seen or changed_datetime)
        return vul_instance_unique

    def to_asset_vul_instance(self, vul_dict, asset_id, asset_name, first_seen, last_seen):
        if vul_dict is None:
            return None

        service = extract(vul_dict, self._vul_child_service_field)
        package = extract(vul_dict, self._vul_child_package_field)

        #   转换为漏洞实例
        asset_vul_instance = {
            "type": extract(vul_dict, self._vul_child_type_field),
            "name": extract(vul_dict, self._vul_child_name_field),
            "cve_id": extract(vul_dict, self._vul_child_cve_id_field),
            "cnvd_id": extract(vul_dict, self._vul_child_cnvd_id_field),
            "cnnvd_id": extract(vul_dict, self._vul_child_cnnvd_id_field),
            "cncve_id": extract(vul_dict, self._vul_child_cncve_id_field),
        }
        if service:
            asset_vul_instance["service"] = service
            asset_vul_instance["type"] = {"text": "主机终端", "value": 1}
        if package:
            asset_vul_instance["package"] = package
            asset_vul_instance["type"] = {"text": "组件包", "value": 3}
        asset_vul_instance["asset_id"] = asset_id
        asset_vul_instance["asset_name"] = asset_name
        asset_vul_instance["cvss_score"] = extract(vul_dict, self._vul_child_cvss_score_field)
        asset_vul_instance["description"] = extract(vul_dict, self._vul_child_description_field)
        asset_vul_instance["solution"] = extract(vul_dict, self._vul_child_solution_field)
        asset_vul_instance["adapters"] = extract(vul_dict, self._vul_child_adapters_field)
        asset_vul_instance["categories"] = extract(vul_dict, self._vul_child_categories_field)
        asset_vul_instance["summary"] = extract(vul_dict, self._vul_child_summary_field)
        vul_id = self.make_vul_id(asset_vul_instance)
        _tmp_a_instance = self._format_asset_vul_instance(vul_id, asset_vul_instance, last_seen, first_seen)
        return _tmp_a_instance

    @staticmethod
    def _to_vul_instance_unique(vul_dict, changed_datetime):
        if vul_dict is None:
            return None

        #   转换为唯一漏洞实例
        base = {}
        entity_id = extract(vul_dict, "base.entity_id")
        asset_id = extract(vul_dict, "asset_vul_instance.asset_id")
        asset_name = extract(vul_dict, "asset_vul_instance.asset_name")

        vul_unique = extract(vul_dict, "asset_vul_instance")

        _a_relation = {
            "rel_id": asset_id,
            "display_value": asset_name,
        }
        vul_unique["asset"] = _a_relation
        base["entity_id"] = entity_id

        cve_id = extract(vul_dict, "asset_vul_instance.cve_id") or ""
        cvss_score = extract(vul_dict, "asset_vul_instance.cvss_score")
        if cve_id:
            cve_data = entity_service.get_entity(Category.VUL, "base.entity_id", cve_id)
            if cve_data:
                cve = cve_data.get("vul")
                vul_unique["traits"] = list(
                    set((extract(vul_dict, "asset_vul_instance.categories") or []) + (cve.get("traits") or []))
                )
                cve_vul_id = cve["vul_id"]
                cve_display_value = cve_id
                _v_relation = {
                    "rel_id": cve_vul_id,
                    "display_value": cve_display_value,
                }
                vul_unique["vulnerability"] = _v_relation
                cvss_score = extract(vul_dict, "asset_vul_instance.cvss_score")
                if cvss_score is None:
                    if "cvss_2" in cve:
                        cvss_score = cve["cvss_2"]["base_score"]
                    elif "cvss_3" in cve:
                        cvss_score = cve["cvss_3"]["base_score"]
            if cvss_score is not None:
                vul_unique["cvss_score"] = cvss_score
                if cvss_score == 0:
                    severity = 5
                    severity_text = "无"
                elif cvss_score < 4:
                    severity = 4
                    severity_text = "低危"
                elif cvss_score < 7:
                    severity = 3
                    severity_text = "中危"
                elif cvss_score < 9:
                    severity = 2
                    severity_text = "高危"
                elif cvss_score <= 10:
                    severity = 1
                    severity_text = "严重"
                else:
                    severity = 1
                    severity_text = "无"
                vul_unique["severity"] = {"value": severity, "text": severity_text}
        vul_unique["source"] = {"value": 0, "text": "自动采集"}
        vul_unique["remediation_severity"] = {
            "value": 5,
            "text": "无",
        }
        vul_unique["status"] = {"value": VulStatus.ACTIVE.value, "text": VUL_STATUS_MAPPING.get(VulStatus.ACTIVE)}
        vul_unique["response_status"] = {
            "value": VulResponseStatus.ACTIVE.value,
            "text": VUL_RESPONSE_STATUS_MAPPING.get(VulResponseStatus.ACTIVE),
        }

        adapter_names = extract(vul_dict, "base.adapters") or []
        adapter_name = adapter_names[0] if adapter_names else None

        _first_seen_field = "base.first_seen"
        _last_seen_field = "base.last_seen"
        first_seen = extract(vul_dict, _first_seen_field)
        last_seen = extract(vul_dict, _last_seen_field)
        adapter_data = {
            "base": {
                "entity_id": entity_id,
                "first_seen": first_seen or changed_datetime,
                "last_seen": last_seen,
                "adapter_name": adapter_name,
                "adapter_count": len(adapter_names),
                "adapters": adapter_names,
                "asset_type": Category.VUL_INSTANCE_UNIQUE,
            },
            "vul_instance_unique": vul_unique,
            "change_base": {
                "status": {
                    "value": ChangeStatus.APPEARED.value,
                    "text": CHANGE_STATUS_MAPPING.get(ChangeStatus.APPEARED),
                },
                "changed_datetime": changed_datetime,
            },
        }
        if "asset_id" in vul_unique:
            del vul_unique["asset_id"]
        if "asset_name" in vul_unique:
            del vul_unique["asset_name"]
        if "categories" in vul_unique:
            del vul_unique["categories"]
        return adapter_data

    def update(self):
        latest = unique_service.get_latest_date(Category.VUL_INSTANCE_UNIQUE)
        if latest is None:
            return
        offset = 0
        max_fetch = 1000
        new_latest = None
        vul_updates = []
        appeared_vul_uniques = []
        total = change_service.change_details_count(Category.ASSET, original_date_from=latest)
        if total == 0:
            return
        unique_index_service.begin_index(Category.VUL_INSTANCE_UNIQUE)
        entity_service.update_multi_direct(
            {
                "bool": {
                    "should": [
                        {
                            "terms": {
                                "vul_instance_unique.status.value": [
                                    VulStatus.NEW.value,
                                    VulStatus.ONLINE.value,
                                    VulStatus.RECURRENT.value,
                                ]
                            }
                        }
                    ]
                }
            },
            {
                "vul_instance_unique": {
                    "status": {
                        "value": VulStatus.ACTIVE.value,
                        "text": VUL_STATUS_MAPPING.get(VulStatus.ACTIVE),
                    }
                }
            },
            table=entity_service.get_table(Category.VUL_INSTANCE_UNIQUE, unique_finished=False),
        )
        entity_service.update_multi_direct(
            {"bool": {"should": [{"term": {"change_base.status.value": ChangeStatus.APPEARED.value}}]}},
            {
                "change_base": {
                    "status": {
                        "value": ChangeStatus.EXIST.value,
                        "text": CHANGE_STATUS_MAPPING.get(ChangeStatus.EXIST),
                    }
                }
            },
            table=entity_service.get_table(Category.VUL_INSTANCE_UNIQUE, unique_finished=False),
        )
        count = 0

        changes = []
        entity_change_record_id = entity_change_record_service.begin(Category.VUL_INSTANCE_UNIQUE)
        while True:
            change_records = change_service.find_change_details(
                Category.ASSET, original_date_from=latest, field_name=self._vul_field, offset=offset, limit=max_fetch
            )
            offset += max_fetch
            has = False
            for change_detail in change_records:
                count += 1
                has = True
                change_detail: ChangeDetail = change_detail
                asset_id = change_detail.entity_id
                if change_detail.child_change_type == ChangeType.APPEARED:
                    #   新增漏洞
                    date_ = change_detail.new_date
                    new_latest = date_
                    asset_dict = entity_service.get_entity(
                        Category.ASSET,
                        self._entity_id_field,
                        asset_id,
                        date_,
                        fields=[
                            "base.entity_id",
                            self._vul_field,
                            self._priority_addr_field,
                            self._hostname_field,
                            self._asset_name_field,
                            self._first_seen_field,
                            self._last_seen_field,
                        ],
                    )
                    if not asset_dict:
                        continue
                    asset_name = self._get_asset_name(asset_dict)
                    vulners = extract(asset_dict, self._vul_field)
                    if not isinstance(vulners, list):
                        continue
                    index = change_detail.new_value
                    if not isinstance(index, int):
                        return
                    if index >= len(vulners):
                        continue
                    vul_dict = vulners[index]
                    first_seen = extract(asset_dict, self._first_seen_field)
                    last_seen = extract(asset_dict, self._last_seen_field)
                    new_vul_unique = self.create_vul_instance_unique(
                        vul_dict, asset_id, asset_name, first_seen, last_seen, change_detail.changed_datetime
                    )
                    changed_datetime = last_seen or change_detail.changed_datetime
                    if isinstance(changed_datetime, datetime):
                        changed_datetime = changed_datetime.strftime(DATETIME_FORMAT)
                    new_vul_id = extract(new_vul_unique, self._entity_id_field)
                    existing_vul_unique = entity_service.get_entity(
                        Category.VUL_INSTANCE_UNIQUE,
                        self._entity_id_field,
                        new_vul_id,
                        fields=[self._entity_id_field],
                        unique_finished=False,
                    )
                    if change_detail.change_type == ChangeType.APPEARED:
                        #   资产也是新增
                        if existing_vul_unique:
                            #   已有漏洞，表示漏洞伴随资产重新上线
                            vul_updates.append(
                                {
                                    "_id": existing_vul_unique["_id"],
                                    "vul_instance_unique": {
                                        "status": {
                                            "value": VulStatus.ONLINE.value,
                                            "text": VUL_STATUS_MAPPING.get(VulStatus.ONLINE),
                                        }
                                    },
                                    "change_base": {
                                        "status": {
                                            "value": ChangeStatus.APPEARED.value,
                                            "text": CHANGE_STATUS_MAPPING.get(ChangeStatus.APPEARED),
                                        },
                                        "changed_datetime": changed_datetime,
                                    },
                                    "base": {"last_seen": last_seen},
                                }
                            )
                            self._create_vul_change(
                                changes,
                                new_vul_id,
                                ChangeType.APPEARED,
                                "vul_instance_unique.status",
                                VUL_STATUS_MAPPING.get(VulStatus.ONLINE),
                                extract(existing_vul_unique, "vul_instance_unique.status.text"),
                                changed_datetime,
                                entity_change_record_id,
                                change_detail.id,
                            )
                        else:
                            #   没有，漏洞的确是新增的
                            appeared_vul_uniques.append(new_vul_unique)
                            self._create_vul_change(
                                changes,
                                new_vul_id,
                                ChangeType.APPEARED,
                                "",
                                VUL_STATUS_MAPPING.get(VulStatus.NEW),
                                "",
                                changed_datetime,
                                entity_change_record_id,
                                change_detail.id,
                            )
                    elif change_detail.change_type == ChangeType.CHANGED:
                        if existing_vul_unique:
                            #   已有漏洞，表示漏洞复现
                            vul_updates.append(
                                {
                                    "_id": existing_vul_unique["_id"],
                                    "vul_instance_unique": {
                                        "status": {
                                            "value": VulStatus.RECURRENT.value,
                                            "text": VUL_STATUS_MAPPING.get(VulStatus.RECURRENT),
                                        }
                                    },
                                    "change_base": {
                                        "status": {
                                            "value": ChangeStatus.APPEARED.value,
                                            "text": CHANGE_STATUS_MAPPING.get(ChangeStatus.APPEARED),
                                        },
                                        "changed_datetime": changed_datetime,
                                    },
                                    "base": {"last_seen": last_seen},
                                }
                            )
                            self._create_vul_change(
                                changes,
                                new_vul_id,
                                ChangeType.APPEARED,
                                "vul_instance_unique.status",
                                VUL_STATUS_MAPPING.get(VulStatus.RECURRENT),
                                extract(existing_vul_unique, "vul_instance_unique.status.text"),
                                changed_datetime,
                                entity_change_record_id,
                                change_detail.id,
                            )
                        else:
                            #   没有，漏洞的确是新增的
                            #   因资产已存在，漏洞首次发现时间，取资产的最新时间或者变化时间
                            restore("base.first_seen", changed_datetime, new_vul_unique)
                            appeared_vul_uniques.append(new_vul_unique)
                            self._create_vul_change(
                                changes,
                                new_vul_id,
                                ChangeType.APPEARED,
                                "",
                                VUL_STATUS_MAPPING.get(VulStatus.NEW),
                                "",
                                changed_datetime,
                                entity_change_record_id,
                                change_detail.id,
                            )
                elif change_detail.child_change_type == ChangeType.DISAPPEARED:
                    #   漏洞消亡
                    date_ = change_detail.original_date
                    new_latest = date_
                    asset_dict = entity_service.get_entity(
                        Category.ASSET,
                        self._entity_id_field,
                        asset_id,
                        date_,
                        fields=["base.entity_id", self._vul_field, self._last_seen_field],
                    )
                    if not asset_dict:
                        continue
                    vulners = extract(asset_dict, self._vul_field)
                    if not isinstance(vulners, list):
                        continue
                    last_seen = extract(asset_dict, self._last_seen_field)
                    index = change_detail.original_value
                    if not isinstance(index, int):
                        return
                    if index >= len(vulners):
                        continue
                    vul = vulners[index]
                    if not vul:
                        break
                    vul = deepcopy(vul)
                    vul["asset_id"] = asset_id
                    vul_id = self.make_vul_id(vul, True)
                    vul_unique = entity_service.get_entity(
                        Category.VUL_INSTANCE_UNIQUE, self._entity_id_field, vul_id, unique_finished=False
                    )
                    changed_datetime = change_detail.changed_datetime
                    if isinstance(changed_datetime, datetime):
                        changed_datetime = changed_datetime.strftime(DATETIME_FORMAT)
                    if change_detail.change_type == ChangeType.DISAPPEARED:
                        #   资产消亡
                        if vul_unique:
                            vul_updates.append(
                                {
                                    "_id": vul_unique["_id"],
                                    "vul_instance_unique": {
                                        "status": {
                                            "value": VulStatus.OFFLINE.value,
                                            "text": VUL_STATUS_MAPPING.get(VulStatus.OFFLINE),
                                        }
                                    },
                                    "change_base": {
                                        "status": {
                                            "value": ChangeStatus.DISAPPEARED.value,
                                            "text": CHANGE_STATUS_MAPPING.get(ChangeStatus.DISAPPEARED),
                                        },
                                        "changed_datetime": changed_datetime,
                                    },
                                    "base": {"last_seen": last_seen},
                                }
                            )
                            self._create_vul_change(
                                changes,
                                vul_id,
                                ChangeType.DISAPPEARED,
                                "vul_instance_unique.status",
                                VUL_STATUS_MAPPING.get(VulStatus.OFFLINE),
                                extract(vul_unique, "vul_instance_unique.status.text"),
                                changed_datetime,
                                entity_change_record_id,
                                change_detail.id,
                            )
                    elif change_detail.change_type == ChangeType.CHANGED:
                        #   资产变化
                        if vul_unique:
                            vul_updates.append(
                                {
                                    "_id": vul_unique["_id"],
                                    "vul_instance_unique": {
                                        "status": {
                                            "value": VulStatus.DISAPPEARED.value,
                                            "text": VUL_STATUS_MAPPING.get(VulStatus.DISAPPEARED),
                                        }
                                    },
                                    "change_base": {
                                        "status": {
                                            "value": ChangeStatus.DISAPPEARED.value,
                                            "text": CHANGE_STATUS_MAPPING.get(ChangeStatus.DISAPPEARED),
                                        },
                                        "changed_datetime": changed_datetime,
                                    },
                                    "base": {"last_seen": last_seen},
                                }
                            )
                            self._create_vul_change(
                                changes,
                                vul_id,
                                ChangeType.DISAPPEARED,
                                "vul_instance_unique.status",
                                VUL_STATUS_MAPPING.get(VulStatus.DISAPPEARED),
                                extract(vul_unique, "vul_instance_unique.status.text"),
                                changed_datetime,
                                entity_change_record_id,
                                change_detail.id,
                            )
                else:
                    #   应该不存在漏洞属性变化情况
                    pass
                print(count, total)
                self._create_vul_uniques(appeared_vul_uniques)
                self._update_vul_uniques(vul_updates)
                self._save_changes(changes)
            if not has:
                break
        self._create_vul_uniques(appeared_vul_uniques, True)
        self._update_vul_uniques(vul_updates, True)
        self._save_changes(changes, True)
        entity_change_record_service.finish_change_record(entity_change_record_id)
        entity_service.refresh_entity(Category.VUL_INSTANCE_UNIQUE)
        unique_index_service.finish_index(Category.VUL_INSTANCE_UNIQUE)
        if new_latest:
            unique_service.save_unique(Category.VUL_INSTANCE_UNIQUE, new_latest)

    @staticmethod
    def _create_vul_change(
        changes_: List[ChangeDetail],
        entity_id: str,
        change_type: ChangeType,
        field_name: str,
        new_display: str,
        original_display: str,
        changed_datetime: str,
        entity_change_record_id: str,
        change_id: ObjectId,
    ):
        change_detail_ = entity_change_detail_schema.load(
            {
                "category": Category.VUL_INSTANCE_UNIQUE,
                "entity_id": entity_id,
                "change_type": change_type,
                "field_name": field_name,
                "new_display": new_display,
                "original_display": original_display,
                "changed_datetime": changed_datetime,
                "source": SourceType.APP,
                "app_name": "漏洞管理",
                "record_id": entity_change_record_id,
                "change_id": change_id,
                "finished": False,
            }
        )
        changes_.append(change_detail_)

    @staticmethod
    def _save_changes(changes: List[ChangeDetail], force=True):
        if not force and len(changes) < 1000:
            return
        entity_change_service.save_changes(changes)
        changes.clear()

    @staticmethod
    def _update_vul_uniques(disappeared_vul_uniques, force=False):
        if disappeared_vul_uniques:
            if not force and len(disappeared_vul_uniques) < 1000:
                return
            entity_service.update_entity_stream_direct(
                Category.VUL_INSTANCE_UNIQUE, None, disappeared_vul_uniques, False
            )
            disappeared_vul_uniques.clear()

    @staticmethod
    def _format_asset_vul_instance(vul_id, asset_vul_instances, last_seen=None, first_seen=None):
        adapter_names = asset_vul_instances.get("adapters") or []
        adapter_name = adapter_names[0] if adapter_names else None

        adapter_data = {
            "base": {
                "entity_id": vul_id,
                "first_seen": first_seen,
                "last_seen": last_seen,
                "adapter_name": adapter_name,
                "adapter_count": len(asset_vul_instances["adapters"]),
                "asset_type": Category.VUL_INSTANCE_UNIQUE,
                "adapters": adapter_names,
            },
            "asset_vul_instance": asset_vul_instances,
        }
        if "adapters" in asset_vul_instances:
            del asset_vul_instances["adapters"]
        return adapter_data


if __name__ == "__main__":
    # aa = entity_service.get_direct({}, table=entity_service.get_table(Category.VUL_INSTANCE_UNIQUE))

    migrator = VulMigrator()
    migrator.init(True)
    migrator.update()

    d = entity_service.get_entity(Category.VUL, "base.entity_id", "CVE-2021-44228")
    dd = 1
