from caasm_alarm.gen_alarm import gen_alarm
from caasm_config.config import caasm_config
from caasm_meta_data.constants import Category
from caasm_render.export.clean import clean_exports
from caasm_scheduler.jobs.adapter_customized_fetch import adapter_customized_fetch
from caasm_scheduler.jobs.alarm_generation import alarm_generation
from caasm_scheduler.jobs.api_call_record_clear import api_call_record_clear
from caasm_scheduler.jobs.data_collect import data_collect, data_fetch
from caasm_scheduler.jobs.demo import hello
from caasm_scheduler.jobs.intercept_alarm_clear import intercept_alarm_clear
from caasm_scheduler.jobs.job_monitor import job_monitor
from caasm_scheduler.jobs.order_logs_sync import sync_order_logs
from caasm_scheduler.jobs.order_sync_360 import sync_order_details_360
from caasm_scheduler.jobs.poc_log_record_clear import poc_log_record_clear
from caasm_scheduler.jobs.snapshot_clear import snapshot_clear
from caasm_scheduler.jobs.storage_clear import storage_clear
from caasm_scheduler.jobs.sync_data_stream_360 import sync_data_stream_360
from caasm_scheduler.jobs.sync_kafka_intercept_alarms import sync_kafka_intercept_alarms
from caasm_scheduler.jobs.system_status import collect_system_status
from caasm_workflow.cleaner import clean_workflows

JOB_TEMPLATES = {
    "job_monitor": {
        "callback_name": "job_monitor",
        "trigger_type": "cron",
        "trigger_info": {
            "second": "31",
        },
    },
    "alarm_generation": {
        "callback_name": "alarm_generation",
        "trigger_type": "cron",
        "trigger_info": {
            "second": "30",
        },
    },
    "data_collect": {
        "callback_name": "data_collect",
        "trigger_type": "interval",
        "trigger_info": {
            "days": 1,
            "start_date": "2023-04-01 00:00:00",
        },
        "params": {"day": 1, "time": "00:00:00"},
        "depend_flag": True,
    },
    "poc_log_record_clear": {
        "callback_name": "poc_log_record_clear",
        "trigger_type": "cron",
        "trigger_info": {"minute": "*/20"},
    },
    "api_call_record_clear": {
        "callback_name": "api_call_record_clear",
        "trigger_type": "cron",
        "trigger_info": {"minute": "*/30"},
    },
    "snapshot_clear": {
        "callback_name": "snapshot_clear",
        "trigger_type": "cron",
        "trigger_info": {"minute": "*/40"},
    },
    "intercept_alarm_clear": {
        "callback_name": "intercept_alarm_clear",
        "trigger_type": "cron",
        "trigger_info": {"hour": "0"},
        "params": {"days": 2},
    },
    "collect_system_status": {
        "callback_name": "collect_system_status",
        "trigger_type": "cron",
        "trigger_info": {"minute": "*/1"},
    },
    "workflow_cleaner": {
        "callback_name": "clean_workflows",
        "trigger_type": "cron",
        "trigger_info": {"hour": "*/1"},
    },
    # "monitor_stuck_workflows": {
    #     "callback_name": "monitor_stuck_workflows",
    #     "trigger_type": "cron",
    #     "trigger_info": {"minute": "*/10"},
    # },
    "clean_exports": {
        "callback_name": "clean_exports",
        "trigger_type": "cron",
        "trigger_info": {"minute": "*/10"},
    },
    "sync_data_stream_360": {
        "callback_name": "sync_data_stream_360",
        "trigger_type": "cron",
        "trigger_info": {"minute": "*/5"},
    },
    "sync_kafka_intercept_alarms": {
        "callback_name": "sync_kafka_intercept_alarms",
        "trigger_type": "cron",
        "trigger_info": {"minute": "*/5"},
        "misfire_grace_time": 300,
        "coalesce": True,
        "max_instances": 1,
    },
    "order_logs_sync": {
        "callback_name": "sync_order_logs",
        "trigger_type": "cron",
        "trigger_info": {"minute": "0"},  # 每小时整点执行一次
        "misfire_grace_time": 300,
        "coalesce": True,
        "max_instances": 1,
    },
    "order_details_sync": {
        "callback_name": "sync_order_details_360",
        "trigger_type": "cron",
        "trigger_info": {"minute": "0"},  # 每小时整点执行一次
        "misfire_grace_time": 300,
        "coalesce": True,
        "max_instances": 1,
    },
}

CATEGORIES = [
    Category.ASSET,
    Category.OWNER,
    Category.ACCOUNT,
    Category.BUSINESS,
    Category.DEPARTMENT,
    Category.NETWORK,
]

for fetch_index, fetch_type in enumerate(CATEGORIES):
    JOB_TEMPLATES[f"adapter_customized_{fetch_type}_fetch"] = {
        "callback_name": "adapter_customized_fetch",
        "trigger_type": "cron",
        "trigger_info": {"second": f"{fetch_index + 30}"},
        "params": {"fetch_type": fetch_type},
    }

    JOB_TEMPLATES[f"storage_{fetch_type}_clear"] = {
        "callback_name": "storage_clear",
        "trigger_type": "cron",
        "trigger_info": {"minute": f"*/{10 + fetch_index}"},
        "params": {"category": fetch_type, "days": 7},
    }

JOB_TEMPLATES.update(caasm_config.SCHEDULER_TEMPLATE) if caasm_config.SCHEDULER_TEMPLATE else ...

for stop_job in caasm_config.SCHEDULER_STOP_JOBS:
    JOB_TEMPLATES.pop(stop_job, None)

CALLBACK_MAPPER = {
    "data_collect": data_collect,
    "data_fetch": data_fetch,
    "job_monitor": job_monitor,
    "adapter_customized_fetch": adapter_customized_fetch,
    "hello": hello,
    "storage_clear": storage_clear,
    "api_call_record_clear": api_call_record_clear,
    "snapshot_clear": snapshot_clear,
    "poc_log_record_clear": poc_log_record_clear,
    "alarm_generation": alarm_generation,
    "gen_alarm": gen_alarm,
    "collect_system_status": collect_system_status,
    "clean_workflows": clean_workflows,
    # "monitor_stuck_workflows": monitor_stuck_workflows,
    "clean_exports": clean_exports,
    "sync_data_stream_360": sync_data_stream_360,
    "sync_kafka_intercept_alarms": sync_kafka_intercept_alarms,
    "intercept_alarm_clear": intercept_alarm_clear,
    "sync_order_logs": sync_order_logs,
    "sync_order_details_360": sync_order_details_360,
}
