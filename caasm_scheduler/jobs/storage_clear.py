import datetime
import logging
import traceback

from caasm_config.config import caasm_config
from caasm_persistence.handler.runtime import mongo_handler
from caasm_service.runtime import (
    fetch_service,
    merge_service,
    convert_service,
    entity_type_lineage_stage_service,
)
from caasm_tool.constants import DATETIME_FORMAT

log = logging.getLogger()


class BaseStorageClear(object):
    def __init__(self, category, days):
        self.category = category
        self.days = days
        self._find_table_func = {
            "fetch": (self._find_need_delete_fetch_table, self._remove_table, self._after_fetch_remove),
            "merge": (self._find_need_delete_merge_table, self._remove_table, self._after_merge_remove),
            "convert": (self._find_need_delete_convert_table, self._remove_table, self._after_convert_remove),
            "lineage": (self._find_need_delete_lineage_table, self._remove_table, self._after_lineage_remove),
        }

    def clear(self):
        for key, (find_func, remove_func, after_func) in self._find_table_func.items():
            try:
                log.info(f"{key} start storage clearing............")
                tables = find_func()
                new_tables = [i for i in tables if i not in caasm_config.STORAGE_CLEAR_WHITE_TABLES]
                log.info(f"{key} need delete table is {new_tables}")
                remove_func(new_tables)
                log.info(f"{key} clear success")
                after_func(new_tables)
            except Exception as e:
                log.error(f"{key} clear failed({e}). detail is {traceback.format_exc()}")

    def _find_need_delete_fetch_table(self):
        result = []

        time_range = self._build_time_range()
        for fetch_record in fetch_service.find_fetch_record(
            fetch_type=self.category, start_time_lte=time_range, data_deleted=False, latest=False
        ):
            table = fetch_service.build_fetch_data_table(
                fetch_record.adapter_name, fetch_record.adapter_instance_id, self.category, fetch_record.index
            )
            result.append(table)
        return result

    def _after_fetch_remove(self, tables):
        for table in tables:
            adapter_name = fetch_service.extract_adapter_name(table)
            adapter_instance_id = fetch_service.extract_adapter_instance_id(table)
            index = fetch_service.extract_index(table)
            fetch_service.mark_fetch_record_data_deleted(adapter_name, adapter_instance_id, self.category, index)
            entity_type_lineage_stage_service.delete_multi({"table": table})

    def _find_need_delete_merge_table(self):
        result = []

        time_range = self._build_time_range()
        for merge_record in merge_service.find_merge_record(
            start_time_lte=time_range, data_deleted=False, fetch_type=self.category, latest=False
        ):
            table = merge_service.build_merge_data_table(self.category, merge_record.adapter_name, merge_record.index)
            result.append(table)
        return result

    def _after_merge_remove(self, tables):
        for table in tables:
            adapter_name = merge_service.extract_adapter_name(table)
            index = merge_service.extract_index(table)
            merge_service.mark_merge_record_data_deleted(adapter_name, self.category, index)
            entity_type_lineage_stage_service.delete_multi({"table": table})

    def _find_need_delete_convert_table(self):
        result = []

        time_range = self._build_time_range()
        for convert_record in convert_service.find_convert_record(
            start_time_lte=time_range, data_deleted=False, fetch_type=self.category, latest=False
        ):
            table = convert_service.build_convert_data_table(
                self.category, convert_record.adapter_name, convert_record.index
            )
            result.append(table)
        return result

    def _after_convert_remove(self, tables):
        for table in tables:
            adapter_name = convert_service.extract_adapter_name(table)
            index = convert_service.extract_index(table)
            convert_service.mark_convert_record_data_deleted(adapter_name, self.category, index)
            entity_type_lineage_stage_service.delete_multi({"table": table})

    def _find_need_delete_lineage_table(self):
        result = []
        time_range = datetime.datetime.fromisoformat(self._build_time_range()).date()
        collections = mongo_handler.database().list_collection_names()
        for collection in collections:
            collection: str = collection
            if not collection.startswith("lineage.entity_stages.fabric") and not collection.startswith(
                "lineage.value_stages.fabric"
            ):
                continue
            segments = collection.split(".")
            if len(segments) != 5:
                continue
            try:
                lineage_date = datetime.date.fromisoformat(segments[3])
            except ValueError:
                continue
            if segments[4] != self.category:
                continue
            if lineage_date >= time_range:
                continue
            result.append(collection)
        return result

    def _after_lineage_remove(self, tables):
        ...

    @classmethod
    def _remove_table(cls, tables):
        for table in tables:
            mongo_handler.drop(table_name=table)

    def _build_time_range(self):
        time_range = datetime.datetime.now() - datetime.timedelta(days=self.days)
        return time_range.strftime(DATETIME_FORMAT)


def storage_clear(category, days):
    return BaseStorageClear(category, days).clear()


if __name__ == "__main__":
    bsc = BaseStorageClear("asset", 7)
    print(bsc._find_need_delete_lineage_table())
