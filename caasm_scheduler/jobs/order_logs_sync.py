import logging
from typing import List, Tuple
import requests
from datetime import datetime
import urllib.parse

from caasm_config.config import caasm_config
from caasm_service.constants.order import OrderStatusEnum
from caasm_service.entity.order import OrderEntity
from caasm_service.runtime import order_service
from caasm_tool import log

# 默认配置（从配置文件中读取，如果不存在则使用默认值）
DEFAULT_BASE_URL = "http://itsm.ho.ncips"
DEFAULT_REQUEST_TIMEOUT = 30  # 默认请求超时时间（秒）
DEFAULT_USERNAME = "anquan_pt"  # 默认用户名
DEFAULT_PASSWORD = "#EDC&YHN"  # 默认密码

# 工单类型对应的API路径
ORDER_TYPE_APIS = {
    "SRM": "/xhrsSrmMobileSelectAction!getProcessByNo.action",  # 服务工单
    "INC": "/xhrsIncMobileSelectAction!getProcessByNo.action",  # 事件工单
}

# 工单状态获取API路径
ORDER_STATUS_API = "/itsmServiceAction!getOrderStep.action"

# 工单类型映射
ORDER_TYPE_MAPPING = {
    "SRM": "SERVICE_INFO",  # 服务工单
    "INC": "INCIDENT",  # 事件工单
}

# 外部状态到内部状态的映射
EXTERNAL_TO_INTERNAL_STATUS_MAPPING = {
    # 基本状态
    "新建": OrderStatusEnum.PENDING,
    "已分派": OrderStatusEnum.PROCESSING,
    "处理中": OrderStatusEnum.PROCESSING,
    "已解决": OrderStatusEnum.COMPLETED,
    "已关闭": OrderStatusEnum.CLOSED,
    # 详细状态
    "开始": OrderStatusEnum.PENDING,
    "主管审核": OrderStatusEnum.PROCESSING,
    "部门负责人审批": OrderStatusEnum.PROCESSING,
    "分公司IT": OrderStatusEnum.PROCESSING,
    "一线服务台": OrderStatusEnum.PROCESSING,
    "已分派一线团队": OrderStatusEnum.PROCESSING,
    "一线团队处理中": OrderStatusEnum.PROCESSING,
    "反馈提交人": OrderStatusEnum.COMPLETED,
}

# 从配置文件中读取配置，如果不存在则使用默认值
BASE_URL = getattr(caasm_config, "ORDER_BASE_URL", DEFAULT_BASE_URL)
REQUEST_TIMEOUT = getattr(caasm_config, "ORDER_API_TIMEOUT", DEFAULT_REQUEST_TIMEOUT)
USERNAME = getattr(caasm_config, "ORDER_API_USERNAME", DEFAULT_USERNAME)
PASSWORD = getattr(caasm_config, "ORDER_API_PASSWORD", DEFAULT_PASSWORD)
REQUEST_TIMEOUT = int(REQUEST_TIMEOUT)


def get_order_status(order_id):
    """
    从外部接口获取工单状态

    Args:
        order_id: 工单ID

    Returns:
        str: 工单状态，如果获取失败则返回None
    """
    try:
        # 根据工单号前三个字符判断工单类型
        if not order_id or len(order_id) < 3:
            log.error(f"工单号 {order_id} 格式不正确，无法判断工单类型")
            return None

        order_type = order_id[:3].upper()

        # 检查工单类型是否支持
        if order_type not in ORDER_TYPE_MAPPING:
            log.error(f"不支持的工单类型: {order_type}，工单号: {order_id}")
            return None

        # 获取对应的工单类型参数
        order_type_param = ORDER_TYPE_MAPPING[order_type]

        # 构建请求参数
        params = {"userName": USERNAME, "orderNo": order_id, "orderType": order_type_param, "pwd": PASSWORD}

        # 构建完整的URL
        url = f"{BASE_URL}{ORDER_STATUS_API}"

        # 调用外部接口获取工单状态
        log.info(f"调用外部接口获取工单 {order_id} 的状态: {url}")
        response = requests.get(url, params=params, timeout=REQUEST_TIMEOUT)

        # 检查响应状态码
        if response.status_code != 200:
            log.error(f"获取工单 {order_id} 状态失败，状态码: {response.status_code}")
            return None

        # 解析响应数据
        response_data = response.json()

        # 检查响应是否成功
        if not response_data.get("success", False):
            error_msg = response_data.get("msg", "未知错误")
            log.error(f"获取工单 {order_id} 状态失败: {error_msg}")
            log.debug(f"完整响应: {response_data}")
            return None

        # 提取工单状态
        if "data" in response_data and "status" in response_data["data"]:
            data = response_data["data"]
            return data
        else:
            log.warning(f"工单 {order_id} 的响应数据中没有状态信息: {response_data}")
            return None
    except Exception as e:
        log.error(f"获取工单 {order_id} 状态失败，未知错误: {str(e)}")
        return None


def sync_order_logs(**kwargs):
    """
    同步工单操作日志和状态

    从外部接口获取工单的操作日志和状态，并更新到工单中
    只处理非关闭状态的工单
    """
    log.info("开始同步工单操作日志和状态")

    # 查询所有非关闭状态的工单
    non_closed_orders: List[OrderEntity] = order_service.find_orders(status={"$ne": OrderStatusEnum.CLOSED})
    non_closed_orders = list(non_closed_orders)

    if not non_closed_orders:
        log.info("没有找到非关闭状态的工单")
        return

    log.info(f"找到 {len(non_closed_orders)} 个非关闭状态的工单")

    # 遍历工单，获取操作日志和状态并更新
    for order in non_closed_orders:
        try:
            # 调用外部接口获取工单操作日志
            logs, creator_info = get_order_logs_from_external_api(order.order_id)

            # 更新操作日志
            if logs:
                order_service.sync_operation_logs(order.order_id, logs, creator_info)
                log.info(f"工单 {order.order_id} 操作日志同步成功，共 {len(logs)} 条")
            else:
                log.info(f"工单 {order.order_id} 没有操作日志")

            # 获取并更新工单状态
            data = get_order_status(order.order_id)
            if data:
                # 将外部状态映射到系统内部状态
                internal_status = map_external_status_to_internal(data.get("status"))
                order_service.update_order_status(
                    order.order_id,
                    status=internal_status,
                    business_system=data.get("ctiName", None),
                )
                log.info(f"工单 {order.order_id} 状态更新为: {internal_status}，外部状态: {internal_status}")
        except Exception as e:
            log.error(f"同步工单 {order.order_id} 信息失败: {str(e)}")

    log.info("工单操作日志和状态同步完成")


def map_external_status_to_internal(external_status):
    """
    将外部状态映射到系统内部状态

    Args:
        external_status: 外部系统的工单状态

    Returns:
        str: 系统内部的工单状态
    """
    if not external_status:
        return None

    # 使用映射表将外部状态转换为内部状态
    internal_status = EXTERNAL_TO_INTERNAL_STATUS_MAPPING.get(external_status, None)

    if not internal_status:
        log.warning(f"未知的外部状态: {external_status}，使用默认状态: {OrderStatusEnum.PROCESSING}")
        return OrderStatusEnum.PROCESSING

    return internal_status


def get_order_logs_from_external_api(order_id) -> Tuple[List[dict], dict]:
    """
    从外部接口获取工单操作日志

    根据工单号前三个字符判断工单类型，并调用相应的接口：
    - SRM: 服务工单，调用 /xhrsSrmMobileSelectAction!getProcessByNo.action
    - INC: 事件工单，调用 /xhrsIncMobileSelectAction!getProcessByNo.action

    Args:
        order_id: 工单ID

    Returns:
        Tuple[List[Dict], Dict]: 操作日志列表和创建人信息
    """
    try:
        # 根据工单号前三个字符判断工单类型
        if not order_id or len(order_id) < 3:
            log.error(f"工单号 {order_id} 格式不正确，无法判断工单类型")
            return [], {}

        order_type = order_id[:3].upper()
        log.info(f"工单 {order_id} 的类型为: {order_type}")

        # 检查工单类型是否支持
        if order_type not in ORDER_TYPE_APIS:
            log.error(f"不支持的工单类型: {order_type}，工单号: {order_id}")
            return [], {}

        # 获取对应的API路径
        api_path = ORDER_TYPE_APIS[order_type]

        # 构建请求参数
        params = {"userName": USERNAME, "pwd": PASSWORD}

        # 根据工单类型设置不同的参数名
        if order_type == "SRM":
            params["srmNo"] = order_id
        elif order_type == "INC":
            params["incNo"] = order_id

        # 构建完整的URL
        url = f"{BASE_URL}{api_path}"

        # 调用外部接口获取工单操作日志
        log.info(f"调用外部接口获取工单 {order_id} 的操作日志: {url}")
        response = requests.get(url, params=params, timeout=REQUEST_TIMEOUT)

        # 检查响应状态码
        if response.status_code != 200:
            log.error(f"获取工单 {order_id} 操作日志失败，状态码: {response.status_code}")
            return [], {}

        # 解析响应数据
        response_data = response.json()

        # 检查响应是否成功
        if not response_data.get("success", False):
            error_msg = response_data.get("msg", "未知错误")
            log.error(f"获取工单 {order_id} 操作日志失败: {error_msg}")
            log.debug(f"完整响应: {response_data}")
            return [], {}

        # 提取操作日志
        logs = []
        creater_info = {}

        # 根据接口文档，操作日志在 root 字段中
        if "root" in response_data:
            log_items = response_data["root"]
            log.info(f"工单 {order_id} 获取到 {len(log_items)} 条操作日志")

            for log_item in log_items:
                # 转换为我们的操作日志格式
                operation_log = {
                    "operation_type": log_item.get("incStatusName", "未知"),
                    "operator": log_item.get("operatorName", "未知"),
                    "operation_time": log_item.get("operateDate", ""),
                    "content": log_item.get("userlog", ""),
                    "details": {
                        "id": log_item.get("id", ""),
                        "action_code": log_item.get("actionCode", ""),
                        "inc_processing": log_item.get("incProcessing", ""),
                        "conn": log_item.get("conn", ""),
                    },
                }
                if not creater_info.get("update_time"):
                    creater_info["update_time"] = log_item.get("operation_time", "")
                # 如果是"新建"状态，表示这是创建人信息
                if log_item.get("incStatusName") == "新建":
                    creater_info["operator"] = operation_log.get("operator")
                    creater_info["create_time"] = operation_log.get("operation_time")
                    creater_info["creator_id"] = operation_log.get("details", {}).get("id", None)
                    user_infor_list = log_item.get("conn", "").split("，")
                    if len(user_infor_list) > 0:
                        for user_infor in user_infor_list:
                            user_infor = user_infor.replace("：", ":")
                            if "手机号:" in user_infor and len(user_infor.split(":")) > 1:
                                creater_info["mobile"] = user_infor.split(":")[1]
                            elif "办公电话:" in user_infor and len(user_infor.split(":")) > 1:
                                creater_info["phone"] = user_infor.split(":")[1]
                            elif "邮箱:" in user_infor and len(user_infor.split(":")) > 1:
                                creater_info["email"] = user_infor.split(":")[1]
                    log.info(f"工单 {order_id} 的创建人是: {operation_log['operator']}")
                if log_item.get("incStatusName") == "反馈提交人" and "关闭" in log_item.get("actionCode", ""):
                    creater_info["order_status"] = OrderStatusEnum.CLOSED.value
                    creater_info["order_closed_time"] = operation_log.get("operation_time")
                logs.append(operation_log)
        else:
            log.warning(f"工单 {order_id} 的响应数据中没有 root 字段: {response_data}")

        return logs, creater_info
    except Exception as e:
        log.error(f"获取工单 {order_id} 操作日志失败，未知错误: {str(e)}")
        return [], {}


if __name__ == "__main__":
    sync_order_logs()
