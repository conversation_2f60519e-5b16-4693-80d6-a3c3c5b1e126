import traceback

import requests

from caasm_service.constants.setting import SettingName
from caasm_service.entity.data_stream_360 import DataStream360RecordEntity, DataStream360RealmEntity
from caasm_service.runtime import (
    setting_service,
    data_stream_360_record_service,
    data_stream_360_service,
    data_stream_360_realm_service,
)


def _get_setting(setting_name):
    setting = setting_service.get_setting(setting_name)
    if setting:
        return setting.value
    else:
        return None


def sync_data_stream_360():
    url = _get_setting(SettingName.DATA_STREAM_360_URL.value)
    if not url:
        return False
    api_key = _get_setting(SettingName.DATA_STREAM_360_APIKEY.value)
    if not api_key:
        return False
    src_count = _get_setting(SettingName.DATA_STREAM_360_SRC_COUNT.value) or 10
    dst_count = _get_setting(SettingName.DATA_STREAM_360_DST_COUNT.value) or 10
    json_data = {
        "dimensions": [
            {
                "type": "term",
                "field": "src_address_segment_name",
                "top": {"type": "metric", "metric": "count", "field": "id", "order": "desc", "value": src_count},
            },
            {
                "type": "term",
                "field": "dst_address_segment_name",
                "top": {"type": "metric", "metric": "count", "field": "id", "order": "desc", "value": dst_count},
            },
            {"type": "metric", "metric": "count", "field": "id"},
        ],
        "chartType": "AggTablePanel",
        "chartFlag": True,
        "source": "event",
        "filter": "",
        "timeFilter": "发生时间 >=now-15m AND 发生时间 <=now",
        "mode": "bi",
    }

    try:
        response = requests.post(url, json=json_data, headers={"x-api-key": f"{api_key}"}, verify=False)
        if response.status_code == 200:
            result = response.json()
            count = _parse_result(result)
            if count:
                data_stream_360_record_service.save(
                    data_stream_360_record_service.load_entity(successful=True, result=response.text)
                )
                successful = True
            else:
                successful = False
        else:
            error = response.text
            data_stream_360_record_service.save(
                data_stream_360_record_service.load_entity(successful=False, error=error)
            )
            successful = False
    except Exception as exc:
        data_stream_360_record_service.save(
            data_stream_360_record_service.load_entity(successful=False, error=f"{exc}:{traceback.format_exc()}")
        )
        successful = False
    #   仅保留10条采集记录
    if data_stream_360_record_service.count() > 10:
        records = list(data_stream_360_record_service.find(limit=10, sort_fields=[("create_time", -1)]))
        earliest_record: DataStream360RecordEntity = records[-1]
        data_stream_360_record_service.delete_multi({"_id": {"$lt": earliest_record.id}})
    return successful


def _save(streams, force=False):
    if not streams:
        return 0
    if len(streams) > 100 or force:
        data_stream_360_service.save_multi(streams)
        streams.clear()
        return len(streams)
    return 0


def _parse_result(stream_json):
    status_code = stream_json.get("statusCode")
    if status_code != 0:
        return 0
    data = stream_json.get("data")
    if not data:
        return 0
    stream_json = data.get("aggs")
    if not stream_json:
        return 0
    count = 0
    #   加载数据中心安全域数据
    realms = {}
    for realm in data_stream_360_realm_service.find():
        realm: DataStream360RealmEntity = realm
        names = []
        if realm.data_center:
            names.append(realm.data_center)
        if realm.realm:
            names.append(realm.realm)
        realms["_".join(names)] = realm
        realms[realm.data_center] = realm

    data_stream_360_service.begin()
    streams = []
    for stream in stream_json:
        src = stream.get("key")
        if not src:
            continue
        src_segments = src.split("_")
        src_data_center = src_segments[0]
        if len(src_segments) > 1:
            src_realm = src_segments[1]
        else:
            src_realm = None
        dst_list = stream.get("value")
        if src in realms:
            src_province = realms[src].province
        elif src_data_center in realms:
            src_province = realms[src_data_center].province
        else:
            # src_province = "未知"
            continue
        if not dst_list:
            continue
        for dst_dict in dst_list:
            dst = dst_dict.get("key")
            if not dst:
                continue
            dst_segments = dst.split("_")
            dst_data_center = dst_segments[0]
            if len(dst_segments) > 1:
                dst_realm = dst_segments[1]
            else:
                dst_realm = None
            if dst in realms:
                dst_province = realms[dst].province
            elif dst_data_center in realms:
                dst_province = realms[dst_data_center].province
            else:
                # dst_province = "未知"
                continue
            count_list = dst_dict.get("value")
            if not count_list:
                continue
            count = count_list[0]
            streams.append(
                data_stream_360_service.load_entity(
                    src_province=src_province,
                    src_data_center=src_data_center,
                    src_realm=src_realm,
                    dst_province=dst_province,
                    dst_data_center=dst_data_center,
                    dst_realm=dst_realm,
                    count=count,
                    finished=False,
                )
            )
        count += _save(streams)
    count += _save(streams, True)
    data_stream_360_service.finish()
    return count


if __name__ == "__main__":
    sync_data_stream_360()
