from caasm_manage.util import start_fabric_workflow
from caasm_workflow.sdk.workflow import workflow_sdk


def data_collect(**kwargs):
    start_fabric_workflow()


def data_fetch(adapter_name, adapter_instance_id, fetch_type):
    params = {"adapter_name": adapter_name, "adapter_instance_id": adapter_instance_id, "category": fetch_type}
    workflow_sdk.start_workflow("adapter_instance_fetch", params=params)
