import requests
import datetime
from typing import Dict, Optional, List

from caasm_service.entity.order import OrderEntity
from caasm_service.runtime import order_service
from caasm_service.constants.order import OrderStatusEnum
from caasm_tool.constants import DATETIME_FORMAT
from caasm_scheduler.jobs.order_logs_sync import ORDER_TYPE_MAPPING
from caasm_tool import log

# 默认配置（从配置文件中读取，如果不存在则使用默认值）
DEFAULT_BASE_URL = "http://10.1.196.100"
DEFAULT_REQUEST_TIMEOUT = 30  # 默认请求超时时间（秒）
API_KEY = "50d5687015e7c86b9e51f095f367f6ab"  # api_key

# 工单详情API路径
ORDER_DETAILS_API = "/__rest/work_orders"


def get_order_details(order_id: str) -> Optional[Dict]:
    """
    从360工单系统获取工单详情

    Args:
        order_id: 工单ID

    Returns:
        Dict: 工单详情，如果获取失败则返回None
    """
    try:
        # 构建完整的URL
        url = f"{DEFAULT_BASE_URL}{ORDER_DETAILS_API}/{order_id}"

        # 调用外部接口获取工单详情
        log.info(f"调用外部接口获取工单 {order_id} 的详情: {url}")
        response = requests.get(url, headers={"x-api-key": f"{API_KEY}"}, verify=False, timeout=DEFAULT_REQUEST_TIMEOUT)

        # 检查响应状态码
        if response.status_code != 200:
            log.error(f"获取工单 {order_id} 详情失败，状态码: {response.status_code}")
            return None

        # 解析响应数据
        response_data = response.json()

        # 检查响应是否成功
        if response_data.get("statusCode", 1) != 0:
            error_msg = response_data.get("messages", ["未知错误"])[0]
            log.error(f"获取工单 {order_id} 详情失败: {error_msg}")
            log.debug(f"完整响应: {response_data}")
            return None

        # 提取工单详情
        order_details = response_data.get("data", {})
        if not order_details:
            log.error(f"工单 {order_id} 详情为空")
            return None

        log.info(f"成功获取工单 {order_id} 的详情")
        return order_details
    except Exception as e:
        log.error(f"获取工单 {order_id} 详情失败，未知错误: {str(e)}")
        return None


def update_order_with_details(order: OrderEntity, details: Dict) -> bool:
    """
    使用获取到的工单详情更新工单信息

    Args:
        order: 工单实体
        details: 工单详情

    Returns:
        bool: 更新是否成功
    """
    try:
        # 提取需要更新的字段
        creator_info = {}

        # 工单标题
        if "title" in details and details["title"]:
            creator_info["title"] = details["title"]

        # 工单内容/描述
        if "content" in details and details["content"]:
            creator_info["description"] = details["content"]
        if details.get("createTime"):
            creator_info["create_time"] = format_datetime(details.get("createTime"))
        if details.get("updateTime"):
            creator_info["update_time"] = format_datetime(details.get("updateTime"))
        if details.get("endTime"):
            creator_info["finish_time"] = format_datetime(details.get("endTime"))
        if details.get("workOrderCreator"):
            creator_info["operator"] = details.get("workOrderCreator")
            creator_info["creator_id"] = details.get("workOrderCreator")
        # 工单操作历史
        operation_logs = []
        if "history" in details and details["history"]:
            for history_item in details["history"]:
                operation_log = {
                    "operator": history_item.get("operator", None),
                    "operation_time": format_datetime(history_item.get("time", None)),
                    "content": history_item.get("audit", ""),
                    "details": {
                        "inc_processing": history_item.get("audit", ""),
                        "id": history_item.get("operation", ""),
                        "comment": history_item.get("comment", ""),
                    },
                }
                operation_logs.append(operation_log)
        # 更新工单信息
        result = order_service.sync_operation_logs(order.order_id, logs=operation_logs, creator_info=creator_info)
        if result:
            log.info(f"工单 {order.order_id} 详情更新成功")
            return True
        else:
            log.error(f"工单 {order.order_id} 详情更新失败")
            return False

    except Exception as e:
        log.error(f"更新工单 {order.order_id} 详情失败: {str(e)}")
        return False


def sync_order_details_360():
    """
    同步工单详情

    从360工单系统获取工单详情，并更新到工单中
    只处理非关闭状态的工单

    Args:
        **_: 忽略的参数，用于兼容调度器的参数传递
    """
    log.info("开始同步工单详情")

    non_closed_orders: List[OrderEntity] = order_service.find_orders(status={"$ne": OrderStatusEnum.CLOSED})
    non_closed_orders = list(non_closed_orders)

    if not non_closed_orders:
        log.info("没有找到非关闭状态的工单")
        return

    log.info(f"找到 {len(non_closed_orders)} 个非关闭状态的工单")

    # 遍历工单，获取详情并更新
    success_count = 0
    for order in non_closed_orders:
        try:
            order_type = order.order_id[:3].upper()

            # 检查工单类型是否支持
            if order_type in ORDER_TYPE_MAPPING:
                continue
            # 调用外部接口获取工单详情
            details = get_order_details(order.order_id)

            # 如果获取到详情，则更新工单
            if details:
                if update_order_with_details(order, details):
                    success_count += 1
            else:
                log.warning(f"未能获取工单 {order.order_id} 的详情")
        except Exception as e:
            log.error(f"同步工单 {order.order_id} 详情失败: {str(e)}")

    log.info(f"工单详情同步完成，成功更新 {success_count} 个工单")


def format_datetime(timestamp):
    if isinstance(timestamp, int) or (isinstance(timestamp, str) and timestamp.isdigit()):
        timestamp = int(timestamp)
        # 判断是否为毫秒级时间戳
        if timestamp > 1000000000000:
            timestamp = timestamp / 1000
        dt = datetime.datetime.fromtimestamp(timestamp)
        return dt.strftime(DATETIME_FORMAT)
    else:
        return timestamp


if __name__ == "__main__":
    sync_order_details_360()
