import json
import logging
import traceback
import ipaddress
import socket
from typing import List, Dict, Any, Optional

from confluent_kafka import Consumer, KafkaError

from caasm_config.config import caasm_config
from caasm_service.entity.data_stream_360 import DataStream360RealmEntity, DataStream360IpSegmentEntity
from caasm_service.runtime import intercept_alarm_service, data_stream_360_realm_service
from caasm_tool.util import get_now


log = logging.getLogger()


def validate_kafka_connection(bootstrap_servers: str) -> bool:
    """
    验证Kafka服务器连接是否可用

    Args:
        bootstrap_servers: Kafka服务器地址，格式为"host:port,host:port"

    Returns:
        bool: 连接是否可用
    """
    try:
        # 解析所有服务器地址
        servers = bootstrap_servers.split(",")
        for server in servers:
            host, port = server.strip().split(":")
            # 尝试解析主机名
            socket.gethostbyname(host)
        return True
    except Exception as e:
        log.error(f"Kafka服务器地址验证失败: {e}")
        return False


def sync_kafka_intercept_alarms(max_messages: int = 0):
    """
    从Kafka获取访问拦截告警数据，并保存到系统中

    Args:
        max_messages: 最大处理消息数量，0表示不限制
    """
    # 从配置文件中获取告警Kafka配置
    alarm_kafka_config = getattr(caasm_config, "ALARM", {}).get("kafka", {})
    if not alarm_kafka_config:
        log.error("未在配置文件中找到告警Kafka配置")
        return False

    bootstrap_servers = alarm_kafka_config.get("bootstrap_servers")
    if not bootstrap_servers:
        log.error("未配置Kafka服务器地址")
        return False
    # 验证Kafka服务器地址
    if not validate_kafka_connection(bootstrap_servers):
        log.error(f"Kafka服务器地址无效: {bootstrap_servers}")
        return False

    topic_name = alarm_kafka_config.get("topic")
    if not topic_name:
        log.error("未配置Kafka主题")
        return False

    # 每次使用带时间戳的消费者组ID，确保从最早的消息开始消费
    import time

    group_id = alarm_kafka_config.get("group_id", f"caasm_intercept_group_{int(time.time())}")

    # 预加载所有安全域信息
    realms: List[DataStream360RealmEntity] = list(data_stream_360_realm_service.find())
    log.info(f"已加载 {len(realms)} 个安全域信息")

    # 创建Kafka消费者配置
    conf = {
        "bootstrap.servers": bootstrap_servers,
        "group.id": group_id,
        "auto.offset.reset": "earliest",  # 从最早的偏移量开始消费，以获取历史数据
        "enable.auto.commit": True,
        "max.poll.interval.ms": 300000,  # 5分钟
        "session.timeout.ms": 10000,  # 10秒
        "heartbeat.interval.ms": 3000,  # 3秒
        "fetch.max.bytes": 1048576,  # 1MB
        "fetch.min.bytes": 1,
        "fetch.wait.max.ms": 500,  # 500ms
        "max.partition.fetch.bytes": 1048576,  # 1MB
        "socket.timeout.ms": 10000,  # 10秒
        "metadata.max.age.ms": 300000,  # 5分钟
        "reconnect.backoff.ms": 1000,  # 1秒
        "reconnect.backoff.max.ms": 10000,  # 10秒
    }

    # 创建Kafka消费者
    try:
        consumer = Consumer(conf)
        consumer.subscribe([topic_name])

        processed_count = 0
        retry_count = 0
        max_retries = 3

        while True:
            # 检查是否达到最大消息数量限制
            if max_messages > 0 and processed_count >= max_messages:
                log.info(f"已达到最大处理消息数量限制 {max_messages}，停止消费")
                break

            try:
                # 设置超时时间为5秒，确保不会长时间阻塞调度任务
                msg = consumer.poll(5.0)

                if msg is None:
                    log.info("没有更多消息，退出消费")
                    break

                if msg.error():
                    if msg.error().code() == KafkaError._PARTITION_EOF:
                        # 到达分区末尾
                        log.info(f"到达分区 {msg.partition()} 末尾")
                        continue
                    else:
                        log.error(f"Kafka错误: {msg.error()}")
                        retry_count += 1
                        if retry_count >= max_retries:
                            log.error("达到最大重试次数，退出")
                            break
                        continue

                # 重置重试计数
                retry_count = 0

                try:
                    # 处理消息
                    alarm_data = json.loads(msg.value().decode("utf-8"))

                    # 记录消息元数据
                    log.debug(f"处理消息: 主题={msg.topic()}, 分区={msg.partition()}, 偏移量={msg.offset()}")

                    # 构建唯一键
                    src_ip = alarm_data.pop("srcIp", "")
                    dst_ip = alarm_data.pop("dstIp", "")

                    # 查找源IP和目的IP的数据中心和安全域信息
                    src_realm_info = find_realm_info(src_ip, realms)
                    dst_realm_info = find_realm_info(dst_ip, realms)

                    # 将安全域信息添加到告警数据中
                    alarm_data["src_ip"] = src_ip
                    alarm_data["dst_ip"] = dst_ip
                    alarm_data["src_data_center"] = src_realm_info.get("data_center", None)
                    alarm_data["src_realm"] = src_realm_info.get("realm", None)
                    alarm_data["src_province"] = src_realm_info.get("province", None)
                    alarm_data["dst_data_center"] = dst_realm_info.get("data_center", None)
                    alarm_data["dst_realm"] = dst_realm_info.get("realm", None)
                    alarm_data["dst_province"] = dst_realm_info.get("province", None)

                    _create_intercept_alarm(alarm_data)
                    processed_count += 1
                except Exception as e:
                    log.error(f"处理Kafka消息时出错: {e}\n{traceback.format_exc()}")

            except Exception as e:
                log.error(f"Kafka消费者出错: {e}\n{traceback.format_exc()}")
                retry_count += 1
                if retry_count >= max_retries:
                    log.error("达到最大重试次数，退出")
                    break

        consumer.close()
        log.info(f"成功处理 {processed_count} 条访问拦截告警数据")
        return True

    except Exception as e:
        log.error(f"从Kafka获取访问拦截告警时出错: {e}\n{traceback.format_exc()}")
        return False


def find_realm_info(ip: str, realms: List[DataStream360RealmEntity]) -> Dict[str, str]:
    """
    根据IP查找数据中心和安全域信息

    Args:
        ip: IP地址
        realms: 安全域信息列表

    Returns:
        包含data_center和realm的字典，如果未找到则返回空值
    """
    if not ip:
        return {"data_center": "", "realm": ""}

    try:
        # 将IP转换为IPv4Address对象进行比较
        ip_obj = ipaddress.ip_address(ip)

        for realm in realms:
            # 检查每个安全域的IP段
            for segment in realm.segments:
                # 检查CIDR范围
                if segment.cidr:
                    try:
                        network = ipaddress.ip_network(segment.cidr)
                        if ip_obj in network:
                            return {"data_center": realm.data_center, "realm": realm.realm, "province": realm.province}
                    except ValueError:
                        continue
        for realm in realms:
            # 检查每个安全域的IP段
            for segment in realm.segments:
                # 检查起始和结束IP地址范围
                if segment.start and segment.end:
                    try:
                        start_ip_obj = ipaddress.ip_address(segment.start)
                        end_ip_obj = ipaddress.ip_address(segment.end)
                        if start_ip_obj <= ip_obj <= end_ip_obj:
                            return {"data_center": realm.data_center, "realm": realm.realm, "province": realm.province}
                    except ValueError:
                        continue

        # 未找到匹配的安全域
        return {"data_center": "", "realm": "", "province": ""}
    except ValueError:
        # IP地址格式无效
        return {"data_center": "", "realm": "", "province": ""}


def _create_intercept_alarm(alarm_data: Dict[str, Any]):
    """创建新的拦截告警记录"""
    # 从Kafka数据中提取告警信息
    src_ip = alarm_data.get("src_ip", "")
    src_group_relationship_name = alarm_data.get("src_group", "")
    dst_ip = alarm_data.get("dst_ip", "")
    dst_port = alarm_data.get("dst_port", 0)
    dst_group_relationship_name = alarm_data.get("dst_group", "")
    count = 1  # 初始拦截次数为1
    reason = alarm_data.get("reason", f"检测到从 {src_ip} 到 {dst_ip}:{dst_port} 的流量被拦截")

    # 获取数据中心和安全域信息
    src_data_center = alarm_data.get("src_data_center", "")
    src_realm = alarm_data.get("src_realm", "")
    dst_data_center = alarm_data.get("dst_data_center", "")
    dst_realm = alarm_data.get("dst_realm", "")
    src_province = alarm_data.get("src_province", "")
    dst_province = alarm_data.get("dst_province", "")

    # 构建告警记录
    alarm = intercept_alarm_service.load_entity(
        **{
            "src_ip": src_ip,
            "src_group_relationship_name": src_group_relationship_name,
            "dst_ip": dst_ip,
            "dst_port": dst_port,
            "dst_group_relationship_name": dst_group_relationship_name,
            "count": count,
            "reason": reason,
            "src_data_center": src_data_center,
            "src_realm": src_realm,
            "dst_data_center": dst_data_center,
            "dst_realm": dst_realm,
            "create_time": get_now(),
            "update_time": get_now(),
            "src_province": src_province,
            "dst_province": dst_province,
        }
    )

    intercept_alarm_service.add_intercept_alarm_record(alarm)


def _update_intercept_alarm(record_id, alarm_data: Dict[str, Any]):
    """更新现有拦截告警记录"""
    existing_alarm = intercept_alarm_service.get_intercept_alarm_info(record_id=record_id)
    if not existing_alarm:
        return

    # 增加拦截次数
    count = existing_alarm.get("count", 0) + 1

    # 更新其他可能变化的字段
    src_group_relationship_name = alarm_data.get("src_group", existing_alarm.get("src_group_relationship_name", ""))
    dst_group_relationship_name = alarm_data.get("dst_group", existing_alarm.get("dst_group_relationship_name", ""))
    reason = alarm_data.get("reason", existing_alarm.get("reason", ""))

    # 获取数据中心和安全域信息
    src_data_center = alarm_data.get("src_data_center", existing_alarm.get("src_data_center", ""))
    src_realm = alarm_data.get("src_realm", existing_alarm.get("src_realm", ""))
    dst_data_center = alarm_data.get("dst_data_center", existing_alarm.get("dst_data_center", ""))
    dst_realm = alarm_data.get("dst_realm", existing_alarm.get("dst_realm", ""))

    # 更新拦截告警记录
    intercept_alarm_service.update_intercept_alarm_by_id(
        record_id=record_id,
        value={
            "count": count,
            "src_group_relationship_name": src_group_relationship_name,
            "dst_group_relationship_name": dst_group_relationship_name,
            "reason": reason,
            "src_data_center": src_data_center,
            "src_realm": src_realm,
            "dst_data_center": dst_data_center,
            "dst_realm": dst_realm,
            "update_time": get_now(),
        },
    )


if __name__ == "__main__":
    sync_kafka_intercept_alarms()
