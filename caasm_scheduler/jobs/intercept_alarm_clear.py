import datetime
import logging
from typing import List, Optional

from bson import ObjectId

from caasm_service.entity.intercept_alarm import InterceptAlarmEntity
from caasm_service.runtime import intercept_alarm_service
from caasm_tool.util import get_now

log = logging.getLogger()


def intercept_alarm_clear(days: int = 2) -> bool:
    """
    清理拦截告警数据，只保留最近n天的数据

    Args:
        days: 要保留的天数，默认为2天

    Returns:
        bool: 清理是否成功
    """
    try:
        # 计算截止时间，比当前时间早n天的日期
        now: datetime.datetime = get_now()
        cutoff_date: datetime.datetime = now - datetime.timedelta(days=days)

        # 查找所有创建时间早于截止日期的告警记录
        records: List[InterceptAlarmEntity] = intercept_alarm_service.find_intercept_alarm_records()
        old_record_ids: List[ObjectId] = []

        for record in records:
            # 对于InterceptAlarmEntity对象，直接访问create_time属性
            if record.create_time and record.create_time < cutoff_date:
                old_record_ids.append(record.id)

        # 删除过期记录
        if old_record_ids:
            deleted_count: int = intercept_alarm_service.delete_intercept_alarm_records(record_ids=old_record_ids)
            log.info(f"已清理 {deleted_count} 条超过 {days} 天的拦截告警记录")
        else:
            log.info(f"没有找到超过 {days} 天的拦截告警记录")

        return True
    except Exception as e:
        log.error(f"清理拦截告警记录时出错: {e}")
        return False


if __name__ == "__main__":
    intercept_alarm_clear()
