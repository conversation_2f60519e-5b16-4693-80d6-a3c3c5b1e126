import datetime
import logging
from collections import defaultdict

from caasm_service.entity.meta_model import MetaFieldType
from caasm_service.runtime import (
    meta_view_service,
    meta_asset_type_view_service,
    meta_model_service,
    meta_field_service,
    entity_service,
    meta_field_snapshot_record_service,
    meta_model_snapshot_record_service,
    asset_type_service,
)
from caasm_tool.util import extract, deduplicate

log = logging.getLogger()


class BaseQuery(object):
    _meta_model_field_names = ["friends"]
    _asset_type_field = "base.asset_type"
    _model_sort_fields = [("priority", 1)]
    _field_sort_fields = [("priority", 1)]
    _default_pk_field = "base.entity_id"
    _meta_view_default_field = ["pk_field"]

    @classmethod
    def get_entity_data(cls, category, date, entity_id, fields=None):
        return entity_service.get_entity(category, cls.get_pk_field(category), entity_id, fields=fields, date=date)

    @classmethod
    def get_asset_type(cls, entity):
        return cls.extract(entity, cls._asset_type_field)

    @classmethod
    def get_asset_type_view(cls, category, entity):
        asset_type = cls.get_asset_type(entity)
        if not asset_type:
            return None
        res = meta_asset_type_view_service.get_meta_asset_type_view(category=category, asset_type=asset_type)
        if not res:
            return None
        return res

    @classmethod
    def get_meta_view(cls, category, fields=None):
        meta_view = meta_view_service.get_view(category, fields=fields)
        return meta_view

    @classmethod
    def find_field_to_model_mapper(cls, category, date):
        meta_models, fields = cls.find_model_and_field(category, date=date)
        field_mapper = defaultdict(list)
        for field in fields:
            field_mapper[field.model_id].append(field)
        result = {meta_model.id: field_mapper[meta_model.id] for meta_model in meta_models}
        return meta_models, result

    @classmethod
    def find_field_to_list(cls, category):
        meta_models, fields = cls.find_model_and_field(category)
        return fields

    @classmethod
    def find_field_to_mapper(cls, category, use_snapshot=True, date=""):
        meta_models, fields = cls.find_model_and_field(category, use_snapshot, date)
        return {field.full_name: field for field in cls.open_up_field(fields)}

    @classmethod
    def find_model(cls, category, meta_model_ids=None):
        if not meta_model_ids:
            meta_models = meta_model_service.find_meta_model(category=category, sort_fields=cls._model_sort_fields)
            meta_model_ids = []
            for meta_model in meta_models:
                meta_model_ids.append(meta_model.id)
                meta_model_ids.extend(meta_model.friends) if meta_model.friends else ...
            meta_model_ids = deduplicate(meta_model_ids)
            if not meta_model_ids:
                return []
        cur = meta_model_service.find_meta_model(model_ids=meta_model_ids, sort_fields=cls._model_sort_fields)
        return list(cur)

    @classmethod
    def extract(cls, entity_data, field):
        return extract(entity_data, field)

    @classmethod
    def find_model_and_field(cls, category, use_snapshot=True, date=""):

        meta_model_ids = []
        if use_snapshot and date:
            _meta_model_snapshot = meta_model_snapshot_record_service.get_meta_model(category, date=date)
            if not _meta_model_snapshot:
                log.warning(f"Not found category({category}) date({date}) meta_model snapshot")
            else:
                meta_model_ids = _meta_model_snapshot.meta_model_ids

        meta_models = cls.find_model(category, meta_model_ids=meta_model_ids)
        if not meta_models:
            return [], []

        meta_model_ids = [meta_model.id for meta_model in meta_models]
        meta_model_id_flag = set(meta_model_ids)
        fields = []
        queried_snapshot_model_id_flag = set()

        if use_snapshot and date:
            meta_model_snapshots = meta_field_snapshot_record_service.find_meta_field(meta_model_ids, date=date)
            for meta_model_snapshot in meta_model_snapshots:
                queried_snapshot_model_id_flag.add(meta_model_snapshot.model_id)
        need_requery_model_ids = list(meta_model_id_flag - queried_snapshot_model_id_flag)
        if need_requery_model_ids:
            cur = meta_field_service.find_meta_field(
                model_ids=need_requery_model_ids, sort_fields=cls._field_sort_fields
            )
            fields.extend(list(cur))

        return meta_models, fields

    @classmethod
    def open_up_field(cls, fields, result=None):
        if result is None:
            result = []

        for field in fields:
            children = field.children
            field_type = field.type

            if field_type == MetaFieldType.LIST:
                if children and children[0].type == MetaFieldType.OBJECT:
                    children = children[0].children
                else:
                    children = []

            result.append(field)
            cls.open_up_field(children, result)
        return result

    @classmethod
    def get_pk_field(cls, category):
        meta_view = cls.get_meta_view(category, fields=cls._meta_view_default_field)
        return meta_view.pk_field if meta_view else cls._default_pk_field
