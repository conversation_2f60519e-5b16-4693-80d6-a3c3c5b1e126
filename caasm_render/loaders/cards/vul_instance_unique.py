import logging
import traceback

from nanoid import generate
from pyorient import OrientRecord

from caasm_businesses.enums import GRADE_PROTECTION_LEVEL_MAPPING, MAGNITUDE_MAPPING, CRITICAL_INFRASTRUCTURE_MAPPER
from caasm_meta_data.constants import Category
from caasm_render.loaders.cards.base import Card<PERSON><PERSON><PERSON>and<PERSON>
from caasm_render.loaders.constants import CardType
from caasm_service.runtime import entity_service
from caasm_service.service.asset_graph_service import <PERSON><PERSON>tyGraph<PERSON>and<PERSON>
from caasm_tool.util import deduplicate
from caasm_vul.consts import (
    BUSINESS_LEVEL_MAPPING,
    WEB_PROTECTION_COVERAGES,
    DB_PROTECTION_COVERAGES,
    PROTECTION_COVERAGES,
    DATA_COVERAGES,
    RESPONSE_COVERAGES,
)

log = logging.getLogger()


class VulInstanceUniqueCardHandler(CardBaseHandler):
    _asset_vul_cve_id_field = "vul_instance_unique.cve_id"
    _asset_vul_asset_field = "vul_instance_unique.asset"
    _vul_instance_unique_traits_field = "vul_instance_unique.traits"
    _vul_instance_field_mapper = {
        "name": "vul_instance_unique.name",
        "status": "vul_instance_unique.status",
        "update_datetime": "base.last_seen",
        "sources": "vul_instance_unique.source",
        "severity": "vul_instance_unique.severity",
        "priority_score": "vul_instance_unique.priority_score",
        "remediation_severity": "vul_instance_unique.remediation_severity",
        "priority_hits": "vul_instance_unique.priority_hits",
        "cve_id": _asset_vul_cve_id_field,
        "description": "vul_instance_unique.description",
        "priority": "vul_instance_unique.priority",
        "adapter_sources": "base.adapters",
    }

    _vul_entity_id_field = "base.entity_id"
    _vul_cvss3_base_score_field = "vul.cvss_3.base_score"
    _vul_cvss2_base_score_field = "vul.cvss_2.base_score"
    _vul_exploits_field = "vul.exploits"
    _vul_cwes_field = "vul.cwes"
    _vul_traits_field = "vul.traits"
    _vul_cvss_3_field = "vul.cvss_3"
    _vul_field_mapper = {
        "solution": "vul.solution",
        "cnvd": "vul.cnvd_id",
        "cnnvd": "vul.cnnvd_id",
        "published": "vul.published",
        "vendor_advisory": "vul.vendor_advisory",
        "exploits": _vul_exploits_field,
        "cvss3_vector": "vul.cvss_3.vector",
        "cvss3_base_score": "vul.cvss_3.base_score",
        "av": "vul.cvss_3.av",
        "ac": "vul.cvss_3.ac",
        "pr": "vul.cvss_3.pr",
        "cvss2_vector": "vul.cvss_2.vector",
        "cvss2_base_score": "vul.cvss_2.base_score",
    }

    _asset_id_field = "base.entity_id"
    _asset_field_mapper = {
        "asset_ip": "network.priority_addr",
        "os_type": "computer.os.type",
    }
    _asset_port_field = "network.ports"
    _asset_port_mapper = {
        "internal_ip": "ip",
        "internal_port": "number",
        "internet_ip": "internet_ip",
        "internet_port": "internet_port",
    }
    _asset_port_meta_field_mapper = {
        "ip": "network.ports.ip",
        "number": "network.ports.number",
        "internet_ip": "network.ports.internet_ip",
        "internet_port": "network.ports.internet_port",
    }
    _asset_business_field = "asset_base.businesses"
    _asset_adapter_properties_field = "asset_base.adapter_properties"
    _asset_db_field = "computer.dbs"
    _asset_website_field = "computer.websites"
    _asset_account_field = "computer.accounts"
    _asset_type_name_field = "base.asset_type_display_name"
    _asset_owner_field = "asset_base.owners"

    _business_name_field = "business.name"

    @property
    def card_type(self):
        return CardType.VUL_INSTANCE_UNIQUE

    @classmethod
    def _build_impact(cls, impact, impacts_node, impact_name: str):
        if impact == "N":
            impact_severity = "none"
            name = f"无{impact_name}影响"
        elif impact == "L":
            impact_severity = "medium"
            name = f"{impact_name}影响为低"
        else:
            impact_severity = "critical"
            name = f"{impact_name}影响为高"
        impact_id = generate()
        impacts_node["children"].append({"name": name, "id": impact_id, "severity": impact_severity})

    @classmethod
    def _padding_field_info(cls, manager, meta_field_mapper, record, result):
        cls.__padding_core(manager, meta_field_mapper, cls._vul_instance_field_mapper, record, result)

    @classmethod
    def _padding_vul_info(cls, manager, record, result, exploitability_node, impact_node, risk_node):
        cve_id = cls.extract(record, cls._asset_vul_cve_id_field)
        if not cve_id:
            return
        vul_mapper = entity_service.get_entity(Category.VUL, "base.entity_id", cve_id)
        if not vul_mapper:
            return

        _vulnerability_field_mapper = manager.default_query.find_field_to_mapper(Category.VUL)

        cls.__padding_core(manager, _vulnerability_field_mapper, cls._vul_field_mapper, vul_mapper, result)

        base_score = (
            cls.extract(vul_mapper, cls._vul_cvss3_base_score_field)
            or cls.extract(vul_mapper, cls._vul_cvss2_base_score_field)
            or 0
        )

        if base_score == 0:
            severity = "none"
        elif base_score < 4:
            severity = "low"
        elif base_score < 7:
            severity = "medium"
        elif base_score < 9:
            severity = "high"
        else:
            severity = "critical"
        vul_score_node = {
            "name": f"CVSS基础评分为{base_score}",
            "id": generate(),
            "severity": severity,
        }
        exploitability_node["children"].append(vul_score_node)

        vectors = dict()
        vector = result.get("cvss2_vector").get("value") or result.get("cvss3_vector").get("value")
        vector_split = vector.split("/") if vector else []
        for segment in vector_split:
            segment_split = segment.split(":")
            vectors[segment_split[0]] = segment_split[1]
        integrity_impact = vectors.get("I")
        confidentiality_impact = vectors.get("C")
        availability_impact = vectors.get("A")

        vul_impact_node = {
            "id": generate(),
            "name": "对目标资产影响",
            "severity": "none",
            "children": [],
        }
        impact_node["children"].append(vul_impact_node)
        cls._build_impact(integrity_impact, vul_impact_node, "完整性")
        cls._build_impact(confidentiality_impact, vul_impact_node, "保密性")
        cls._build_impact(availability_impact, vul_impact_node, "可用性")

        #   攻击/访问方式
        access_vector = vectors.get("AV")
        if access_vector == "L":
            name = "访问/攻击方式为本地"
            severity = "medium"
        elif access_vector == "N":
            name = "访问/攻击方式为网络"
            severity = "critical"
        elif access_vector == "A":
            name = "访问/攻击方式为域内网络"
            severity = "high"
        else:
            name = "访问/攻击方式为物理"
            severity = "low"
        risk_node["children"].append({"id": generate(), "severity": severity, "name": name})

        #   利用脚本
        exploits = cls.extract(vul_mapper, cls._vul_exploits_field)
        if exploits:
            exps_node = {
                "name": "利用脚本",
                "id": generate(),
                "severity": "none",
                "children": [],
            }
            exploitability_node["children"].append(exps_node)
            sources_already_used = set()
            for exploit in exploits:
                source = exploit["source"]
                if source in sources_already_used:
                    continue
                sources_already_used.add(source)
                exp_node = {
                    "name": f"{source}有POC或EXP脚本",
                    "id": generate(),
                    "severity": "critical",
                }
                exps_node["children"].append(exp_node)
        else:
            no_exp_node = {
                "name": "未发现POC或EXP脚本",
                "id": generate(),
                "severity": "low",
            }
            exploitability_node["children"].append(no_exp_node)

        #   CWE
        cwes = cls.extract(record, cls._vul_cwes_field)
        if cwes:
            cwes_node = {
                "id": generate(),
                "name": "漏洞CWE",
                "severity": "none",
                "children": [],
            }
            risk_node["children"].append(cwes_node)
            for cwe in cwes:
                cwes_node["children"].append(
                    {
                        "id": generate(),
                        "name": cwe,
                        "severity": "critical",
                    }
                )

        #   标签
        tags = cls.extract(vul_mapper, cls._vul_traits_field)
        if tags:
            tags_node = {
                "id": generate(),
                "name": "漏洞特征",
                "severity": "none",
                "children": [],
            }
            risk_node["children"].append(tags_node)
            for tag in tags:
                tags_node["children"].append({"id": generate(), "name": tag, "severity": "critical"})

    @classmethod
    def _padding_asset_info(cls, manager, date, record, result, exploitability_node, impact_node, response_node):
        asset_relation = cls.extract(record, cls._asset_vul_asset_field)
        if not asset_relation:
            return
        asset_id = asset_relation.get("rel_id")
        asset_dict = entity_service.get_entity(Category.ASSET, cls._asset_id_field, asset_id, date=date)
        if not asset_dict:
            return
        asset_meta_field_mapper = manager.default_query.find_field_to_mapper(Category.ASSET)

        cls.__padding_core(manager, asset_meta_field_mapper, cls._asset_field_mapper, asset_dict, result)

        ports = cls.extract(asset_dict, cls._asset_port_field) or []
        exposures = []
        for port in ports:
            if not (port.get("exposure") and port.get("exposure").get("value") == 3):
                continue

            exposure = {}
            cls.__padding_single_data(
                manager,
                cls._asset_port_mapper,
                cls._asset_port_meta_field_mapper,
                asset_meta_field_mapper,
                port,
                exposure,
            )
            exposures.append(exposure)
        result["exposures"] = exposures
        expose_node = {
            "id": generate(),
            "name": "互联网暴露度",
            "severity": "none",
            "children": [],
        }
        exploitability_node["children"].append(expose_node)
        if exposures:
            expose_node["children"].append(
                {
                    "id": generate(),
                    "name": "资产有外网映射",
                    "severity": "critical",
                }
            )
        else:
            expose_node["children"].append({"id": generate(), "name": "资产无外网映射", "severity": "low"})

        businesses = cls.extract(record, cls._asset_business_field) or []
        business_severities = []
        businesses_node = {
            "name": "业务系统",
            "id": generate(),
            "severity": "none",
            "children": [],
        }
        impact_node["children"].append(businesses_node)
        for business in businesses:
            business_name = business.get("name")
            business_mapper = entity_service.get_entity(Category.BUSINESS, cls._business_name_field, business_name)
            if not business_mapper:
                continue
            business_severity = {}
            business_name = business_mapper.get("full_name", "")

            grade_protection_level = business_mapper.get("grade_protection_level", {}).get("value")
            gpl_desc = GRADE_PROTECTION_LEVEL_MAPPING.get(grade_protection_level, "未知")

            magnitude = business_mapper.get("magnitude", {}).get("value")
            magnitude_desc = MAGNITUDE_MAPPING.get(magnitude, "未知")

            critical_infrastructure = business_mapper.get("critical_infrastructure", {}).get("value")
            ci_desc = CRITICAL_INFRASTRUCTURE_MAPPER.get(critical_infrastructure, "未知")

            business_severity["name"] = business_name
            business_severity["grade_protection_level"] = gpl_desc
            business_severity["magnitude"] = magnitude_desc
            business_severity["critical_infrastructure"] = ci_desc

            business_severities.append(business_severity)

            business_score, severity, level = BUSINESS_LEVEL_MAPPING[grade_protection_level]
            business_node = {
                "name": business_name,
                "id": generate(),
                "severity": "none",
                "children": [],
            }
            businesses_node["children"].append(business_node)
            business_node["children"].append(
                {
                    "name": f"等保等级为{level}",
                    "id": generate(),
                    "severity": severity,
                }
            )
        result["business_severities"] = business_severities

        adapter_properties = cls.extract(asset_dict, cls._asset_adapter_properties_field) or []
        if adapter_properties:
            adapter_properties = deduplicate(adapter_properties)

        workloads = {}
        workloads_node = {
            "id": generate(),
            "name": "工作负载",
            "severity": "none",
            "children": [],
        }
        impact_node["children"].append(workloads_node)
        workloads_dbs = list()
        dbs = cls.extract(asset_dict, cls._asset_db_field)
        if dbs:
            dbs_node = {
                "id": generate(),
                "name": "数据库",
                "severity": "none",
                "children": [],
            }
            workloads_node["children"].append(dbs_node)
            for db in dbs:
                db_name = db.get("name")
                if db_name:
                    workloads_dbs.append(db_name)
                    dbs_node["children"].append(
                        {
                            "id": generate(),
                            "name": db_name,
                            "severity": "critical",
                        }
                    )
            dbs_protections_node = {
                "id": generate(),
                "name": "数据库安全防护覆盖",
                "severity": "none",
                "children": [],
            }
            exploitability_node["children"].append(dbs_protections_node)
            for prop, name in DB_PROTECTION_COVERAGES:
                if prop in adapter_properties:
                    dbs_protections_node["children"].append(
                        {
                            "id": generate(),
                            "name": f"{name}已覆盖",
                            "severity": "low",
                        }
                    )
                else:
                    dbs_protections_node["children"].append(
                        {
                            "id": generate(),
                            "name": f"{name}未覆盖",
                            "severity": "critical",
                        }
                    )
        workloads["dbs"] = deduplicate(workloads_dbs)

        workloads_websites = []
        websites = cls.extract(asset_dict, cls._asset_website_field)
        if websites:
            websites_node = {
                "id": generate(),
                "name": "Web应用",
                "severity": "none",
                "children": [],
            }
            workloads_node["children"].append(websites_node)
            for website in websites:
                domains = website.get("domains")
                domain_names = list()
                if domains:
                    for domain in domains:
                        domain_name = domain.get("name")
                        if domain_name:
                            domain_names.append(domain_name)
                text = ", ".join(domain_names)
                website_server = website.get("server")
                if website_server:
                    workloads_websites.append(website_server)
                    websites_node["children"].append(
                        {
                            "id": generate(),
                            "name": text or website_server,
                            "severity": "critical",
                        }
                    )
            workloads["webapps"] = deduplicate(workloads_websites)
            websites_protections_node = {
                "id": generate(),
                "name": "Web应用安全防护覆盖",
                "severity": "none",
                "children": [],
            }
            exploitability_node["children"].append(websites_protections_node)
            for prop, name in WEB_PROTECTION_COVERAGES:
                if prop in adapter_properties:
                    websites_protections_node["children"].append(
                        {
                            "id": generate(),
                            "name": f"{name}已覆盖",
                            "severity": "low",
                        }
                    )
                else:
                    websites_protections_node["children"].append(
                        {
                            "id": generate(),
                            "name": f"{name}未覆盖",
                            "severity": "critical",
                        }
                    )
        privileged_accounts = []
        _accounts = cls.extract(asset_dict, cls._asset_account_field) or []
        for account in _accounts:
            account_name = account.get("name")
            if account_name == "Administrator" or account_name == "root":
                privileged_accounts.append(account_name)
            if account.get("sudo"):
                privileged_accounts.append(account_name)
        workloads["privileged_accounts"] = deduplicate(privileged_accounts)
        result["workloads"] = workloads
        coverage = {}

        coverage["HIDS"] = "HIDS" in adapter_properties
        coverage["VA"] = "VA" in adapter_properties
        coverage["CMDB"] = "CMDB" in adapter_properties
        coverage["OSM"] = "OSM" in adapter_properties
        result["coverage"] = coverage
        #   防护措施
        asset_type_name = cls.extract(record, cls._asset_type_name_field)
        if asset_type_name in PROTECTION_COVERAGES:
            response_coverage_node = {
                "id": generate(),
                "name": f"{asset_type_name}安全防护覆盖",
                "severity": "none",
                "children": [],
            }
            exploitability_node["children"].append(response_coverage_node)
            protection_coverages_of_asset = PROTECTION_COVERAGES[asset_type_name]
            for prop, name in protection_coverages_of_asset:
                if prop in adapter_properties:
                    response_coverage_node["children"].append(
                        {
                            "id": generate(),
                            "name": f"{name}已覆盖",
                            "severity": "low",
                        }
                    )
                else:
                    response_coverage_node["children"].append(
                        {
                            "id": generate(),
                            "name": f"{name}未覆盖",
                            "severity": "critical",
                        }
                    )

        #   响应
        _owners = cls.extract(asset_dict, cls._asset_owner_field)
        if _owners:
            response_node["children"].append({"id": generate(), "name": "已指定责任人", "severity": "low"})
        else:
            response_node["children"].append({"id": generate(), "name": "未指定责任人", "severity": "critical"})
        if asset_type_name in DATA_COVERAGES:
            response_coverage_node = {
                "id": generate(),
                "name": f"{asset_type_name}数据采集覆盖",
                "severity": "none",
                "children": [],
            }
            response_node["children"].append(response_coverage_node)
            data_coverages_of_asset = DATA_COVERAGES[asset_type_name]
            for prop, name in data_coverages_of_asset:
                if prop in adapter_properties:
                    response_coverage_node["children"].append(
                        {
                            "id": generate(),
                            "name": f"{name}已覆盖",
                            "severity": "low",
                        }
                    )
                else:
                    response_coverage_node["children"].append(
                        {
                            "id": generate(),
                            "name": f"{name}未覆盖",
                            "severity": "critical",
                        }
                    )
        if asset_type_name in RESPONSE_COVERAGES:
            response_coverage_node = {
                "id": generate(),
                "name": f"{asset_type_name}安全响应覆盖",
                "severity": "none",
                "children": [],
            }
            response_node["children"].append(response_coverage_node)
            response_coverages_of_asset = RESPONSE_COVERAGES[asset_type_name]
            for prop, name in response_coverages_of_asset:
                if prop in adapter_properties:
                    response_coverage_node["children"].append(
                        {
                            "id": generate(),
                            "name": f"{name}已覆盖",
                            "severity": "low",
                        }
                    )
                else:
                    response_coverage_node["children"].append(
                        {
                            "id": generate(),
                            "name": f"{name}未覆盖",
                            "severity": "critical",
                        }
                    )

    def parse(self, manager, record, category, date, card_setting, setting=None, asql=None):
        meta_field_mapper = setting.get("meta_field_mapper")
        result = {"traits": self.extract(record, self._vul_instance_unique_traits_field) or []}

        root = {
            "name": "漏洞优先级",
            "id": generate(),
            "severity": "none",
            "children": [],
        }
        result["priority_tree"] = root

        risk_node = {
            "id": generate(),
            "name": "漏洞风险",
            "severity": "none",
            "children": [],
        }
        root["children"].append(risk_node)
        exploitability_node = {
            "id": generate(),
            "name": "利用难度",
            "severity": "none",
            "children": [],
        }
        root["children"].append(exploitability_node)
        impact_node = {
            "id": generate(),
            "name": "利用后影响",
            "severity": "none",
            "children": [],
        }
        root["children"].append(impact_node)
        response_node = {
            "id": generate(),
            "name": "利用后响应",
            "severity": "none",
            "children": [],
        }
        root["children"].append(response_node)

        self._padding_field_info(manager, meta_field_mapper, record, result)

        self._padding_vul_info(manager, record, result, exploitability_node, impact_node, risk_node)
        self._padding_asset_info(manager, date, record, result, exploitability_node, impact_node, response_node)

        #   漏洞图谱
        vertices = list()
        edges = list()
        graph_handler = None
        vul_id = self.extract(record, self._vul_entity_id_field)
        try:
            graph_handler = EntityGraphHandler(Category.ASSET, date)
            graph_handler.include_registry()

            #   todo: 防止注入
            result_obj = graph_handler.client.query(
                f"match {{class:Vul, where:(vul_id='{vul_id}'),as:a}}.bothE(){{as:b}}"
                f".bothV(){{class:Port, as:c}} return $paths"
            )
            if result_obj:
                pass
            else:
                result_obj = graph_handler.client.query(
                    f"match {{class:Vul, where:(vul_id='{vul_id}'),as:a}}"
                    f".bothE(){{as:b}}.bothV(){{class:Component, as:c}} return $paths"
                )
            if result_obj:
                vertices_by_id = set()
                edges_by_id = set()
                for record in result_obj:
                    record: OrientRecord = record
                    vertex_a_id = str(record.oRecordData["a"])
                    vertex_a = graph_handler.graph.get_element(vertex_a_id)
                    edge_b = graph_handler.graph.get_element(str(record.oRecordData["b"]))
                    edge_in_id = edge_b._in
                    edge_out_id = edge_b._out
                    edge_id = f"{edge_in_id}-{edge_out_id}"
                    vertex_b_id = str(record.oRecordData["c"])
                    vertex_b = graph_handler.graph.get_element(vertex_b_id)
                    if vertex_a_id not in vertices_by_id:
                        vertices.append(
                            {
                                "id": vertex_a_id,
                                "display_name": vertex_a._props["value"],
                                "data": vertex_a._props,
                                "type": type(vertex_a).__name__,
                            }
                        )
                        vertices_by_id.add(vertex_a_id)
                    if vertex_b_id not in vertices_by_id:
                        vertices.append(
                            {
                                "id": vertex_b_id,
                                "display_name": vertex_b._props["value"],
                                "data": vertex_b._props,
                                "type": type(vertex_b).__name__,
                            }
                        )
                        vertices_by_id.add(vertex_b_id)
                    if edge_id not in edges_by_id:
                        edges.append({"display_name": edge_b.relation, "src": edge_out_id, "dst": edge_in_id})
                        edges_by_id.add(edge_id)
        except Exception as e:
            log.warning(f"OrientDB handle error({str(e)}) detail({traceback.format_exc()})")
        finally:
            graph_handler.finish() if graph_handler else ...
        result["graph"] = {"vertices": vertices, "edges": edges}
        return result

    @classmethod
    def __padding_core(cls, manager, meta_field_mapper, field_mapper, record, result):
        for key_name, field_name in field_mapper.items():
            _field_value = cls.extract(record, field_name)
            _meta_field = meta_field_mapper.get(field_name)
            if _meta_field:
                result[key_name] = manager.render(_field_value, _meta_field)

    @classmethod
    def __padding_single_data(cls, manager, field_mapper, meta_field_relation, meta_field_mapper, record, result):
        for key_name, relation_name in field_mapper.items():
            _field_name = meta_field_relation[relation_name]
            _meta_field = meta_field_mapper[_field_name]
            _field_value = cls.extract(record, relation_name)
            result[key_name] = manager.render(_field_value, _meta_field)
