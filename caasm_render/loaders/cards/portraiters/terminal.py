from caasm_render.loaders.cards.portraiters.computer import ComputerPortraiter
from caasm_tool.util import extract


class TerminalPortraiter(ComputerPortraiter):
    @property
    def portrait_type(self):
        return "terminal"

    _ignore_mac = "00:00:00:00:00:00"
    _ignore_multi_ip = ["127.0.0.1", "0.0.0.0"]
    _adapter_query_fields = ["name", "display_name"]

    _name_field = "asset_base.name"
    _priority_addr_field = "network.priority_addr"
    _asset_type_field = "base.asset_type"
    _asset_type_name_field = "base.asset_type_display_name"
    _last_seen_field = "base.last_seen"
    _adapters_name_field = "base.adapters"
    _owner_field = "asset_base.owners"
    _department_field = "asset_base.departments"
    _device_field = "asset_base.device"
    _interface_field = "computer.interfaces"
    _os_field = "computer.os"
    _host_name_field = "computer.host_name"
    _ip_field = "network.ips"
    _port_field = "network.ports"
    _software_field = "computer.softwares"
    _package_field = "computer.packages"
    _business_field = "asset_base.businesses"
    _account_field = "computer.accounts"
    _vul_field = "vulnerability.vulners"
    _website_field = "computer.websites"
    _jar_field = "computer.jars"
    _webframe_field = "computer.webframes"
    _adapter_properties_field = "asset_base.adapter_properties"
    _process_field = "computer.processes"

    _exposure_field = "exposure.value"
    _addr_field = "addr"
    _internet_ip_field = "internet_ip"
    _internet_port_field = "internet_port"
    _intranet_ip_field = "ip"
    _intranet_port_field = "number"
    _port_protocol_field = "protocol"
    _port_process_field = "process"
    _process_pid_field = "pid"
    _process_name_field = "name"
    _process_user_field = "uname"
    _account_name_field = "name"

    def portrait(self, manager, record, category, card_setting, setting=None):
        setting = setting or {}

        meta_field_mapper = setting.get("meta_field_mapper", {})

        basic_info = self.get_basic_info(manager, meta_field_mapper, record)
        ownership = self.get_ownership(record)
        os = self.get_os()
        fingerprints = self.get_fingerprints(record)
        department = self.get_department()
        coverage = self.get_coverage(record)
        change_records = self.get_change_record(record)

        result = {
            "basic_info": basic_info,
            "ownership": ownership,
            "os": os,
            "department": department,
            "fingerprints": fingerprints,
            "coverage": coverage,
            "change_records": change_records,
        }
        return result

    @classmethod
    def get_department(cls):
        return [{"name": "部门信息", "field": cls._department_field}]

    @classmethod
    def get_os(cls):
        return {"name": "操作系统", "field": cls._os_field}

    @classmethod
    def get_fingerprints(cls, record):
        result = []
        if extract(record, cls._ip_field):
            result.append({"name": "IP地址", "field": cls._ip_field})
        if extract(record, cls._port_field):
            result.append({"name": "端口", "field": cls._port_field})
        if extract(record, cls._package_field):
            result.append({"name": "安装包", "field": cls._package_field})
        if extract(record, cls._software_field):
            result.append({"name": "软件", "field": cls._software_field})
        if extract(record, cls._account_field):
            result.append({"name": "账户", "field": cls._account_field})
        if extract(record, cls._process_field):
            result.append({"name": "进程", "field": cls._process_field})

        return result

    @classmethod
    def get_coverage(cls, record):
        adapter_properties = extract(record, cls._adapter_properties_field) or []
        return {
            "终端准入": "终端准入" in adapter_properties,
            "EDR": "EDR" in adapter_properties,
            "终端防病毒": "终端防病毒" in adapter_properties,
            "数据防泄漏": "数据防泄漏" in adapter_properties,
        }
