from abc import ABC

from caasm_render.loaders.cards.asset_portraiter import Asset<PERSON>ortraiter
from caasm_service.runtime import adapter_service
from caasm_tool.util import extract


class ComputerPortraiter(AssetPortraiter, ABC):
    _owner_field = "asset_base.owners"
    _department_field = "asset_base.departments"
    _business_field = "asset_base.businesses"
    _adapters_name_field = "base.adapters"
    _adapter_query_fields = ["name", "display_name"]
    _priority_addr_field = "network.priority_addr"
    _name_field = "asset_base.name"
    _asset_type_name_field = "base.asset_type_display_name"
    _last_seen_field = "base.last_seen"
    _os_type_field = "computer.os.type"
    _os_full_field = "computer.os.full"
    _os_distribution_field = "computer.os.distribution"
    _unit_field = "asset_base.ownership.unit"

    @classmethod
    def get_ownership(cls, record):
        owners, emails, phones, department_name = [], [], [], ""

        owner_data_list = extract(record, cls._owner_field) or []
        for owner in owner_data_list:
            tmp_owner_primary_flag = owner.get("nickname") or owner.get("username")
            owners.append(tmp_owner_primary_flag) if tmp_owner_primary_flag else ...
            emails.extend(owner.get("emails", []))
            phones.extend(owner.get("phones", []))

        departments = extract(record, cls._department_field) or []
        if departments:
            department_name = departments[0].get("name", "")
        businesses = extract(record, cls._business_field) or []
        business_names = [business["name"] for business in businesses if business.get("name")]

        return [
            {"name": "责任人", "value": owners},
            {"name": "电子邮件", "value": emails},
            {"name": "联系电话", "value": phones},
            {"name": "部门", "value": department_name},
            {"name": "业务系统", "value": business_names},
            {"name": "公司", "value": extract(record, cls._unit_field) or ""},
        ]

    @classmethod
    def get_basic_info(cls, manager, meta_field_mapper, record):
        adapter_names = extract(record, cls._adapters_name_field) or []
        if adapter_names:
            adapters = adapter_service.find_adapter(names=adapter_names, fields=cls._adapter_query_fields)
            adapter_mapper = {adapter.name: adapter.display_name for adapter in adapters}
        else:
            adapter_mapper = {}

        basic_info = {
            "ip": extract(record, cls._priority_addr_field),
            "name": extract(record, cls._name_field),
            "asset_type": extract(record, cls._asset_type_name_field),
            "os": extract(record, cls._os_distribution_field)
            or extract(record, cls._os_full_field)
            or extract(record, cls._os_type_field),
            "risks": [],
            "last_seen": manager.render(
                extract(record, cls._last_seen_field), meta_field_mapper.get(cls._last_seen_field)
            ),
            "adapters": [{"name": i, "display_name": adapter_mapper.get(i)} for i in adapter_names],
        }

        return basic_info

    @classmethod
    def get_change_record(cls, record):
        change_records = [{"content": "无", "datetime": ""}]
        return change_records
