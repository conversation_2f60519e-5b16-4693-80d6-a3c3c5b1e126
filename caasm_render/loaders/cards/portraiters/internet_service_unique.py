from caasm_meta_data.constants import Category
from caasm_render.loaders.cards.asset_portraiter import AssetPortraiter
from caasm_tool.util import extract


class InternetServiceUniqueHandler(AssetPortraiter):
    _entity_id_field = "base.entity_id"
    _assets_field = "internet_mapping.asset.assets.id"
    _vulners_field = "internet_mapping.vulnerability.vulners"

    def portrait(self, manager, record, category, card_setting, setting=None):
        return {
            "basic_info": self._get_basic_info(record),
            "coverages": self._get_coverages(record),
        }

    def _get_basic_info(self, record):
        business = extract(record, "internet_service_unique.business") or {}
        business["category"] = Category.BUSINESS_UNIQUE
        adapters = extract(record, "base.adapters") or []
        owner = extract(record, "asset_base.ownership.owner.nickname")
        departments = extract(record, "asset_base.departments") or []
        department_names = [extract(department, "name") for department in departments]
        realms = extract(record, "asset_base.realms") or []
        realm_names = [extract(realm, "name") for realm in realms]
        owners = extract(record, "asset_base.owners") or []
        owner_names = [extract(owner, "nickname") for owner in owners]
        return [
            {
                "name": "数据源",
                "value": adapters,
                "type": "list",
                "element_type": "adapter",
            },
            {"name": "最后发现", "value": extract(record, "base.last_seen") or ""},
            {"name": "首次发现", "value": extract(record, "base.first_seen") or ""},
            {"name": "域名", "value": ", ".join(extract(record, "internet_service_unique.domains") or [])},
            {"name": "中国电信IP", "value": extract(record, "internet_service_unique.ctcc_ip")},
            {"name": "中国移动IP", "value": extract(record, "internet_service_unique.cmcc_ip")},
            {"name": "中国联通IP", "value": extract(record, "internet_service_unique.cucc_ip")},
            {"name": "服务端口", "value": extract(record, "internet_service_unique.port")},
            {"name": "业务系统", "value": business, "type": "relation"},
            {"name": "部署位置", "value": extract(record, "asset_base.deployment_zone")},
            {"name": "安全域", "value": ",".join(realm_names)},
            {"name": "部门", "value": ", ".join(department_names)},
            {"name": "责任人", "value": ",".join(owner_names)},
        ]

    def _get_ownership(self, record):
        return {{"ownership": {"role": "业务责任人"}}}

    def _get_coverages(self, record):
        properties = extract(record, "asset_base.properties") or set()
        return {
            "coverages": [
                {"display_name": "EASM覆盖", "covered": "EASM" in properties},
                {"display_name": "WAF覆盖", "covered": "WAF" in properties},
                {"display_name": "NDR", "covered": "NDR" in properties},
            ],
            "assets": [
                {
                    "display_name": "主机",
                    "config": {
                        "category": Category.INTERNET_SERVICE_UNIQUE,
                        "entity_id": extract(record, "base.entity_id"),
                        "field": self._assets_field,
                        "field_category": Category.ASSET,
                        "asset_type": "host",
                    },
                }
            ],
        }

    @property
    def portrait_type(self):
        return Category.INTERNET_SERVICE_UNIQUE
