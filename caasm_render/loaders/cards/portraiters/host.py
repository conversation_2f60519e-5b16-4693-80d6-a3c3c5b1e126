from uuid import uuid4

from caasm_render.loaders.cards.portraiters.computer import ComputerPortraiter
from caasm_tool.util import extract, deduplicate


class HostPortraiter(ComputerPortraiter):
    @property
    def portrait_type(self):
        return "host"

    _ignore_mac = "00:00:00:00:00:00"
    _ignore_multi_ip = ["127.0.0.1", "0.0.0.0"]
    _adapter_query_fields = ["name", "display_name"]

    _name_field = "asset_base.name"
    _priority_addr_field = "network.priority_addr"
    _asset_type_field = "base.asset_type"
    _asset_type_name_field = "base.asset_type_display_name"
    _last_seen_field = "base.last_seen"
    _adapters_name_field = "base.adapters"
    _owner_field = "asset_base.owners"
    _department_field = "asset_base.departments"
    _device_field = "asset_base.device"
    _interface_field = "computer.interfaces"
    _os_field = "computer.os"
    _host_name_field = "computer.host_name"
    _ip_field = "network.ips"
    _port_field = "network.ports"
    _software_field = "computer.softwares"
    _db_field = "computer.dbs"
    _business_field = "asset_base.businesses"
    _account_field = "computer.accounts"
    _vul_field = "vulnerability.vulners"
    _website_field = "computer.websites"
    _jar_field = "computer.jars"
    _webframe_field = "computer.webframes"
    _adapter_properties_field = "asset_base.adapter_properties"
    _process_field = "computer.processes"

    _exposure_field = "exposure.value"
    _addr_field = "addr"
    _internet_ip_field = "internet_ip"
    _internet_port_field = "internet_port"
    _intranet_ip_field = "ip"
    _intranet_port_field = "number"
    _port_protocol_field = "protocol"
    _port_process_field = "process"
    _process_pid_field = "pid"
    _process_name_field = "name"
    _process_user_field = "uname"
    _account_name_field = "name"

    def portrait(self, manager, record, category, card_setting, setting=None):
        setting = setting or {}

        meta_field_mapper = setting.get("meta_field_mapper", {})

        basic_info = self.get_basic_info(manager, meta_field_mapper, record)
        ownership = self.get_ownership(record)
        fingerprints = self.get_fingerprints(record)
        coverage = self.get_coverage(record)
        change_records = self.get_change_record(record)
        internet_attack_surfaces = self.get_internet_attack_surfaces(record)
        detail = self.get_detail(record)

        result = {
            "basic_info": basic_info,
            "ownership": ownership,
            "internet_attack_surfaces": internet_attack_surfaces,
            "fingerprints": fingerprints,
            "coverage": coverage,
            "change_records": change_records,
            "detail": detail,
        }
        return result

    @classmethod
    def get_internet_attack_surfaces(cls, record):
        asset_name = extract(record, cls._host_name_field)
        results = []
        ports = extract(record, cls._port_field) or []
        exposed = False
        for port in ports:
            exposure = extract(port, cls._exposure_field)
            if exposure == 3:
                exposed = True
                break
        if not exposed:
            return []
        processes = extract(record, cls._process_field) or []
        processes_by_pid = {}
        for process in processes:
            pid = extract(process, cls._process_pid_field)
            if pid:
                processes_by_pid[pid] = process
        accounts = extract(record, cls._account_field) or []
        accounts_by_name = {}
        for account in accounts:
            account_name = extract(account, cls._account_name_field)
            accounts_by_name[account_name] = account

        index = 0
        exposure_ips = []
        for port in ports:
            exposure = extract(port, cls._exposure_field)
            if exposure == 3:
                index += 1
                port_vertices = []
                port_edges = []
                details = []
                internet_ip = extract(port, cls._internet_ip_field)
                if internet_ip:
                    exposure_ips.append(internet_ip)
                internet_port = extract(port, cls._internet_port_field)
                intranet_ip = extract(port, cls._intranet_ip_field)
                intranet_port = extract(port, cls._intranet_port_field)
                internet_id = str(uuid4())
                port_vertices.append({"id": internet_id, "display_name": "互联网", "vertex_type": "internet"})
                internet_port_id = str(uuid4())
                port_vertices.append(
                    {"id": internet_port_id, "display_name": str(internet_port), "vertex_type": "internet_port"}
                )
                port_edges.append({"source": internet_id, "target": internet_port_id, "display_name": "访问"})
                internet_ip_id = str(uuid4())
                port_vertices.append({"id": internet_ip_id, "display_name": internet_ip, "vertex_type": "internet_ip"})
                port_edges.append({"source": internet_port_id, "target": internet_ip_id, "display_name": "访问"})
                nat_id = str(uuid4())
                port_vertices.append({"id": nat_id, "display_name": "NAT映射", "vertex_type": "nat"})
                port_edges.append({"source": internet_ip_id, "target": nat_id, "display_name": "访问"})
                intranet_ip_id = str(uuid4())
                port_vertices.append({"id": intranet_ip_id, "display_name": intranet_ip, "vertex_type": "ip"})
                port_edges.append({"source": nat_id, "target": intranet_ip_id, "display_name": "映射"})
                details.append(
                    {"display_name": "IP地址", "setting": {"field": "network.ips", "condition": {"addr": intranet_ip}}}
                )
                intranet_port_id = str(uuid4())
                port_vertices.append(
                    {"id": intranet_port_id, "display_name": str(intranet_port), "vertex_type": "port"}
                )
                port_edges.append({"source": intranet_ip_id, "target": intranet_port_id, "display_name": "开放"})
                details.append(
                    {
                        "display_name": "端口",
                        "setting": {"field": "network.ports", "condition": {"number": intranet_port}},
                    }
                )
                process_pid = extract(port, cls._process_pid_field)
                if process_pid in processes_by_pid:
                    process_id = str(uuid4())
                    process = processes_by_pid[process_pid]
                    process_name = extract(process, cls._process_name_field)
                    port_vertices.append({"id": process_id, "display_name": process_name, "vertex_type": "process"})
                    port_edges.append({"source": process_id, "target": intranet_port_id, "display_name": "绑定"})
                    details.append(
                        {
                            "display_name": "进程",
                            "setting": {"field": "computer.processes", "condition": {"pid": process_pid}},
                        }
                    )
                    process_user = extract(process, cls._process_user_field)
                    if process_user in accounts_by_name:
                        process_account = accounts_by_name[process_user]
                        account_id = str(uuid4())
                        account_name = extract(process_account, cls._account_name_field)
                        port_vertices.append({"id": account_id, "display_name": account_name, "vertex_type": "account"})
                        port_edges.append({"source": account_id, "target": process_id, "display_name": "启动"})
                        details.append(
                            {
                                "display_name": "进程",
                                "setting": {"field": "computer.accounts", "condition": {"name": account_name}},
                            }
                        )
                results.append(
                    {
                        "display_name": f"暴露详情{index}",
                        "graph": {"vertices": port_vertices, "edges": port_edges},
                        "details": details,
                    }
                )

        asset_exposure_vertices = []
        asset_exposure_edges = []
        exposure_ips = set(exposure_ips)
        internet_id = str(uuid4())
        asset_exposure_vertices.append({"id": internet_id, "display_name": "互联网", "vertex_type": "internet"})
        asset_exposure_vertices.extend(
            [
                {
                    "id": exposure_ip,
                    "display_name": exposure_ip,
                    "vertex_type": "ip",
                }
                for exposure_ip in exposure_ips
            ]
        )
        asset_exposure_edges.extend(
            [{"source": internet_id, "target": exposure_ip, "display_name": "访问"} for exposure_ip in exposure_ips]
        )
        nat_id = str(uuid4())
        asset_exposure_vertices.append(
            {
                "id": nat_id,
                "display_name": "NAT映射",
                "vertex_type": "nat",
            }
        )
        asset_exposure_edges.extend(
            [{"source": exposure_ip, "target": nat_id, "display_name": "映射"} for exposure_ip in exposure_ips]
        )
        host_id = str(uuid4())
        asset_exposure_vertices.append({"id": host_id, "display_name": asset_name, "vertex_type": "host"})
        asset_exposure_edges.append({"source": nat_id, "target": host_id, "display_name": "访问"})
        details = []
        results.insert(
            0,
            {
                "display_name": "资产暴露",
                "graph": {"vertices": asset_exposure_vertices, "edges": asset_exposure_edges},
                "details": details,
            },
        )

        return results

    @classmethod
    def get_fingerprints(cls, record):
        result = []
        if extract(record, cls._ip_field):
            result.append({"name": "IP地址", "field": cls._ip_field})
        if extract(record, cls._port_field):
            result.append({"name": "端口", "field": cls._port_field})
        if extract(record, cls._db_field):
            result.append({"name": "数据库", "field": cls._db_field})
        if extract(record, cls._website_field):
            result.append({"name": "Web站点", "field": cls._website_field})
        if extract(record, cls._webframe_field):
            result.append({"name": "Web框架", "field": cls._webframe_field})
        if extract(record, cls._jar_field):
            result.append({"name": "Jar包", "field": cls._jar_field})
        if extract(record, cls._account_field):
            result.append({"name": "账户", "field": cls._account_field})
        if extract(record, cls._process_field):
            result.append({"name": "进程", "field": cls._process_field})

        return result

    @classmethod
    def get_change_record(cls, record):
        change_records = [{"content": "无", "datetime": ""}]
        return change_records

    @classmethod
    def get_coverage(cls, record):
        adapter_properties = extract(record, cls._adapter_properties_field) or []
        return {
            "HIDS": "HIDS" in adapter_properties,
            "VA": "漏洞扫描" in adapter_properties,
            "CMDB": "CMDB" in adapter_properties,
            "OSM": "堡垒机" in adapter_properties,
        }

    @classmethod
    def get_attack_surface(cls, record):
        return {
            "accounts": len(extract(record, cls._account_field) or []),
            "softwares": len(extract(record, cls._software_field) or []),
            "ips": len(extract(record, cls._ip_field) or []),
            "ports": len(extract(record, cls._port_field) or []),
            "vulers": len(extract(record, cls._vul_field) or []),
            "websites": len(extract(record, cls._website_field) or []),
            "jars": len(extract(record, cls._jar_field) or []),
            "webframes": len(extract(record, cls._webframe_field) or []),
        }

    @classmethod
    def get_detail(cls, record):
        #  物理层
        physicals = []
        device = extract(record, cls._device_field) or {}
        interfaces = extract(record, cls._interface_field) or []
        device_distribution = device.get("distribution", "") or device.get("name", "")
        physicals.append(device_distribution)
        macs = [i["mac"] for i in interfaces if i.get("mac") and i["mac"] != cls._ignore_mac]
        physicals.extend(macs)

        #   系统层
        systems, system_oses = [], []
        os = extract(record, cls._os_field) or {}
        os_distribution = os.get("distribution")
        if os_distribution:
            system_oses.append(os_distribution)
        systems.extend(system_oses)
        host_name = extract(record, cls._host_name_field)
        if host_name:
            systems.append(host_name)

        #   网络层
        ips = extract(record, cls._ip_field) or []
        ports = extract(record, cls._port_field) or []
        network_ips = [ip["addr"] for ip in ips if ip.get("addr") and ip.get("addr") in cls._ignore_multi_ip]
        network_ports = [port["number"] for port in ports if port.get("number")]

        #   软件层
        apps = extract(record, cls._software_field) or []
        app_names = [app["name"] for app in apps if app.get("name")]

        #   数据层
        dbs = extract(record, cls._db_field) or []
        db_names = [db["name"] for db in dbs if db.get("name")]

        #   业务层
        businesses = extract(record, cls._business_field) or []
        business_names = [business["name"] for business in businesses if business.get("name")]

        #   人员层
        owners = extract(record, cls._owner_field) or []
        operators = [i.get("nickname") or i.get("username") for i in owners if (i.get("nickname") or i.get("username"))]

        result = {
            "operators": operators,
            "businesses": business_names,
            "data": db_names,
            "softwares": app_names,
            "network": deduplicate(network_ips + network_ports),
            "network.ips": deduplicate(network_ips),
            "network.ports": deduplicate(network_ports),
            "system": systems,
            "system.os": system_oses,
            "physicals": physicals,
            "physical.device.distribution": device_distribution,
            "physical.mac": macs,
        }
        return result
