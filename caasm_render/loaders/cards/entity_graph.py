import json
import zlib
from typing import Dict

from IPy import IP

from caasm_render.loaders.cards.base import CardBaseHandler
from caasm_render.loaders.constants import CardType
from caasm_service.entity.link_graph import EntityLinkGraph, LinkGraph
from caasm_service.runtime import entity_link_graph_service, link_graph_service
from caasm_tool.util import extract


class EntityGraphCard(CardBaseHandler):
    _entity_id_field = "base.entity_id"
    _combo_highlight_color = "#DB4437"
    _vertex_highlight_color = "#DB4437"

    def __init__(self):
        super(EntityGraphCard, self).__init__()
        self._highlighters = [self._highlight_ip, self._highlight_domain]

    @property
    def card_type(self):
        return CardType.ENTITY_GRAPH

    def parse(self, manager, record, category, date, card_setting, setting=None, asql=None):
        return self._get_graph(category, record, asql)

    def _get_graph(self, category, record, asql=None):
        entity_id = extract(record, self._entity_id_field)
        entity_graph: EntityLinkGraph = entity_link_graph_service.get_graph(category, entity_id)
        if not entity_graph:
            return {}
        graph: LinkGraph = link_graph_service.get_graph(entity_graph.graph_id)
        if graph and graph.graph:
            graph_dict: Dict = json.loads(zlib.decompress(graph.graph).decode("utf-8"))
            for highlighter in self._highlighters:
                if highlighter(graph_dict, asql):
                    break
            return graph_dict
        else:
            return {}

    def _highlight_asql(self, graph: Dict, asql, compare):
        for vertex in graph["vertices"]:
            context = vertex.get("context", [])
            for content in context:
                if compare(content, asql):
                    vertex["fill"] = self._vertex_highlight_color
                    # vertex["stroke"] = self._vertex_highlight_color
                    pass

        for combo in graph["combos"]:
            context = combo.get("context", [])
            for content in context:
                if compare(content, asql):
                    combo["fill"] = self._combo_highlight_color
                    # combo["stroke"] = self._combo_highlight_color
                    pass

    @staticmethod
    def _equal(content, asql):
        return content == asql

    def _highlight_ip(self, graph: Dict, asql):
        if not asql:
            return False
        try:
            expected_ip = IP(asql)
            expected_ip = expected_ip.strFullsize()
        except ValueError:
            return False
        self._highlight_asql(graph, asql, self._in)
        return True

    @staticmethod
    def _in(content, asql):
        return content is not None and asql in content

    def _highlight_domain(self, graph, asql):
        if not asql:
            return False
        self._highlight_asql(graph, asql, self._in)
        return True
