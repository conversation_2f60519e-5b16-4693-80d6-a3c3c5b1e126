import logging
from collections import namedtuple

from caasm_render.loaders.cards.base import <PERSON><PERSON>ase<PERSON>and<PERSON>
from caasm_render.loaders.constants import CardType
from caasm_service.entity.meta_model import MetaFieldType

log = logging.getLogger()


class FabricCard<PERSON><PERSON>ler(CardBaseHandler):
    _default_menu = namedtuple("_DefaultMenu", ["name", "display_name", "children"])
    _basic_field = "basic_info"
    _item_type = "item"
    _table_type = "table"

    @property
    def card_type(self):
        return CardType.FABRIC

    def parse(self, manager, record, category, date, card_setting, setting=None, asql=None):
        setting = setting or {}
        field = setting.get("field")
        keyword = setting.get("keyword").strip() if isinstance(setting.get("keyword"), str) else None
        page_index = setting.get("page_index") or 1
        condition = setting.get("condition") or {}

        limit = setting.get("page_size") or 20
        offset = (page_index - 1) * limit

        data, data_type = None, self._item_type
        result = {"data": data, "type": data_type, "name": field}

        if not field:
            log.warning(f"Missing field, record_id is {record.get('_id')}")
            return result

        if field == self._basic_field:
            meta_models, meta_field_by_model_id = manager.default_query.find_field_to_model_mapper(category, date)
            data = self.parse_base(manager, record, meta_models, meta_field_by_model_id)
            result["data"] = data
            return result

        meta_field_mapper = manager.default_query.find_field_to_mapper(category)

        meta_field = meta_field_mapper.get(field)
        if not meta_field:
            log.warning(f"Not found meta field by field_name({field})")
            return None

        meta_field_type = meta_field.type

        if meta_field_type not in (MetaFieldType.OBJECT, MetaFieldType.LIST):
            log.warning(f"Query field({field}) type is not object or list")
            return None

        meta_children_field = meta_field.children
        if meta_field_type == MetaFieldType.OBJECT:
            data = self.parse_object(manager, record, meta_field, meta_children_field)
            data_type = self._item_type
        else:
            if meta_children_field and meta_children_field[0].type != MetaFieldType.OBJECT:
                log.warning(f"Query list_field but children field is not object")
                return None
            meta_children_field = meta_children_field[0].children
            if condition:
                data = self.parse_condition_list(manager, record, meta_field, meta_children_field, condition)
                data_type = self._item_type
            else:
                data = self.parse_list(
                    manager, record, field, meta_children_field, meta_field_mapper, keyword, offset, limit
                )
                data_type = self._table_type
        result["data"] = data
        result["type"] = data_type
        return result

    def parse_object(self, manager, record, meta_field, meta_children_field, full_name=True):
        result = self._parse_base(manager, record, meta_children_field, full_name=full_name)
        return [{"name": meta_field.name, "display_name": meta_field.display_name, "items": result}]

    def parse_list(
        self, manager, record, parent_field_name, meta_children_field, meta_field_mapper, keyword, offset, limit
    ):
        titles = self._parse_list_title(meta_children_field)
        values = self._parse_list_values(record, manager, titles, parent_field_name, meta_field_mapper, keyword)
        new_titles, new_values = self._adjust_list_order(titles, values)
        return {"titles": new_titles, "values": new_values[offset : offset + limit], "total": len(new_values)}

    def parse_condition_list(self, manager, record, meta_field, meta_children_field, condition):
        filtered_row = self._parse_list_by_condition(record, meta_field.full_name, condition)
        return self.parse_object(manager, filtered_row, meta_field, meta_children_field, False)

    @classmethod
    def _parse_list_by_condition(cls, record, parent_field_name, condition):
        parent_values = cls.extract(record, parent_field_name)
        if not parent_values:
            return None
        for child_value in parent_values:
            found = True
            for key, value in condition.items():
                _value = cls.extract(child_value, key)
                if value != _value:
                    found = False
                    break
            if found:
                return child_value

    @classmethod
    def _parse_list_values(cls, record, manager, titles, parent_field_name, meta_field_mapper, keyword):
        result = []
        parent_values = cls.extract(record, parent_field_name)
        if not parent_values:
            return result

        for child_value in parent_values:
            tmp_result = []
            keyword_match = False if keyword else True
            for title in titles:
                _name = title.get("name")
                _full_name = title.get("full_name")
                check_name = _full_name[len(parent_field_name) + 1 :]
                _value = cls.extract(child_value, check_name)
                if keyword and _value and str(keyword) in str(_value):
                    keyword_match = True

                _meta_field = meta_field_mapper[_full_name]
                _render_value = manager.render(_value, _meta_field)
                _render_value["name"] = _name
                _render_value["full_name"] = _full_name
                tmp_result.append(_render_value)
            result.append(tmp_result) if keyword_match else ...
        return result

    @classmethod
    def _parse_list_title(cls, fields):
        result = []
        for field in fields:
            if field.hidden:
                continue
            field_type = field.type
            children_field = field.children
            if field_type == MetaFieldType.OBJECT:
                result.extend(cls.__build_object_title_result(field))
            if field_type == MetaFieldType.LIST and children_field and children_field[0] == MetaFieldType.OBJECT:
                continue
            tmp_result = cls.__build_list_title_result(field)
            if tmp_result:
                result.append(tmp_result)
        return result

    def parse_base(self, manager, record, meta_models, field_mapper):
        groups = []
        for meta_model in meta_models:
            self._parse_base(manager, record, field_mapper[meta_model.id], groups)

        new_group_mapper = {}
        for group in groups:
            parent_name = group["parent_name"] or "basic_info"
            parent_display_name = group["parent_name"] or "基础信息"
            if parent_name in new_group_mapper:
                new_group_mapper[parent_name]["items"].append(group)
            else:
                new_group_mapper[parent_name] = {
                    "name": parent_name,
                    "display_name": parent_display_name,
                    "items": [group],
                }

        if not new_group_mapper:
            return None

        return list(new_group_mapper.values())

    def _parse_base(
        self,
        manager,
        record,
        fields,
        result=None,
        parent_full_name=None,
        parent_display_name=None,
        full_name=True,
        handled_set=None,
    ):
        if result is None:
            result = []
        if handled_set is None:
            handled_set = set()
        for field in fields:
            if field.hidden:
                continue
            _parent_full_name = parent_full_name
            _parent_display_name = parent_display_name
            field_type = field.type
            children = field.children

            if field_type == MetaFieldType.LIST and children and children[0].type == MetaFieldType.OBJECT:
                continue

            if field_type == MetaFieldType.OBJECT and children:
                if not _parent_full_name:
                    _parent_full_name = field.full_name
                if not _parent_display_name:
                    _parent_display_name = field.display_name
                self._parse_base(
                    manager,
                    record,
                    children,
                    result,
                    parent_full_name=_parent_full_name,
                    parent_display_name=_parent_display_name,
                    full_name=full_name,
                    handled_set=handled_set,
                )
            else:
                if full_name:
                    val = self.extract(record, field.full_name)
                else:
                    val = self.extract(record, field.name)
                if val is None:
                    continue
                render_value = manager.render(val, field)
                tmp_result = {
                    "description": field.description,
                    "display_name": field.display_name,
                    "full_name": field.full_name,
                    "parent_field": _parent_full_name,
                    "parent_name": _parent_display_name,
                    "type": field.type,
                    "element_type": self.__get_element_type(field),
                }
                if field.full_name in handled_set:
                    continue
                handled_set.add(field.full_name)
                tmp_result.update(render_value)
                result.append(tmp_result)
        return result

    def get_tree(self, record, meta_models, meta_field_mapper, field_mapper, meta_asset_type_view):
        if not meta_asset_type_view:
            _menus = self._build_default_menu(meta_models, meta_field_mapper)
        else:
            _menus = meta_asset_type_view.menu_tree
            self._padding_other_menu(_menus, meta_models, meta_field_mapper)

        _new_menus = self._filter_empty_menus(record, _menus, field_mapper)
        _new_menus.insert(0, self._default_menu(name="basic_info", display_name="基础信息", children=[]))
        return _new_menus

    def _build_default_menu(self, meta_models, meta_field_mapper):
        _menus = []
        for meta_model in meta_models:
            _tmp_menus = self.__build_default_menu(meta_field_mapper.get(meta_model.id))
            if not _tmp_menus:
                continue
            _menus.extend(_tmp_menus)
        return _menus

    def _filter_empty_menus(self, record, menus, field_mapper):
        result = []
        for menu in menus:
            name = menu.name
            children = menu.children
            display_name = menu.display_name

            new_menu = None
            if children:
                tmp_result = self._filter_empty_menus(record, children, field_mapper)
                if tmp_result:
                    new_menu = self._default_menu(name=name, children=tmp_result, display_name=display_name)
            else:
                tmp_result = self.extract(record, name)
                if tmp_result:
                    if not menu.display_name:
                        meta_field = field_mapper.get(menu.name)
                        if meta_field:
                            display_name = meta_field.display_name
                    new_menu = self._default_menu(name=name, children=[], display_name=display_name)
            if not new_menu:
                continue
            result.append(new_menu)

        return result

    @classmethod
    def _adjust_list_order(cls, titles, values):
        new_titles, new_values = [], []
        value_mapper = {value["full_name"]: value for value in values[0]} if values else []

        if not value_mapper:
            return titles, values

        handle_names = set()
        for title in titles:
            name = title.get("full_name")
            value_res = value_mapper.get(name)
            if value_res is None:
                continue
            value = value_res.get("value")
            if value is not None:
                new_titles.append(title)
                handle_names.add(name)
        for title in titles:
            name = title.get("full_name")
            if name not in handle_names:
                new_titles.append(title)

        for value in values:
            new_value = []
            for new_title in new_titles:
                new_title_name = new_title["full_name"]

                value_mapper = {sub_value["full_name"]: sub_value for sub_value in value}
                new_value.append(value_mapper[new_title_name])
            new_values.append(new_value)
        return new_titles, new_values

    def _padding_other_menu(self, menus, meta_models, meta_field_mapper):
        _total_menus = self._build_default_menu(meta_models, meta_field_mapper)
        _total_names = self.__format_menu_name(menus)

        _other_menus = []
        for _tmp_menu in _total_menus:
            _tmp_menu_name = _tmp_menu.name
            if _tmp_menu_name in _total_names:
                continue
            _other_menus.append(_tmp_menu)
        if not _other_menus:
            return
        menus.append(self._default_menu(name="other", display_name="其它信息", children=_other_menus))

    def __format_menu_name(self, menus, result=None):
        if result is None:
            result = []

        for menu in menus:
            children = menu.children
            if children:
                self.__format_menu_name(children, result)
            else:
                result.append(menu.name)
        return result

    def __build_default_menu(self, meta_fields, result=None, handle_menu_set=None):
        if result is None:
            result = []
            handle_menu_set = set()
        if not meta_fields:
            return result

        for meta_field in meta_fields:
            if meta_field.hidden:
                continue
            field_type = meta_field.type
            children = meta_field.children
            full_name = meta_field.full_name
            display_name = meta_field.display_name
            if field_type == MetaFieldType.LIST:
                if children[0].type != MetaFieldType.OBJECT:
                    continue
                else:
                    if full_name in handle_menu_set:
                        continue

                    handle_menu_set.add(full_name)
                    tmp_result = self._default_menu(name=full_name, display_name=display_name, children=[])
                    result.append(tmp_result)

            elif field_type == MetaFieldType.OBJECT:
                self.__build_default_menu(children, result, handle_menu_set)
        return result

    @classmethod
    def __build_list_title_result(cls, meta_field):
        element_type = None
        if meta_field.type == MetaFieldType.LIST:
            element_type = meta_field.children[0].type

        if meta_field.type == MetaFieldType.OBJECT:
            return {}

        return {
            "name": meta_field.name,
            "full_name": meta_field.full_name,
            "display_name": meta_field.display_name,
            "full_display_name": meta_field.full_display_name,
            "data_type": meta_field.type,
            "element_type": element_type,
        }

    @classmethod
    def __build_object_title_result(cls, meta_field):
        result = []
        if meta_field.hidden:
            return result
        children_field = meta_field.children
        for field in children_field:
            if field.hidden:
                continue
            field_type = field.type
            if field_type == MetaFieldType.OBJECT:
                result.extend(cls.__build_object_title_result(field))
            if field_type == MetaFieldType.LIST:
                continue
            tmp_result = {
                "name": field.name,
                "full_name": field.full_name,
                "display_name": f"{meta_field.display_name}-{field.display_name}",
                "full_display_name": field.full_display_name,
                "data_type": field.type,
                "element_type": None,
            }
            result.append(tmp_result)
        return result

    @classmethod
    def __get_element_type(cls, field):
        if field.type != MetaFieldType.LIST:
            return None

        child_field = field.children[0]
        return child_field.type
