from caasm_render.handlers.adapter_render import Adapter<PERSON><PERSON>
from caasm_render.handlers.address_render import <PERSON><PERSON><PERSON><PERSON>
from caasm_render.handlers.any_render import <PERSON><PERSON><PERSON>
from caasm_render.handlers.boolean_render import <PERSON>oleanR<PERSON>
from caasm_render.handlers.enum_render import <PERSON><PERSON><PERSON><PERSON>
from caasm_render.handlers.list_render import ListRender
from caasm_render.handlers.relation_render import Relation<PERSON><PERSON>
from caasm_render.handlers.version_render import VersionRender
from caasm_render.loaders.default_loader import DefaultLoader
from caasm_render.manager import RenderManager
from caasm_render.query.base import BaseQuery

# 初始化管理器
render_manager = RenderManager()

# 注册渲染器
render_list = [
    EnumRender,
    AnyRender,
    ListRender,
    AdapterRender,
    VersionRender,
    RelationRender,
    BooleanRender,
    AddressRender,
]

for render in render_list:
    render_manager.register_render(render)
render_manager.register_default_render(AnyRender)

# 注册加载器
loader_list = []

for loader in loader_list:
    render_manager.register_loader(loader)

render_manager.register_default_loader(DefaultLoader)

# 注册查询器
render_manager.register_default_query(BaseQuery)
