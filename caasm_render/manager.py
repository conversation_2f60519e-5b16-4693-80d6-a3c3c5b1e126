from typing import Optional

from caasm_render.handlers.base import <PERSON><PERSON><PERSON>
from caasm_render.query.base import BaseQuery
from caasm_service.entity.meta_model import MetaFieldType, MetaField


class RenderManager(object):
    def __init__(self):
        self._default_render = None
        self._default_loader = None
        self._render_mapper = {}
        self._loader_mapper = {}
        self._default_query: Optional[BaseQuery] = None

    def register_render(self, render):
        render_instance = render()
        self._render_mapper[render_instance.field_type] = render_instance

    def register_default_render(self, render):
        self._default_render = render()

    def register_default_query(self, query):
        self._default_query = query()

    def register_loader(self, loader):
        loader_instance = loader()
        self._loader_mapper[loader_instance.name] = loader_instance

    def register_default_loader(self, loader):
        self._default_loader = loader()

    def render(self, value, meta_field):
        data_type = meta_field.type if meta_field else None

        render = self._render_mapper.get(data_type, self._default_render)
        return render.render(value, meta_field)

    def my_render(self, value, meta_field):
        render = self._get_render(meta_field)
        return render.render(value, meta_field)

    def my_plain(self, value, meta_field: MetaField):
        if isinstance(value, list) and meta_field.type != MetaFieldType.LIST:
            render: BaseRender = self._get_render_by_type(MetaFieldType.LIST)
        else:
            render: BaseRender = self._get_render(meta_field)
        return render.my_plain(value, meta_field)

    def get_render_value(self, value, meta_field):
        render = self._get_render(meta_field)
        return render.my_render(value, meta_field)

    def get_render_real_value(self, value, meta_field):
        render = self._get_render(meta_field)
        return render.my_value(value, meta_field)

    def get_card_result(
        self, record, category, date, card_type, meta_asset_type_view, card_setting=None, setting=None, asql=None
    ):
        name = f"{meta_asset_type_view.category}-{meta_asset_type_view.asset_type}" if meta_asset_type_view else ""
        loader = self._loader_mapper.get(name, self._default_loader)
        return loader.get_card_result(self, record, category, date, card_type, card_setting, setting, asql)

    def _get_render(self, meta_field):
        data_type = meta_field.type if meta_field else None
        return self._get_render_by_type(data_type)

    def _get_render_by_type(self, data_type: MetaFieldType):
        return self._render_mapper.get(data_type, self._default_render)

    @property
    def default_query(self):
        return self._default_query

    @property
    def default_render(self):
        return self._default_render

    @property
    def default_loader(self):
        return self._default_loader
