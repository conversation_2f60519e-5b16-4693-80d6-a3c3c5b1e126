import os
import signal
import subprocess
from datetime import datetime, timedelta

import psutil

from caasm_manage.cleaner import clean_locks
from caasm_service.constants.setting import SettingName
from caasm_service.runtime import workflow_task_service, workflow_record_service, setting_service


def shutdown(clean=False):
    clean_workers()
    clean_workflows(clean)


def clean_workers():
    #   关闭后台
    subprocess.run(["/opt/supervisor/bin/supervisorctl", "stop", "caasm_soar:"])
    #   关闭进程
    for proc in psutil.process_iter():
        cmdline = proc.cmdline()
        if "python" not in cmdline[0]:
            continue
        if len(cmdline) < 3:
            continue
        if cmdline[1] == "-c":
            if "resource_tracker" in cmdline[2] or "spawn_main" in cmdline[2]:
                try:
                    os.kill(proc.pid, signal.SIGKILL)
                except ProcessLookupError:
                    continue
                except PermissionError:
                    raise


def clean_workflows(clean=False):
    if clean:
        #   清理流程
        workflow_task_service.drop()
        workflow_record_service.drop()
    else:
        #   清理部分流程
        deadline = datetime.now() + timedelta(days=-30)
        for workflow in workflow_record_service.find({"create_time": {"$lt": deadline.isoformat()}}):
            workflow_task_service.delete_multi({"workflow_id": workflow.id})
        workflow_record_service.delete_multi({"create_time": {"$lt": deadline.isoformat()}})


def startup():
    subprocess.run(["/opt/supervisor/bin/supervisorctl", "start", "caasm_soar:"])


def restart(clean):
    shutdown(clean)
    clean_locks()
    startup()


def monitor_stuck_workflows():
    #   监控是否有卡住的工作流
    workflow_exceeded_time_setting = setting_service.get_setting(SettingName.WORKFLOW_EXCEEDED_TIME.value)
    if workflow_exceeded_time_setting:
        exceeded_time = workflow_exceeded_time_setting.value
    else:
        exceeded_time = 24 * 60 * 60
    #   加载所有workflow检查状态
    now = datetime.now()
    exceeded_workflows = []
    for workflow in workflow_record_service.find_stuck_workflows():
        #   计算执行时长
        duration = now - workflow.create_time
        if duration.total_seconds() > exceeded_time:
            exceeded_workflows.append(workflow.id)
    #   如果存在超时工作流，则清理。Todo: 精细化处理
    if exceeded_workflows:
        restart(True)


if __name__ == "__main__":
    monitor_stuck_workflows()
