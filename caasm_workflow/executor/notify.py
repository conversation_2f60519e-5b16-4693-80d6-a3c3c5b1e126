import logging

from caasm_service.runtime import workflow_record_service, workflow_task_service
from caasm_tool.util import load_entry
from caasm_workflow.constant import Category
from caasm_workflow.executor._base import BaseExecutor

log = logging.getLogger()


class NotifyExecutor(BaseExecutor):
    def execute(self):
        record = self._get_record()
        if not record:
            log.warning(f"Not found any record by biz_info({self.biz_info})")
            return
        callback_entry = record.callback_entry
        if not callback_entry:
            return
        log.info(f"Do Record({self.biz_info}) notify callback entry")
        load_entry(callback_entry, entry_params={"record": record})

    def _get_record(self):
        record = None
        if self.category == Category.WORKFLOW:
            record = workflow_record_service.get_workflow(workflow_id=self.record_id)
        elif self.category == Category.TASK:
            record = workflow_task_service.get_task(task_id=self.record_id)
        return record

    @property
    def record_id(self):
        return self.biz_info.get("record_id")

    @property
    def category(self):
        return self.biz_info.get("category")
