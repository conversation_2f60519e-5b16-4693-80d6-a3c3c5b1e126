import logging

from caasm_service.constants.workflow import WorkflowStatus
from caasm_service.runtime import workflow_task_service
from caasm_workflow.action import TASK_NODES
from caasm_workflow.executor._base import BaseExecutor

log = logging.getLogger()


class TaskExecutor(BaseExecutor):
    _NODE_MAPPER = {node.TYPE: node for node in TASK_NODES}

    def __init__(self):
        super(TaskExecutor, self).__init__()
        self._task = None

    def filter_message(self, message):
        filter_result = super(TaskExecutor, self).filter_message(message)
        if not filter_result:
            return filter_result

        self._task = self._load_task()
        if not self._task:
            log.warning(f"Not found any task by query({self._task_query})")
            return False
        if self.cancel():
            return False
        self.work_1_heartbeat()
        return True

    def work_1_heartbeat(self, runtime_id=None):
        if not self._task_query:
            return
        if self._cancel_flag:
            return
        workflow_task_service.heartbeat(**self._task_query, value_runtime_id=runtime_id)

    def execute(self):
        self.work_1_heartbeat(self.ident)
        self._node_instance.start()
        self._biz_info = None

    def cancel(self):
        if not self._cancel_flag:
            return False
        self._cancel()
        return True

    def _cancel(self):
        if self._task.finished:
            self._node_instance.notify()
            return

        log.debug(f"{self.name} will cancel task({self._task.id}) executing")

        original_task_status = self._task.status
        self._reset_task_cancel_status()
        self._node_instance.start()
        if original_task_status == WorkflowStatus.DOING:
            self._close()

    def _reset_task_cancel_status(self):
        self._task.finished = True
        self._task.status = WorkflowStatus.CANCEL

    def _close(self):
        self.close_executor(self._task.runtime_id)

    def _load_task(self):
        return workflow_task_service.get_task(**self._task_query) if self._task_query else None

    @property
    def _node_instance(self):
        node_class = self._NODE_MAPPER.get(self._task.type)
        return node_class(task=self._task, force=self.force)

    @property
    def _task_query(self):
        if not self.biz_info:
            return
        return {
            "task_id": self.biz_info.get("task_id"),
            "workflow_id": self.biz_info.get("workflow_id"),
            "name": self.biz_info.get("name"),
            "node_type": self.biz_info.get("node_type"),
        }

    @property
    def worker_wait_time_settings(self):
        return {"work_1_heartbeat": 60}

    @property
    def _cancel_flag(self):
        return self.biz_info.get("cancel_flag")
