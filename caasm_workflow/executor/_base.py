import json
import logging
import os
import queue
import signal
import time
import traceback

from caasm_workflow.common import WorkerProxy

log = logging.getLogger()


class BaseExecutor(WorkerProxy):
    WORKER_RESET_TIMES = 10240

    def __init__(self):
        self._execute_times = None
        self._ident = None
        self._data_queue = None
        self._reset_times = None
        self._biz_info = None
        self._close_flag = None
        super(BaseExecutor, self).__init__()

    def initialize_1_property(self):
        self._ident = str(self.kwargs.get("ident", os.getpid()))
        self._reset_times = self.kwargs.get("reset_times") or self.WORKER_RESET_TIMES
        self._data_queue = self.kwargs.get("channel")
        self._execute_times = 0
        self._close_flag = False
        log.info(f"worker execute max times is {self._reset_times}")

    def work_2_reset(self):
        if self._execute_times <= self._reset_times:
            return
        self._close_flag = True
        self.close_executor(self.ident)

    @classmethod
    def close_executor(cls, ident):
        try:
            if not ident:
                log.error(f"ident empty")
                return
            ident = int(ident)
            os.kill(ident, signal.SIGTERM)
            #   等待5秒
            for _ in range(5 * 10):
                time.sleep(0.1)
                try:
                    os.kill(ident, 0)
                except OSError:
                    # 进程已经不存在了
                    return
            os.kill(ident, signal.SIGKILL)
        except Exception as e:
            log.error(f"send kill process({ident}) error({e})")
        else:
            log.info(f"send kill process({ident}) success")

    def work_3_execute(self):
        empty_flag = False
        try:
            message = self._data_queue.get(timeout=self.DEFAULT_WORKER_WAIT_TIME)
            if not self.filter_message(message):
                return
            log.debug(f"start executing message({self.biz_info})")
            self.execute()
        except queue.Empty:
            empty_flag = True
        except Exception as e:
            log.error(f"{self.name} execute error({e}) detail is({traceback.format_exc()})")
        finally:
            if not empty_flag:
                self._add_execute_times()

    def filter_message(self, message):
        info = message.get("info")
        if not info:
            return False
        try:
            biz_info = json.loads(info)
        except Exception as e:
            log.warning(f"Message ({message}) convert error({e})")
        else:
            self._biz_info = biz_info
        return True

    def execute(self):
        pass

    def _add_execute_times(self):
        self._execute_times += 1
        log.debug(f"finish executing. now worker execute ({self._execute_times}) times")

    @property
    def ident(self):
        return self._ident

    @property
    def biz_info(self):
        return self._biz_info

    @property
    def force(self):
        return self.biz_info.get("force")

    @property
    def running(self):
        if self._close_flag:
            return False
        return super(BaseExecutor, self).running

    def finish(self):
        while self._biz_info:
            time.sleep(0.1)
        return super().finish()
