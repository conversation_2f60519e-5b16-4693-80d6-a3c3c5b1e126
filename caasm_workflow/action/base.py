import itertools
import logging
import traceback
from functools import cached_property

from caasm_service.constants.workflow import WorkflowStatus, CheckType
from caasm_service.runtime import workflow_task_service, workflow_record_service
from caasm_workflow.common import BaseObject
from caasm_workflow.expection import StopException
from caasm_workflow.param.parser import Param<PERSON>arser
from caasm_workflow.sdk.task import task_sdk
from caasm_workflow.util import parse_display_name

log = logging.getLogger()


class BaseNode(BaseObject):
    TYPE = None

    def __init__(self, task=None, force=None):
        self.task = task
        self.force = force
        self.param_parser = ParamParser()

        self._params_list_flag = None
        self._params = None

        self._execute_status = task.status if task else None
        self._execute_error = None
        self._execute_result = None

        super(BaseNode, self).__init__()

    def initialize_0_task(self):
        if self._execute_status == WorkflowStatus.CANCEL:
            self.cancel()
            raise StopException("receive cancel info")
        if self.task.finished:
            self.notify()
            raise StopException("finished")
        return

    def initialize_1_check_prev_task_finish(self):
        if self._check_prev_task_is_finish():
            return
        raise StopException("predecessors is not finished")

    def initialize_2_check_prev_task_status(self):
        status = self._check_prev_task_status()
        if status == WorkflowStatus.CANCEL:
            self._execute_status = WorkflowStatus.CANCEL
            self.cancel()
            raise StopException("need cancel")

    def cancel(self):
        self._start_task()
        self.record_task_result()
        self.notify()

    def sync_1_start_task(self):
        if self._start_task():
            return
        if not self.force:
            raise StopException("already start")

    def sync_2_extract_params(self):
        self._params_list_flag, self._params = self._extract_params()

    def sync_3_change_task_name(self):
        self._change_display_name()

    def sync_4_execute_task(self):
        self._execute()

    def sync_5_get_task_execute_status(self):
        self._execute_status = WorkflowStatus.FAILED if self._execute_error else self.get_task_latest_status()

    def sync_6_record_task_execute_result(self):
        self.record_task_result()

    def sync_7_task_notify(self):
        if not workflow_task_service.check_status_finish(self._execute_status):
            return
        self.notify()

    def error_handle(self, error_message):
        self._execute_status = WorkflowStatus.FAILED
        self.record_task_result()
        self.notify()

    def _extract_params(self):
        if not self.task.params:
            list_flag, params = self._parse_params()
            value_params = params if list_flag else params[0]
            self.modify_runtime_params(value_params)
        else:
            params = self.task.params
            list_flag = isinstance(params, list)
            if not list_flag:
                params = [params]
        return list_flag, params

    def _execute(self):
        error = ""
        try:
            result = [self.call(param) for param in self._params]
            if not self._params_list_flag:
                result = result[0]
        except Exception as e:
            log.error(f"{self.name} call error({e}), detail is {traceback.format_exc()}")
            result = [] if self._params_list_flag else {}
            error = str(e)
        self._execute_result = result
        self._execute_error = error

    def modify_runtime_params(self, value_params):
        workflow_task_service.update_single_task(task_id=self.task_id, value_params=value_params)

    def _change_display_name(self):
        task_display_name = parse_display_name(self.task.display_name, self._params[0])
        if task_display_name == self.task.display_name:
            return
        workflow_task_service.update_single_task(task_id=self.task_id, value_display_name=task_display_name)

    def call(self, param):
        return {}

    def get_task_latest_status(self):
        return WorkflowStatus.SUCCESS

    def notify(self):
        for task_name in self.task.next_nodes:
            task_sdk.start_task(name=task_name, workflow_id=self.task.workflow_id)

    def _check_prev_task_is_finish(self):
        total_count = finish_count = 0
        for prev_task in self.prev_tasks:
            if prev_task.finished:
                finish_count += 1
            total_count += 1
        return total_count == finish_count

    def _check_prev_task_status(self):
        status_flag = set()
        for prev_task in self.prev_tasks:
            status_flag.add(prev_task.status)
        if not status_flag:
            return WorkflowStatus.SUCCESS
        return self._check_status_core(status_flag)

    def _check_status_core(self, status_flag):
        if self.task.check_type == CheckType.ALL_SUCCESS and (
            WorkflowStatus.FAILED in status_flag or WorkflowStatus.CANCEL in status_flag
        ):
            return WorkflowStatus.CANCEL

        if WorkflowStatus.SUCCESS not in status_flag:
            return WorkflowStatus.CANCEL

        return WorkflowStatus.SUCCESS

    def _start_task(self):
        return workflow_task_service.start_task(self.task_id).modified_count

    def _parse_params(self):
        return self._parse_params_core(self.task.param_template)

    def _parse_params_core(self, param_template):

        param_by_field = self.param_parser.extract_root(param_template)

        task_names = []
        for _, field in param_by_field.items():
            task_names.extend(field.task_names)

        task_result_mapper = self._find_prev_task_result(task_names)
        list_flag = self._parse_list_flag(task_result_mapper)

        return list_flag, self._build_request_params(param_by_field, task_result_mapper)

    _WORKFLOW_KEY = "__global__"

    def _build_request_params(self, param_by_field, task_result_mapper):

        result, task_indices, task_result = [], [], []

        for task_name, tmp_task_result in task_result_mapper.items():
            if tmp_task_result is None:
                tmp_task_result = {}
            if isinstance(tmp_task_result, dict):
                tmp_task_result = [tmp_task_result]
            task_result.append(tmp_task_result)
            task_indices.append(task_name)

        for single_task_result in itertools.product(*task_result):
            single_task_request_params = {self._WORKFLOW_KEY: self.workflow.params}
            for index, row in enumerate(single_task_result):
                single_task_request_params[task_indices[index]] = row

            tmp_result = {}
            for request_name, field_resp in param_by_field.items():
                if field_resp.is_static:
                    value = field_resp.val
                else:
                    value = self.param_parser.fill_params(field_resp.val, single_task_request_params)
                tmp_result[request_name] = value
            result.append(tmp_result)
        return result

    def _format_result(self, template, result, list_flag=None):
        new_result = []
        if list_flag is None:
            list_flag = isinstance(result, list)

        if not list_flag and not isinstance(result, list):
            result = [result]

        for data in result:
            new_result.append(self._format_single_result(data, template))

        if list_flag:
            return new_result
        return new_result[0] if new_result else {}

    def _format_single_result(self, data, output_template):
        result = {}
        data = data or {}
        for output_name, output_param in output_template.items():
            result[output_name] = self.param_parser.extract_value(output_param.name, data)
        return result

    def record_task_result(self):
        result = self._format_result(self.task.output_template, self._execute_result)
        workflow_task_service.finish_task(self.task_id, self._execute_status, result, error=self._execute_error)

    _PREV_FIELDS = ["name", "result"]

    def _find_prev_task_result(self, task_names):
        if not task_names:
            return {}
        task_names = list(set(task_names))
        tasks = workflow_task_service.find_task(
            workflow_id=self.task.workflow_id, names=task_names, fields=self._PREV_FIELDS
        )
        return {task.name: task.result for task in tasks}

    @classmethod
    def _parse_list_flag(cls, task_result):
        for key, val in task_result.items():
            if isinstance(val, list):
                return True
        return False

    @property
    def task_id(self):
        return self.task.id

    _WORKFLOW_FIELDS = ["result", "params", "superior_task_id", "output_template", "name", "callback_entry"]

    @cached_property
    def workflow(self):
        return workflow_record_service.get_workflow(workflow_id=self.task.workflow_id, fields=self._WORKFLOW_FIELDS)

    _FINISH_FIELDS = ["finished", "status"]

    @cached_property
    def prev_tasks(self):
        return workflow_task_service.find_task(
            workflow_id=self.workflow.id,
            next_task_name=self.task.name,
            fields=self._FINISH_FIELDS,
        )

    @property
    def name(self):
        task_id = self.task.id if self.task else ""
        task_name = self.task.name if self.task else ""
        return f"Task[Id:{task_id},Name:{task_name}]"
