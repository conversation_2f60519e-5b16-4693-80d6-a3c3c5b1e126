import logging

from caasm_service.constants.workflow import NodeType, WorkflowStatus
from caasm_service.runtime import workflow_record_service
from caasm_workflow.action.base import BaseNode
from caasm_workflow.constant import Category
from caasm_workflow.sdk.notify import notify_sdk
from caasm_workflow.sdk.task import task_sdk

log = logging.getLogger()


class EndNode(BaseNode):
    TYPE = NodeType.END

    def get_task_latest_status(self):
        return WorkflowStatus.SUCCESS

    def cancel(self):
        super(EndNode, self).cancel()
        self._change_workflow_output()

    def notify(self):
        self._notify_callback()
        self._notify_superior()

    def sync_6_record_task_execute_result(self):
        super(EndNode, self).sync_6_record_task_execute_result()
        self._change_workflow_output()

    def _change_workflow_output(self):
        if workflow_record_service.get_workflow_count(workflow_id=self.workflow.id, finished=True):
            return
        workflow_record_service.finish_workflow(
            workflow_id=self.workflow.id,
            value_status=self._execute_status,
            value_result=self.workflow_result,
        )

    def _notify_callback(self):
        notify_sdk.start_callback_notify(self.workflow.id, Category.WORKFLOW)

    def _notify_superior(self):
        superior_task_id = self.workflow.superior_task_id
        if not superior_task_id:
            return
        task_sdk.start_task(task_id=superior_task_id)

    @property
    def workflow_result(self):
        list_flag, original_data = self._parse_params_core(self.workflow.output_template)
        return self._format_result(self.workflow.output_template, original_data, list_flag)
