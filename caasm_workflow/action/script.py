from functools import cached_property

from caasm_service.constants.workflow import NodeType, WorkflowStatus
from caasm_service.runtime import workflow_task_service
from caasm_tool.util import load_entry
from caasm_workflow.action.base import BaseNode
from caasm_workflow.action.tool.constant import PLAYBOOK_SWITCH_ENTRY


class ScriptNode(BaseNode):
    TYPE = NodeType.ACTION

    def call(self, param):
        result = self.entry(**param)
        self._change_type()
        return result

    def get_task_latest_status(self):
        if self.is_switch_playbook:
            return WorkflowStatus.SLEEP
        return super(ScriptNode, self).get_task_latest_status()

    def modify_runtime_params(self, value_params):
        if self.is_switch_playbook:
            self._modify_switch(value_params)
        return super(ScriptNode, self).modify_runtime_params(value_params)

    def _modify_switch(self, value_params):
        if isinstance(value_params, dict):
            value_params["superior_task_id"] = self.task.id
            return
        for value_param in value_params:
            value_param["superior_task_id"] = self.task.id

    def _change_type(self):
        if not self.is_switch_playbook:
            return
        workflow_task_service.update_single_task(task_id=self.task_id, value_node_type=NodeType.PLAYBOOK)

    @cached_property
    def is_switch_playbook(self):
        return self.task.entry == PLAYBOOK_SWITCH_ENTRY

    @cached_property
    def entry(self):
        return load_entry(self.task.entry)
