from caasm_service.constants.workflow import NodeType, WorkflowStatus
from caasm_service.runtime import workflow_record_service, workflow_task_service
from caasm_workflow.action.base import BaseNode
from caasm_workflow.expection import StopException
from caasm_workflow.sdk.task import task_sdk
from caasm_workflow.sdk.workflow import workflow_sdk


class PlaybookNode(BaseNode):
    TYPE = NodeType.PLAYBOOK

    def call(self, param):
        workflow_sdk.start_workflow(playbook_name=self.task.name, params=param, superior_task_id=self.task.id)
        return super(PlaybookNode, self).call(param)

    def get_task_latest_status(self):
        return WorkflowStatus.SLEEP

    def initialize_0_task(self):
        super(PlaybookNode, self).initialize_0_task()
        self._check_sub_workflow()

    def initialize_2_check_prev_task_status(self):
        super(PlaybookNode, self).initialize_2_check_prev_task_status()

    def _check_sub_workflow(self):
        count = workflow_record_service.get_workflow_count(superior_task_id=self.task.id)
        if count:
            finished, result, status = self._parse_sub_workflow_info()
            if not finished:
                raise StopException(f"{self.name}`s sub workflow is not total finished")
            self._finish_task(status, result)
            raise StopException(f"{self.name}`s  sub workflow is all finished")

    def _finish_task(self, status, result):
        workflow_task_service.finish_task(self.task_id, status=status, result=result)
        self.notify()

    _SUB_WORKFLOW_FIELDS = ["finished", "result", "status"]

    def _parse_sub_workflow_info(self):
        curl = workflow_record_service.find_workflow(superior_task_id=self.task.id, fields=self._SUB_WORKFLOW_FIELDS)
        result, status_flag = [], set()

        finished_cnt = total_cnt = 0
        for sub_workflow in curl:
            status_flag.add(sub_workflow.status)
            if sub_workflow.finished:
                result.append(sub_workflow.result)
                finished_cnt += 1
            total_cnt += 1

        if total_cnt != finished_cnt:
            return False, None, WorkflowStatus.DOING

        return True, (result if len(result) != 1 else result[0]), self._check_status_core(status_flag)

    def notify(self):
        super(PlaybookNode, self).notify()
        self._notify_lower()

    _LOWER_FIELDS = ["_id"]

    def _notify_lower(self):
        if self._execute_status != WorkflowStatus.CANCEL:
            return

        sub_workflows = workflow_record_service.find_workflow(
            superior_task_id=self.task_id, finished=False, fields=self._LOWER_FIELDS
        )
        sub_workflow_ids = [sub_workflow_record.id for sub_workflow_record in sub_workflows]
        if not sub_workflow_ids:
            return

        sub_workflow_tasks = workflow_task_service.find_task(
            workflow_ids=sub_workflow_ids,
            fields=self._LOWER_FIELDS,
            finished=False,
        )

        for sub_workflow_task in sub_workflow_tasks:
            task_sdk.cancel_task(sub_workflow_task.id)
