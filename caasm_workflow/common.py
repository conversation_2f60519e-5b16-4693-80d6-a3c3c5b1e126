import logging
import os
import signal
import sys
import threading
import time
import traceback
from functools import wraps
from threading import Thread

from caasm_persistence.handler.storage.runtime import close_client
from caasm_tool.reflect import parse_instance_callable
from caasm_workflow.constant import SIGNALS
from caasm_workflow.expection import StopException

log = logging.getLogger()


class BaseObject(object):
    def __init__(self, **kwargs):
        self._initializer_refs = parse_instance_callable(self, "initialize_")
        self._sync_refs = parse_instance_callable(self, "sync_")
        self._kwargs = kwargs

    @classmethod
    def start_thread(cls, callback, daemon=False, *args, **kwargs):
        thread = Thread(target=callback, daemon=daemon, args=args, kwargs=kwargs)
        thread.start()
        return thread

    @property
    def kwargs(self):
        return self._kwargs

    def initialize(self):
        for _, initialize_ref in self._initializer_refs.items():
            initialize_ref()

    def start(self, **kwargs):
        if kwargs:
            self._kwargs = kwargs
        self.prepare()
        try:
            self.initialize()
            self.handle()
        except StopException as e:
            log.warning(f"[Name:{self.name} Kwargs:{self._kwargs}] stop executing, because {e}")
        except Exception as e:
            log.warning(f"{self.name} handle error({e}) detail is {traceback.format_exc()}")
            self.error_handle(str(e))
        self.after()

    def handle(self):
        for _, sync_ref in self._sync_refs.items():
            sync_ref()

    def error_handle(self, error_message):
        pass

    def after(self):
        pass

    def prepare(self):
        pass

    @property
    def name(self):
        return self.__class__.__name__


def _loop_coat(self, func, error_callback=None):
    @wraps(func)
    def inner(*args, **kwargs):
        while self.running:
            func_name = func.__name__
            try:
                func(*args, **kwargs)
            except Exception as e:
                log.error(f"Func({func_name}) handle error({e}) detail is {traceback.format_exc()}")
                if error_callback:
                    error_callback()
            wait_time = self._get_worker_wait_time(func_name)
            if wait_time:
                time.sleep(wait_time)

    return inner


class WorkerProxy(BaseObject):
    DEFAULT_WORKER_WAIT_TIME = 0.1
    DEFAULT_FINISH_WAIT_TIME = 1
    DEFAULT_WORKER_DAEMON = True

    def __init__(self, **kwargs):
        self._running = None
        self._workers = []
        self._pid = None
        self._worker_refs = parse_instance_callable(self, "work_", filter_flag=False)
        super(WorkerProxy, self).__init__(**kwargs)

    def prepare(self):
        self._running = threading.Event()
        self.signal()
        self._pid = os.getpid()

    def signal(self):
        for sg in SIGNALS:
            signal.signal(sg, self.exit)

    def handle(self):
        super(WorkerProxy, self).handle()
        for worker_ref_name, worker_ref_callable in self._worker_refs.items():
            daemon = self._get_worker_daemon(worker_ref_name)
            error_callback = self._get_worker_error_callback(worker_ref_name)
            ref = _loop_coat(self, worker_ref_callable, error_callback=error_callback)
            worker = threading.Thread(name=worker_ref_name, target=ref, daemon=daemon)
            worker.start()
            self._workers.append(worker)

    def notify(self):
        close_client()

    def _get_worker_daemon(self, worker_name):
        return self.worker_daemon_settings.get(worker_name, self.DEFAULT_WORKER_DAEMON)

    def _get_worker_error_callback(self, worker_name):
        return self.worker_error_callback_settings.get(worker_name)

    def _get_worker_wait_time(self, worker_name):
        return self.worker_wait_time_settings.get(worker_name, self.DEFAULT_WORKER_WAIT_TIME)

    def error_handle(self, error_message):
        self.finish()

    def exit(self, signalnum, handler):
        self.finish()

    def after(self):
        super(WorkerProxy, self).after()
        self._running.wait()

    def finish(self):
        self._running.set()
        try:
            self.notify()
        except Exception as e:
            log.warning(f"Notify error({e}) detail({traceback.format_exc()})")

        log.info(f"{self.name}(Process:{self.pid}) will close.....")

        sys.exit(0)

    @property
    def running(self):
        return not self._running.is_set()

    @property
    def kwargs(self):
        return self._kwargs

    @property
    def pid(self):
        return self._pid

    @property
    def worker_daemon_settings(self):
        return {}

    @property
    def worker_error_callback_settings(self):
        return {}

    @property
    def worker_wait_time_settings(self):
        return {}
