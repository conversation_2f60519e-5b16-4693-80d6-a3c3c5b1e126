import errno
import logging
import multiprocessing
import os
import re
import signal
from dataclasses import dataclass

from caasm_tool.util import load_entry

log = logging.getLogger()


@dataclass
class WorkerRef(object):
    process: multiprocessing.Process
    worker: callable
    pid: int = ""

    def __post_init__(self):
        if not self.pid:
            self.pid = self.process.pid


def create_worker_proxy(worker_proxy, **kwargs):
    try:
        worker = worker_proxy()
        process = multiprocessing.Process(target=worker.start, kwargs=kwargs)
        process.start()
    except OSError as e:
        pass
    else:
        return WorkerRef(process=process, worker=worker)


def close_worker_proxy(worker_ref):
    try:
        pid = worker_ref.pid
        os.kill(pid, signal.SIGTERM)
        while True:
            pid, _ = os.waitpid(pid, 0)
            if pid == 0:
                break
    except ProcessLookupError as e:
        pass
    except OSError as e:
        if e.errno != errno.ECHILD:
            raise


_DISPLAY_NAME_ENTRY_RE = re.compile(r"entry:(.+):entry")


def parse_display_name(display_name, params):
    result = display_name
    try:
        if not display_name:
            return result
        entries = _DISPLAY_NAME_ENTRY_RE.findall(display_name)
        if not entries:
            return result
        result = _DISPLAY_NAME_ENTRY_RE.sub("", result)
        for entry in entries:
            result = result.format(**load_entry(entry, entry_params=params, no_params_execute_flag=True))
    except Exception as e:
        log.error(f"load display_name({display_name}) error({e})")
    return result
