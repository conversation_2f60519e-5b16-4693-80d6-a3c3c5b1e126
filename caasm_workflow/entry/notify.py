import multiprocessing

if __name__ == "__main__":
    multiprocessing.set_start_method("spawn")

from caasm_config.config import caasm_config
from caasm_tool import log

if __name__ == "__main__":
    log.install(caasm_config.WORKFLOW_NOTIFY_LOG)

from caasm_workflow.entry._base import BaseEntry


class NotifyEntry(BaseEntry):
    @property
    def manager_class(self):
        return "caasm_workflow.manager.notify:NotifyManager"


if __name__ == "__main__":
    NotifyEntry().start()
