import multiprocessing

if __name__ == "__main__":
    multiprocessing.set_start_method("spawn")

from caasm_tool import log
from caasm_config.config import caasm_config

if __name__ == "__main__":
    log.install(caasm_config.WORKFLOW_TASK_LOG)

from caasm_workflow.entry._base import BaseEntry


class TaskEntry(BaseEntry):
    @property
    def manager_class(self):
        return "caasm_workflow.manager.task:TaskManager"


if __name__ == "__main__":
    TaskEntry().start()
