from caasm_config.config import caasm_config
from caasm_tool import log

if __name__ == "__main__":
    log.install(caasm_config.WORKFLOW_MONITOR_LOG)

from caasm_workflow.entry._base import BaseEntry


class MonitorEntry(BaseEntry):
    @property
    def manager_class(self):
        return "caasm_workflow.monitor.runtime:load_entry"

    def sync_1_start(self):
        return self.manager()


if __name__ == "__main__":
    MonitorEntry().start()
