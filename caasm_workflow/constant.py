import signal


class ExecutorType(object):
    TASK = "task"
    WORKFLOW = "workflow"
    NOTIFY = "notify"


class Category(object):
    TASK = "task"
    WORKFLOW = "workflow"


class SchedulerType(object):
    REDIS = "redis"


class Action(object):
    NORMAL = "normal"
    CANCEL = "cancel"
    CALLBACK = "callback"


SIGNALS = [signal.SIGTERM, signal.SIGHUP, signal.SIGUSR1, signal.SIGUSR2, signal.SIGINT]
