from apscheduler.executors.pool import Thread<PERSON>oolExecutor
from apscheduler.schedulers.blocking import BlockingScheduler

from caasm_config.config import caasm_config
from caasm_workflow.monitor import jobs


def load_entry():
    executors = {"default": ThreadPoolExecutor()}
    job_defaults = {"coalesce": True, "max_instance": 1, "misfire_grace_time": 60}
    timezone = caasm_config.SCHEDULER_TIME_ZONE

    scheduler = BlockingScheduler(executors=executors, job_defaults=job_defaults, timezone=timezone)
    _join_job(scheduler)

    scheduler.start()


def _join_job(scheduler):
    for job_class_name in jobs.__all__:
        job_class = getattr(jobs, job_class_name, None)
        if not job_class:
            continue
        scheduler.add_job(
            func=job_class.execute,
            trigger=job_class.get_trigger_type(),
            name=job_class.get_job_name(),
            kwargs=job_class.get_params(),
            **job_class.get_trigger_info(),
        )


if __name__ == "__main__":
    load_entry()
