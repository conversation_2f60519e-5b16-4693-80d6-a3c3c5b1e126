import datetime

from caasm_service.constants.workflow import WorkflowStatus
from caasm_service.runtime import workflow_task_service
from caasm_tool.constants import DATETIME_FORMAT
from caasm_workflow.monitor.jobs.base import Job
from caasm_workflow.sdk.task import task_sdk


class InitTaskInspectionJob(Job):
    @classmethod
    def execute(cls, **kwargs):
        for start_task in cls.find_task():
            task_sdk.start_task(task_id=start_task.id, force=cls._force())

    _TASK_FIELDS = ["_id"]

    @classmethod
    def find_task(cls):
        return workflow_task_service.find_task(
            status=WorkflowStatus.INIT,
            create_time_lte=cls._time_condition(),
            limit=cls._size_condition(),
            fields=cls._TASK_FIELDS,
        )

    @classmethod
    def get_trigger_info(cls):
        return {"minute": "*/5"}

    @classmethod
    def _time_condition(cls):
        return (datetime.datetime.now() - datetime.timedelta(minutes=cls._time_cond())).strftime(DATETIME_FORMAT)

    @classmethod
    def _size_condition(cls):
        return 500

    @classmethod
    def _time_cond(cls):
        return 5

    @classmethod
    def _force(cls):
        return False
