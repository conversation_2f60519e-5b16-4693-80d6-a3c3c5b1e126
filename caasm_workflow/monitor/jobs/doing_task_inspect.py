from caasm_service.constants.workflow import WorkflowStatus
from caasm_service.runtime import workflow_task_service
from caasm_workflow.monitor.jobs.init_task_inspect import InitTaskInspectionJob


class DoingTaskInspectionJob(InitTaskInspectionJob):
    @classmethod
    def find_task(cls):
        return workflow_task_service.find_task(
            status=WorkflowStatus.DOING,
            heartbeat_time_lte=cls._time_condition(),
            limit=cls._size_condition(),
            fields=cls._TASK_FIELDS,
        )

    @classmethod
    def get_trigger_info(cls):
        return {"minute": "*/5"}

    @classmethod
    def _time_cond(cls):
        return 10

    @classmethod
    def _force(cls):
        return True
