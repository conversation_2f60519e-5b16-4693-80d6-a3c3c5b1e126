import logging
import multiprocessing
import traceback

from caasm_workflow.common import WorkerProxy
from caasm_workflow.util import create_worker_proxy, close_worker_proxy

log = logging.getLogger()


class BaseManager(WorkerProxy):
    def __init__(self):
        super(BaseManager, self).__init__()
        self._executor_mapper = {}
        self._scheduler = None
        self._channel = None

    def initialize_0_channel(self):
        self._channel = multiprocessing.Queue()

    def initialize_1_executor(self):
        for _ in range(self.executor_count):
            self._join_executor_mapper()

    def initialize_2_scheduler(self):
        self._scheduler = self.scheduler_class(**self.scheduler_params)

    def work_1_monitor_executor_count(self):
        if len(self._executor_mapper) >= self.executor_count:
            return
        self._join_executor_mapper()

    def work_2_monitor_executor_status(self):
        proxies = list(self._executor_mapper.values())
        for proxy in proxies:
            if proxy.process.is_alive():
                continue
            try:
                close_worker_proxy(proxy)
            except Exception as e:
                log.error(f"Proxy({proxy}) close error({e}), detail is {traceback.format_exc()}")
            else:
                self._executor_mapper.pop(proxy.pid, None)

    def sync_2_start_scheduler(self):
        self.start_thread(self._scheduler.start, daemon=True, channel=self._channel, **self.scheduler_params)

    def _join_executor_mapper(self):
        proxy = create_worker_proxy(self.executor_class, channel=self._channel, **self.executor_params)
        if not proxy:
            log.warning("worker proxy create error")
            return
        self._executor_mapper[proxy.pid] = proxy
        log.info(f"{self.name} create executor(process:{proxy.pid}) success")

    @property
    def executor_class(self):
        raise NotImplementedError

    @property
    def scheduler_class(self):
        raise NotImplementedError

    @property
    def executor_count(self):
        raise NotImplementedError

    @property
    def executor_params(self):
        raise NotImplementedError

    @property
    def scheduler_params(self):
        raise NotImplementedError

    @property
    def scheduler(self):
        return self._scheduler
