from caasm_config.config import caasm_config
from caasm_workflow.executor.task import TaskExecutor
from caasm_workflow.manager._base import BaseManager
from caasm_workflow.scheduler.redis_scheduler import RedisScheduler


class TaskManager(BaseManager):
    @property
    def executor_class(self):
        return TaskExecutor

    @property
    def scheduler_class(self):
        return RedisScheduler

    @property
    def executor_count(self):
        return caasm_config.WORKFLOW_TASK_EXECUTOR.get("max_worker")

    @property
    def executor_params(self):
        return {"reset_times": caasm_config.WORKFLOW_TASK_EXECUTOR.get("close_times")}

    @property
    def scheduler_params(self):
        return {
            "consumer_name": caasm_config.WORKFLOW_TASK_SCHEDULER.get("consumer_name"),
            "stream_name": caasm_config.WORKFLOW_TASK_SCHEDULER.get("stream_name"),
            "group_name": caasm_config.WORKFLOW_TASK_SCHEDULER.get("group_name"),
        }
