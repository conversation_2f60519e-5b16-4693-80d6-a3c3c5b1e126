from caasm_config.config import caasm_config
from caasm_workflow.executor.notify import NotifyExecutor
from caasm_workflow.manager._base import BaseManager
from caasm_workflow.scheduler.redis_scheduler import RedisScheduler


class NotifyManager(BaseManager):
    @property
    def executor_class(self):
        return NotifyExecutor

    @property
    def scheduler_class(self):
        return RedisScheduler

    @property
    def executor_count(self):
        return caasm_config.WORKFLOW_NOTIFY_EXECUTOR.get("max_worker")

    @property
    def executor_params(self):
        return {"reset_times": caasm_config.WORKFLOW_NOTIFY_EXECUTOR.get("close_times")}

    @property
    def scheduler_params(self):
        return {
            "consumer_name": caasm_config.WORKFLOW_NOTIFY_SCHEDULER.get("consumer_name"),
            "stream_name": caasm_config.WORKFLOW_NOTIFY_SCHEDULER.get("stream_name"),
            "group_name": caasm_config.WORKFLOW_NOTIFY_SCHEDULER.get("group_name"),
        }
