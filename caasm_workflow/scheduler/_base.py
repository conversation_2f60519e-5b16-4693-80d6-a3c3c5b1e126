import logging

from caasm_workflow.common import WorkerProxy

log = logging.getLogger()


class BaseScheduler(WorkerProxy):
    def __init__(self, **kwargs):
        super(BaseScheduler, self).__init__(**kwargs)
        self._size = None
        self._monitor_wait_time = None
        self._poll_wait_time = None
        self._queue = None

    def signal(self):
        pass

    def initialize_1_property(self):
        self._size = self.kwargs.get("size", 100)
        self._monitor_wait_time = self.kwargs.get("monitor_wait_time", 60)
        self._poll_wait_time = self.kwargs.get("poll_wait_time", 20)
        self._queue = self.kwargs.get("channel")

    def work_1_monitor(self):
        result = self.ping()
        log.debug(f"{self.name} Ping response ({result})")
        return

    def work_2_poll(self):
        messages = self.poll()
        if not messages:
            return
        for message in messages:
            log.debug(f"Original Message is {message}")
            self._queue.put(self.build_message(*message))

    def send(self, task):
        raise NotImplementedError

    def poll(self):
        raise NotImplementedError

    def ping(self):
        pass

    @property
    def size(self):
        return self._size

    @property
    def worker_wait_time_settings(self):
        return {"work_1_monitor": 20}

    @classmethod
    def build_message(cls, message_id, info):
        info["message_id"] = message_id
        return info
