import json

import redis

from caasm_config.config import caasm_config
from caasm_workflow.scheduler._base import BaseScheduler


class RedisScheduler(BaseScheduler):
    def __init__(self, **kwargs):
        self._stream_name = None
        self._group_name = None
        self._consumer_name = None
        self._redis_conn = None
        self._max_length = None
        super(RedisScheduler, self).__init__(**kwargs)

    def initialize_1_property(self):
        self._consumer_name = self.kwargs.get("consumer_name")
        self._stream_name = self.kwargs.get("stream_name")
        self._group_name = self.kwargs.get("group_name")
        self._max_length = self.kwargs.get("max_length", 100000)
        super(RedisScheduler, self).initialize_1_property()

    def initialize_3_redis_conn(self):
        connection_pool = redis.ConnectionPool(decode_responses=True, **caasm_config.REDIS)
        self._redis_conn = redis.Redis(connection_pool=connection_pool)

    def initialize_4_params_check(self):
        if not self._stream_name:
            raise ValueError("No stream")
        if not self._group_name:
            raise ValueError("No group")
        if not self._consumer_name:
            raise ValueError("No consumer")

    def initialize_5_create(self):
        groups = []
        try:
            groups = self._redis_conn.xinfo_groups(self._stream_name)
        except redis.exceptions.ResponseError:
            pass
        if self._group_name not in [i["name"] for i in groups]:
            self._redis_conn.xgroup_create(self._stream_name, self._group_name, mkstream=True)

    def ping(self):
        return self._redis_conn.ping()

    def send(self, data):
        message = {"info": json.dumps(data)}
        self._redis_conn.xadd(self._stream_name, message, maxlen=self._max_length)

    def poll(self):
        try:
            response = self._redis_conn.xreadgroup(
                groupname=self._group_name,
                consumername=self._consumer_name,
                streams={self._stream_name: ">"},
                count=self.size,
                noack=True,
            )
        except redis.exceptions.TimeoutError:
            response = None
        return response[0][1] if response else []

    def notify(self):
        if not self._redis_conn:
            return
        self._redis_conn.close()
