from functools import wraps

from caasm_tool.util import load_class


class SDKToolBase(object):
    def __init__(self):
        self._manager = None

    @property
    def manager_class_define(self):
        raise NotImplementedError

    @property
    def scheduler(self):
        return self._manager.scheduler


def _pack_manager(func):
    @wraps(func)
    def inner(self, *args, **kwargs):
        if not self._manager:
            self._manager = load_class(self.manager_class_define)()
            self._manager.initialize_2_scheduler()
            self._manager.scheduler.initialize()
        result = func(self, *args, **kwargs)
        return result

    return inner
