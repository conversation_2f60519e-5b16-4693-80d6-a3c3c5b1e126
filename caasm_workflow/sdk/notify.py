from caasm_workflow.sdk._base import SDKToolBase, _pack_manager


class NotifySDK(SDKToolBase):
    @property
    def manager_class_define(self):
        return "caasm_workflow.manager.notify:NotifyManager"

    @_pack_manager
    def start_callback_notify(self, record_id, category):
        info = {"record_id": str(record_id), "category": category}
        return self.scheduler.send(info)


notify_sdk = NotifySDK()
