import logging
import traceback
from functools import cached_property

from caasm_service.constants.workflow import WorkflowStatus, NodeType
from caasm_service.runtime import workflow_playbook_service, workflow_record_service, workflow_task_service
from caasm_workflow.sdk.task import task_sdk
from caasm_workflow.util import parse_display_name

log = logging.getLogger()


class WorkflowStarter(object):
    def __init__(self, playbook_name, params=None, superior_task_id=None):
        self._playbook_name = playbook_name
        self._superior_task_id = superior_task_id
        self._params = params or {}

        self._task_ids = []
        self._workflow_id = None

    def execute(self):
        try:
            self._create_biz_info()
        except Exception as e:
            log.error(f"Workflow execute error({e}), detail is {traceback.format_exc()}")
            self._error_handle(str(e))
        else:
            self._after()
            return self._workflow_id

    def _create_biz_info(self):
        self._create_workflow()
        self._create_task()

    def _error_handle(self, error_message):
        self._close_workflow(error_message)

    def _after(self):
        task_sdk.start_task(workflow_id=self._workflow_id, node_type=NodeType.START)

    def _create_workflow(self):
        workflow = workflow_record_service.load_entity(
            callback_entry=self.playbook.callback_entry,
            display_name=parse_display_name(self.playbook.display_name, self._params),
            playbook_id=self.playbook.id,
            name=self.playbook.name,
            status=WorkflowStatus.INIT,
            finished=False,
            params=self._params,
            superior_task_id=self._superior_task_id,
            result=None,
        )
        workflow.param_template = self.playbook.param_template
        workflow.output_template = self.playbook.output_template
        resp = workflow_record_service.save(workflow)
        if not resp.flag:
            raise ValueError(f"Workflow create error({resp.msg})")

        self._workflow_id = resp.inserted_id
        workflow_record_service.update_single_workflow(self._workflow_id, value_top_workflow_id=self._top_workflow_id)
        self._workflow = workflow

    def _create_task(self):
        for node_name, node in self.playbook.node_mapper.items():
            task = workflow_task_service.load_entity(
                display_name=node.display_name,
                name=node_name,
                entry=node.entry,
                type=node.type,
                next_nodes=node.next_nodes,
                workflow_id=self._workflow_id,
                status=WorkflowStatus.INIT,
                callback_entry=self.playbook.callback_entry,
                finished=False,
                top_workflow_id=self._top_workflow_id,
                check_type=node.check_type,
            )
            task.param_template = node.param_template
            task.output_template = node.output_template
            save_res = workflow_task_service.save(task)
            if not save_res.flag:
                raise ValueError(f"Task create error({save_res})")

    @cached_property
    def _top_workflow_id(self):
        if not self._superior_task_id:
            return self._workflow_id
        return workflow_task_service.get_task(self._superior_task_id, fields=["top_workflow_id"]).top_workflow_id

    @cached_property
    def playbook(self):
        playbook = workflow_playbook_service.get_playbook(name=self._playbook_name, enabled=True)
        if not playbook:
            raise ValueError(f"Not found playbook by name({self._playbook_name})")
        return playbook

    def _close_workflow(self, error_message):
        if not self._workflow_id:
            return
        workflow_record_service.finish_workflow(
            self._workflow_id,
            value_status=WorkflowStatus.FAILED,
            value_error=error_message,
        )
        workflow_task_service.update_more_task(
            task_ids=self._task_ids,
            finished=False,
            value_status=WorkflowStatus.FAILED,
            value_error=error_message,
        )


class WorkflowSDK(object):
    @classmethod
    def start_workflow(cls, playbook_name, params=None, superior_task_id=None):
        return WorkflowStarter(playbook_name, params=params, superior_task_id=superior_task_id).execute()

    _CANCEL_FIELDS = ["_id"]

    @classmethod
    def cancel_workflow(cls, workflow_id):
        tasks = workflow_task_service.find_task(workflow_id=workflow_id, fields=cls._CANCEL_FIELDS, finished=False)
        task_ids = [task.id for task in tasks]
        for task_id in task_ids:
            task_sdk.cancel_task(task_id)


workflow_sdk = WorkflowSDK()
