from caasm_service.constants.workflow import NodeType
from caasm_workflow.sdk._base import SDKToolBase, _pack_manager


class TaskSDK(SDKToolBase):
    @property
    def manager_class_define(self):
        return "caasm_workflow.manager.task:TaskManager"

    @_pack_manager
    def start_task(self, workflow_id=None, task_id=None, name="", node_type="", force=None):
        send_data = {}
        if workflow_id:
            send_data["workflow_id"] = str(workflow_id)
        if task_id:
            send_data["task_id"] = str(task_id)
        if name:
            send_data["name"] = name
        if node_type:
            if isinstance(node_type, NodeType):
                node_type = node_type.value
            send_data["node_type"] = str(node_type)
        if force is not None:
            send_data["force"] = force
        self.scheduler.send(send_data)

    @_pack_manager
    def cancel_task(self, task_id):
        send_data = {"task_id": str(task_id), "cancel_flag": True}
        self.scheduler.send(send_data)


task_sdk = TaskSDK()
