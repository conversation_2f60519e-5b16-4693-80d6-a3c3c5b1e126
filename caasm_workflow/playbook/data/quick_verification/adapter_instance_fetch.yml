name: quick_adapter_instance_fetch
description: 适配器信息采集
display_name: "适配器实例【entry:caasm_workflow.action.tool.translate:translate_adapter_instance:entry{adapter_instance_display_name}】数据采集"
params:
  - name: category
  - name: adapter_name
  - name: adapter_instance_id
  - name: system_call
nodes:
  - name: start
    type: start
    display_name: "采集开始"
    next_nodes:
      - quick_fetch

  - name: quick_fetch
    type: action
    display_name: "适配器实例【entry:caasm_workflow.action.tool.translate:translate_adapter_instance:entry{adapter_instance_display_name}】数据采集"
    entry: "caasm_manage.steps.fetch:default_fetch"
    params:
      - name: adapter_instance_id
        value: "${__global__.adapter_instance_id}"
      - name: adapter_name
        value: "${__global__.adapter_name}"
      - name: fetch_type
        value: "${__global__.category}"
      - name: system_call
        value: "${__global__.system_call}"
    next_nodes:
      - end

  - name: end
    display_name: "采集结束"
    type: end
