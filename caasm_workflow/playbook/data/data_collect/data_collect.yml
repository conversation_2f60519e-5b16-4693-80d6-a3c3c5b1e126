name: data_collect
description: 数据治理
display_name: "数据治理"
callback_entry: "caasm_workflow.notify.entry.unlock:unlock"
nodes:
  - name: start
    type: start
    display_name: "开始"
    next_nodes:
      - collect_lock

  - name: collect_lock
    type: playbook
    display_name: "全局加锁"
    params:
      - name: category
        value: "system_data_collect"
    next_nodes:
      - gen_snapshot_date

  - name: gen_snapshot_date
    display_name: "生成快照记录"
    type: action
    entry: "caasm_service.runtime:snapshot_record_service.generate_snapshot_record"
    output:
      - name: date
      - name: _id
    next_nodes:
      - find_category

  - name: find_category
    display_name: "查询分类信息"
    type: action
    entry: "caasm_service.runtime:category_service.find_category"
    params:
      - name: dict_resp
        value: true
      - name: adapter_instance_exists
        value: true
    output:
      - name: name
    next_nodes:
      - check_empty

  - name: check_empty
    display_name: "检查分类信息是否空"
    type: playbook
    params:
      - name: value
        value: "${find_category.name}"
    next_nodes:
      - category_collect

  - name: category_collect
    display_name: "执行数据治理"
    type: playbook
    check_type: "all_success"
    params:
      - name: category
        value: "${find_category.name}"
      - name: date
        value: "${gen_snapshot_date.date}"
    next_nodes:
      - collect_hook

  - name: collect_hook
    display_name: "额外处理"
    type: playbook
    check_type: "all_success"
    params:
      - name: snapshot_record_id
        value: ${gen_snapshot_date._id}
      - name: date
        value: "${gen_snapshot_date.date}"
    next_nodes:
      - collect_release

  - name: collect_release
    display_name: "释放全局锁"
    type: playbook
    params:
      - name: category
        value: "system_data_collect"
    next_nodes:
      - end

  - name: end
    display_name: "结束"
    type: end
