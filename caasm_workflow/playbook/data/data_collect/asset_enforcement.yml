name: asset_enforcement
description: "资产类数据执行默认后处理"
display_name: "资产类数据后处理"
params:
  - name: category
  - name: date
nodes:
  - name: start
    type: start
    display_name: "开始"
    next_nodes:
      - create_tmp_table_index


  - name: create_tmp_table_index
    type: action
    display_name: "创建临时表索引"
    entry: "caasm_manage.steps.enforcement:create_table_index_for_asset"
    params:
      - name: date
        value: "${__global__.date}"
    next_nodes:
      - enrich_owner
      - enrich_department
      - business_check
      - enrich_intranet_ips

  - name: enrich_owner
    type: action
    display_name: "富化责任人"
    entry: "caasm_manage.steps.enforcement:update_for_asset"
    params:
      - name: date
        value: "${__global__.date}"
      - name: param
        value: "enrich_owner"
    next_nodes:
      - create_query_template

  - name: business_check
    type: action
    display_name: "业务系统检查"
    entry: "caasm_manage.steps.enforcement:update_for_asset"
    params:
      - name: date
        value: "${__global__.date}"
      - name: param
        value: "business_check"
    next_nodes:
      - enrich_business

  - name: enrich_department
    type: action
    display_name: "富化部门"
    entry: "caasm_manage.steps.enforcement:update_for_asset"
    params:
      - name: date
        value: "${__global__.date}"
      - name: param
        value: "enrich_department"
    next_nodes:
      - clean_departments

  - name: clean_departments
    type: action
    display_name: "清洗部门"
    entry: "caasm_manage.steps.enforcement:update_for_asset"
    params:
      - name: date
        value: "${__global__.date}"
      - name: param
        value: "clean_departments"
    next_nodes:
      - create_query_template

  - name: enrich_intranet_ips
    type: action
    display_name: "富化内网ip"
    entry: "caasm_manage.steps.enforcement:update_for_asset"
    params:
      - name: date
        value: "${__global__.date}"
      - name: param
        value: "enrich_intranet_ips"
    next_nodes:
      - create_query_template

  - name: enrich_business
    type: action
    display_name: "富化业务系统"
    entry: "caasm_manage.steps.enforcement:update_for_asset"
    params:
      - name: date
        value: "${__global__.date}"
      - name: param
        value: "enrich_business"
    next_nodes:
      - create_query_template

  - name: create_query_template
    type: action
    display_name: "创建查询模版"
    entry: "caasm_manage.steps.enforcement:creat_query_template_for_asset"
    params:
      - name: date
        value: "${__global__.date}"
    next_nodes:
      - storage_to_query_engine

  - name: storage_to_query_engine
    type: action
    display_name: "数据存入ES"
    entry: "caasm_manage.steps.enforcement:update_for_asset"
    params:
      - name: date
        value: "${__global__.date}"
      - name: param
        value: "storage_to_query_engine"
    next_nodes:
      - detect_change_for_business
      - detect_change_for_asset

  - name: detect_change_for_business
    type: action
    display_name: "探测业务系统变化"
    entry: "caasm_manage.steps.enforcement:detect_change_fora_asset"
    params:
      - name: date
        value: "${__global__.date}"
      - name: change
        value: "business"
    next_nodes:
      - migrate_vul


  - name: detect_change_for_asset
    type: action
    display_name: "探测资产变化"
    entry: "caasm_manage.steps.enforcement:detect_change_fora_asset"
    params:
      - name: date
        value: "${__global__.date}"
      - name: change
        value: "asset"
    next_nodes:
      - migrate_vul

  - name: migrate_vul
    type: action
    display_name: "迁移漏洞"
    entry: "caasm_manage.steps.enforcement:update_for_asset"
    params:
      - name: date
        value: "${__global__.date}"
      - name: param
        value: "migrate_vul"
    next_nodes:
      - compute_asset_fetch_count
      - enrich_vul_unique

#  - name: migrate_vul_unique
#    type: action
#    display_name: "迁移唯一漏洞"
#    entry: "caasm_manage.steps.enforcement:update_for_asset"
#    params:
#      - name: date
#        value: "${__global__.date}"
#      - name: param
#        value: "migrate_vul_unique"
#    next_nodes:
#      - enrich_vul_unique

  - name: enrich_vul_unique
    type: action
    display_name: "富化唯一漏洞"
    entry: "caasm_manage.steps.enforcement:update_for_asset"
    params:
      - name: date
        value: "${__global__.date}"
      - name: param
        value: "migrate_vul_unique"
    next_nodes:
      - update_vul_priority

  - name: update_vul_priority
    type: action
    display_name: "更新漏洞优先级"
    entry: "caasm_manage.steps.enforcement:update_for_asset"
    params:
      - name: date
        value: "${__global__.date}"
      - name: param
        value: "update_vul_priority"
    next_nodes:
      - end

  - name: compute_asset_fetch_count
    type: action
    display_name: "计算资产采集数量"
    entry: "caasm_manage.steps.enforcement:update_for_asset"
    params:
      - name: date
        value: "${__global__.date}"
      - name: param
        value: "compute_asset_fetch_count"
    next_nodes:
      - end

  - name: end
    display_name: "结束"
    type: end
