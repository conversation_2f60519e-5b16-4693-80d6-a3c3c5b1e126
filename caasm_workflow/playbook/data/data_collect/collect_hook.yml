name: collect_hook
description: 数据治理钩子
display_name: "全局后处理"
params:
  - name: snapshot_record_id
  - name: date
nodes:
  - name: start
    type: start
    display_name: "开始"
    next_nodes:
      - enrich_by_ip_segment

  - name: enrich_by_ip_segment
    type: action
    display_name: "IP段富化资产"
    entry: "caasm_manage.steps.enforcement:update_for_asset"
    params:
      - name: date
        value: "${__global__.date}"
      - name: param
        value: "enrich_by_ip_segment"
    next_nodes:
      - storage_to_query_engine

  - name: storage_to_query_engine
    type: action
    display_name: "数据存入ES"
    entry: "caasm_manage.steps.enforcement:update_for_asset"
    params:
      - name: date
        value: "${__global__.date}"
      - name: param
        value: "storage_to_query_engine"
    next_nodes:
      - enrich_asset_ownership


  - name: enrich_asset_ownership
    type: action
    display_name: "更新权属"
    entry: "caasm_manage.steps.enforcement:update_for_asset"
    params:
      - name: date
        value: "${__global__.date}"
      - name: param
        value: "enrich_asset_ownership"
    next_nodes:
      - storage_to_query_engine_1

  - name: storage_to_query_engine_1
    type: action
    display_name: "数据存入ES_1"
    entry: "caasm_manage.steps.enforcement:update_for_asset"
    params:
      - name: date
        value: "${__global__.date}"
      - name: param
        value: "storage_to_query_engine"
    next_nodes:
      - remove_offline_terminals

  - name: remove_offline_terminals
    type: action
    entry: "caasm_manage.steps.enforcement:update_for_category"
    display_name: "移除离线终端"
    params:
      - name: category
        value: "global"
      - name: date
        value: "${__global__.date}"
      - name: param
        value: "remove_offline_terminals"
    next_nodes:
      - alarm_generation
      - update_business_portrait
      - migrate_network_mapping
      - update_chart_result

  - name: migrate_network_mapping
    type: action
    entry: "caasm_manage.steps.enforcement:update_for_category"
    display_name: "生成网络映射配置"
    params:
      - name: category
        value: "global"
      - name: date
        value: "${__global__.date}"
      - name: param
        value: "migrate_network_mapping"
    next_nodes:
      - build_link_graph

  - name: build_link_graph
    type: action
    entry: "caasm_manage.steps.enforcement:update_for_category"
    display_name: "制作网络链路"
    params:
      - name: category
        value: "global"
      - name: date
        value: "${__global__.date}"
      - name: param
        value: "build_link_graph"
    next_nodes:
      - migrate_exposed_surface

  - name: migrate_exposed_surface
    type: action
    entry: "caasm_manage.steps.enforcement:update_for_category"
    display_name: "迁移暴露面"
    params:
      - name: category
        value: "global"
      - name: date
        value: "${__global__.date}"
      - name: param
        value: "migrate_exposed_surface"
    next_nodes:
      - update_snapshot_record

  - name: alarm_generation
    type: action
    display_name: "生成告警"
    entry: "caasm_manage.steps.enforcement:update_for_asset"
    params:
      - name: date
        value: "${__global__.date}"
      - name: param
        value: "alarm_generation"
    next_nodes:
      - update_snapshot_record

  - name: update_business_portrait
    type: action
    display_name: "更新业务画像"
    entry: "caasm_manage.steps.enforcement:update_for_asset"
    params:
      - name: date
        value: "${__global__.date}"
      - name: param
        value: "update_business_portrait"
    next_nodes:
      - update_business_realms

  - name: update_business_realms
    type: action
    display_name: "更新业务画像所属安全域"
    entry: "caasm_manage.steps.enforcement:update_for_asset"
    params:
      - name: date
        value: "${__global__.date}"
      - name: param
        value: "update_business_realms"
    next_nodes:
      - update_business_level

  - name: update_business_level
    type: action
    display_name: "更新业务系统等级"
    entry: "caasm_manage.steps.enforcement:update_for_asset"
    params:
      - name: date
        value: "${__global__.date}"
      - name: param
        value: "update_business_level"
    next_nodes:
      - update_snapshot_record

  - name: update_chart_result
    type: action
    display_name: "更新图表结果"
    entry: "caasm_manage.steps.enforcement:update_for_asset"
    params:
      - name: date
        value: "${__global__.date}"
      - name: param
        value: "update_chart_result"
    next_nodes:
      - update_snapshot_record


  - name: update_snapshot_record
    type: action
    entry: "caasm_service.runtime:snapshot_record_service.finish_snapshot"
    display_name: "更新快照状态"
    params:
      - name: record_id
        value: "${__global__.snapshot_record_id}"
      - name: status
        value: "success"
    next_nodes:
      - end
  - name: end
    type: end