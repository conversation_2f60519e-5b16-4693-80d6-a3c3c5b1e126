name: adapter_fetch
display_name: "适配器【entry:caasm_workflow.action.tool.translate:translate_adapter:entry{adapter_display_name}】数据采集"
description: 适配器信息采集
params:
  - name: category
  - name: adapter_name

nodes:
  - name: start
    type: start
    display_name: 开始
    next_nodes:
      - find_adapter_instance

  - name: find_adapter_instance
    type: action
    entry: "caasm_service.runtime:adapter_instance_service.find_adapter_instance"
    display_name: "查询适配器【entry:caasm_workflow.action.tool.translate:translate_adapter:entry{adapter_display_name}】实例列表"
    params:
      - name: enabled
        value: true
      - name: adapter_name
        value: "${__global__.adapter_name}"
      - name: dict_resp
        value: true
    output:
      - name: _id
    next_nodes:
      - adapter_instance_fetch

  - name: adapter_instance_fetch
    display_name: "适配器【entry:caasm_workflow.action.tool.translate:translate_adapter:entry{adapter_display_name}采集数据发起"
    type: playbook
    params:
      - name: adapter_instance_id
        value: "${find_adapter_instance._id}"
      - name: adapter_name
        value: "${__global__.adapter_name}"
      - name: category
        value: "${__global__.category}"
      - name: system_call
        value: true
    next_nodes:
      - end

  - name: end
    display_name: "结束"
    type: end
