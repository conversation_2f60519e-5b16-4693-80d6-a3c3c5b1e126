name: enforcement
description: 数据后处理
display_name: "后处理"
params:
  - name: category
  - name: date
nodes:
  - name: start
    type: start
    display_name: "开始"
    next_nodes:
      - switch_enforcement

  - name: switch_enforcement
    type: action
    display_name: "后处理剧本分发"
    entry: "caasm_workflow.action.tool.playbook_switch:playbook_switch"
    params:
      - name: playbook_mapper
        value:
          asset: asset_enforcement
          flaw: flaw_enforcement
      - name: key
        value: "${__global__.category}"
      - name: default_playbook
        value: default_enforcement

      - name: category
        value: "${__global__.category}"
      - name: date
        value: "${__global__.date}"
    next_nodes:
      - end

  - name: end
    display_name: "结束"
    type: end