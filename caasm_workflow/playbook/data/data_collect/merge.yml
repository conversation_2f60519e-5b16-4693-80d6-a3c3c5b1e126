name: adapter_merge
description: 适配器实例数据合并
display_name: "适配器【entry:caasm_workflow.action.tool.translate:translate_adapter:entry{adapter_display_name}】数据合并"
params:
  - name: category
  - name: adapter_name
nodes:
  - name: start
    display_name: "开始"
    type: start
    next_nodes:
      - merge

  - name: merge
    type: action
    display_name: "执行适配器【entry:caasm_workflow.action.tool.translate:translate_adapter:entry{adapter_display_name}】数据合并"
    entry: "caasm_manage.steps.merge:default_merge"
    params:
      - name: adapter_name
        value: "${__global__.adapter_name}"
      - name: fetch_type
        value: "${__global__.category}"
    next_nodes:
      - end

  - name: end
    display_name: "结束"
    type: end
