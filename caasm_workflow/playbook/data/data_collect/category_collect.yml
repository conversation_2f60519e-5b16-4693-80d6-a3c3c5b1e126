name: category_collect
description: 数据治理（分类）
display_name: 'entry:caasm_workflow.action.tool.translate:translate_category:entry{category_display_name}'
callback_entry: "caasm_workflow.notify.entry.unlock:unlock"
params:
  - name: category
  - name: date
nodes:
  - name: start
    type: start
    display_name: 开始
    next_nodes:
      - show_info

  - name: show_info
    display_name: 'entry:caasm_workflow.action.tool.translate:translate_category:entry{category_display_name}'
    entry: "caasm_workflow.action.tool.translate:translate_category"
    type: action
    params:
      - name: category
        value: "${__global__.category}"
    next_nodes:
      - collect_lock

  - name: collect_lock
    type: playbook
    display_name: 加锁
    params:
      - name: category
        value: "${__global__.category}"
    next_nodes:
      - find_adapter

  - name: find_adapter
    type: action
    entry: "caasm_service.runtime:adapter_service.find_adapter"
    display_name: "获取适配器列表"
    params:
      - name: dict_resp
        value: true
      - name: adapter_instance_exists
        value: true
      - name: category
        value: ${__global__.category}
    output:
      - name: name
    next_nodes:
      - adapter_fetch

  - name: adapter_fetch
    type: playbook
    display_name: "适配器数据采集发起"
    params:
      - name: category
        value: "${__global__.category}"
      - name: adapter_name
        value: "${find_adapter.name}"
    next_nodes:
      - adapter_merge

  - name: adapter_merge
    type: playbook
    display_name: "适配器数据合并发起"
    params:
      - name: category
        value: "${__global__.category}"
      - name: adapter_name
        value: "${find_adapter.name}"
    next_nodes:
      - adapter_convert

  - name: adapter_convert
    display_name: "适配器数据转换发起"
    type: playbook
    params:
      - name: category
        value: "${__global__.category}"
      - name: adapter_name
        value: "${find_adapter.name}"
    next_nodes:
      - fabric

  - name: fabric
    display_name: "数据融合发起"
    type: playbook
    params:
      - name: category
        value: "${__global__.category}"
      - name: date
        value: "${__global__.date}"
    next_nodes:
      - enforcement

  - name: enforcement
    display_name: "后处理发起"
    type: playbook
    params:
      - name: category
        value: "${__global__.category}"
      - name: date
        value: "${__global__.date}"
    next_nodes:
      - collect_release

  - name: collect_release
    type: playbook
    display_name: "解锁"
    params:
      - name: category
        value: "${__global__.category}"
    next_nodes:
      - end

  - name: end
    display_name: "结束"
    type: end
