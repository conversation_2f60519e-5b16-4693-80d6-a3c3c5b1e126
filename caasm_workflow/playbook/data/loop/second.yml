name: second
description: ""
params:
  - name: playbook_name
nodes:
  - name: start
    type: start
    next_nodes:
      - switch_playbook

  - name: switch_playbook
    display_name: "剧本分发"
    entry: "caasm_workflow.action.tool.playbook_switch:playbook_switch"
    type: action
    params:
      - name: playbook_mapper
        value:
          third: third
          four: four
      - name: key
        value: "${__global__.playbook_name}"
      - name: default_playbook
        value: four
    next_nodes:
      - end


  - name: end
    type: end
