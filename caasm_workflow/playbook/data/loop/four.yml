name: four
description: ""
nodes:
  - name: start
    type: start
    next_nodes:
      - five
      - sleep
      - show_info

  - name: five
    display_name: "五"
    type: playbook
    next_nodes:
      - end

  - name: show_info
    display_name: 'entry:caasm_workflow.action.tool.translate:translate_category:entry{category_display_name}'
    entry: "caasm_workflow.action.tool.translate:translate_category"
    type: action
    params:
      - name: category
        value: "asset"
    next_nodes:
      - end

  - name: sleep
    display_name: "四休眠"
    type: action
    entry: "caasm_tool.util:sleep"
    params:
      - name: seconds
        value: 1
    next_nodes:
      - end

  - name: end
    type: end
