name: demo_output
description: 测试父剧本
nodes:
  - name: start
    type: start
    next_nodes:
      - find_adapter

  - name: find_adapter
    type: action
    entry: "caasm_service.runtime:adapter_service.find_adapter"
    params:
      - name: dict_resp
        value: true
      - name: names
        value:
          - qingteng_wanxiang
          - ns_rsas
    next_nodes:
      - get_adapter
    output:
      - name: _id
      - name: name
      - name: display_name

  - name: get_adapter
    type: action
    entry: "caasm_service.runtime:adapter_service.get_adapter"
    params:
      - name: dict_resp
        value: true
      - name: name
        value: canway_cmdb
    next_nodes:
      - end
    output:
      - name: _id
      - name: name
      - name: display_name

  - name: end
    type: end

output:
  - name: adapter_name
    value: "${find_adapter.name}"
  - name: test
    value: "测试测试"
  - name: category
    value: [ 1, 2 ]
  - name: xx_adapter
    value: "${get_adapter.name}"

