import json
import logging

import yaml

from caasm_config.config import caasm_config
from caasm_service.constants.workflow import NodeType
from caasm_service.runtime import workflow_playbook_service
from caasm_tool.util import compute_md5

log = logging.getLogger()


class Initializer(object):
    _PLAYBOOK_FIELDS = ["sign", "name"]

    def __init__(self, path, customized_path):
        self._path = path
        self._customized_path = customized_path
        self._error_hits = []
        self._playbook_by_name = {}
        self._invalid_playbooks = []

        self._node_check_method = {NodeType.ACTION: self._check_action_node}

    def execute(self):
        self._clear_playbook()
        self._load_playbook()
        self._check_playbook_circle()
        self._save_playbook()
        self._console_error_playbook()

    @classmethod
    def _clear_playbook(cls):
        workflow_playbook_service.delete_multi({})

    def _load_playbook(self):
        self._load_playbook_common(self._path)
        self._load_playbook_common(self._customized_path)

    def _load_playbook_common(self, path):
        if not path.exists():
            return

        if path.is_dir():
            for sub_path in path.iterdir():
                self._load_playbook_common(sub_path)
            return
        playbook = self._build_playbook(path)
        if not playbook:
            return
        self._playbook_by_name[playbook.name] = playbook

    def _check_playbook_basic(self, playbook):
        return self._check_node(playbook)

    def _check_node(self, playbook):
        start_exist_flag = end_exist_flag = None
        for node_name, node in playbook.node_mapper.items():
            if node.type not in NodeType._value2member_map_:
                self._error_join(f"剧本({playbook.name})中的节点({node.name})未定义类型", playbook.name)
                return
            node_check_method = self._node_check_method.get(node.type)
            node_check_res = node_check_method(node) if node_check_method else None
            if node_check_res:
                self._error_join(f"剧本({playbook.name})中的节点({node.name})校验失败:{node_check_res}", playbook.name)
                return

            if node.type == NodeType.START:
                start_exist_flag = True
            elif node.type == NodeType.END:
                end_exist_flag = True

        if not start_exist_flag:
            self._error_join(f"剧本({playbook.name})没有开始节点", playbook)
            return

        if not end_exist_flag:
            self._error_join(f"剧本({playbook.name})没有结束节点", playbook)
            return

        for node_name, node in playbook.node_mapper.items():
            if node.type == NodeType.END:
                continue
            if not self._check_end_reachable(node, playbook.node_mapper):
                self._error_join(f"剧本({playbook.name})中的节点({node.name})无法到达结束节点", playbook.name)
                return

        return True

    def _check_playbook_circle(self):
        for _, playbook in self._playbook_by_name.items():
            self._check_playbook_circle_common(playbook)

    def _check_playbook_circle_common(self, playbook, flag=None, title=""):

        if flag is None:
            flag = set()

        if not playbook.node_mapper:
            return

        for node_name, node in playbook.node_mapper.items():
            key = f"{title}{playbook.name}-{node_name}"
            if key in flag:
                self._error_join(f"剧本({playbook.name})中的节点({node_name})存在环", playbook.name)
                continue

            for next_node in node.next_nodes:
                if next_node not in playbook.node_mapper:
                    self._error_join(f"剧本({playbook.name})中的节点({node_name})存在无效的节点({next_node})引用", playbook.name)
                    continue

            flag.add(key)
            if node.type == NodeType.PLAYBOOK:
                sub_playbook = self._playbook_by_name.get(node_name)
                if not sub_playbook:
                    self._error_join(f"剧本({playbook.name})中的节点({node_name})引用了不存在的剧本", playbook.name)
                    continue
                self._check_playbook_circle_common(sub_playbook, title=playbook.name)

    def _console_error_playbook(self):
        for error_hit in self._error_hits:
            print(error_hit)

    def _save_playbook(self):
        valid_playbooks = [
            playbook for name, playbook in self._playbook_by_name.items() if name not in self._invalid_playbooks
        ]
        workflow_playbook_service.save_multi(valid_playbooks) if valid_playbooks else ...

    @classmethod
    def _check_sign_is_change(cls, content, playbook):
        return content["sign"] == playbook.sign

    @classmethod
    def _get_playbook(cls, content):
        name = content.get("name")
        return workflow_playbook_service.get_playbook(name=name, fields=cls._PLAYBOOK_FIELDS)

    def _build_playbook(self, path):
        content = {}
        try:
            with open(path, "rb") as fd:
                content = yaml.safe_load(fd)
        except Exception as e:
            log.error(f"read playbook(path:({path})) error({e}) ")
        else:
            nodes = content.pop("nodes")
            node_mapper = {}
            for node in nodes:
                self._convert_param(node, "params", "param_template")
                self._convert_param(node, "output", "output_template")
                node_mapper[node["name"]] = node

            self._convert_param(content, "params", "param_template")
            self._convert_param(content, "output", "output_template")

            content["node_mapper"] = node_mapper
            content["enabled"] = content.get("enabled", True)
            content["sign"] = compute_md5(json.dumps(content))
        playbook = workflow_playbook_service.load_entity(**content)

        return playbook if self._check_playbook_basic(playbook) else None

    def _check_end_reachable(self, node, node_mapper):
        if node.type == NodeType.END:
            return True
        if not node.next_nodes:
            return False

        for next_node_name in node.next_nodes:
            if next_node_name not in node_mapper:
                return False
            next_node = node_mapper[next_node_name]
            if not self._check_end_reachable(next_node, node_mapper):
                return False
        return True

    @classmethod
    def _check_action_node(cls, node):
        if not node.entry:
            return f"动作节点缺少入口定义"

    def _error_join(self, error_hit, playbook):
        self._error_hits.append(error_hit + "【导入失败】")
        self._invalid_playbooks.append(playbook)

    @classmethod
    def _convert_param(cls, data, src_key, dst_key):
        params = data.pop(src_key, None) or []
        result = {param["name"]: param for param in params}
        data[dst_key] = result

    @property
    def path(self):
        return self._path


def load_entry():
    system_path = caasm_config.ROOT_DIR / "caasm_workflow" / "playbook" / "data"
    custom_path = caasm_config.CUSTOMER_PATH / "workflow" / "playbook"
    Initializer(system_path, custom_path).execute()


if __name__ == "__main__":
    load_entry()
