[group:caasm_soar]
programs = monitor, task, notify


[program:monitor]
command = /usr/bin/python3 /opt/caasm/caasm_workflow/entry/monitor.py
autostart = true
autorestart = true
killasgroup = true
stopasgroup = true
stdout_logfile = /data/log/supervisor/caasm_soar_monitor_stdout.log
stderr_logfile = /data/log/supervisor/caasm_soar_monitor_stderr.log


[program:task]
command = /usr/bin/python3 /opt/caasm/caasm_workflow/entry/task.py
autostart = true
autorestart = true
killasgroup = true
stopasgroup = true
stdout_logfile = /data/log/supervisor/caasm_soar_task_stdout.log
stderr_logfile = /data/log/supervisor/caasm_soar_task_stderr.log

[program:notify]
command = /usr/bin/python3 /opt/caasm/caasm_workflow/entry/notify.py
autostart = true
autorestart = true
killasgroup = true
stopasgroup = true
stdout_logfile = /data/log/supervisor/caasm_soar_notify_stdout.log
stderr_logfile = /data/log/supervisor/caasm_soar_notify_stderr.log