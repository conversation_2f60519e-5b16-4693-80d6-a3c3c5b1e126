[group:caasm_grpc_server]
programs = meta_data,merge

[program:meta_data]
command = /opt/caasm/caasm_data/service/meta_data/meta_data
autostart = true
autorestart = true
stdout_logfile = /data/log/supervisor/caasm_meta_data_web_stdout.log
stderr_logfile = /data/log/supervisor/caasm_meta_data_web_stderr.log
directory = /opt/caasm/caasm_data/service/meta_data


[program:merge]
command = /opt/caasm/caasm_data/service/caasm_merge/caasm_merge
autostart = true
autorestart = true
stdout_logfile = /data/log/supervisor/caasm_merge_stdout.log
stderr_logfile = /data/log/supervisor/caasm_merge_stderr.log
directory = /opt/caasm/caasm_data/service/caasm_merge