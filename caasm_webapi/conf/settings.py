"""
Django settings for djangoProject project.

Generated by 'django-admin startproject' using Django 3.2.5.

For more information on this file, see
https://docs.djangoproject.com/en/3.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/3.2/ref/settings/
"""

import redis

from caasm_config.config import caasm_config
from caasm_tool import log

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = caasm_config.ROOT_DIR / "caasm_webapi"
DATA_DIR = BASE_DIR / "data"

caasm_config.install()
log.install(caasm_config.API_LOG)

REDIS_CONN = redis.Redis(connection_pool=redis.ConnectionPool(decode_responses=True, **caasm_config.REDIS))
# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/3.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = "django-insecure-a(suz^g6-espi91o=b97p&izv(p3=r@vyxyyq!oj%8dexo#o6*"

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = caasm_config.API_DEBUG

ALLOWED_HOSTS = caasm_config.API_ALLOW_HOST

# Application definition

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "django_extensions",
    "rest_framework",
    "corsheaders",
]

MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "caasm_webapi.middleware.auth.AuthMiddleware",
    "caasm_webapi.middleware.app_call_times.AppCallTimesMiddleware",
    "caasm_webapi.middleware.log.LogMiddleware",
    "caasm_webapi.middleware.permission.PermissionMiddleware",
    "caasm_webapi.middleware.asql_query_times.AsqlCallTimesMiddleware",
    "caasm_webapi.middleware.whitelist.WhitelistMiddleware",
]

ROOT_URLCONF = "caasm_webapi.conf.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [BASE_DIR / "templates"],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "caasm_webapi.conf.wsgi.application"

# Database
# https://docs.djangoproject.com/en/3.2/ref/settings/#databases

DATABASES = {}
# Password validation
# https://docs.djangoproject.com/en/3.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]

# Internationalization
# https://docs.djangoproject.com/en/3.2/topics/i18n/

LANGUAGE_CODE = "zh-hans"

TIME_ZONE = "Asia/Shanghai"

USE_I18N = True

USE_L10N = True

USE_TZ = True

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/3.2/howto/static-files/

STATIC_URL = "/static/"

# Default primary key field type
# https://docs.djangoproject.com/en/3.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

REST_FRAMEWORK = {
    #   为了满足两种语言编码规范，将python的蛇形命名转换为js的驼峰命名
    "DEFAULT_RENDERER_CLASSES": (
        # 'rest_framework.renderers.BrowsableAPIRenderer',
        "caasm_webapi.util.renderers.MyCamelCaseJSONRenderer",
    ),
    #   为了满足两种语言编码规范，将js的驼峰命名转换为python的蛇形命名
    "DEFAULT_PARSER_CLASSES": (
        "djangorestframework_camel_case.parser.CamelCaseFormParser",
        "djangorestframework_camel_case.parser.CamelCaseMultiPartParser",
        "caasm_webapi.common.parser.CustomParser",
    ),
    #   因为需要和元数据对应，因此资产库数据字段不进行驼峰化；另外元数据字段名称不影响前端编码，因此不存在js编码规范问题
    "JSON_UNDERSCOREIZE": {
        "ignore_fields": [
            "inventories",
            "entities",
            "demo",
            "raw",
            "adapter_instance_connection",
        ]
    },
}

CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": f"redis://{caasm_config.REDIS.get('host', '127.0.0.1')}:{caasm_config.REDIS.get('port', 6379)}/{caasm_config.REDIS.get('db', 0)}",
        "KEY_PREFIX": "CAASM_WEB_API:",
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            "PASSWORD": caasm_config.REDIS.get("password"),
        },
    }
}

CORS_ALLOW_CREDENTIALS = True
CORS_ORIGIN_ALLOW_ALL = True
CORS_ALLOW_HEADERS = ("*",)

#   上传文件设置
FILE_UPLOAD_HANDLERS = ["django.core.files.uploadhandler.MemoryFileUploadHandler"]
FILE_UPLOAD_MAX_MEMORY_SIZE = 20 * 1024 * 1024
DATA_UPLOAD_MAX_NUMBER_FILES = 1
