from caasm_config.config import caasm_config
from caasm_webapi.util.token import token_service


class BaseAuth(object):
    token_detail_define = None

    def logout(self, token):
        token_service.remove_token(token)

    def login(self, settings):
        pass

    def refresh(self, token):
        token_service.refresh_token(token, caasm_config.AUTH_COOKIE_EXPIRE)

    def get_token_detail(self, token):
        return {}

    def build_token_detail(self, token):
        token_detail = self.get_token_detail(token)
        return self.token_detail_define(token_detail)

    def check_token(self, token):
        return token_service.check_token_invalid(token)

    @classmethod
    def make_response(cls, auth_flag=False, token=None, err_msg=None):
        return auth_flag, token, err_msg
