import datetime
import hashlib
import json
import uuid
from typing import Set

from caasm_auth_manage.sso_config.base import SSOConfigBaseHandle
from caasm_config.config import caasm_config
from caasm_service.constants.auth import AuthenticationType
from caasm_service.constants.setting import SettingName
from caasm_service.runtime import setting_service, user_service
from caasm_tool.util import rsa_decrypt
from caasm_webapi.auth.base import BaseAuth
from caasm_webapi.auth.model import UserToken
from caasm_webapi.conf.settings import REDIS_CONN
from caasm_webapi.util.token import captcha_service


class UsernameAuth(BaseAuth):
    token_detail_define = UserToken

    def login(self, settings):
        username = settings.get("username", "")
        enc_password = settings.get("password", "")

        # 解密
        private_content = setting_service.get_setting(SettingName.PRIVATE_KEY).value
        dec_password = rsa_decrypt(private_content, enc_password)
        if not dec_password:
            return self.make_response()

        # 通过用户名获取用户数据
        user = user_service.get_user(username=username)
        if not user:
            return self.make_response()
        # 验证用户是否锁定
        locked, gap = self.lock_status(user)
        if locked:
            return self.make_response(err_msg=f"账号已锁定,请{gap}分钟之后再尝试")

        req_dec_password = user_service.get_enc_password(dec_password)
        if user.password != req_dec_password:
            # 增加登录错误的次数
            self.add_try_login_count(user=user)
            return self.make_response(err_msg="密码错误")
        user_id = user.id
        self.reset_try_login_count(user=user)
        token = self.generate_user_token(user)
        self.append_user_token(user_id, token)
        self.record_user_info(token, user, caasm_config.AUTH_COOKIE_EXPIRE)
        return self.make_response(True, token, err_msg=None)

    def logout(self, token):
        super(UsernameAuth, self).logout(token)
        self.pop_user_token(token)

    def refresh(self, token):
        super(UsernameAuth, self).refresh(token)
        self.refresh_user_info_expire(token, caasm_config.AUTH_COOKIE_EXPIRE)

    @classmethod
    def get_token_detail(cls, token):
        user_id_token = cls.build_user_id_token(token)
        user_id = REDIS_CONN.get(user_id_token)
        if not user_id:
            return {}
        user_str = REDIS_CONN.get(cls.build_user_detail_token(user_id))
        if not user_str:
            return {}
        return json.loads(user_str)

    @classmethod
    def refresh_user_info_expire(cls, token, expire):
        user_id_token_name = cls.build_user_id_token(token)
        user_id = REDIS_CONN.get(user_id_token_name)
        if not user_id:
            return

        user_detail_token_name = cls.build_user_detail_token(user_id)

        REDIS_CONN.expire(user_id_token_name, expire)
        REDIS_CONN.expire(user_detail_token_name, expire)

    @classmethod
    def generate_user_token(cls, user_id):
        return hashlib.md5(f"{user_id}-{uuid.uuid4()}".encode()).hexdigest()

    @classmethod
    def append_user_token(cls, user_id, token):
        user_token_set_name = cls.build_user_token_set_name(user_id)
        REDIS_CONN.sadd(user_token_set_name, token)

    @classmethod
    def record_user_info(cls, token, user, expire):
        user_id_token_name = cls.build_user_id_token(token)
        REDIS_CONN.set(user_id_token_name, str(user.id), ex=expire)
        cls.refresh_user_info(user, expire)

    @classmethod
    def refresh_user_info(cls, user, expire=None):
        user_str = json.dumps(user.as_dict(), default=str)
        user_detail_token_name = cls.build_user_detail_token(user.id)
        REDIS_CONN.set(user_detail_token_name, user_str, ex=expire)

    @classmethod
    def pop_user_token(cls, token):
        user_id_token = cls.build_user_id_token(token)
        user_id = REDIS_CONN.get(user_id_token)
        if not user_id:
            return
        user_token_set_name = cls.build_user_token_set_name(user_id)
        REDIS_CONN.srem(user_token_set_name, token)
        REDIS_CONN.delete(user_id_token)

    @classmethod
    def flush_user_info(cls, user_id):
        user_token_set_name = cls.build_user_token_set_name(user_id)
        tokens = REDIS_CONN.smembers(user_token_set_name)
        for token in tokens:
            user_id_token = cls.build_user_id_token(token)
            user_detail_token = cls.build_user_detail_token(token)
            REDIS_CONN.delete(user_id_token)
            REDIS_CONN.delete(user_detail_token)
        REDIS_CONN.delete(user_token_set_name)

    @classmethod
    def add_try_login_count(cls, user):
        """超过最大次数,锁定账号时间"""
        _, _, max_try, expired = cls.get_auth_config()
        user.try_login_count += 1
        if user.lock_expired and user.lock_expired <= datetime.datetime.now() - datetime.timedelta(minutes=expired):
            user.lock_expired = datetime.datetime.now()
            user.try_login_count = 1
        if user.try_login_count >= max_try:
            user.lock_expired = datetime.datetime.now() + datetime.timedelta(minutes=expired)
        user_service.update_user(user=user)

    @classmethod
    def get_auth_config(cls) -> Set:
        auth_initial = SSOConfigBaseHandle(authentication_type=AuthenticationType.DEFAULT.value)
        return auth_initial.get_auth_config()

    @classmethod
    def reset_try_login_count(cls, user):
        user.try_login_count = 0
        user.lock_expired = datetime.datetime.now()
        user_service.update_user(user=user)

    @classmethod
    def lock_status(cls, user) -> Set:
        """
        返回用户是否在锁定状态
        """
        if user.lock_expired is None:
            return False, 0
        current_time = datetime.datetime.now()
        if user.lock_expired > current_time:
            return True, int((user.lock_expired - current_time + datetime.timedelta(minutes=1)).total_seconds() / 60)
        return False, 0

    @classmethod
    def remove_captcha(cls, addr: str):
        try:
            captcha_service.remove_captcha(addr)
        except Exception:
            pass

    @classmethod
    def build_user_token_set_name(cls, user_id):
        return f"AuthUserTokens-{user_id}"

    @classmethod
    def build_user_id_token(cls, token):
        return f"AuthUserId-{token}"

    @classmethod
    def build_user_detail_token(cls, user_id):
        return f"AuthUserDetail-{user_id}"
