from caasm_tool.patch.router import DefaultRouter
from caasm_webapi.app.exposed_surface.views import ExposedSurfaceCoveragesAPIView
from caasm_webapi.app.vul.views import (
    ListVulFilesAPIView,
    CreateVulFileAPIView,
    DownloadVulFileAPIView,
    DeleteVulFileAPIView,
)

exposed_surface_router = DefaultRouter("exposedSurface/")

exposed_surface_router.join_path(
    "<category>/coverages/", ExposedSurfaceCoveragesAPIView, name="ExposedSurfaceCoveragesAPIView"
)
