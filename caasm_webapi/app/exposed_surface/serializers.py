from rest_framework import fields
from rest_framework.serializers import Serializer

from caasm_tool.patch.serializer import SerializerMixin
from caasm_webapi.app.exposed_surface.request import ExposedSurfaceCoverageRequest


class ExposedSurfaceCoverageRequestSerializer(SerializerMixin):
    page_size = fields.IntegerField()
    page_index = fields.IntegerField()
    entity_id = fields.CharField()
    field = fields.CharField()
    field_category = fields.CharField()
    asset_type = fields.CharField()

    def create(self, validated_data):
        req = ExposedSurfaceCoverageRequest()
        req.entity_id = validated_data["entity_id"]
        req.field = validated_data["field"]
        req.field_category = validated_data["field_category"]
        req.asset_type = validated_data["asset_type"]
        req.page_size = validated_data["page_size"]
        req.page_index = validated_data["page_index"]
        return req


class ExposedSurfaceCoverageHeaderSerializer(Serializer):
    name = fields.Char<PERSON><PERSON>()
    title = fields.Char<PERSON><PERSON>()
    date_type = fields.Char<PERSON>ield()

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        pass


class ExposedSurfaceCoverageSerializer(Serializer):
    headers = fields.ListField(child=ExposedSurfaceCoverageHeaderSerializer())
    rows = fields.ListField(child=fields.ListField(child=fields.DictField()))

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        pass
