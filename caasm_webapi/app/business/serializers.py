import datetime
from typing import Optional

from rest_framework import fields, serializers

from caasm_businesses.enums import (
    Magnitude,
    GRADE_PROTECTION_LEVEL_MAPPING,
    GradeProtectionLevel,
    MAGNITUDE_MAPPING,
    CRITICAL_INFRASTRUCTURE_MAPPER,
    IS_INTERNET_ACCESSIBLE_MAPPING,
    STATUS_MAPPING,
)
from caasm_meta_data.constants import Category
from caasm_service.runtime import adapter_service, entity_service, snapshot_record_service
from caasm_tool.constants import DATE_FORMAT_1
from caasm_tool.patch.serializer import SerializerMixin
from caasm_tool.util import extract
from caasm_webapi.app.business.requests import BusinessListRequest
from caasm_webapi.common.serializers import ResponseSerializer, AsqlViewSerializer


class SnapshotDateSerializer(SerializerMixin):
    date = fields.CharField(required=False, default=lambda: snapshot_record_service.get_latest_useful_date())

    def validate_date(self, date):
        try:
            if date == None:
                date = datetime.datetime.now().strftime(DATE_FORMAT_1)
            result = datetime.datetime.strptime(date, DATE_FORMAT_1)
        except Exception as e:
            raise serializers.ValidationError("date参数格式无效")
        else:
            return result.strftime(DATE_FORMAT_1)


class BusinessQueryRequestSerializer(SnapshotDateSerializer):
    page_index = fields.IntegerField(default=1, min_value=1, required=False, help_text="分页索引，从1开始")
    page_size = fields.IntegerField(default=9, min_value=1, max_value=100, required=False, help_text="分页大小")
    keyword = fields.CharField(required=False, help_text="搜索名称关键字")
    grade_protection_level = fields.ChoiceField(
        required=False,
        allow_null=True,
        choices=[item.value for item in GradeProtectionLevel],
        help_text="等保等级",
    )
    magnitude = fields.ChoiceField(
        required=False,
        allow_null=True,
        choices=[item.value for item in Magnitude],
        help_text="核心系统等级",
    )
    critical_infrastructure = fields.BooleanField(required=False, allow_null=True, help_text="是否关基系统")
    owner_keyword = fields.CharField(required=False, help_text="责任人姓名关键字")
    realm = fields.CharField(required=False)
    data_center = fields.CharField(required=False, help_text="数据中心名称，多个数据中心用逗号分隔", allow_null=True)

    def create(self, validated_data):
        request = BusinessListRequest()
        request.page_index = validated_data["page_index"]
        request.page_size = validated_data["page_size"]
        request.keyword = validated_data.get("keyword")
        request.date = validated_data["date"]
        grade_protection_level: str = validated_data.get("grade_protection_level")
        if grade_protection_level:
            request.grade_protection_level = GradeProtectionLevel(grade_protection_level)
        magnitude = validated_data.get("magnitude")
        if magnitude is not None:
            request.magnitude = Magnitude(magnitude)
        critical_infrastructure: Optional[bool] = validated_data.get("critical_infrastructure")
        if critical_infrastructure is not None:
            request.critical_infrastructure = critical_infrastructure
        request.owner_keyword = validated_data.get("owner_keyword")
        realm = validated_data.get("realm")
        if realm:
            realm_segments = realm.split("_")
            request.data_center = realm_segments[0]
            if len(realm_segments) > 1:
                request.realm = realm_segments[1]

        # 处理data_centers字段
        data_center = validated_data.get("data_center")
        if data_center:
            request.data_center = data_center
        return request


class AdapterSerializer(SerializerMixin):
    name = fields.SerializerMethodField(help_text="适配器英文名称")
    display_name = fields.SerializerMethodField(help_text="适配器中文名称")

    def get_name(self, obj):
        return obj

    def get_display_name(self, obj):
        adapter = adapter_service.get_adapter(obj, fields=["display_name"])
        if adapter:
            display_name = adapter.display_name
        else:
            display_name = obj
        return display_name


class BusinessPortraitBaseSerializer(SerializerMixin):
    id = fields.SerializerMethodField(help_text="业务画像主键")
    name = fields.CharField(help_text="业务系统名称")
    update_time = fields.DateTimeField(help_text="更新时间", format="%Y年%m月%d日 %H:%M:%S")

    def get_id(self, obj):
        if isinstance(obj, dict):
            return obj["id"]
        else:
            return str(obj.id)

    def get_name(self, obj):
        name = ""
        if isinstance(obj, dict):
            name = obj["business"]["name"]
        else:
            business = entity_service.get_entity(
                Category.BUSINESS, "base.entity_id", obj.business_id, date=self.context.get("date")
            )
            if business:
                name = business["business"]["full_name"]
        return name


class BusinessPortraitItemSerializer(SerializerMixin):
    name = fields.CharField(help_text="名称")
    display_name = fields.CharField(help_text="显示名称")
    value = fields.CharField(help_text="显示值")


class BusinessPortraitAssetItemSerializer(SerializerMixin):
    name = fields.CharField(help_text="名称")
    display_name = fields.CharField(help_text="显示名称")
    value = fields.CharField(help_text="显示值")
    percent = fields.IntegerField(help_text="百分比")


class BusinessPortraitAssetsSerializer(BusinessPortraitItemSerializer):
    items = BusinessPortraitAssetItemSerializer(many=True, help_text="资产列表")


class BusinessPortraitSummarySerializer(BusinessPortraitBaseSerializer):
    items = BusinessPortraitItemSerializer(many=True, help_text="显示项列表")
    vul_count = BusinessPortraitItemSerializer(many=True, help_text="显示项列表")
    assets = BusinessPortraitAssetsSerializer(help_text="资产列表")
    coverage_charts = fields.JSONField(help_text="统计图表集合")


class BusinessPortraitSummariesSerializer(SerializerMixin):
    portraits = BusinessPortraitSummarySerializer(many=True, help_text="业务系统信息列表")
    update_time = fields.DateTimeField(help_text="更新时间", format="%Y年%m月%d日 %H:%M:%S")
    count = fields.IntegerField(help_text="总数")


class BusinessPortraitSummaryResponseSerializer(ResponseSerializer):
    data = BusinessPortraitSummariesSerializer


class BusinessPortraitNetworkSerializer(SerializerMixin):
    ips = fields.ListField(child=fields.CharField(), help_text="IP地址列表")
    ports = fields.ListField(child=fields.IntegerField(), help_text="端口列表")


class OwnerMinix(object):
    @classmethod
    def parse_owner_show_single_info(cls, owner_mapper, owner_names, field):
        return ",".join(
            map(str, [extract(owner_mapper[name], field) for name in owner_names if extract(owner_mapper[name], field)])
        )

    @classmethod
    def parse_owner_show_multi_info(cls, owner_mapper, owner_names, field):
        result = []

        for name in owner_names:
            data = extract(owner_mapper[name], field)
            if not data:
                continue
            result.extend(data)
        return ",".join(map(str, result))


class OwnersSerializer(SerializerMixin, OwnerMinix):
    owner = fields.SerializerMethodField(help_text="负责人")
    operator = fields.SerializerMethodField(help_text="运维人")
    admin_accounts = fields.ListField(child=fields.CharField(), help_text="账户")

    def get_owner(self, obj):
        return self.parse_owner_show_single_info(self.context["owner_mapper"], obj["owner_names"], "owner.nickname")

    def get_operator(self, obj):
        field = "owner.nickname"
        return self.parse_owner_show_single_info(self.context["operator_mapper"], obj["operator_names"], field)


class BusinessPortraitSerializer(BusinessPortraitBaseSerializer, OwnerMinix):
    _entity_id_field = "base.entity_id"

    domain = fields.SerializerMethodField(help_text="对应的域名列表")
    critical_infrastructure = fields.SerializerMethodField(help_text="是否为关基系统")
    grade_protection_level = fields.SerializerMethodField(help_text="等保备案等级")
    magnitude = fields.SerializerMethodField(help_text="业务系统重要等级")
    status = fields.SerializerMethodField()
    is_internet_accessible = fields.SerializerMethodField()
    owner = fields.SerializerMethodField(help_text="业务负责人")
    asset_count = fields.IntegerField(help_text="资产数量")
    host_count = fields.IntegerField(help_text="主机数量")
    db_count = fields.IntegerField(help_text="数据库数量")
    middleware_count = fields.IntegerField(help_text="中间件数量")
    assets = BusinessPortraitItemSerializer(many=True, help_text="资产列表")

    adapters = AdapterSerializer(help_text="适配器数据来源", many=True)
    department = fields.SerializerMethodField(help_text="业务部门")
    unit = fields.SerializerMethodField(help_text="单位")
    phone = fields.SerializerMethodField(help_text="联系电话")
    email = fields.SerializerMethodField(help_text="邮件地址")

    owners = OwnersSerializer(help_text="责任人层")
    owners_asql_view = AsqlViewSerializer(help_text="责任人层查询视图")
    data = fields.ListField(child=fields.CharField(), help_text="数据层")
    data_asql_view = AsqlViewSerializer(help_text="数据层查询视图")
    softwares = fields.ListField(source="software", child=fields.CharField(), help_text="软件层")
    softwares_asql_view = AsqlViewSerializer(help_text="软件层查询视图")
    network = BusinessPortraitNetworkSerializer(help_text="网络层")
    network_asql_view = AsqlViewSerializer(help_text="网络层查询视图")
    system = fields.ListField(child=fields.CharField(), help_text="系统层")
    system_asql_view = AsqlViewSerializer(help_text="系统层查询视图")
    physical = fields.ListField(child=fields.CharField(), help_text="物理层")
    physical_asql_view = AsqlViewSerializer(help_text="物理层查询视图")

    external_domain_count = fields.IntegerField(help_text="对外域名数量")
    external_domain_asql_view = AsqlViewSerializer(help_text="对外域名查询视图")
    external_ip_count = fields.IntegerField(help_text="对外IP数量")
    external_ip_asql_view = AsqlViewSerializer(help_text="对外IP查询视图")
    external_url_count = fields.IntegerField(help_text="对外URL数量")
    external_url_asql_view = AsqlViewSerializer(help_text="对外URL查询视图")
    external_port_count = fields.IntegerField(help_text="对外端口数量")
    external_port_asql_view = AsqlViewSerializer(help_text="对外端口查询视图")

    internal_ip_count = fields.IntegerField(help_text="内网IP")
    internal_ip_asql_view = AsqlViewSerializer(help_text="内网IP查询视图")
    vulnerability_count = fields.IntegerField(help_text="漏洞")
    vulnerability_asql_view = AsqlViewSerializer(help_text="漏洞查询视图")
    software_count = fields.IntegerField(help_text="安装软件")
    software_asql_view = AsqlViewSerializer(help_text="安装软件查询视图")
    website_count = fields.IntegerField(help_text="web站点")
    website_asql_view = AsqlViewSerializer(help_text="web站点查询视图")
    internal_port_count = fields.IntegerField(help_text="内网端口")
    internal_port_asql_view = AsqlViewSerializer(help_text="内网端口查询视图")
    account_count = fields.IntegerField(help_text="账号")
    account_asql_view = AsqlViewSerializer(help_text="账号查询视图")
    jar_count = fields.IntegerField(help_text="开发类库")
    jar_asql_view = AsqlViewSerializer(help_text="开发类库查询视图")
    webframe_count = fields.IntegerField(source="web_frame_count", help_text="web框架")
    webframe_asql_view = AsqlViewSerializer(help_text="web框架查询视图")

    without_cmdb_count = fields.IntegerField(help_text="主机CMDB未覆盖")
    without_cmdb_asql = fields.CharField(default="", help_text="主机CMDB未覆盖ASQL")
    without_cmdb_asql_view = AsqlViewSerializer(default="", help_text="主机CMDB未覆盖查询视图")
    without_hids_count = fields.IntegerField(help_text="主机HIDS未覆盖")
    without_hids_asql = fields.CharField(default="", help_text="主机HIDS未覆盖ASQL")
    without_hids_asql_view = AsqlViewSerializer(default="", help_text="主机HIDS未覆盖查询视图")
    without_osm_count = fields.IntegerField(help_text="主机堡垒机未覆盖")
    without_osm_asql = fields.CharField(default="", help_text="主机堡垒机未覆盖ASQL")
    without_osm_asql_view = AsqlViewSerializer(default="", help_text="主机堡垒机未覆盖查询视图")
    without_va_count = fields.IntegerField(help_text="主机漏扫未覆盖")
    without_va_asql = fields.CharField(default="", help_text="主机漏扫未覆盖ASQL")
    without_va_asql_view = AsqlViewSerializer(default="", help_text="主机漏扫未覆盖查询视图")
    without_av_count = fields.IntegerField(help_text="主机防病毒未覆盖")
    without_av_asql = fields.CharField(default="", help_text="主机防病毒未覆盖ASQL")
    without_av_asql_view = AsqlViewSerializer(default="", help_text="主机防病毒未覆盖查询视图")

    def get_owner(self, obj):
        field = "nickname"
        return self.parse_owner_show_single_info(self.context["owner_mapper"], obj.owners["owner_names"], field)

    def get_phone(self, obj):
        return self._get_core(obj, "phones")

    def get_email(self, obj):
        return self._get_core(obj, "emails")

    def get_department(self, obj):
        return self._get_core(obj, "departments")

    def _get_core(self, obj, field):
        o_p_1 = self.parse_owner_show_multi_info(self.context["owner_mapper"], obj.owners["owner_names"], field)
        o_p_2 = self.parse_owner_show_multi_info(self.context["operator_mapper"], obj.owners["operator_names"], field)
        result = []
        result.append(o_p_1) if o_p_1 else ...
        result.append(o_p_2) if o_p_2 else ...
        return ",".join(result)

    def get_grade_protection_level(self, obj):
        return GRADE_PROTECTION_LEVEL_MAPPING.get(obj.grade_protection_level)

    def get_magnitude(self, obj):
        return MAGNITUDE_MAPPING.get(obj.magnitude)

    def get_critical_infrastructure(self, obj):
        return CRITICAL_INFRASTRUCTURE_MAPPER.get(obj.critical_infrastructure)

    def get_is_internet_accessible(self, obj):
        return IS_INTERNET_ACCESSIBLE_MAPPING.get(getattr(obj, "is_internet_accessible", ""), "")

    def get_status(self, obj):
        return STATUS_MAPPING.get(getattr(obj, "status", ""), "")

    def get_unit(self, obj):
        return extract(self.context["business"], "business.unit") or ""

    def get_domain(self, obj):
        return extract(self.context["business"], "business.domain") or ""
