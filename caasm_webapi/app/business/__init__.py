from caasm_tool.patch.router import DefaultRouter
from caasm_webapi.app.business.views import (
    BusinessPortraitSummaryAPIView,
    BusinessPortraitAPIView,
    BusinessPortraitIconAPIView,
    BusinessPortraitMagnitudeAPIView,
    BusinessPortraitGradeProtectionLevelAPIView,
    BusinessCriticalInfrastructureAPIView,
    BusinessIsInternetAccessibleAPIVIEW,
    BusinessStatusAPIView,
)

business_router = DefaultRouter("")
business_router.join_url("business/portraits/", BusinessPortraitSummaryAPIView, name="business_portraits")
business_router.join_path("business/portrait/<business_portrait_id>", BusinessPortraitAPIView)
business_router.join_path("business/portraitIcon/<icon_name>", BusinessPortraitIconAPIView)
business_router.join_url("business/portrait/magnitudeOptions/", BusinessPortraitMagnitudeAPIView)
business_router.join_url("business/portrait/gradeProtectionLevelOptions/", BusinessPortraitGradeProtectionLevelAPIView)
business_router.join_url("business/portrait/criticalInfrastructureOptions/", BusinessCriticalInfrastructureAPIView)
business_router.join_url("business/portrait/statusOptions/", BusinessStatusAPIView)
business_router.join_url("business/portrait/isInternetAccessibleOptions/", BusinessIsInternetAccessibleAPIVIEW)

default_app_config = "caasm_webapi.app.business.appconf.CaasmBusinessConfig"
