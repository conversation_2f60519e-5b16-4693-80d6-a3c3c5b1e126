from typing import Optional

from caasm_businesses.enums import GradeProtectionLevel, Magnitude


class BusinessListRequest:
    def __init__(self):
        self.page_index: Optional[int] = 1
        self.page_size: Optional[int] = 20
        self.keyword: Optional[str] = None
        self.grade_protection_level: Optional[GradeProtectionLevel] = None
        self.magnitude: Optional[Magnitude] = None
        self.critical_infrastructure: Optional[bool] = None
        self.owner_keyword: Optional[str] = None
        self.date = None
        self.data_center = None
        self.realm = None

    @property
    def offset(self):
        return self.page_index * self.page_size
