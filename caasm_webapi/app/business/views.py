import hashlib
import logging
from datetime import datetime
from typing import List

from django.core.cache import cache
from django.http import FileResponse
from rest_framework import status
from rest_framework.request import Request
from rest_framework.response import Response
from rest_framework.views import APIView

from caasm_aql.tool import parse_aql
from caasm_businesses.enums import (
    MAGNITUDE_MAPPING,
    GRADE_PROTECTION_LEVEL_MAPPING,
    Magnitude,
    GradeProtectionLevel,
)
from caasm_meta_data.constants import Category
from caasm_service.entity.business_poraiter import Status, IsInternetAccessible, BusinessPortrait
from caasm_service.runtime import entity_service, business_portraiter_service
from caasm_tool.util import deduplicate, extract
from caasm_webapi.app.business.requests import BusinessListRequest
from caasm_webapi.app.business.serializers import (
    BusinessQueryRequestSerializer,
    BusinessPortraitSerializer,
    BusinessPortraitSummariesSerializer,
    SnapshotDateSerializer,
)
from caasm_webapi.conf.settings import BASE_DIR
from caasm_webapi.util.response import build_success, build_failed, ResponseCode

log = logging.getLogger()


def get_fields(extra_fields):
    if not extra_fields:
        return None
    return ["base.asset_type_display_name", "asset_base.name"] + extra_fields


def generate_asql_view(asql: str, *fields):
    display_fields = get_fields(list(fields))
    view = {"asql": asql}
    view.update({"fields": deduplicate(display_fields)}) if fields else ...
    return view


class BusinessPortraitSummaryAPIView(APIView):
    _keyword_asset_types = ["主机", "终端", "未知"]
    _other_asset_type = "其它"
    _owner_max_length = 4
    _critical_infrastructure_mapper = {0: "否", 1: "是", 2: "否"}

    _cache_time = 30 * 1000

    @classmethod
    def _find_person_mapper(cls, names):
        if not names:
            return {}
        names = list(names)
        _fields = [
            "asset_base.owners.nickname",
            "asset_base.owners.username",
            "asset_base.owners.emails",
            "asset_base.owners.phones",
            "asset_base.owners.departments",
        ]

        asql = f"$.asset_base.owners.username.in({names})"
        try:
            _condition = parse_aql(asql, Category.ASSET)
        except Exception as e:
            _condition = {}
        try:
            result = entity_service.find_entity_loop(Category.ASSET, condition=_condition, fields=_fields)
        except Exception as e:
            log.warning(f"Find owner({_condition}) info error({e})")
            result = []
        owner_mapper = {}
        _new_names = []
        for owner in result:
            owners = extract(owner, "asset_base.owners")
            for _owner in owners:
                user_name = extract(_owner, "username")
                nick_name = extract(_owner, "nickname")
                if user_name not in owner_mapper:
                    owner_mapper[user_name] = _owner
                if nick_name not in owner_mapper:
                    owner_mapper[nick_name] = _owner
        for name in names:
            if name in owner_mapper:
                continue
            owner_mapper[name] = {"owner": {"nickname": name, "username": name}}
        return owner_mapper

    @classmethod
    def _build_cache_key(cls, entity):
        key = ",".join([f"{k}:{v}" for k, v in entity.items()]).encode()
        return hashlib.md5(key).hexdigest()

    def post(self, request: Request):
        serializer: BusinessQueryRequestSerializer = BusinessQueryRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)
        req: BusinessListRequest = serializer.save()
        # 添加缓存
        data = cache.get(self._build_cache_key(serializer.validated_data))
        if data is not None:
            return build_success(data)

        portrait_cursor = business_portraiter_service.find_business_portraiter(
            keyword=req.keyword,
            owner_keyword=req.owner_keyword,
            magnitude=req.magnitude,
            limit=req.page_size,
            critical_infrastructure=req.critical_infrastructure,
            offset=req.offset,
            grade_protection_level=req.grade_protection_level,
            data_center=req.data_center,
            realm=req.realm,
        )
        portraits: List[BusinessPortrait] = list(portrait_cursor)
        portrait_count = business_portraiter_service.get_business_portraiter_count(
            keyword=req.keyword,
            owner_keyword=req.owner_keyword,
            magnitude=req.magnitude,
            critical_infrastructure=req.critical_infrastructure,
            grade_protection_level=req.grade_protection_level,
            data_center=req.data_center,
            realm=req.realm,
        )

        portrait_result, owner_names, operator_names = [], [], []

        for portrait in portraits:
            owner_names.extend(portrait.owner_names) if portrait.owner_names else ...
            operator_names.extend(portrait.operator_names) if portrait.operator_names else ...
        owner_mapper = self._find_person_mapper(owner_names)
        o_field = "nickname"
        for portrait in portraits:
            owners = [i for i in [extract(owner_mapper[o], o_field) for o in portrait.owner_names] if i]
            owner_show_info = ",".join(owners[: self._owner_max_length])
            if len(owners) > self._owner_max_length:
                owner_show_info += f" 等共计{len(owners)}人"
            portrait_dict = {
                "id": str(portrait.id),
                "name": portrait.name,
                "update_time": portrait.update_time,
                "vul_count": [
                    {
                        "name": "low_vulnerability_count",
                        "display_name": "低危",
                        "value": portrait.low_vulnerability_count,
                    },
                    {
                        "name": "mid_vulnerability_count",
                        "display_name": "中危",
                        "value": portrait.mid_vulnerability_count,
                    },
                    {
                        "name": "high_vulnerability_count",
                        "display_name": "高危",
                        "value": portrait.high_vulnerability_count,
                    },
                ],
                "items": [
                    {
                        "name": "grade_protection_level",
                        "display_name": "备案等级",
                        "value": GRADE_PROTECTION_LEVEL_MAPPING.get(portrait.grade_protection_level, "无"),
                    },
                    {
                        "name": "owner",
                        "display_name": "业务负责人",
                        "value": portrait.owner_names[0] if portrait.owner_names else "",
                    },
                    {
                        "name": "privileged_accounts",
                        "display_name": "特权账号",
                        "value": portrait.account_count,
                    },
                    {
                        "name": "website_count",
                        "display_name": "web站点数量",
                        "value": portrait.website_count,
                    },
                    {
                        "name": "software_count",
                        "display_name": "软件供应链数量",
                        "value": portrait.software_count,
                    },
                    {
                        "name": "vulnerability_count",
                        "display_name": "漏洞数量",
                        "value": portrait.vulnerability_count,
                    },
                ],
                "assets": self._build_asset_statistics(portrait),
                "coverage_charts": portrait.coverage_charts,
            }
            portrait_result.append(portrait_dict)

        update_time = portraits[0].update_time if portraits else datetime.now()
        instance = {"portraits": portrait_result, "count": portrait_count, "update_time": update_time}
        data = BusinessPortraitSummariesSerializer(instance=instance, context={"date": req.date}).data

        cache.set(self._build_cache_key(serializer.validated_data), data, self._cache_time)
        return build_success(data)

    @classmethod
    def _build_asset_statistics(cls, portrait):
        item_mapper = {}
        for name, count in portrait.asset_group.items():
            percent = int((count / portrait.asset_count) * 100)
            if name not in cls._keyword_asset_types:
                continue
                # _key = cls._other_asset_type
                # if _key not in item_mapper:
                #     item_mapper[_key] = {"name": _key, "display_name": _key, "value": count, "percent": percent}
                # else:
                #     item_mapper[_key]["value"] += count
                #     item_mapper[_key]["percent"] += percent
            else:
                item_mapper[name] = {"name": name, "display_name": name, "value": count, "percent": percent}

        items = []
        for key, tmp_items in item_mapper.items():
            if key == cls._other_asset_type:
                continue
            items.append(tmp_items)

        items.append(item_mapper[cls._other_asset_type]) if item_mapper.get(cls._other_asset_type) else ...

        return {"name": "assets", "display_name": "资产数量", "value": "", "items": items}


class BusinessPortraitMagnitudeAPIView(APIView):
    def get(self, request: Request):
        results = list()
        for item in Magnitude:
            results.append({"display_name": MAGNITUDE_MAPPING.get(item), "value": item.value})
        return build_success(results)


class BusinessPortraitGradeProtectionLevelAPIView(APIView):
    def get(self, request: Request):
        results = list()
        results.append({"display_name": "不限", "value": None})
        for item in GradeProtectionLevel:
            results.append(
                {
                    "display_name": GRADE_PROTECTION_LEVEL_MAPPING.get(item),
                    "value": item.value,
                }
            )
        return build_success(results)


class BusinessCriticalInfrastructureAPIView(APIView):
    def get(self, request: Request):
        results = list()
        results.append({"display_name": "不限", "value": None})
        results.append({"display_name": "是", "value": True})
        results.append({"display_name": "否", "value": False})
        return build_success(results)


class BusinessStatusAPIView(APIView):
    def get(self, request):
        results = [
            {
                "display_name": "未知",
                "value": Status.UNKNOWN,
            },
            {
                "display_name": "使用中",
                "value": Status.USING,
            },
            {
                "display_name": "建设中",
                "value": Status.BUILDING,
            },
            {
                "display_name": "已下线",
                "value": Status.OFFLINE,
            },
            {
                "display_name": "已删除",
                "value": Status.DELETED,
            },
        ]
        return build_success(results)


class BusinessIsInternetAccessibleAPIVIEW(APIView):
    def get(self, request):
        results = [
            {
                "display_name": "未知",
                "value": IsInternetAccessible.UNKNOWN,
            },
            {
                "display_name": "是",
                "value": IsInternetAccessible.YES,
            },
            {
                "display_name": "否",
                "value": IsInternetAccessible.NO,
            },
        ]
        return build_success(results)


class BusinessPortraitAPIView(APIView):
    def get(self, request, business_portrait_id):
        serializer = SnapshotDateSerializer(data=request.query_params)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)

        business_portrait = business_portraiter_service.get_business_portraiter(business_portrait_id)

        if not business_portrait:
            return Response(status=status.HTTP_404_NOT_FOUND)
        business_name = business_portrait.name

        business = entity_service.get_entity(Category.BUSINESS, "business.full_name", business_name)
        business = business or {}

        owner_names = business_portrait.owner_names
        operator_names = business_portrait.operator_names

        owner_mapper = BusinessPortraitSummaryAPIView._find_person_mapper(owner_names)
        operator_mapper = BusinessPortraitSummaryAPIView._find_person_mapper(operator_names)

        asql: str = f'$.asset_base.businesses.full_name="{business_name}"'
        business_portrait.owners = {
            "admin_accounts": business_portrait.admin_accounts,
            "owner_names": owner_names,
            "operator_names": operator_names,
        }
        business_portrait.owners_asql_view = generate_asql_view(asql, "asset_base.owners", "computer.accounts")
        business_portrait.data_asql_view = generate_asql_view(asql, "computer.dbs")
        business_portrait.softwares_asql_view = generate_asql_view(asql, "computer.softwares")
        business_portrait.network_asql_view = generate_asql_view(asql, "network.priority_addr")
        business_portrait.system_asql_view = generate_asql_view(asql, "computer.os.distribution")
        business_portrait.physical_asql_view = generate_asql_view(
            asql, "asset_base.device.distribution", "computer.interfaces"
        )

        business_portrait.external_domain_asql_view = generate_asql_view(asql)
        business_portrait.external_ip_asql_view = generate_asql_view(
            f'$.asset_base.businesses.full_name="{business_name}" and $.network.ips.exposure = 3', "network.ips"
        )
        business_portrait.external_url_asql_view = generate_asql_view(asql)
        business_portrait.external_port_asql_view = generate_asql_view(
            f'$.asset_base.businesses.full_name="{business_name}" and $.network.ports.exposure = 3',
            "network.priority_addr",
            "network.ports",
        )
        business_portrait.internal_ip_asql_view = generate_asql_view(asql, "network.ips")
        business_portrait.vulnerability_asql_view = generate_asql_view(asql, "vulnerability.vulners")
        business_portrait.software_asql_view = generate_asql_view(asql, "computer.softwares")
        business_portrait.website_asql_view = generate_asql_view(asql, "computer.websites")
        business_portrait.internal_port_asql_view = generate_asql_view(asql, "network.priority_addr", "network.ports")
        business_portrait.account_asql_view = generate_asql_view(asql, "computer.accounts")
        business_portrait.jar_asql_view = generate_asql_view(asql, "computer.jars")
        business_portrait.webframe_asql_view = generate_asql_view(asql, "computer.webframes")

        business_portrait.assets = [
            {
                "name": name,
                "display_name": name,
                "value": count,
                "percent": int((count / business_portrait.asset_count) * 100),
            }
            for name, count in business_portrait.asset_group.items()
        ]

        business_portrait.without_cmdb_asql = f'$.base.asset_type_display_name="主机" and $.asset_base.businesses.full_name="{business_name}" and not $.asset_base.adapter_properties = "CMDB"'
        business_portrait.without_cmdb_asql_view = generate_asql_view(business_portrait.without_cmdb_asql)
        business_portrait.without_hids_asql = f'$.base.asset_type_display_name="主机" and $.asset_base.businesses.full_name="{business_name}" and not $.asset_base.adapter_properties = "HIDS"'
        business_portrait.without_hids_asql_view = generate_asql_view(business_portrait.without_hids_asql)
        business_portrait.without_osm_asql = f'$.base.asset_type_display_name="主机" and $.asset_base.businesses.full_name="{business_name}" and not $.asset_base.adapter_properties = "堡垒机"'
        business_portrait.without_osm_asql_view = generate_asql_view(business_portrait.without_osm_asql)
        business_portrait.without_va_asql = f'$.base.asset_type_display_name="主机" and $.asset_base.businesses.full_name="{business_name}" and not $.asset_base.adapter_properties = "漏洞扫描"'
        business_portrait.without_va_asql_view = generate_asql_view(business_portrait.without_va_asql)
        business_portrait.without_av_asql = f'$.base.asset_type_display_name="主机" and $.asset_base.businesses.full_name="{business_name}" and not $.asset_base.adapter_properties = "防病毒"'
        business_portrait.without_av_asql_view = generate_asql_view(business_portrait.without_av_asql)

        context = {"business": business, "owner_mapper": owner_mapper, "operator_mapper": operator_mapper}
        serializer = BusinessPortraitSerializer(instance=business_portrait, context=context)
        return build_success(serializer.data)


class BusinessPortraitIconAPIView(APIView):
    _default_svg = BASE_DIR / "statics" / "businesses" / f"default.svg"

    def get(self, request, icon_name):
        _path = BASE_DIR / "statics" / "businesses" / f"{icon_name}.svg"
        if not _path.exists():
            _path = self._default_svg
        res = FileResponse(open(_path, "rb"))
        res["Content-Type"] = "image/svg+xml"
        return res
