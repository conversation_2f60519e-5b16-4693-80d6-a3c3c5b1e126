from rest_framework import fields
from rest_framework.serializers import Serializer

from caasm_webapi.app.vul_priority.requests import VulPriorityIDRequest, VulPriorityRequest, VulPrioritySettingRequest


class VulPriorityRequestSerializer(Serializer):
    id = fields.Char<PERSON><PERSON>(required=False)
    name = fields.Char<PERSON>ield(required=True, max_length=32)
    priority = fields.IntegerField(required=True, min_value=1, max_value=100)

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        req = VulPriorityRequest()
        req.id_ = validated_data.get("id")
        req.name = validated_data["name"]
        req.priority = validated_data["priority"]
        return req


class VulPrioritySettingSerializer(Serializer):
    id = fields.CharField()
    name = fields.Char<PERSON>ield(required=True)
    asset_condition = fields.Char<PERSON>ield(required=True, allow_null=True)
    vul_condition = fields.Char<PERSON><PERSON>(required=True, allow_null=True)
    vul_unique_condition = fields.Char<PERSON>ield(required=True, allow_null=True)

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        pass


class VulPrioritySerializer(Serializer):
    id = fields.CharField()
    name = fields.CharField(max_length=32)
    priority = fields.IntegerField(min_value=1, max_value=100)
    setting_count = fields.SerializerMethodField()

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        pass

    def get_setting_count(self, obj):
        id_ = obj.id
        from caasm_service.runtime import vul_priority_service

        return vul_priority_service.count_of_settings(id_)


class VulPrioritySettingRequestSerializer(Serializer):
    id = fields.CharField(required=True)
    setting_id = fields.CharField(required=False)
    name = fields.CharField(required=True, max_length=32)
    asset_condition = fields.CharField(required=False, max_length=4096, allow_null=True)
    vul_condition = fields.CharField(required=False, max_length=4096, allow_null=True)
    vul_unique_condition = fields.CharField(required=False, max_length=4096, allow_null=True)

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        req = VulPrioritySettingRequest()
        req.id_ = validated_data.get("id")
        req.setting_id = validated_data.get("setting_id")
        req.name = validated_data["name"]
        req.asset_condition = validated_data.get("asset_condition")
        req.vul_condition = validated_data.get("vul_condition")
        req.vul_unique_condition = validated_data.get("vul_unique_condition")
        return req


class VulPriorityIDRequestSerializer(Serializer):
    id = fields.CharField(required=True)
    setting_id = fields.CharField(required=True)

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        req = VulPriorityIDRequest()
        req.id = validated_data["id"]
        req.setting_id = validated_data["setting_id"]
        return req


class VulPriorityNameRequestSerializer(Serializer):
    name = fields.CharField(required=True, max_length=8)

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        return validated_data["name"]
