from rest_framework.request import Request
from rest_framework.views import APIView

from caasm_service.entity.vul_priority import VulPrioritySetting
from caasm_service.runtime import vul_priority_service
from caasm_service.schema.runtime import vul_priority_setting_schema
from caasm_webapi.app.vul_priority.requests import VulPriorityIDRequest, VulPriorityRequest, VulPrioritySettingRequest
from caasm_webapi.app.vul_priority.serializers import (
    VulPrioritySettingSerializer,
    VulPriorityIDRequestSerializer,
    VulPriorityRequestSerializer,
    VulPrioritySerializer,
    VulPrioritySettingRequestSerializer,
    VulPriorityNameRequestSerializer,
)
from caasm_webapi.util.response import build_success, build_failed


class CreateVulPriorityAPIView(APIView):
    def post(self, request):
        serializer = VulPriorityRequestSerializer(data=request.data)
        if serializer.is_valid():
            req: VulPriorityRequest = serializer.save()
            if vul_priority_service.exist_priority(req.name):
                return build_failed(-1, "该名称已存在")
            priority = vul_priority_service.create_priority(req.name, req.priority)
            if priority:
                return build_success({"id": str(priority.inserted_id)})
            else:
                return build_failed(-1, "")
        else:
            return build_failed(-1, str(serializer.errors))


class DeleteVulPriorityAPIView(APIView):
    def get(self, request, id_):
        vul_priority_service.delete_priority(id_)
        return build_success({})


class UpdateVulPriorityAPIView(APIView):
    def post(self, request):
        serializer = VulPriorityRequestSerializer(data=request.data)
        if serializer.is_valid():
            req: VulPriorityRequest = serializer.save()
            if vul_priority_service.update_priority(req.id_, req.name, req.priority):
                return build_success({})
            else:
                return build_failed(-1, "更新失败")
        else:
            return build_failed(-1, str(serializer.errors))


class ListVulPrioritiesAPIView(APIView):
    def get(self, request):
        return build_success(VulPrioritySerializer(instance=vul_priority_service.list_priorities(), many=True).data)


class CreateVulPrioritySettingAPIView(APIView):
    def post(self, request: Request):
        serializer = VulPrioritySettingRequestSerializer(data=request.data)
        if serializer.is_valid():
            req: VulPrioritySettingRequest = serializer.save()
            vul_priority_setting: VulPrioritySetting = vul_priority_setting_schema.load(
                {
                    "name": req.name,
                    "asset_condition": req.asset_condition,
                    "vul_condition": req.vul_condition,
                    "vul_unique_condition": req.vul_unique_condition,
                }
            )
            vul_priority_service.create_setting(req.id_, vul_priority_setting)
            return build_success({})
        else:
            return build_failed(-1, str(serializer.errors))


class DeleteVulPrioritySettingAPIView(APIView):
    def post(self, request: Request):
        serializer = VulPriorityIDRequestSerializer(data=request.data)
        if serializer.is_valid():
            req: VulPriorityIDRequest = serializer.save()
            if vul_priority_service.delete_setting(req.id, req.setting_id):
                return build_success({})
            else:
                return build_failed(-1, "删除失败")
        else:
            return build_failed(-1, str(serializer.errors))


class UpdateVulPrioritySettingAPIView(APIView):
    def post(self, request: Request):
        serializer = VulPrioritySettingRequestSerializer(data=request.data)
        if serializer.is_valid():
            req: VulPrioritySettingRequest = serializer.save()
            vul_priority_setting: VulPrioritySetting = vul_priority_setting_schema.load(
                {
                    "_id": req.setting_id,
                    "name": req.name,
                    "asset_condition": req.asset_condition,
                    "vul_condition": req.vul_condition,
                    "vul_unique_condition": req.vul_unique_condition,
                }
            )
            if vul_priority_service.update_setting(req.id_, vul_priority_setting):
                return build_success({})
            else:
                return build_failed(-1, "更新失败")
        else:
            return build_failed(-1, str(serializer.errors))


class ListVulPrioritySettingsAPIView(APIView):
    def get(self, request, id_):
        settings = list(vul_priority_service.list_settings(id_))
        return build_success(VulPrioritySettingSerializer(instance=settings, many=True).data)


class ListVulPrioritySettingsByNameAPIView(APIView):
    _NAME_MAPPER = {"高级": "致命", "中级": "高", "低级": "中"}

    def post(self, request):
        serializer = VulPriorityNameRequestSerializer(data=request.data)
        if serializer.is_valid():
            name = serializer.save()
            setting_name = self._NAME_MAPPER.get(name)
            if setting_name:
                settings = vul_priority_service.list_settings_by_priority_name(setting_name)
                if settings:
                    result = VulPrioritySettingSerializer(instance=settings, many=True).data
                else:
                    result = {}
            else:
                result = {}
            return build_success(result)
        return build_failed(-1, str(serializer.errors))
