from caasm_tool.patch.router import DefaultRouter
from caasm_webapi.app.vul_priority.views import (
    ListVulPrioritySettingsAPIView,
    CreateVulPriorityAPIView,
    UpdateVulPrioritySettingAPIView,
    CreateVulPrioritySettingAPIView,
    DeleteVulPrioritySettingAPIView,
    UpdateVulPriorityAPIView,
    DeleteVulPriorityAPIView,
    ListVulPrioritiesAPIView,
    ListVulPrioritySettingsByNameAPIView,
)

vul_priority_router = DefaultRouter("vulPriority")

vul_priority_router.join_path("createPriority/", CreateVulPriorityAPIView, "CreateVulPriority")
vul_priority_router.join_path("deletePriority/<id_>/", DeleteVulPriorityAPIView, "DeleteVulPriority")
vul_priority_router.join_path("updatePriority/", UpdateVulPriorityAPIView, "UpdateVulPriority")
vul_priority_router.join_path("listPriorities/", ListVulPrioritiesAPIView, "ListVulPriorities")
vul_priority_router.join_path("createSetting/", CreateVulPrioritySettingAPIView, "CreateVulPrioritySetting")
vul_priority_router.join_path("deleteSetting/", DeleteVulPrioritySettingAPIView, "DeleteVulPrioritySetting")
vul_priority_router.join_path("updateSetting/", UpdateVulPrioritySettingAPIView, "UpdateVulPrioritySetting")
vul_priority_router.join_path("listSettings/<id_>/", ListVulPrioritySettingsAPIView, "ListVulPrioritySettings")
vul_priority_router.join_path(
    "listSettingsByName/", ListVulPrioritySettingsByNameAPIView, "ListVulPrioritySettingsByNameAPIView"
)
