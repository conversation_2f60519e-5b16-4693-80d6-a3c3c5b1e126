from caasm_tool.patch.router import DefaultRouter
from caasm_webapi.app.snapshot.views.snapshot_record import (
    SnapshotRecordListAPI,
    SnapshotRecordClearDayGetAPI,
    SnapshotRecordClearDaySetAPI,
    SnapshotRecordClearAPI,
    SnapshotTotalAvailableRecordAPI,
)

snapshot_router = DefaultRouter("snapshot")

snapshot_router.join_path("record/list/", SnapshotRecordListAPI)
snapshot_router.join_path("record/clear/", SnapshotRecordClearAPI)
snapshot_router.join_path("record/clearDay/set/", SnapshotRecordClearDaySetAPI)
snapshot_router.join_path("record/clearDay/get/", SnapshotRecordClearDayGetAPI)
snapshot_router.join_path("record/available/", SnapshotTotalAvailableRecordAPI)
