from rest_framework import serializers

from caasm_service.constants.setting import Setting<PERSON>ame
from caasm_tool.constants import DATETIME_FORMAT
from caasm_tool.patch.serializer import SerializerMixin
from caasm_tool.util import size_convert


class SnapshotRecordDetailSerializer(SerializerMixin):
    date = serializers.CharField()
    record_id = serializers.CharField(source="id")
    size = serializers.SerializerMethodField("get_size")
    create_time = serializers.DateTimeField(format=DATETIME_FORMAT)
    update_time = serializers.DateTimeField(format=DATETIME_FORMAT)

    def get_size(self, obj):
        return size_convert(obj.size or 0)


class SnapshotRecordClearDaySetSerializer(SerializerMixin):
    snapshot_remove_day = serializers.IntegerField(required=True, min_value=1, max_value=140)
    snapshot_remove_disk_usage_rate = serializers.IntegerField(required=True, min_value=1, max_value=100)

    def create(self, validated_data):
        create_data = {
            SettingName.SNAPSHOT_REMOVE_DAY: validated_data["snapshot_remove_day"],
            SettingName.SNAPSHOT_REMOVE_DISK_USAGE_RATE: validated_data["snapshot_remove_disk_usage_rate"],
        }
        return create_data, validated_data


class SnapshotRecordDeleteRequestSerializer(SerializerMixin):
    record_ids = serializers.ListField(
        child=serializers.CharField(min_length=24, max_length=24), min_length=1, required=True
    )


class SnapshotRecordListRequestSerializer(SerializerMixin):
    page_index = serializers.IntegerField(required=True, min_value=1)
    page_size = serializers.IntegerField(required=True, max_value=200)
    sort_fields = serializers.ListField(child=serializers.CharField(), default=lambda: ["-update_time", "-date"])
