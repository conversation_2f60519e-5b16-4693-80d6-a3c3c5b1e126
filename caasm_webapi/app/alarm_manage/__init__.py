from caasm_tool.patch.router import DefaultRouter
from caasm_webapi.app.alarm_manage.view.alarm import AlarmRecordApiView, AlarmNameChooseApiView
from caasm_webapi.app.overview.views.intercept_alarm import InterceptAlarmListAPIView
from caasm_webapi.app.alarm_manage.view.rule import (
    AlarmRuleAddView,
    AlarmRuleListView,
    AlarmRuleDeleteView,
    AlarmRuleUpdateView,
    AlarmRuleResultTypeView,
    AlarmRuleEnableUpdateView,
    AlarmRuleChooseView,
    AlarmRuleInstanceRunView,
)
from caasm_webapi.app.alarm_manage.view.rule_run_record import RuleRunRecordApiView
from caasm_webapi.app.alarm_manage.view.rule_template import (
    RuleTemplateAddApiView,
    RuleTemplateListApiView,
    RuleTemplateUpdateApiView,
    RuleTemplateDeleteApiView,
    RuleCategoryChooseView,
    RuleTemplateChooseView,
    RuleLevelChooseView,
)

alarm_manage_router = DefaultRouter()

alarm_manage_router.join_path("rule/category/choose/", RuleCategoryChooseView, name="ruleCategory")
alarm_manage_router.join_path("rule/template/choose/", RuleTemplateChooseView, name="RuleTemplateChooseView")
alarm_manage_router.join_path("rule/level/choose/", RuleLevelChooseView, name="RuleLevelChooseView")

# 模版
alarm_manage_router.join_path("rule/template/add/", RuleTemplateAddApiView, name="alarmRuleTemplateAdd")
alarm_manage_router.join_path("rule/template/list/", RuleTemplateListApiView, name="alarmRuleTemplateList")
alarm_manage_router.join_path("rule/template/update/", RuleTemplateUpdateApiView, name="alarmRuleTemplateUpdate")
alarm_manage_router.join_path("rule/template/delete/", RuleTemplateDeleteApiView, name="alarmRuleTemplateDelete")

# 规则
alarm_manage_router.join_path("rule/instance/add/", AlarmRuleAddView, name="AlarmRuleAddView")
alarm_manage_router.join_path("rule/instance/list/", AlarmRuleListView, name="AlarmRuleListView")
alarm_manage_router.join_path("rule/instance/delete/", AlarmRuleDeleteView, name="AlarmRuleDeleteView")
alarm_manage_router.join_path("rule/instance/update/", AlarmRuleUpdateView, name="AlarmRuleUpdateView")
alarm_manage_router.join_path("rule/instance/resultChoose/", AlarmRuleResultTypeView, name="AlarmRuleResultTypeView")
alarm_manage_router.join_path(
    "rule/instance/updateStatus/", AlarmRuleEnableUpdateView, name="AlarmRuleEnableUpdateView"
)
alarm_manage_router.join_path("rule/instance/choose/", AlarmRuleChooseView, name="AlarmRuleChooseView")
alarm_manage_router.join_path("rule/instance/run/", AlarmRuleInstanceRunView, name="AlarmRuleInstanceRunView")

# 规则执行历史
alarm_manage_router.join_path("rule/instance/runRecord/", RuleRunRecordApiView, name="RuleRunRecordApiView")

# 告警
alarm_manage_router.join_path("alarm/list/", AlarmRecordApiView, name="AlarmRecordApiView")
alarm_manage_router.join_path("alarm/<category>/nameChoose/", AlarmNameChooseApiView, name="AlarmNameChooseApiView")


default_app_config = "caasm_webapi.app.adapter.appconf.CaasmAlarmConfig"
