from rest_framework.views import APIView

from caasm_meta_data.constants import CATEG<PERSON>Y_TRANSLATE
from caasm_service.constants.alarm import ALARM_RULE_LEVEL_MAPPER, ALARM_DISPOSED_STATUS_MAPPER, ALARMStatus
from caasm_service.entity.meta_model import TYPE_NAME_MAPPER, MetaFieldType
from caasm_service.runtime import alarm_record_service, alarm_rule_instance_service, snapshot_record_service
from caasm_tool.constants import DATETIME_FORMAT
from caasm_webapi.app.alarm_manage.serializers.alarm import AlarmRecordListSerializer
from caasm_webapi.app.query_engine.views.entity import MetaViewBase
from caasm_webapi.util.response import build_failed, ResponseCode, build_success


class AlarmRecordApiView(APIView):
    def get(self, request):
        serializer = AlarmRecordListSerializer(data=request.query_params)

        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)

        validated_data = serializer.validated_data

        keyword = validated_data.get("keyword", None)
        alarm_name = validated_data.get("alarm_name", None)
        alarm_category = validated_data.get("alarm_category", None)
        rule_instance_id = validated_data.get("ruleId", None)
        alarm_level = validated_data.get("alarmLevel", None)
        page_index = validated_data.get("page_index", None)
        page_size = validated_data.get("page_size", None)

        sort_fields = validated_data.get("sort_fields", None)
        sort_fields = [("alarm_level", 1), ("update_time", -1)]

        count = alarm_record_service.get_alarm_record_count(
            alarm_name=alarm_name,
            alarm_category=alarm_category,
            alarm_status_list=[ALARMStatus.ACTIVE.value, ALARMStatus.RECURRENT.value, ALARMStatus.NEW.value],
            rule_instance_id=rule_instance_id,
            alarm_level=alarm_level,
            keyword=keyword,
        )
        alarm_record_info = alarm_record_service.find_alarm_rule_instance_info(
            alarm_category=alarm_category,
            rule_instance_id=rule_instance_id,
            alarm_level=alarm_level,
            alarm_status_list=[ALARMStatus.ACTIVE.value, ALARMStatus.RECURRENT.value, ALARMStatus.NEW.value],
            alarm_name=alarm_name,
            keyword=keyword,
            page_size=page_size,
            page_index=page_index,
            sort_fields=sort_fields,
        )

        rule_instances = alarm_rule_instance_service.find_alarm_rule_instance_info()
        rule_instance_mapper = {str(item.id): item.rule_name for item in rule_instances}
        result = []
        for alarm in alarm_record_info:
            result.append(
                {
                    "alarm_id": str(alarm.id),
                    "alarm_name": alarm.alarm_name,
                    "rule_name": rule_instance_mapper.get(str(alarm.rule_instance_id), "未知"),
                    "alarm_level": alarm.alarm_level,
                    "alarm_level_desc": ALARM_RULE_LEVEL_MAPPER.get(alarm.alarm_level, "无"),
                    "alarm_category": alarm.alarm_category,
                    "alarm_category_desc": CATEGORY_TRANSLATE.get(alarm.alarm_category),
                    "check_count": alarm.check_count,
                    "hit_count": alarm.hit_count,
                    "status": alarm.disposal_status,
                    "status_desc": ALARM_DISPOSED_STATUS_MAPPER.get(alarm.disposal_status),
                    "asql": alarm.asql,
                    "update_time": alarm.update_time.strftime(DATETIME_FORMAT),
                    "alarm_timeline": alarm.alarm_timeline,
                }
            )
        return build_success(data={"total": count, "data": result})


class AlarmNameChooseApiView(APIView, MetaViewBase):
    def get(self, request, category):
        entity = {
            "label": "实体相关",
            "value": "entityName",
            "children": [{"label": "实体名称", "value": "{$category}"}],
        }

        result_count = {
            "label": "结果数量",
            "value": "resultCount",
            "children": [{"label": "命中数量", "value": "{$hitCount}"}],
        }

        rule = {
            "label": "规则相关",
            "value": "ruleAttributes",
            "children": [
                {"label": "告警等级", "value": "{$alarmLevel}"},
                {"label": "规则描述", "value": "{$ruleDescription}"},
            ],
        }
        date = snapshot_record_service.get_latest_useful_date()
        meta_models, meta_field_mapper = self.find_field_to_model_mapper(category, date=date)
        field = {
            "label": "字段名称",
            "value": "fieldName",
            "children": self.clean_field(meta_models, meta_field_mapper),
        }
        field_value = {
            "label": "字段值",
            "value": "fieldValue",
            "children": self.clean_field(meta_models, meta_field_mapper, value=True),
        }

        data = [entity, result_count, rule, field, field_value]
        return build_success(data=data)

    def clean_field(self, meta_models, meta_field_mapper, value=False):
        result = []
        checker = set()
        for meta_model in meta_models:
            tmp_meta_fields = meta_field_mapper.get(meta_model.id)
            if not tmp_meta_fields:
                continue
            tmp_fields = self._clean_field(tmp_meta_fields, checker=checker, value=value)
            data_value = "{$" + f"{meta_model.name}" + "}"
            tmp_result = {
                "value": data_value if not value else f"{data_value}.value",
                # "display_name": meta_model.display_name,
                "label": meta_model.display_name,
            }
            if tmp_fields:
                tmp_result["children"] = tmp_fields
            result.append(tmp_result)
        return result

    def _clean_field(self, fields, checker, result=None, value=False):
        if result is None:
            result = []
        for field in fields:
            if field.full_name in checker:
                continue
            checker.add(field.full_name)
            if field.hidden:
                continue

            data_type = field.type
            children = field.children
            name = field.full_name
            full_display_name = field.full_display_name
            if data_type == MetaFieldType.LIST:
                if children and children[0].type == MetaFieldType.OBJECT:
                    children = children[0].children
                else:
                    children = []
            data_value = "{$" + f"{name}" + "}"
            children = self._clean_field(children, checker=checker)
            tmp_result = {
                "value": data_value if not value else f"{data_value}.value",
                # "display_name": display_name,
                "label": full_display_name,
            }
            if children:
                tmp_result["children"] = children
            result.append(tmp_result)
        return result
