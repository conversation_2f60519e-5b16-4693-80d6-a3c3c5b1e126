from caasm_tool.patch.serializer import SerializerMixin
from rest_framework import serializers


class AlarmRecordListSerializer(SerializerMixin):

    keyword = serializers.CharField(help_text="关键字", required=False)

    alarm_name = serializers.CharField(help_text="规则名称", required=False)
    ruleId = serializers.CharField(help_text="规则ID", required=False)
    alarm_category = serializers.CharField(help_text="模版分类", required=False)
    alarmLevel = serializers.CharField(help_text="告警等级", required=False)
    page_index = serializers.IntegerField(help_text="页码", required=False, default=1)
    page_size = serializers.IntegerField(help_text="每页最大小", required=False, default=20)
    sort_fields = serializers.ListField(
        child=serializers.Char<PERSON>ield(default="-update_time"),
        help_text="排序字段",
        required=False,
        default=["-update_time"],
    )


class InterceptAlarmListSerializer(SerializerMixin):
    """拦截告警列表序列化器"""

    src_province = serializers.CharField(help_text="源省份", required=False)
    dst_province = serializers.CharField(help_text="目省份", required=False)
    src_ip = serializers.CharField(help_text="源IP地址", required=False)
    dst_ip = serializers.CharField(help_text="目标IP地址", required=False)
    page_index = serializers.IntegerField(help_text="页码", required=False, default=1)
    page_size = serializers.IntegerField(help_text="每页最大小", required=False, default=20)
