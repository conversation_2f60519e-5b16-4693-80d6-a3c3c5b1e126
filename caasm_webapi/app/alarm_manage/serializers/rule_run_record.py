from caasm_service.constants.alarm import AlarmRuleResultTypeEnum
from caasm_tool.patch.serializer import SerializerMixin
from rest_framework import serializers


class AlarmRuleRunRecordListSerializer(SerializerMixin):

    keyword = serializers.CharField(help_text="关键字", required=False)
    ruleId = serializers.CharField(help_text="规则ID", required=False)
    rule_name = serializers.CharField(help_text="规则名称", required=False)
    ruleCategory = serializers.CharField(help_text="模版分类", required=False)
    ruleLevel = serializers.ChoiceField(
        help_text="规则类型", choices=AlarmRuleResultTypeEnum._value2member_map_, required=False
    )
    page_index = serializers.IntegerField(help_text="页码", required=False, default=1)
    page_size = serializers.IntegerField(help_text="每页最大小", required=False, default=20)
    sort_fields = serializers.ListField(
        child=serializers.Char<PERSON>ield(default="-update_time"),
        help_text="排序字段",
        required=False,
        default=["-update_time"],
    )
