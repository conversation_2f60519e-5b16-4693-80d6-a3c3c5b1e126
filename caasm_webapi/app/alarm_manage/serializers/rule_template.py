from caasm_service.constants.alarm import AlarmRuleTypeEnum
from caasm_service.runtime import alarm_rule_template_service
from caasm_tool.patch.serializer import SerializerMixin
from rest_framework import serializers


class AlarmRuleTemplateDeleteSerializer(SerializerMixin):

    template_ids = serializers.ListField(child=serializers.CharField(), help_text="模版ids", required=False)


class AlarmRuleTemplateListSerializer(SerializerMixin):

    keyword = serializers.CharField(help_text="关键字", required=False)

    template_name = serializers.CharField(help_text="模版名称", required=False)
    templateCategory = serializers.CharField(help_text="模版分类", required=False)
    template_description = serializers.CharField(help_text="模版藐视", required=False)
    template_statement = serializers.CharField(help_text="模版语句", required=False)
    page_index = serializers.IntegerField(help_text="页码", required=False, default=1)
    page_size = serializers.IntegerField(help_text="每页最大小", required=False, default=20)
    sort_fields = serializers.ListField(
        child=serializers.CharField(default="-update_time"),
        help_text="排序字段",
        required=False,
        default=["-update_time"],
    )


class AlarmRuleTemplateAddSerializer(SerializerMixin):

    template_name = serializers.CharField(help_text="模版名称", required=True)
    template_category = serializers.CharField(help_text="模版分类", required=True)
    template_statement = serializers.CharField(help_text="模版语句", required=True)
    template_description = serializers.CharField(help_text="模版描述", required=True)
    template_type = serializers.ChoiceField(
        choices=[AlarmRuleTypeEnum.CUSTOMIZE.value], default=AlarmRuleTypeEnum.CUSTOMIZE.value
    )

    def validate(self, attrs):
        template_name = attrs.get("template_name")

        count = alarm_rule_template_service.get_alarm_rule_template_count(template_name=template_name)
        if count:
            raise serializers.ValidationError("模版名称重复")
        return attrs


class AlarmRuleTemplateUpdateSerializer(AlarmRuleTemplateAddSerializer):

    template_id = serializers.CharField(help_text="模版ID", required=True)

    def validate(self, attrs):
        template_name = attrs["template_name"]
        template_id = attrs["template_id"]
        template_info = alarm_rule_template_service.get_alarm_rule_template_info(template_id=template_id)

        count = alarm_rule_template_service.get_alarm_rule_template_count(template_name=template_name)
        if count:
            if template_info.template_name != template_name:
                raise serializers.ValidationError("规则名称已经存在")
        return attrs
