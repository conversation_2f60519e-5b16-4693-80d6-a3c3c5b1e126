from rest_framework import fields
from rest_framework.exceptions import ValidationError

from caasm_aql.aql import AQL_TYPES
from caasm_aql.base import AsqlType
from caasm_tool.constants import DATETIME_FORMAT
from caasm_tool.patch.serializer import SerializerMixin


class AssetAqlCollectQuerySerializer(SerializerMixin):
    page_size = fields.IntegerField(max_value=100, min_value=1, default=20, allow_null=True, help_text="分页条数")
    page_index = fields.IntegerField(min_value=1, default=1, allow_null=True, help_text="分页索引，从1开始")
    sort_fields = fields.ListField(child=fields.CharField(), default=[])
    keyword = fields.CharField(max_length=4097, default="")


class AqlSceneQuerySerializer(SerializerMixin):
    page_size = fields.IntegerField(max_value=100, min_value=1, default=20, allow_null=True, help_text="分页条数")
    page_index = fields.IntegerField(min_value=1, default=1, allow_null=True, help_text="分页索引，从1开始")
    sort_fields = fields.ListField(child=fields.CharField(), default=[])


class AssetAqlCollectDeleteSerializer(SerializerMixin):
    collect_ids = fields.ListField(child=fields.CharField(max_length=64), min_length=1)


class AssetAqlHistoryListRequestSerializer(SerializerMixin):
    page_size = fields.IntegerField(max_value=100, min_value=1, default=20, allow_null=True, help_text="分页条数")
    page_index = fields.IntegerField(min_value=1, default=1, allow_null=True, help_text="分页索引，从1开始")
    keyword = fields.CharField(max_length=4097, default="")
    sort_fields = fields.ListField(child=fields.CharField(max_length=128), default=["-create_time"])


class AssetAqlHistoryListResponseSerializer(SerializerMixin):
    aql = fields.CharField(max_length=4098)
    history_id = fields.SerializerMethodField("get_history_id")
    adapters = fields.SerializerMethodField("find_adapter")
    aql_type = fields.CharField()
    create_time = fields.DateTimeField(format=DATETIME_FORMAT)

    @classmethod
    def get_history_id(cls, obj):
        return str(obj.id)

    def find_adapter(self, obj):
        adapter_names = obj.adapter_names
        if not adapter_names:
            return "所有资产"
        adapter_name_result = [self.context[i].display_name for i in adapter_names if self.context.get(i)]
        return ",".join(adapter_name_result)


class AssetAqlHistoryDeleteRequestSerializer(SerializerMixin):
    history_ids = fields.ListField(child=fields.CharField(max_length=32), required=False, max_length=1000)


class AssetAqlCollectCreateSerializer(SerializerMixin):
    aql = fields.CharField(max_length=4096, required=True, min_length=1)
    name = fields.CharField(max_length=128, required=True)
    tag = fields.CharField(max_length=128, required=False, default="")
    aql_type = fields.ChoiceField(choices=AQL_TYPES, default=AsqlType.ASQL.value)

    def validate_aql(self, value):
        value = value.strip()
        if not value:
            raise ValidationError("aql格式无效")
        return value


class AssetAqlCollectListResponseSerializer(SerializerMixin):
    name = fields.CharField(help_text="收藏名称")
    tag = fields.CharField(help_text="标签")
    aql = fields.SerializerMethodField("get_aql", help_text="aql语句")
    adapters = fields.SerializerMethodField("find_adapter", help_text="资产源")
    collect_id = fields.SerializerMethodField("get_collect_id", help_text="收藏记录ID")
    aql_type = fields.CharField()

    def get_aql(self, obj):
        statement = self.context["statement"].get(obj.retrieve_statement_id)
        return statement.aql if statement else ""

    def find_adapter(self, obj):
        statement = self.context["statement"].get(obj.retrieve_statement_id)
        adapter_names = statement.adapter_names if statement else []
        result = []
        for adapter_name in adapter_names:
            adapter = self.context["adapter"].get(adapter_name)
            if not adapter:
                continue
            result.append(adapter.display_name)

        return ",".join(result) if result else "所有资产"

    @classmethod
    def get_collect_id(cls, obj):
        return str(obj.id)
