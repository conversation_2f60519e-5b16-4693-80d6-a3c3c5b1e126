from caasm_tool.patch.router import DefaultRouter
from caasm_webapi.app.user.view.asset import (
    AssetAqlHistoryListAPI,
    AssetAqlHistoryDeleteAPI,
    AssetAqlCollectDeleteView,
    AssetAqlCollectListView,
    AssetAqlCollectAddAPI,
)

user_router = DefaultRouter("user")

user_router.join_path("asset/aqlHistory/list/", AssetAqlHistoryListAPI, name="AssetAqlHistoryListAPI")
user_router.join_path("asset/aqlHistory/delete/", AssetAqlHistoryDeleteAPI, name="AssetAqlHistoryDeleteAPI")

user_router.join_path("asset/aqlCollect/add/", AssetAqlCollectAddAPI, name="AssetAqlCollectAPI")
user_router.join_path("asset/aqlCollect/list/", AssetAqlCollectListView, name="AssetAqlCollectListView")
user_router.join_path("asset/aqlCollect/delete/", AssetAqlCollectDeleteView, name="AssetAqlCollectDeleteView")
