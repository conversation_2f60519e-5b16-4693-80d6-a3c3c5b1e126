import logging

from rest_framework.views import APIView

from caasm_aql.queriers.es import ESLogicalGroupQuerier
from caasm_service.runtime import (
    user_asset_aql_history_service,
    adapter_service,
    retrieve_statement_service,
    user_asset_aql_collect_service,
)
from caasm_tool.util import deduplicate
from caasm_webapi.app.user.serializer.asset import (
    AssetAqlHistoryListRequestSerializer,
    AssetAqlHistoryListResponseSerializer,
    AssetAqlHistoryDeleteRequestSerializer,
    AssetAqlCollectCreateSerializer,
    AssetAqlCollectQuerySerializer,
    AssetAqlCollectListResponseSerializer,
    AssetAqlCollectDeleteSerializer,
)
from caasm_webapi.util.response import build_failed, ResponseCode, build_success, build_page_result
from caasm_webapi.util.tool import get_user_id

log = logging.getLogger()


class AssetAqlHistoryListAPI(APIView):
    def get(self, request):
        serializer = AssetAqlHistoryListRequestSerializer(data=request.query_params)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)

        validated_data = serializer.validated_data
        keyword = validated_data.get("keyword")
        page_index = validated_data.get("page_index", 0)
        page_size = validated_data.get("page_size", 20)
        sort_fields = validated_data.get("sort_fields")

        user_id = get_user_id(request)

        count = user_asset_aql_history_service.get_user_aql_history_count(user_id, keyword)
        if not count:
            return build_success(build_page_result())

        asset_aql_history_cur = user_asset_aql_history_service.find_user_aql_history(
            user_id=user_id,
            keyword=keyword,
            sort_fields=sort_fields,
            offset=page_index * page_size,
            limit=page_size,
        )

        asset_aql_history = list(asset_aql_history_cur)
        adapter_names = []
        for i in asset_aql_history:
            adapter_names.extend(i.adapter_names)
        adapter_names = deduplicate(adapter_names)
        adapter_mapper = adapter_service.find_adapter_to_mapper(names=adapter_names) if adapter_names else {}

        data = AssetAqlHistoryListResponseSerializer(asset_aql_history, many=True, context=adapter_mapper).data
        page_result = build_page_result(total=count, result=data)
        return build_success(page_result)


class AssetAqlHistoryDeleteAPI(APIView):
    def post(self, request):
        serializer = AssetAqlHistoryDeleteRequestSerializer(data=request.data)

        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)

        user_id = get_user_id(request)
        if not user_id:
            return build_success()

        validated_data = serializer.validated_data
        history_ids = validated_data.get("history_ids")

        user_asset_aql_history_service.delete_user_aql_history(user_id, history_ids=history_ids)
        return build_success()


class AssetAqlCollectAddAPI(APIView):
    def post(self, request):
        serializer = AssetAqlCollectCreateSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)

        validated_data = serializer.validated_data
        aql = validated_data["aql"]

        dataset = []
        try:
            dataset = ESLogicalGroupQuerier.parse_dateset_exclude_inventory_by_aql(aql)
        except Exception as e:
            log.warning(f"parse aql({aql}) error({e})")

        user_id = get_user_id(request)
        if not user_id:
            return build_success()
        try:
            self._create_aql_collect(user_id, adapter_names=dataset, **validated_data)
        except Exception as e:
            log.warning(f"Create aql collect error({e})")
        return build_success()

    @classmethod
    def _create_aql_collect(cls, user_id, aql, name, adapter_names=None, tag=None, aql_type=None):
        aql_digest = retrieve_statement_service.get_digest(aql)
        aql_scene = retrieve_statement_service.get_statement(digest=aql_digest)
        if not aql_scene:
            _ret = retrieve_statement_service.save_statement(aql=aql, digest=aql_digest, adapter_names=adapter_names)
            aql_scene_id = _ret.inserted_id
        else:
            aql_scene_id = aql_scene.id

        if not aql_scene_id:
            raise ValueError("语句获取失败")

        user_asset_sql_collect = user_asset_aql_collect_service.get_user_asset_aql_collect(user_id, name=name)
        if user_asset_sql_collect:
            user_asset_sql_collect.retrieve_statement_id = aql_scene_id
            if aql_type:
                user_asset_sql_collect.aql_type = aql_type
            if tag:
                user_asset_sql_collect.tag = tag
            if name:
                user_asset_sql_collect.name = name
            user_asset_aql_collect_service.update(user_asset_sql_collect)
        else:
            user_asset_aql_collect_service.save_user_asset_aql_collect_service(
                user_id, name, tag, aql_scene_id, aql_type=aql_type
            )


class AssetAqlCollectListView(APIView):
    def get(self, request):
        data = request.query_params
        serializer = AssetAqlCollectQuerySerializer(data=data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)

        user_id = get_user_id(request)

        validated_data = serializer.validated_data
        page_index = validated_data.get("page_index", 0)
        page_size = validated_data.get("page_size", 20)
        sort_fields = validated_data.get("sort_fields", None)
        keyword = validated_data.get("keyword")

        count = user_asset_aql_collect_service.get_user_asset_aql_collect_count(user_id=user_id, keyword=keyword)
        if not count:
            return build_success(build_page_result())

        offset = page_index * page_size

        cur = user_asset_aql_collect_service.find_user_asset_aql_collect(
            user_id, keyword, offset=offset, limit=page_size, sort_fields=sort_fields
        )
        user_asset_aql_collect_list = list(cur)

        _sids = [i.retrieve_statement_id for i in user_asset_aql_collect_list]
        statement_mapper = retrieve_statement_service.find_retrieve_aql_to_mapper(retrieve_ids=_sids) if _sids else {}

        adapter_names = []
        for _sid, _statement in statement_mapper.items():
            adapter_names.extend(_statement.adapter_names)
        adapter_names = deduplicate(adapter_names)
        adapter_mapper = adapter_service.find_adapter_to_mapper(names=adapter_names) if adapter_names else {}

        ctx = {"adapter": adapter_mapper, "statement": statement_mapper}
        data = AssetAqlCollectListResponseSerializer(user_asset_aql_collect_list, many=True, context=ctx).data

        return build_success(build_page_result(count, result=data))


class AssetAqlCollectDeleteView(APIView):
    def post(self, request):
        serializer = AssetAqlCollectDeleteSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)

        validated_data = serializer.validated_data
        user_id = get_user_id(request)
        if not user_id:
            return build_success()

        user_asset_aql_collect_service.delete_user_asset_aql_collect(user_id, **validated_data)
        return build_success()
