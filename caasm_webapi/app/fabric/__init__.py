from caasm_tool.patch.router import DefaultRouter
from caasm_webapi.app.fabric.views.config import FabricConfigGetAPI, FabricConfigSetAPI, FabricNextRunTimeGetAPI
from caasm_webapi.app.fabric.views.convert import ConvertTestAP<PERSON>
from caasm_webapi.app.fabric.views.task import TaskList<PERSON><PERSON>, TaskGraphAPI, TaskCancelAPI, TaskStartAPI, TaskDetailAPI

fabric_router = DefaultRouter("fabric/")
fabric_router.join_path("record/detail/", TaskDetailAPI)
fabric_router.join_path("record/list/", TaskListAPI)
fabric_router.join_path("record/graph/", TaskGraphAPI)
fabric_router.join_path("record/cancel/", TaskCancelAPI)
fabric_router.join_path("record/start/", TaskStartAPI)

fabric_router.join_path("config/get/", FabricConfigGetAPI)
fabric_router.join_path("config/set/", FabricConfigSetAPI)
fabric_router.join_path("nextRunTime/", FabricNextRunTimeGetAPI)

fabric_router.join_path("convert/test/", ConvertTestAPI)
