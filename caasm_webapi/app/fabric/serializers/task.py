from rest_framework import serializers

from caasm_service.constants.workflow import WOR<PERSON><PERSON>OW_STATUS_MAPPER, WorkflowStatus
from caasm_tool.constants import DATETIME_FORMAT
from caasm_tool.patch.serializer import SerializerMixin, DynamicField


class TaskListRequestSerializer(SerializerMixin):
    page_index = serializers.IntegerField(required=False, default=1, min_value=1)
    page_size = serializers.IntegerField(required=False, default=10, min_value=1, max_value=300)
    sort_fields = serializers.ListField(
        child=serializers.CharField(), default=lambda: ["-create_time", "-start_time", "-_id"], required=False
    )


class TaskGraphRequestSerializer(SerializerMixin):
    task_id = serializers.CharField(required=True)


class TaskSerializer(SerializerMixin):
    task_id = serializers.CharField(source="id")
    name = serializers.SerializerMethodField("get_name")
    start_time = serializers.SerializerMethodField("get_start_time")
    finish_time = serializers.SerializerMethodField("get_finish_time")
    params = DynamicField()
    status = serializers.SerializerMethodField("get_status")
    status_display_name = serializers.SerializerMethodField("get_status_display_name")
    result = serializers.SerializerMethodField("get_result")
    notice = serializers.SerializerMethodField("get_notice")
    finished = serializers.BooleanField()
    create_time = serializers.DateTimeField(format=DATETIME_FORMAT)
    cost_time = serializers.SerializerMethodField("get_cost_time")

    @classmethod
    def get_result(cls, obj):
        return ""

    @classmethod
    def get_name(cls, obj):
        return obj.display_name or obj.name

    @classmethod
    def get_status_display_name(cls, obj):
        return WORKFLOW_STATUS_MAPPER[obj.status]

    @classmethod
    def get_start_time(cls, obj):
        return obj.start_time.strftime(DATETIME_FORMAT) if obj.start_time else ""

    @classmethod
    def get_finish_time(cls, obj):
        return obj.finish_time.strftime(DATETIME_FORMAT) if obj.finish_time else ""

    @classmethod
    def get_notice(cls, obj):
        return getattr(obj, "error", "")

    @classmethod
    def get_cost_time(cls, obj):
        total_second = 0
        res = ""

        start_time = obj.start_time
        finish_time = obj.finish_time

        if finish_time and start_time:
            total_second = int((finish_time - start_time).total_seconds())

        if obj.finished:
            second = total_second % 60
            minute = total_second // 60 % 60
            hour = total_second // 3600
            if hour:
                res += f"{hour}小时"
            if minute:
                res += f"{minute}分钟"
            if second:
                res += f"{second}秒"

        return res

    @classmethod
    def get_status(cls, obj):
        value = obj.status.value
        if value == WorkflowStatus.SLEEP:
            value = "unknown"
        if value == WorkflowStatus.WAIT_CANCEL:
            value = "cancel"

        return value
