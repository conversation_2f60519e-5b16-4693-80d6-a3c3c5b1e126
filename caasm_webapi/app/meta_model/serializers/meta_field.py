import logging
import traceback

from rest_framework import serializers
from rest_framework_recursive.fields import Recursive<PERSON>ield

from caasm_service.entity.meta_model import MetaFieldType, TYPE_NAME_MAPPER
from caasm_tool.constants import DATETIME_FORMAT
from caasm_tool.patch.serializer import SerializerMixin, DynamicField

log = logging.getLogger()


# basic
class RuleFieldSerializer(SerializerMixin):
    _type_name_mapping = {
        MetaFieldType.STRING: ["choices", "lengthRange"],
        MetaFieldType.FLOAT: ["choices", "valueRange"],
        MetaFieldType.INT: ["choices", "valueRange"],
        MetaFieldType.ENUM: ["enum"],
    }

    _type_py_type_mapping = {MetaFieldType.STRING: (str,), MetaFieldType.FLOAT: (float,), MetaFieldType.INT: (int,)}

    def __init__(self, *args, **kwargs):
        super(RuleFieldSerializer, self).__init__(*args, **kwargs)

        self.__validate_setting_mapper = {
            "choices": self.__validate_choices_setting,
            "lengthRange": self.__validate_length_range_setting,
            "enum": self.__validate_enum_setting,
            "valueRange": self.__validate_value_range_setting,
        }

    name = serializers.CharField(required=True, help_text="名称")
    setting = serializers.DictField(required=False, help_text="配置信息", allow_null=True, default=dict)

    def validate_name(self, name):
        if name not in self._type_name_mapping[self.context["type"]]:
            raise serializers.ValidationError("规则名称无效")
        return name

    def validate(self, attrs):
        attrs = super(RuleFieldSerializer, self).validate(attrs)
        self._validate_setting(attrs)
        return attrs

    def _validate_setting(self, attrs):
        setting_validate_method = self.__validate_setting_mapper.get(attrs["name"])
        if not setting_validate_method:
            raise serializers.ValidationError("无效的校验规则")

        setting = attrs["setting"] or {}
        if not setting:
            attrs["setting"] = setting
            return
        setting_validate_method(attrs["setting"])

    @classmethod
    def __validate_value_range_setting(cls, setting):
        cls.__validate_key_core("min", (int, float), setting, "值的范围")
        cls.__validate_key_core("max", (int, float), setting, "值的范围")

        min_ = setting.get("min")
        max_ = setting.get("max")
        if min_ is not None and max_ is not None:
            if min_ > max_:
                raise serializers.ValidationError("值的范围大小最小值不能大于最大值")

    def __validate_choices_setting(self, setting):
        values = setting["values"]
        if not isinstance(values, list):
            raise serializers.ValidationError("选择数据的格式不正确")
        type_defines = self._type_py_type_mapping.get(self.context["type"])
        if not type_defines:
            raise serializers.ValidationError("选择数据对应的类型未定义")

        for value in values:
            if not isinstance(value, type_defines):
                raise serializers.ValidationError("选择数据类型不匹配")

    @classmethod
    def __validate_enum_setting(cls, setting):
        if not setting:
            raise serializers.ValidationError("枚举字段缺失校验规则")
        for key, value in setting.item():
            if not isinstance(key, str):
                raise serializers.ValidationError("枚举字段键类型格式无效")
            if not isinstance(value, int):
                raise serializers.ValidationError("枚举字段键类型格式无效")

    @classmethod
    def __validate_length_range_setting(cls, setting):
        cls.__validate_key_core("start", (int, float), setting, "长度范围")
        cls.__validate_key_core("end", (int, float), setting, "长度范围")

        start = setting.get("start")
        end = setting.get("end")
        if start is not None and end is not None:
            if start > end:
                raise serializers.ValidationError("长度范围起始值不能结束最大值")

    @classmethod
    def __validate_key_core(cls, key_name, key_types, settings, error_prefix=""):
        if key_name in settings:
            raise serializers.ValidationError(f"{error_prefix}缺失必要参数({key_name})")
        if not isinstance(key_name, key_types):
            raise serializers.ValidationError(f"{error_prefix}参数({key_name})格式不正确")


##########Request##############


class FieldRequestSerializer(SerializerMixin):
    _rule_valid_types = (MetaFieldType.STRING, MetaFieldType.INT, MetaFieldType.ENUM, MetaFieldType.FLOAT)

    def __init__(self, *args, **kwargs):
        super(FieldRequestSerializer, self).__init__(*args, **kwargs)
        self._handle_children_method = {
            MetaFieldType.LIST: self._handle_children_by_list,
            MetaFieldType.OBJECT: self._handle_children_by_object,
        }

    type = serializers.ChoiceField(help_text="字段类型", choices=MetaFieldType._value2member_map_, required=True)
    name = serializers.CharField(help_text="字段名称", max_length=128, required=True)
    display_name = serializers.CharField(help_text="字段展示名称", max_length=128, required=True)
    description = serializers.CharField(help_text="描述信息", max_length=512, required=False, allow_null=True)
    required = serializers.BooleanField(help_text="是否必传", default=False)
    allow_null = serializers.BooleanField(help_text="是否为空", default=True)
    encrypt = serializers.BooleanField(help_text="是否涉密", default=False)
    unique = serializers.BooleanField(help_text="值是否唯一", default=False)
    default = DynamicField(help_text="默认值", required=False, default=None, allow_null=True)
    children = serializers.ListField(child=RecursiveField(), help_text="子字段列表", required=False, default=list)
    full_text_search = serializers.BooleanField(help_text="是否支持全文检索", required=False, default=False)
    rules = serializers.ListSerializer(
        child=serializers.DictField(), help_text="规则列表", required=False, default=list, allow_null=True
    )

    def validate(self, attrs):
        attrs = super(FieldRequestSerializer, self).validate(attrs)
        self._handle_children(attrs)
        self._handle_rules(attrs)
        return attrs

    def _handle_rules(self, attrs):
        rules = attrs["rules"] or []
        attrs["rules"] = rules

        type_ = attrs["type"]
        if type_ not in self._rule_valid_types:
            attrs["rules"] = []

        if type_ == MetaFieldType.ENUM:
            if not rules:
                raise serializers.ValidationError("枚举类型缺失规则")

        rules_serializer = RuleFieldSerializer(data=rules, many=True, context={"type": type_})
        try:
            rules_serializer.is_valid(raise_exception=True)
        except Exception as e:
            log.warning(traceback.format_exc())
            raise serializers.ValidationError("规则信息配置失败")

        new_rules = rules_serializer.validated_data
        attrs["rules"] = new_rules

    # 处理子字段
    def _handle_children(self, attrs):
        type_ = attrs.get("type")
        _validate_children_method = self._handle_children_method.get(type_, self._handle_children_by_default)
        _validate_children_method(attrs)

    @classmethod
    def _handle_children_by_object(cls, attrs):
        children = attrs.get("children") or []

        child_names = []

        for child in children:
            name = child.get("name")
            if name in child_names:
                raise serializers.ValidationError(f"子字段({name})重复")
            child_names.append(name)

    def _handle_children_by_list(self, attrs):
        request_children = attrs.get("children")

        if request_children and len(request_children) != 1:
            raise serializers.ValidationError("当上级字段类型是列表时，当前字段个数不能超过1个")

        if not request_children:
            attrs["children"] = self._build_default_list_child()

        request_child = attrs["children"][0]

        if request_child["type"] == MetaFieldType.LIST:
            raise serializers.ValidationError("当上级字段类型是列表时，当前字段类型不能为列表")

    @classmethod
    def _handle_children_by_default(cls, attrs):
        attrs["children"] = []

    @classmethod
    def _build_default_list_child(cls):
        return [
            {
                "type": MetaFieldType.ANY,
                "name": "item",
                "display_name": "元素",
                "default": None,
                "description": "",
                "required": False,
                "allow_null": True,
                "encrypt": False,
                "unique": False,
                "children": [],
                "internal": False,
            }
        ]


class FieldRequestDeleteSerializer(SerializerMixin):
    field_ids = serializers.ListField(
        child=serializers.CharField(max_length=24, min_length=24), required=True, min_length=1, help_text="字段ID集合"
    )
    model_id = serializers.CharField(max_length=24, min_length=24, required=True, help_text="模型ID")


class FieldRequestListSerializer(SerializerMixin):
    page_index = serializers.IntegerField(required=False, default=1)
    page_size = serializers.IntegerField(required=False, default=20, max_value=200)
    sort_fields = serializers.ListField(child=serializers.CharField(), default=list, required=False)
    model_id = serializers.CharField(max_length=24, min_length=24, help_text="模型ID")
    keyword = serializers.CharField(max_length=256, required=False, help_text="关键字")
    internal = serializers.BooleanField(required=False, default=None, help_text="是否内置")


class FieldListByFieldTypeSerializer(SerializerMixin):
    field_type = serializers.ListField(child=serializers.CharField())
    keyword = serializers.CharField(max_length=256, required=False, help_text="关键字")
    internal = serializers.BooleanField(required=False, default=None, help_text="是否内置")
    model_id = serializers.CharField(max_length=24, min_length=24, help_text="模型ID")


#### Response


class RuleSerializer(SerializerMixin):
    name = serializers.CharField()
    display_name = serializers.CharField()
    setting = serializers.DictField()


class FieldResponseSerializer(SerializerMixin):
    field_id = serializers.CharField(source="id")
    type = serializers.SerializerMethodField("get_type", help_text="类型")
    type_desc = serializers.SerializerMethodField("get_type_desc", help_text="类型描述")
    name = serializers.CharField(help_text="字段名称")
    full_name = serializers.CharField(help_text="字段全名称")
    display_name = serializers.CharField(help_text="字段展示名称")
    description = serializers.CharField(help_text="描述信息")
    required = serializers.BooleanField(help_text="是否必传")
    allow_null = serializers.BooleanField(help_text="是否为空")
    encrypt = serializers.BooleanField(help_text="是否涉密")
    unique = serializers.BooleanField(help_text="值是否唯一")
    model_id = serializers.CharField(help_text="模型ID")
    default = DynamicField(help_text="默认值")
    full_text_search = serializers.BooleanField()
    internal = serializers.BooleanField(help_text="内置字段")
    internal_desc = serializers.SerializerMethodField("get_internal_desc", help_text="内置描述")
    children = serializers.ListField(child=RecursiveField(), help_text="子字段列表")
    create_time = serializers.DateTimeField(format=DATETIME_FORMAT)
    update_time = serializers.DateTimeField(format=DATETIME_FORMAT)
    rules = serializers.ListSerializer(child=RuleSerializer(), help_text="校验规则")

    def get_type(self, meta_field):
        return meta_field.type.value

    def get_internal_desc(self, meta_field):
        return self.internal_mapper.get(meta_field.internal)

    def get_type_desc(self, meta_field):
        type = meta_field.type
        type_desc = TYPE_NAME_MAPPER.get(meta_field.type)
        if type == MetaFieldType.LIST:
            child = meta_field.children[0]
            child_type_desc = TYPE_NAME_MAPPER.get(child.type, "")
            type_desc = type_desc + ":" + child_type_desc

        return type_desc

    internal_mapper = {True: "是", False: "否"}


class FieldShowResponseSerializer(FieldResponseSerializer):
    full_name = serializers.CharField(help_text="字段全名")
    full_display_name = serializers.CharField(help_text="字段展示全程")


class FieldChooseSerializer(SerializerMixin):
    label = serializers.CharField(help_text="展示名称")
    value = serializers.CharField(help_text="结果")
