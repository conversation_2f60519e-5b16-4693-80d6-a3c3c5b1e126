from rest_framework import serializers

from caasm_config.config import caasm_config
from caasm_service.entity.meta_model import MetaModelType
from caasm_service.runtime import meta_model_service
from caasm_tool.constants import DATETIME_FORMAT
from caasm_tool.patch.serializer import SerializerMixin
from caasm_tool.util import deduplicate
from caasm_webapi.util.tool import Lock


##########Request##############


class MetaModelDeleteSerializer(SerializerMixin):
    model_ids = serializers.ListSerializer(child=serializers.CharField(), help_text="模型ID", required=True, min_length=1)


class MetaModelPreAddSerializer(SerializerMixin):
    type = serializers.ChoiceField(required=True, choices=MetaModelType._value2member_map_)


class MetaModelRequestListSerializer(SerializerMixin):
    page_index = serializers.IntegerField(required=False, default=1)
    page_size = serializers.IntegerField(required=False, default=20, max_value=200)
    sort_fields = serializers.ListField(child=serializers.Char<PERSON>ield(), default=list, required=False)
    keyword = serializers.CharField(max_length=256, required=False, help_text="关键字")
    internal = serializers.BooleanField(required=False, default=None, help_text="是否内置", allow_null=True)


class MetaModelModifySerializer(SerializerMixin):
    model_id = serializers.CharField(required=True, help_text="主键", max_length=24, min_length=24)
    name = serializers.CharField(required=True, help_text="名称", max_length=128)
    display_name = serializers.CharField(required=True, help_text="展示名称", max_length=256)
    category = serializers.CharField(required=False, help_text="标签", allow_null=True)
    description = serializers.CharField(required=False, help_text="描述信息", max_length=512, allow_null=True)
    friends = serializers.ListSerializer(
        child=serializers.CharField(max_length=24, min_length=24), required=False, allow_null=True
    )

    def validate(self, attrs):
        attrs = super(MetaModelModifySerializer, self).validate(attrs)
        attrs["type"] = self.model_type
        attrs["init"] = False
        self.validate_customer(attrs)
        self.validate_basic(attrs)
        return attrs

    def validate_customer(self, attrs):
        pass

    def validate_model_id(self, model_id):
        if not meta_model_service.get_meta_model_count(model_id=model_id, model_type=self.model_type):
            raise serializers.ValidationError("主键信息无效")
        return model_id

    def validate_basic(self, attrs):
        name = attrs.get("name")
        model_id = attrs.get("model_id")

        with Lock() as lock:
            lock_res = lock.acquire(f"{self.__class__}.{self.model_type}.{name}")
            if not lock_res:
                raise serializers.ValidationError("请勿重复提交")
        model_count = meta_model_service.get_meta_model_count(model_type=self.model_type, name=name, model_nid=model_id)
        if model_count:
            raise serializers.ValidationError("名称已存在")

    @property
    def model_type(self):
        raise NotImplementedError


class MetaModelFieldSetModifySerializer(MetaModelModifySerializer):
    @property
    def model_type(self):
        return MetaModelType.FIELD_SET

    def validate_customer(self, attrs):
        attrs["friends"] = []
        attrs["category"] = None


class MetaModelApplicationModifySerializer(MetaModelModifySerializer):
    category_keys = [key for key, val in caasm_config.META_MODEL_CATEGORY_MAPPER.items() if not val["hidden"]]

    def validate_customer(self, attrs):
        friends = attrs.get("friends") or []
        category = attrs.get("category")

        if category not in self.category_keys:
            raise serializers.ValidationError("无效的分类信息")

        friends = deduplicate(friends)

        if friends:
            meta_models = meta_model_service.find_meta_model(
                model_type=MetaModelType.FIELD_SET, model_ids=friends, fields=["_id"]
            )
        else:
            meta_models = []
        new_friends = []

        for meta_model in meta_models:
            new_friends.append(meta_model.id)

        attrs["friends"] = new_friends

    @property
    def model_type(self):
        return MetaModelType.APPLICATION


class MetaModelDetailSerializer(SerializerMixin):
    model_id = serializers.CharField(max_length=24, min_length=24, required=True, help_text="模型ID")

    def validate_model_id(self, mode_id):
        if not meta_model_service.get_meta_model_count(model_id=mode_id, model_type=MetaModelType.APPLICATION):
            raise serializers.ValidationError("无效的模型ID")
        return mode_id


# # Response
class MetaModelTotalFieldSerializer(SerializerMixin):
    label = serializers.CharField(source="title")
    value = serializers.CharField(source="full_name")


class MetaModelResponseSerializer(SerializerMixin):
    name = serializers.CharField()
    display_name = serializers.CharField()
    description = serializers.CharField()
    internal = serializers.BooleanField()
    internal_desc = serializers.SerializerMethodField("get_internal_desc")
    friends = serializers.SerializerMethodField("get_friends")
    model_id = serializers.CharField(source="id")
    category = serializers.CharField()
    category_desc = serializers.SerializerMethodField("get_category_desc")
    create_time = serializers.DateTimeField(format=DATETIME_FORMAT)
    update_time = serializers.DateTimeField(format=DATETIME_FORMAT)

    def get_internal_desc(self, meta_model):
        internal = meta_model.internal
        return self.internal_mapper.get(internal)

    def get_friends(self, meta_model):
        friends = meta_model.friends
        if friends:
            return [{"id": str(friend), "name": self.context.get(friend)} for friend in friends]
        else:
            return []

    def get_category_desc(self, meta_model):
        return self.category_mapper.get(meta_model.category)

    internal_mapper = {True: "是", False: "否"}
    category_mapper = {k: v["display_name"] for k, v in caasm_config.META_MODEL_CATEGORY_MAPPER.items()}
