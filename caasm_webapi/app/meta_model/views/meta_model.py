import uuid
from collections import defaultdict

from rest_framework.views import APIView

from caasm_config.config import caasm_config
from caasm_meta_data.manager import MetaDataManager
from caasm_service.entity.meta_model import MetaModelType, MetaFieldType
from caasm_service.runtime import meta_model_service, meta_field_service
from caasm_webapi.app.meta_model.serializers.meta_field import FieldShowResponseSerializer, FieldChooseSerializer
from caasm_webapi.app.meta_model.serializers.meta_model import (
    MetaModelPreAddSerializer,
    MetaModelFieldSetModifySerializer,
    MetaModelApplicationModifySerializer,
    MetaModelRequestListSerializer,
    MetaModelResponseSerializer,
    MetaModelDeleteSerializer,
    MetaModelDetailSerializer,
    MetaModelTotalFieldSerializer,
)
from caasm_webapi.util.response import build_failed, ResponseCode, build_success


class MetaModelApplication(APIView):
    @property
    def model_type(self):
        return MetaModelType.APPLICATION


class MetaModelFieldSet(APIView):
    @property
    def model_type(self):
        return MetaModelType.FIELD_SET


class MetaModelPreAddAPIView(APIView):
    def post(self, request):
        request_data = request.data

        serializer = MetaModelPreAddSerializer(data=request_data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)

        request_data = serializer.validated_data
        request_data["init"] = True
        request_data["internal"] = False
        request_data["name"] = str(uuid.uuid4())
        save_response = meta_model_service.save_direct(request_data)
        if not save_response.flag:
            return build_failed(ResponseCode.SYSTEM_ERROR, message="模型加载失败")
        return build_success(str(save_response.inserted_id))


class MetaModelChooseApiView(MetaModelApplication):
    def get(self, request):
        data = meta_model_service.find_meta_model(model_type=self.model_type, init=False)
        result = [{"label": item.display_name, "value": item.id} for item in data]
        return build_success(FieldChooseSerializer(instance=result, many=True).data)


class MetaModelModifyAPIView(APIView):
    serializer_class = None

    def post(self, request):
        serializer = self.serializer_class(data=request.data, context={"model_type": self.model_type})
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)

        validated_data = serializer.validated_data
        model_id = validated_data.pop("model_id")
        modify_res = meta_model_service.update_meta_model(model_id=model_id, internal=False, values=validated_data)
        if not modify_res.flag:
            return build_failed(ResponseCode.SYSTEM_ERROR, "修改失败")

        meta_model = meta_model_service.get_meta_model(
            model_id, model_type=self.model_type, fields=["_id", "name", "display_name"]
        )
        self.padding_fields(meta_model)
        return build_success()

    @classmethod
    def padding_fields(cls, meta_model):
        meta_fields = meta_field_service.find_meta_field(
            model_id=meta_model.id, fields=["name", "type", "children", "display_name"]
        )

        meta_field_mappers = [meta_field_service.dump_mapper(meta_field) for meta_field in meta_fields]
        MetaDataManager.padding_field(meta_model.display_name, meta_model.name, meta_field_mappers)
        meta_field_service.update_stream_direct(meta_field_mappers)

    @property
    def model_type(self):
        raise NotImplementedError


class MetaModelListAPIView(APIView):
    def build_result(self, validate_data):
        page_index = validate_data.pop("page_index")
        page_size = validate_data.pop("page_size")
        sort_fields = validate_data.pop("sort_fields")

        validate_data["model_type"] = self.model_type
        validate_data["init"] = False

        model_count = meta_model_service.get_meta_model_count(**validate_data)
        total = []
        if model_count:
            offset = page_index * page_size
            meta_models = list(
                meta_model_service.find_meta_model(
                    **validate_data, offset=offset, limit=page_size, sort_fields=sort_fields
                )
            )
            friends, friend_mapper = set(), {}
            for meta_model in meta_models:
                if meta_model.friends:
                    friends = friends.union(meta_model.friends)
            if friends:
                friend_meta_models = meta_model_service.find_meta_model(
                    model_ids=list(friends), fields=["display_name"]
                )
                friend_mapper = {
                    friend_meta_model.id: friend_meta_model.display_name for friend_meta_model in friend_meta_models
                }

            total = MetaModelResponseSerializer(instance=meta_models, many=True, context=friend_mapper).data
        return {"total": total, "count": model_count}

    def get(self, request):
        serializer = MetaModelRequestListSerializer(data=request.query_params)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)

        validate_data = serializer.validated_data
        result = self.build_result(validate_data)

        return build_success(result)

    @property
    def model_type(self):
        raise NotImplementedError


class MetaModelDeleteAPIView(APIView):
    def post(self, request):
        serializer = MetaModelDeleteSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)

        validate_data = serializer.validated_data
        validate_data["model_type"] = self.model_type
        validate_data["init"] = False
        validate_data["internal"] = False

        meta_model_service.delete_meta_model(**validate_data)
        return build_success()

    @property
    def model_type(self):
        raise NotImplementedError


class MetaModelFieldSetModifyAPIView(MetaModelFieldSet, MetaModelModifyAPIView):
    serializer_class = MetaModelFieldSetModifySerializer


class MetaModelApplicationModifyAPIView(MetaModelApplication, MetaModelModifyAPIView):
    serializer_class = MetaModelApplicationModifySerializer


class MetaModelFieldSetListAPIView(MetaModelFieldSet, MetaModelListAPIView):
    def build_result(self, validate_data):
        result = super(MetaModelFieldSetListAPIView, self).build_result(validate_data)
        total = result["total"]
        model_ids = [i["model_id"] for i in total]
        if model_ids:
            application_models = meta_model_service.find_meta_model(
                friends=model_ids, model_type=MetaModelType.APPLICATION, fields=["display_name", "friends"]
            )
        else:
            application_models = []
        application_model_mapper = defaultdict(list)

        for application_model in application_models:
            if not application_model.friends:
                continue
            for friend in application_model.friends:
                application_model_mapper[str(friend)].append(application_model.display_name)

        for model in total:
            model["applications"] = application_model_mapper[model["model_id"]]
        return result


class MetaModelApplicationListAPIView(MetaModelApplication, MetaModelListAPIView):
    pass


class MetaModelFieldSetDeleteAPIView(MetaModelFieldSet, MetaModelDeleteAPIView):
    pass


class MetaModelApplicationDeleteAPIView(MetaModelApplication, MetaModelDeleteAPIView):
    pass


class MetaModelFieldSetTotalAPIView(APIView):
    default_sort_fields = [("update_time", -1)]
    default_fields = ["display_name"]

    def get(self, request):
        meta_model = meta_model_service.find_meta_model(
            model_type=MetaModelType.FIELD_SET,
            sort_fields=self.default_sort_fields,
            fields=self.default_fields,
            init=False,
        )
        data = [{"id": str(i.id), "name": i.display_name} for i in meta_model]
        return build_success(data)


class MetaModelApplicationTotalCategoryAPIView(APIView):
    default_category = [v for k, v in caasm_config.META_MODEL_CATEGORY_MAPPER.items() if not v["hidden"]]

    def get(self, request):
        return build_success(self.default_category)


class MetaModelApplicationShowAPIView(APIView):
    def get(self, request):
        serializer = MetaModelDetailSerializer(data=request.query_params)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)

        model_id = serializer.validated_data["model_id"]

        meta_model = meta_model_service.get_meta_model(model_id=model_id)

        friends = meta_model.friends or []

        if friends:
            friend_meta_models = meta_model_service.find_meta_model(model_ids=friends)
        else:
            friend_meta_models = []

        fields = self.parse_fields(meta_model)

        for friend_meta_model in friend_meta_models:
            fields.extend(self.parse_fields(friend_meta_model))

        return build_success(FieldShowResponseSerializer(instance=fields, many=True).data)

    def parse_fields(self, meta_model):
        meta_fields = list(meta_field_service.find_meta_field(model_id=meta_model.id))
        self._parse_loop(meta_fields, meta_model)
        return meta_fields

    def _parse_loop(self, fields, meta_model, parent_field=None):
        if not fields:
            return

        for field in fields:
            if not parent_field:
                field.full_name = meta_model.name + "." + field.name
                field.full_display_name = meta_model.display_name + "-" + field.display_name
            else:
                field.full_name = parent_field.full_name + "." + field.name
                field.full_display_name = parent_field.full_display_name + "-" + field.display_name

            self._parse_loop(field.children, meta_model, parent_field=field)


class MetaModelTotalFieldsAPIView(APIView):
    _default_meta_model_fields = ["friends", "name", "display_name"]
    _default_meta_field_fields = ["name", "full_name", "display_name", "children", "type", "model_id"]

    def get(self, request):
        serializer = MetaModelDetailSerializer(data=request.query_params)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)
        validated_data = serializer.validated_data
        request_model_id = validated_data["model_id"]
        meta_model = meta_model_service.get_meta_model(
            model_id=request_model_id, fields=self._default_meta_model_fields
        )
        if not meta_model:
            return build_success([])

        model_ids = [meta_model.id]
        model_ids.extend(meta_model.friends) if meta_model.friends else ...

        models = meta_model_service.find_meta_model(model_ids=model_ids)
        model_mapper = {model.id: model.display_name for model in models}

        fields = list(meta_field_service.find_meta_field(model_ids=model_ids, fields=self._default_meta_field_fields))

        flattened_fields = self._flatten_fields(fields, model_mapper)
        return build_success(MetaModelTotalFieldSerializer(instance=flattened_fields, many=True).data)

    @classmethod
    def _flatten_fields(cls, fields, model_mapper, title="", result=None):
        if result is None:
            for field in fields:
                field.title = model_mapper[field.model_id] + "-" + field.display_name

            result = []

        for field in fields:
            children = field.children
            field.children = []

            if not hasattr(field, "title"):
                field.title = title + "-" + field.display_name

            result.append(field)

            if not children:
                continue
            if field.type == MetaFieldType.LIST:
                if children[0].type != MetaFieldType.OBJECT:
                    continue
                children = children[0].children
            cls._flatten_fields(children, model_mapper, field.title, result)

        return result
