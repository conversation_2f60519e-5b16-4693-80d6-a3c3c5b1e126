from bson import ObjectId
from rest_framework.views import APIView

from caasm_meta_data.manager import MetaDataManager
from caasm_service.entity.meta_model import MetaModelType
from caasm_service.runtime import meta_field_service, meta_model_service
from caasm_webapi.app.meta_model.serializers.meta_field import (
    FieldRequestListSerializer,
    FieldResponseSerializer,
    FieldRequestDeleteSerializer,
    FieldRequestSerializer,
    FieldListByFieldTypeSerializer,
    FieldChooseSerializer,
)
from caasm_webapi.util.response import build_success, build_failed, ResponseCode


class MetaFieldView(APIView):
    @property
    def model_type(self):
        raise NotImplementedError


class MetaFieldAddView(MetaFieldView):
    serializer_class = FieldRequestSerializer

    def post(self, request):
        request_data = request.data
        validate_customer_res = self.validate_customer(request_data)
        if validate_customer_res:
            return validate_customer_res

        serializer = self.serializer_class(data=request.data)
        if not serializer.is_valid():
            return build_failed(code=ResponseCode.REQUEST_ERROR, message=serializer)
        field_mapper = serializer.validated_data

        self.padding(field_mapper, request_data)

        handle_res = self.handle(field_mapper)

        if not handle_res.flag:
            return build_failed(code=ResponseCode.REQUEST_ERROR, message="字段添加失败，请检查字段名称是否重复")

        return build_success(self.build_success_data(handle_res))

    def padding(self, data, request_data):
        query = []
        self.parse_query_loop(data, query)

        model_id = request_data["model_id"]

        data["query"] = "--".join(query)
        data["model_id"] = ObjectId(model_id)
        data["internal"] = False
        meta_model = meta_model_service.get_meta_model(model_id=model_id, fields=["name", "display_name"])
        if meta_model.name:
            MetaDataManager.padding_field(meta_model.display_name, meta_model.name, [data])
        self._padding_children(data["children"])

    @classmethod
    def _padding_children(cls, children):
        if not children:
            return
        for child in children:
            child["internal"] = False
            tmp_children = child["children"]
            cls._padding_children(tmp_children)

    def handle(self, field_mapper):
        return meta_field_service.save_direct(field_mapper)

    @classmethod
    def build_success_data(cls, handle_res):
        return str(handle_res.inserted_id)

    @classmethod
    def parse_query_loop(cls, field, result):
        display_name = field.get("display_name", "").strip()
        description = (field.get("description", "") or "").strip()

        if display_name and display_name not in result:
            result.append(display_name)

        if description and description not in result:
            result.append(description)

        children = field.get("children")
        if not children:
            return

        for child in children:
            cls.parse_query_loop(child, result)

    def validate_customer(self, request_data):
        model_id = request_data.get("model_id")
        validate_id_res = self.validate_id(model_id)
        if validate_id_res:
            return validate_id_res

        validate_model_res = self.validate_model_id(model_id)
        if validate_model_res:
            return validate_model_res

    @classmethod
    def validate_id(cls, id_):
        if not (isinstance(id_, str) and len(id_) == 24):
            return build_failed(ResponseCode.REQUEST_ERROR, message="无效的模型信息")
        try:
            ObjectId(id_)
        except Exception as e:
            return build_failed(ResponseCode.REQUEST_ERROR, message="无效的模型信息")

    def validate_model_id(self, model_id):
        if not meta_model_service.get_meta_model_count(model_type=self.model_type, model_id=model_id):
            return build_failed(ResponseCode.REQUEST_ERROR, message="无效的模型信息")


class MetaFieldModifyView(MetaFieldAddView):
    @classmethod
    def handle(cls, field_mapper):
        field_id = field_mapper.pop("field_id")
        return meta_field_service.update_meta_field(field_id=field_id, internal=False, values=field_mapper)

    def padding(self, data, request_data):
        super(MetaFieldModifyView, self).padding(data, request_data)
        data["field_id"] = request_data["field_id"]

    @classmethod
    def build_success_data(cls, handle_res):
        return handle_res.modified_count

    def validate_customer(self, request_data):
        res = super(MetaFieldModifyView, self).validate_customer(request_data)
        if res:
            return res

        field_id = request_data.get("field_id")
        validate_id_res = self.validate_id(field_id)
        if validate_id_res:
            return validate_id_res

        meta_field = meta_field_service.get_meta_field(field_id, fields=["model_id"])
        meta_model_id = meta_field.model_id

        validate_model_res = self.validate_model_id(meta_model_id)
        if validate_model_res:
            return validate_model_res


class MetaFieldListView(MetaFieldView):
    def get(self, request):
        serializer = FieldRequestListSerializer(data=request.query_params)
        if not serializer.is_valid():
            return build_failed(code=ResponseCode.REQUEST_ERROR, message=serializer)
        request_data = serializer.validated_data

        page_index = request_data.pop("page_index")
        page_size = request_data.pop("page_size")
        sort_fields = request_data.pop("sort_fields")

        offset = page_index * page_size

        model_id = serializer.validated_data.get("model_id")
        count = meta_model_service.get_meta_model_count(model_id=model_id, model_type=self.model_type)
        if not count:
            return build_failed(code=ResponseCode.REQUEST_ERROR, message="无效的模型信息")

        field_count = meta_field_service.get_meta_field_count(**request_data)
        total = []
        if field_count:
            fields = meta_field_service.find_meta_field(
                offset=offset, limit=page_size, sort_fields=sort_fields, **request_data
            )
            total = FieldResponseSerializer(instance=fields, many=True).data
        result = {"total": total, "count": field_count}
        return build_success(result)


class MetaFieldDeleteView(MetaFieldView):
    def post(self, request):
        serializer = FieldRequestDeleteSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)
        validated_data = serializer.validated_data
        model_id = validated_data.get("model_id")
        count = meta_model_service.get_meta_model_count(model_id=model_id, model_type=self.model_type)
        if not count:
            return build_success()

        meta_field_service.delete_meta_field(internal=False, **validated_data)
        return build_success()


class MetaFieldListByFieldTypeView(MetaFieldView):
    def post(self, request):
        serializer = FieldListByFieldTypeSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(code=ResponseCode.REQUEST_ERROR, message=serializer)
        validated_data = serializer.validated_data

        field_type = validated_data.get("field_type")
        model_id = validated_data.get("model_id")
        keyword = validated_data.get("keyword")
        internal = validated_data.get("internal")

        if field_type[0] == "ip":
            field_type.extend(["ipv4", "ipv6"])
        data = meta_model_service.get_meta_model(model_id=model_id)
        model_ids = [data.id]
        model_ids.extend(data.friends)
        fields = meta_field_service.find_meta_field(model_ids=list(set(model_ids)), keyword=keyword, internal=internal)
        fields = list(fields)
        data = self.get_need_fields(fields, field_type)
        full_filed_map = self.get_meta_field_data_map(fields)
        model_display_map = self.get_model_display_map()
        data = self.translate_field_full_name(data, full_filed_map, model_display_map)
        result = [{"label": item.display_name, "value": item.full_name} for item in data]
        return build_success(FieldChooseSerializer(instance=result, many=True).data)

    def translate_field_full_name(self, need_field, meta_field_map, model_display_map):
        for item in need_field:
            data = item.full_name.split(".")
            display_name = []
            temp = []
            for _field in data:
                temp.append(_field)
                if len(temp) == 1:
                    display_name.append(model_display_map[temp[0]])
                else:
                    temp_data = ".".join(temp)
                    display_name.append(meta_field_map.get(temp_data))
            item.display_name = "-".join(display_name)
        return need_field

    def get_need_fields(self, fields, field_type):
        data = []
        for field in fields:
            if field.children:
                data.extend(self.get_need_fields(field.children, field_type))
            elif field.type.value in field_type or "choice_field" in field_type:
                data.append(field)
            else:
                continue
        return data

    def get_meta_field_data_map(self, meta_field_data):
        data = {}
        for item in meta_field_data:
            if item.children:
                data.update(self.get_meta_field_data_map(item.children))
            data[item.full_name] = item.display_name
        return data

    def get_model_display_map(self):
        data = meta_model_service.find_meta_model()
        return {item.name: item.display_name for item in data}


class MetaFieldSet(object):
    @property
    def model_type(self):
        return MetaModelType.FIELD_SET


class MetaApplication(object):
    @property
    def model_type(self):
        return MetaModelType.APPLICATION


class MetaFieldAddFromFieldSetView(MetaFieldSet, MetaFieldAddView):
    pass


class MetaFieldAddFromApplicationView(MetaApplication, MetaFieldAddView):
    pass


class MetaFieldListFromFieldSetView(MetaFieldSet, MetaFieldListView):
    pass


class MetaFieldListFromApplicationView(MetaApplication, MetaFieldListView):
    pass


class MetaFieldModifyFromFieldSetView(MetaFieldSet, MetaFieldModifyView):
    pass


class MetaFieldModifyFromApplicationView(MetaApplication, MetaFieldModifyView):
    pass


class MetaFieldDeleteFromFieldSetView(MetaFieldSet, MetaFieldDeleteView):
    pass


class MetaFieldDeleteFromApplicationView(MetaApplication, MetaFieldDeleteView):
    pass
