from caasm_tool.patch.router import DefaultRouter
from caasm_webapi.app.meta_model.views.meta_field import (
    MetaFieldAddFromFieldSetView,
    MetaFieldAddFromApplicationView,
    MetaFieldListFromApplicationView,
    MetaFieldListFromApplicationView,
    MetaFieldModifyFromFieldSetView,
    MetaFieldModifyFromApplicationView,
    MetaFieldDeleteFromFieldSetView,
    MetaFieldDeleteFromApplicationView,
    MetaFieldListFromFieldSetView,
    MetaFieldListByFieldTypeView,
)
from caasm_webapi.app.meta_model.views.meta_model import (
    MetaModelPreAddAPIView,
    MetaModelApplicationModifyAPIView,
    MetaModelFieldSetModifyAPIView,
    MetaModelListAPIView,
    MetaModelApplicationListAPIView,
    MetaModelFieldSetListAPIView,
    MetaModelApplicationDeleteAPIVie<PERSON>,
    MetaModelFieldSetDeleteAPIView,
    MetaModelFieldSetTotalAPIView,
    MetaModelApplicationTotalCategoryAPIView,
    MetaModelApplicationShowAPIView,
    MetaModelChooseApiView,
    MetaModelTotalFieldsAPIView,
)

meta_model_router = DefaultRouter(prefix="metaModel")

meta_model_router.join_url("preAdd/$", MetaModelPreAddAPIView)
meta_model_router.join_url("fieldSet/fieldAdd/$", MetaFieldAddFromFieldSetView)
meta_model_router.join_url("application/fieldAdd/$", MetaFieldAddFromApplicationView)
meta_model_router.join_url("fieldSet/fieldList/$", MetaFieldListFromFieldSetView)
meta_model_router.join_url("application/fieldList/$", MetaFieldListFromApplicationView)

meta_model_router.join_url("fieldSet/fieldModify/$", MetaFieldModifyFromFieldSetView)
meta_model_router.join_url("application/fieldModify/$", MetaFieldModifyFromApplicationView)
meta_model_router.join_url("fieldSet/fieldDelete/$", MetaFieldDeleteFromFieldSetView)
meta_model_router.join_url("application/fieldDelete/$", MetaFieldDeleteFromApplicationView)

meta_model_router.join_url("fieldSet/modify/$", MetaModelFieldSetModifyAPIView)
meta_model_router.join_url("application/modify/$", MetaModelApplicationModifyAPIView)

meta_model_router.join_url("fieldSet/list/$", MetaModelFieldSetListAPIView)
meta_model_router.join_url("fieldSet/total/$", MetaModelFieldSetTotalAPIView)
meta_model_router.join_url("application/list/$", MetaModelApplicationListAPIView)
meta_model_router.join_url("application/category/$", MetaModelApplicationTotalCategoryAPIView)
meta_model_router.join_url("application/show/$", MetaModelApplicationShowAPIView)
meta_model_router.join_url("application/TotalFields/$", MetaModelTotalFieldsAPIView)

meta_model_router.join_url("fieldSet/delete/$", MetaModelFieldSetDeleteAPIView)
meta_model_router.join_url("application/delete/$", MetaModelApplicationDeleteAPIView)
meta_model_router.join_url("application/fieldListByfieldType/$", MetaFieldListByFieldTypeView)

meta_model_router.join_url("metaModelChooseShow/", MetaModelChooseApiView)
