from caasm_tool.patch.router import DefaultRouter
from caasm_webapi.app.permission.views.menu import MenuList<PERSON><PERSON>, UserMenuListAPI
from caasm_webapi.app.permission.views.role import (
    RoleAddAPI,
    RoleListAPI,
    RoleModifyAPI,
    RoleDeleteAPI,
    TotalRoleAPI,
)

permission_router = DefaultRouter()

permission_router.join_url("role/add/", RoleAddAPI, name="新增角色")
permission_router.join_url("role/list/", RoleListAPI, name="查询角色列表")
permission_router.join_url("role/delete/", RoleDeleteAPI, name="删除角色")
permission_router.join_url("role/modify/", RoleModifyAPI, name="修改角色")
permission_router.join_url("user/menu/list/", UserMenuListAPI)
permission_router.join_url("menu/list/", MenuListAPI)
permission_router.join_url("role/total/", TotalRoleAPI)
