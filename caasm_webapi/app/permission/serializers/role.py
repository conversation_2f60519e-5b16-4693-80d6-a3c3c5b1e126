from rest_framework import fields
from rest_framework.exceptions import ValidationError
from rest_framework_recursive.fields import Recursive<PERSON>ield


from caasm_service.runtime import menu_service, role_service
from caasm_tool.patch.serializer import SerializerMixin
from caasm_webapi.app.permission.serializers.menu import MenuSerializer


class UserRoleRelationOperateSerializer(SerializerMixin):
    role_codes = fields.ListField(child=fields.CharField(), help_text="角色编码", allow_empty=False, required=True)
    type = fields.CharField(help_text="类型", required=True)
    user_ids = fields.ListField(child=fields.CharField(), help_text="用户ID", allow_empty=False, required=True)

    def validate_type(self, type_):
        if type_ not in ["bind", "unbind"]:
            raise ValidationError("无效的操作类型")
        return type_

    def validate_role_codes(self, role_codes):
        new_codes = [i.code for i in role_service.find_role(codes=role_codes, fields=["code"])]
        if not new_codes:
            raise ValidationError("请求参数无效")
        return new_codes


class RoleMenuSerializer(SerializerMixin):
    code = fields.Char<PERSON>ield(max_length=64)
    action_codes = fields.ListField(child=fields.CharField(max_length=128), required=False, default=list)
    children = fields.ListField(child=RecursiveField(), required=False, default=list)


class RoleAddSerializer(SerializerMixin):
    name = fields.CharField(max_length=64, required=True)
    description = fields.CharField(max_length=512, required=False, default="")
    menus = fields.ListField(child=RoleMenuSerializer(), required=False, default=list, allow_null=True)
    code = fields.CharField(max_length=64, required=False)

    def validate_menus(self, menus):
        if not menus:
            return []
        return menus

    def validate(self, attrs):
        name = attrs.get("name")
        code = attrs.get("code")
        menus = attrs.get("menus")

        if not code:
            code = name

        if role_service.get_role_count(code):
            raise ValidationError("角色信息重复")
        attrs["code"] = code
        attrs["permission_codes"] = self._find_permission_code(menus)
        return attrs

    @classmethod
    def _find_codes(cls, menus, result=None):
        if result is None:
            result = []

        for menu in menus:
            menu_code = menu["code"]
            children = menu.get("children")
            result.append(menu_code)
            if not children:
                continue

            cls._find_codes(children, result)
        return result

    @classmethod
    def __find_permission_code(cls, menu_mapper, menus, result=None):
        if result is None:
            result = set()

        for menu in menus:
            code = menu.get("code")
            action_codes = menu.get("action_codes")
            children = menu.get("children")

            local_menu = menu_mapper.get(code)
            if local_menu:
                for local_action in local_menu.actions:
                    if local_action.code not in action_codes:
                        continue

                    for permission_code in local_action.permission_codes:
                        result.add(permission_code)
            cls.__find_permission_code(menu_mapper, children, result)
        return result

    @classmethod
    def _find_permission_code(cls, menus):
        if not menus:
            return []

        menu_codes = cls._find_codes(menus)

        menu_mapper = {menu.code: menu for menu in menu_service.find_menu(codes=menu_codes)}
        permission_codes = cls.__find_permission_code(menu_mapper, menus)

        return list(permission_codes)


class RoleModifySerializer(RoleAddSerializer):
    role_id = fields.CharField(max_length=24, min_length=24, required=True)
    name = fields.CharField(max_length=64, required=False)

    def validate(self, attrs):
        role_id = attrs.get("role_id")
        role = role_service.get_role(role_id)
        menus = attrs.get("menus")

        if not role:
            raise ValidationError("角色信息无效")
        code = attrs.get("code") or attrs.get("name")
        if code:
            if role.code != code:
                if role_service.get_role_count(code):
                    raise ValidationError("角色信息重复")

                attrs["code"] = code
        attrs["permission_codes"] = self._find_permission_code(menus)
        return attrs


class RoleDeleteSerializer(SerializerMixin):
    role_ids = fields.ListField(
        child=fields.CharField(max_length=24, min_length=24),
        required=True,
        allow_empty=False,
    )


class RoleListSerializer(SerializerMixin):
    page_index = fields.IntegerField(help_text="页码", required=False, default=1)
    page_size = fields.IntegerField(help_text="每夜最大数", required=False, max_value=200, default=20)
    keyword = fields.CharField(max_length=64, required=False, help_text="关键字")
    sort_fields = fields.ListField(child=fields.CharField(), default=list)


class RoleListResponseSerializer(SerializerMixin):
    id = fields.CharField(help_text="主键")
    code = fields.CharField(help_text="编码")
    name = fields.CharField(help_text="名称")
    description = fields.CharField(help_text="描述信息")
    create_time = fields.CharField(help_text="创建时间")
    update_time = fields.CharField(help_text="创建时间")
    menus = fields.ListField(child=MenuSerializer())
