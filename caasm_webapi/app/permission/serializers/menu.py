from rest_framework import fields
from rest_framework_recursive.fields import Recurs<PERSON><PERSON><PERSON>

from caasm_tool.patch.serializer import SerializerMixin


class ActionSerializer(SerializerMixin):
    code = fields.CharField(help_text="动作代码")
    name = fields.CharField(help_text="动作名称")
    icon = fields.CharField(help_text="icon")


class MenuSerializer(SerializerMixin):
    name = fields.CharField(help_text="名称")
    icon = fields.CharField(help_text="icon")
    path = fields.CharField(help_text="路径")
    is_show = fields.BooleanField(help_text="是否展示")
    code = fields.CharField(help_text="菜单代码")
    redirect_path = fields.CharField(help_text="跳转路径")
    actions = fields.ListField(child=ActionSerializer())
    setting = fields.DictField(help_text="配置信息")
    children = fields.ListField(child=RecursiveField(), default=list, required=False, allow_empty=True)
