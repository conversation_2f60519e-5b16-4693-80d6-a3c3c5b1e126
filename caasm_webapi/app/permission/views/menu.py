from rest_framework.views import APIView

from caasm_service.runtime import user_service, menu_service, role_service
from caasm_webapi.app.permission.serializers.menu import MenuSerializer
from caasm_webapi.util.response import build_success


class MenuListAPI(APIView):
    _default_sort_fields = [("priority", -1)]

    def get(self, request):
        menus = list(menu_service.find_menu(level=1, sort_fields=self._default_sort_fields))

        self.__build_menus(menus)

        data = MenuSerializer(instance=menus, many=True).data
        return build_success(data)

    @classmethod
    def __build_menus(cls, menus):
        for menu in menus:
            children_codes = menu.children_codes
            children_menus = []
            if children_codes:
                _cursor = menu_service.find_menu(codes=menu.children_codes, sort_fields=cls._default_sort_fields)
                children_menus = list(_cursor)
                cls.__build_menus(children_menus)
            menu.children = children_menus


class UserMenuListAPI(MenuListAPI):
    def get(self, request):
        user = request._user

        user_id = user.user_id
        is_super = user.is_super

        if is_super:
            return super(UserMenuListAPI, self).get(request)

        user = user_service.get_user(user_id=user_id, fields=["role_codes"])

        if not user or not user.role_codes:
            return build_success([])

        role_codes = user.role_codes
        roles = role_service.find_role(codes=role_codes)

        menu_relation_mapper = {}
        for role in roles:
            self.__padding_menu(role.menus, menu_relation_mapper)

        result = []
        if menu_relation_mapper:
            menu_codes = list(menu_relation_mapper.keys())
            cursor = menu_service.find_menu(codes=menu_codes, level=1, sort_fields=self._default_sort_fields)
            menus = list(cursor)

            self._build_menu(menus, menu_relation_mapper)
            result = MenuSerializer(instance=menus, many=True).data

        return build_success(result)

    @classmethod
    def _build_menu(cls, menus, menu_relation_mapper):
        for menu in menus:
            menu_relation = menu_relation_mapper.get(menu.code)
            if not menu_relation:
                continue

            bind_action_codes = menu_relation.action_codes
            bind_children_codes = [child.code for child in menu_relation.children]

            children_codes = menu.children_codes
            children_menus = []

            actions = [action for action in menu.actions if action.code in bind_action_codes]

            if children_codes:
                _cursor = menu_service.find_menu(codes=menu.children_codes, sort_fields=cls._default_sort_fields)
                children_menus = [child for child in list(_cursor) if child.code in bind_children_codes]
                cls._build_menu(children_menus, menu_relation_mapper)
            menu.children = children_menus
            menu.actions = actions

    @classmethod
    def __padding_menu(cls, menus, result=None):
        if result is None:
            result = {}

        for menu in menus:
            menu_code = menu.code
            children = menu.children

            cls.__padding_menu(children, result)
            if menu_code not in result:
                result[menu_code] = menu
            else:
                result[menu_code].action_codes += menu.action_codes
