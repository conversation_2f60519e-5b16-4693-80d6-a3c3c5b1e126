from caasm_tool.patch.router import DefaultRouter
from caasm_webapi.app.data_management.views.cross_adapter_analyse import (
    CrossAdapterAnalyseAPIView,
    AdaptersAPIView,
    CrossAdapterExportAPIView,
    CrossAdapterMetaViewAPI,
)
from caasm_webapi.app.data_management.views.maintenance import (
    ImportAPIView,
    FormAPIView,
    MaintainAPIView,
    DataAPIView,
    ExportAPIView,
    TemplateAPIView,
    ImportOptionAPIView,
)
from caasm_webapi.app.data_management.views.source_query import SourceQueryAPIView, SourceQueryDetailAPIView

data_management_router = DefaultRouter("dataManagement")
data_management_router.join_path("sourceQuery/", SourceQueryAPIView, name="SourceQueryAPIView")
data_management_router.join_path("sourceQueryDetail/", SourceQueryDetailAPIView, name="SourceQueryDetailAPIView")
data_management_router.join_path("sourceQuery/detail/", SourceQueryDetailAPIView, name="SourceQueryDetailAPIView")

data_management_router.join_path(
    "crossAdapterAnalyse/<category>/analyse/", CrossAdapterAnalyseAPIView, name="CrossAdapterAnalyseAPIView"
)
data_management_router.join_path(
    "crossAdapterAnalyse/<category>/adapters/", AdaptersAPIView, name="EntityTypeAdaptersAPIView"
)
data_management_router.join_path(
    "crossAdapterAnalyse/<category>/export/", CrossAdapterExportAPIView, name="CrossAdapterExportAPIView"
)
data_management_router.join_path(
    "crossAdapterAnalyse/<category>/metaView/", CrossAdapterMetaViewAPI, name="CrossAdapterMetaViewAPI"
)
data_management_router.join_path("maintenance/<category>/maintain/", MaintainAPIView, name="MaintainAPIView")
data_management_router.join_path("maintenance/<category>/import/", ImportAPIView, name="ImportAPIView")
data_management_router.join_path("maintenance/<category>/export/", ExportAPIView, name="ExportAPIView")
data_management_router.join_path("maintenance/<category>/form/", FormAPIView, name="FormAPIView")
data_management_router.join_path("maintenance/<category>/data/", DataAPIView, name="DataAPIView")
data_management_router.join_path("maintenance/<category>/template/", TemplateAPIView, name="TemplateAPIView")
data_management_router.join_path(
    "maintenance/<category>/importOption/", ImportOptionAPIView, name="ImportOptionAPIView"
)
