from rest_framework import fields

from caasm_tool.patch.serializer import SerializerMixin
from caasm_webapi.app.data_management.requests.cross_adapter_analyse import (
    CrossAdapterAnalyseRequest,
)
from caasm_webapi.app.query_engine.serializers.asql import FilterSerializer


class CrossAdapterAnalyseSerializer(SerializerMixin):
    filters = fields.ListField(child=FilterSerializer(required=True), required=False, default=list)
    display_fields = fields.ListField(child=fields.CharField(), required=True)
    adapter_names = fields.ListField(child=fields.CharField(), required=False, max_length=20)
    page_size = fields.IntegerField(required=False, default=20, max_value=100)
    page_index = fields.IntegerField(required=False, default=1, min_value=1)
    field = fields.CharField(required=True)
    date = fields.CharField(required=False)

    def create(self, validated_data):
        req = CrossAdapterAnalyseRequest()
        req.filters = validated_data.get("filters") or []
        req.display_fields = validated_data["display_fields"]
        req.adapter_names = validated_data["adapter_names"]
        req.field = validated_data["field"]
        req.page_index = validated_data["page_index"]
        req.page_size = validated_data["page_size"]
        req.date = validated_data.get("date")
        return req
