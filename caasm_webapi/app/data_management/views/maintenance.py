import csv
import os.path
from datetime import datetime
from io import StringIO

from django.http import StreamingHttpResponse, FileResponse, HttpResponseNotFound
from django.utils.encoding import escape_uri_path
from rest_framework.views import APIView

from caasm_maintenance.form import form_generator
from caasm_maintenance.maintainer import ImportResult
from caasm_maintenance.maintainer_manager import maintainer_manager
from caasm_meta_data.constants import CATEGORY_TRANSLATE
from caasm_script import SCRIPT_DATA_PATH
from caasm_service.constants.maintenance import ImportOption
from caasm_service.entity.maintenance import MaintenanceEntity, BatchOperationEntity
from caasm_service.runtime import entity_service, maintenance_service
from caasm_tool.util import extract
from caasm_webapi.app.data_management.requests.maintenance import (
    EntityMaintenanceRequest,
    MaintenanceFormRequest,
    EntityDataRequest,
    ImportOperationRequest,
    BatchOperationRequest,
)
from caasm_webapi.app.data_management.serializers.maintenance import (
    EntityMaintenanceSerializer,
    MaintenanceTypeSerializer,
    EntityDataSerializer,
    ImportOperationSerializer,
    ExportOperationSerializer,
    ImportOptionSerializer,
)
from caasm_webapi.util.response import build_failed, build_success
from caasm_tool import log


class MaintainAPIView(APIView):
    def post(self, request, category):
        serializer = EntityMaintenanceSerializer(data=request.data)
        if serializer.is_valid():
            req: EntityMaintenanceRequest = serializer.save()
            maintainer = maintainer_manager.get_maintainer(category, req.entity_type)
            if not maintainer:
                return build_failed(-1, "未定义有效的维护规则")
            errors = []
            result = maintainer.maintain(req.operation, req.raw, req.entity_type, req.entity_id, errors)
            if len(errors) == 1:
                error = errors[0]
                if "item_id" not in error:
                    return build_failed(-1, error["message"])
            return build_success({"result": result, "errors": errors})
        return build_failed(-1, str(serializer.errors))


class ImportAPIView(APIView):
    def _generate_entities(self, result: ImportResult):
        buffer: StringIO = StringIO()
        writer = csv.writer(buffer)
        if result.successful:
            buffer.seek(0)
            #   导入成功
            writer.writerow(["导入成功", f"共导入{result.total}条记录"])
            buffer.seek(0)
            line = buffer.read()
            yield line.encode("utf-8")
        else:
            buffer.seek(0)
            writer.writerow(["导入失败", f"共{len(result.errors)}条记录有误"])
            buffer.seek(0)
            line = buffer.read()
            yield line.encode("utf-8")
            for row_index, errors in result.errors:
                error_row = []
                buffer = StringIO()
                writer = csv.writer(buffer)
                buffer.seek(0)
                error_row.append(f"第{row_index}行")
                for error in errors:
                    column_index = error.get("item_id")
                    message = error.get("message")
                    if column_index:
                        error_row.append(f"第{column_index + 1}列，{message}")
                    else:
                        error_row.append(message)
                writer.writerow(error_row)
                buffer.seek(0)
                line = buffer.read()
                yield line.encode("utf-8")

    def post(self, request, category):
        serializer = ImportOperationSerializer(data=request.data)
        if serializer.is_valid():
            req: ImportOperationRequest = serializer.save()
            maintainer = maintainer_manager.get_maintainer(category, req.entity_type)
            if not maintainer:
                return build_failed(-1, "未定义有效的维护规则")
            log.info(f"导入文件的名称:{req.file.name}")
            result: ImportResult = maintainer.import_(req.file.read(), req.operation, req.entity_type)
            if not result.successful:
                if result.error:
                    return build_failed(-1, result.error)
            res = StreamingHttpResponse(self._generate_entities(result))
            res["Content-Type"] = "application/octet-stream;charset=UTF-8"
            now = datetime.now()
            file_name = f"导入报告-{str(now)}.csv"
            res["Content-Disposition"] = f"attachment;filename={escape_uri_path(file_name)}"
            return res
        return build_failed(-1, str(serializer.errors))


class ExportAPIView(APIView):
    def _generate_file(self, category, operation, entity_type):
        maintainer = maintainer_manager.get_maintainer(category, entity_type)
        if not maintainer:
            return build_failed(-1, "未定义有效的维护规则")
        data = maintainer.export(operation, entity_type)
        yield data

    def post(self, request, category):
        serializer = ExportOperationSerializer(data=request.data)
        if serializer.is_valid():
            req: BatchOperationRequest = serializer.save()
            res = StreamingHttpResponse(self._generate_file(category, req.operation, req.entity_type))
            res["Content-Type"] = "application/octet-stream;charset=UTF-8"
            now = datetime.now()
            category_name = CATEGORY_TRANSLATE.get(category)
            file_name = f"{category_name}导出-{str(now)}.xlsx"
            res["Content-Disposition"] = f"attachment;filename={escape_uri_path(file_name)}"
            return res
        return build_failed(-1, str(serializer.errors))


class TemplateAPIView(APIView):
    def get(self, request, category):
        serializer = ImportOptionSerializer(data=request.data)
        if serializer.is_valid():
            req: BatchOperationRequest = serializer.save()
            path = SCRIPT_DATA_PATH / "maintenance" / category / f"{req.entity_type or category}.xlsx"
            if os.path.exists(path):
                return FileResponse(open(path, "rb"), filename=f"{CATEGORY_TRANSLATE.get(category)}导入模板.xlsx")
            else:
                return HttpResponseNotFound()
        return build_failed(-1, str(serializer.errors))


class ImportOptionAPIView(APIView):
    def post(self, request, category):
        serializer = ImportOptionSerializer(data=request.data)
        if serializer.is_valid():
            req: BatchOperationRequest = serializer.save()
            maintenance_entity: MaintenanceEntity = maintenance_service.get_maintenance(category, req.entity_type)
            batch_operation_entity: BatchOperationEntity = maintenance_entity.batch_operations_by_operation.get(
                req.operation
            )
            if batch_operation_entity:
                if batch_operation_entity.import_option == ImportOption.REFRESH:
                    message = "导入成功将会清空现有数据，第一次导入请下载并使用导入模板，后续维护请使用导出功能并在导出表格中修改后再进行导入"
                else:
                    message = "导入成功会修改原有记录或创建新纪录，是否为原有记录将根据当前实体逻辑进行判定"
                return build_success({"import_option": message})
            return build_failed(-1, "未定义批量操作")
        return build_failed(-1, str(serializer.errors))


class FormAPIView(APIView):
    def post(self, request, category):
        serializer = MaintenanceTypeSerializer(data=request.data)
        if serializer.is_valid():
            req: MaintenanceFormRequest = serializer.save()
            form = form_generator.get_form(category, req.operation, req.entity_type)
            if form is None:
                return build_failed(-1, "未定义该类型的表单")
            return build_success(form)
        return build_failed(-1, str(serializer.errors))


class DataAPIView(APIView):
    def post(self, request, category):
        serializer = EntityDataSerializer(data=request.data)
        if serializer.is_valid():
            req: EntityDataRequest = serializer.save()
            entity = entity_service.get_entity(category, "base.entity_id", req.entity_id, fields=["base.asset_type"])
            if not entity:
                return build_failed(-1, "要更新的实体不存在")
            entity_type = extract(entity, "base.asset_type")
            if entity_type == category:
                entity_type = None
            maintainer = maintainer_manager.get_maintainer(category, entity_type)
            if not maintainer:
                return build_failed(-1, "未定义有效的维护规则")
            result, data = maintainer.get_data(req.operation, req.entity_id, entity_type)
            if not result:
                return build_failed(-1, data)
            return build_success({"raw": data})
        return build_failed(-1, str(serializer.errors))
