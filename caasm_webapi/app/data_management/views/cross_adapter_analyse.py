import csv
import json
from collections import defaultdict
from datetime import datetime
from io import String<PERSON>
from typing import Dict

from bson import ObjectId
from django.http import StreamingHttpResponse
from django.utils.encoding import escape_uri_path
from djangorestframework_camel_case.util import underscoreize
from nanoid import generate
from rest_framework.views import APIView

from caasm_aql.asql_plus import AsqlPlusLogicalQuery
from caasm_aql.tool import parse_aql
from caasm_persistence.handler.runtime import mongo_handler
from caasm_render.query.base import BaseQuery
from caasm_render.runtime import render_manager
from caasm_service.entity.adapter import Adapter
from caasm_service.entity.meta_model import Meta<PERSON>ield, MetaFieldType
from caasm_service.runtime import (
    adapter_service,
    entity_service,
    entity_lineage_fabric_stage_service,
    value_lineage_fabric_stage_service,
    convert_service,
    snapshot_record_service,
)
from caasm_tool.constants import DATE_FORMAT_1
from caasm_tool.util import extract
from caasm_webapi.app.data_management.requests.cross_adapter_analyse import (
    CrossAdapterAnalyseRequest,
)
from caasm_webapi.app.data_management.serializers.cross_adapter_analyse import (
    CrossAdapterAnalyseSerializer,
)
from caasm_webapi.app.query_engine import MetaViewAPI
from caasm_webapi.util.response import build_failed, build_success


class AdaptersAPIView(APIView):
    def post(self, request, category):
        adapter_aggs = entity_service.search(
            table=entity_service.get_table(category),
            aggs={
                "filter": {
                    "filter": {"bool": {"must": []}},
                    "aggs": {"adapters": {"terms": {"field": "base.adapters"}}},
                }
            },
        )
        adapter_names = []
        for adapter_agg in adapter_aggs.body["aggregations"]["filter"]["adapters"]["buckets"]:
            adapter_names.append(adapter_agg["key"])

        adapters = []
        adapter_entities = adapter_service.find_adapter(names=adapter_names)
        for adapter_entity in adapter_entities:
            adapter_entity: Adapter = adapter_entity
            adapters.append({"name": adapter_entity.name, "display_name": adapter_entity.display_name})
        return build_success({"adapters": adapters})


class CrossAdapterAnalyseBaseAPIView(APIView, BaseQuery):
    def _get_headers(self, req: CrossAdapterAnalyseRequest, meta_fields):
        headers = []
        #   构造表头
        valid_display_fields = list()
        for display_field in req.display_fields:
            if display_field in meta_fields:
                if display_field in valid_display_fields:
                    continue
                valid_display_fields.append(display_field)
                display_meta_field = meta_fields[display_field]
                display_meta_field: MetaField = display_meta_field
                header = {
                    "title": display_meta_field.full_display_name,
                    "name": display_meta_field.full_name,
                    "data_type": display_meta_field.type,
                }
                if display_meta_field.type == MetaFieldType.LIST:
                    header["child_data_type"] = display_meta_field.children[0].type
                headers.append(header)
        req_field: MetaField = meta_fields[req.field]
        req_header = {
            "title": f"比较字段:{req_field.full_display_name}",
            "name": req_field.full_name,
            "data_type": req_field.type,
        }
        if req_field.type == MetaFieldType.LIST:
            req_header["child_data_type"] = req_field.children[0].type
        headers.append(req_header)
        adapter_row_names = {}
        for adapter_name in req.adapter_names:
            adapter: Adapter = adapter_service.get_adapter(adapter_name)
            if not adapter:
                continue
            if adapter_name in adapter_row_names:
                continue
            field_name = generate()
            adapter_row_names[adapter_name] = field_name
            adapter_value_header = {
                "title": f"{adapter.display_name}:{req_field.full_display_name}",
                "name": field_name,
                "data_type": req_field.type,
            }
            if req_field.type == MetaFieldType.LIST:
                adapter_value_header["child_data_type"] = req_field.children[0].type
            headers.append(adapter_value_header)
        return headers, valid_display_fields, req_field, adapter_row_names

    def _prepare(
        self, category, req: CrossAdapterAnalyseRequest, valid_display_fields, limit, offset, need_count: bool = True
    ):
        #   加载源数据与血缘信息
        entity_ids = list()
        if "base.entity_id" not in valid_display_fields:
            valid_display_fields.append("base.entity_id")
        if req.field not in valid_display_fields:
            valid_display_fields.append(req.field)
        aql = AsqlPlusLogicalQuery.from_list(req.filters) or ""
        condition = parse_aql(aql, category, req.date)
        entities = entity_service.find_entity(
            category,
            condition,
            limit=limit,
            offset=offset,
            fields=valid_display_fields,
        )
        for entity in entities:
            entity_ids.append(extract(entity, "base.entity_id"))
        #   检索血缘信息
        date = req.date or snapshot_record_service.get_latest_useful_date()
        if not date:
            date = datetime.now().strftime(DATE_FORMAT_1)
        entity_lineages = entity_lineage_fabric_stage_service.find_direct(
            {"trace_id": {"$in": entity_ids}},
            table=entity_lineage_fabric_stage_service.get_table(category, date),
            limit=len(entity_ids),
        )
        # value_lineages = value_lineage_fabric_stage_service.find_direct(
        #     {"trace_id": {"$in": entity_ids}},
        #     table=value_lineage_fabric_stage_service.get_table(category, date),
        #     limit=len(entity_ids)
        # )
        #   加载血缘信息
        lineage_entity_ids_by_tables = defaultdict(set)
        entity_lineages_by_entity_id = {}
        entity_lineages_upstreams_by_entity_id = defaultdict(lambda: defaultdict(list))
        for entity_lineage in entity_lineages:
            entity_lineage: Dict = entity_lineage
            for upstream in entity_lineage["upstreams"]:
                upstream: Dict = upstream
                lineage_entity_ids_by_tables[upstream["table"]].add(ObjectId(upstream["sid"]))
                entity_lineages_upstreams_by_entity_id[entity_lineage["trace_id"]][upstream["table"]].append(
                    upstream["sid"]
                )
            entity_lineages_by_entity_id[entity_lineage["trace_id"]] = entity_lineage
        # value_lineages_by_entity_id = {}
        # value_lineages_upstream_by_entity_id = defaultdict(
        #     lambda: defaultdict(lambda: defaultdict(lambda: defaultdict(lambda: defaultdict(list))))
        # )
        # for value_lineage in value_lineages:
        #     value_lineage: Dict = value_lineage
        #     upstreams_by_id = {}
        #     for upstream in value_lineage["upstreams"]:
        #         lineage_entity_ids_by_tables[upstream["table"]].add(ObjectId(upstream["sid"]))
        #         upstreams_by_id[upstream["id"]] = upstream
        #     value_lineages_by_entity_id[value_lineage["trace_id"]] = value_lineage
        #     for field in value_lineage["fields"]:
        #         field: Dict = field
        #         for row in field["rows"]:
        #             row: Dict = row
        #             for upstream in row["upstreams"]:
        #                 upstream: Dict = upstream
        #                 real_upstream: Dict = upstreams_by_id.get(upstream["upstream"])
        #                 if not real_upstream:
        #                     continue
        #                 value_lineages_upstream_by_entity_id[value_lineage["trace_id"]][field["field"]][row["index"]][
        #                     real_upstream["table"]
        #                 ][real_upstream["sid"]].extend(upstream["indices"])
        #   加载转换后的源数据
        lineage_entities_by_tables = defaultdict(dict)
        for table, sids in lineage_entity_ids_by_tables.items():
            if sids:
                src_entities = mongo_handler.find_direct(
                    {"_id": {"$in": list(sids)}}, table=table, fields=valid_display_fields
                )
                for entity in src_entities:
                    sid = str(extract(entity, "_id"))
                    lineage_entities_by_tables[table][sid] = entity
        #   建立表与适配器映射关系
        tables_by_adapter_name = {}
        for table in lineage_entity_ids_by_tables.keys():
            adapter_name = convert_service.extract_adapter_name(table)
            if adapter_name:
                tables_by_adapter_name[adapter_name] = table
        return (
            entities,
            entity_service.get_count(category, date, condition=condition) if need_count else None,
            entity_lineages_by_entity_id,
            {},  # value_lineages_by_entity_id,
            tables_by_adapter_name,
            entity_lineages_upstreams_by_entity_id,
            lineage_entities_by_tables,
        )

    def _generate_rows(
        self,
        req: CrossAdapterAnalyseRequest,
        entities,
        entity_lineages_by_entity_id,
        value_lineages_by_entity_id,
        adapter_row_names,
        tables_by_adapter_name,
        entity_lineages_upstreams_by_entity_id,
        lineage_entities_by_tables,
        req_field,
        meta_fields,
    ):
        #   构造表体
        rows = []
        for entity in entities:
            row = {}
            entity_id = extract(entity, "base.entity_id")
            entity_lineage = entity_lineages_by_entity_id.get(entity_id)
            # value_lineage = value_lineages_by_entity_id.get(entity_id)
            for display_field in req.display_fields:
                display_meta_field = meta_fields.get(display_field)
                value = extract(entity, display_field)
                if value:
                    value = render_manager.my_plain(value, display_meta_field)
                else:
                    value = "[空值]"
                row[display_field] = value
            value = extract(entity, req.field)
            if value:
                value = render_manager.my_plain(value, meta_fields.get(req.field))
            else:
                value = "[空值]"
            row[req.field] = value
            for adapter_name, adapter_field_name in adapter_row_names.items():
                if not entity_lineage:
                    row[adapter_field_name] = "[空值]"
                    continue
                table = tables_by_adapter_name.get(adapter_name)
                if not table:
                    row[adapter_field_name] = "[空值]"
                    continue
                sids = extract(entity_lineages_upstreams_by_entity_id, f"{entity_id}.{table}")
                if not sids:
                    row[adapter_field_name] = "[空值]"
                    continue
                values = []
                for sid in sids:
                    source_entity = extract(lineage_entities_by_tables, f"{table}.{sid}")
                    if not source_entity:
                        continue
                    value = extract(source_entity, req.field)
                    if value is not None:
                        plain_value = render_manager.my_plain(value, req_field)
                        values.append(plain_value)
                row[adapter_field_name] = ";".join(values)
            rows.append(row)
        return rows

    def _after_post(self, category, req, meta_fields, limit, offset):
        raise NotImplementedError()

    def post(self, request, category):
        if "data" in request.data:
            data = underscoreize(json.loads(request.data["data"]))
        else:
            data = request.data
        serializer = CrossAdapterAnalyseSerializer(data=data)
        if serializer.is_valid():
            req: CrossAdapterAnalyseRequest = serializer.save()
            meta_fields = self.find_field_to_mapper(category)
            if not meta_fields:
                return build_failed("请求的实体类别不存在")
            if req.field not in meta_fields:
                return build_failed(-1, "请求比较的字段不存在")
            return self._after_post(category, req, meta_fields, req.page_size, req.page_index * req.page_size)
        return build_failed(-1, str(serializer.errors))


class CrossAdapterAnalyseAPIView(CrossAdapterAnalyseBaseAPIView):
    def _after_post(self, category, req, meta_fields, limit, offset):
        headers, valid_display_fields, req_field, adapter_row_names = self._get_headers(req, meta_fields)
        (
            entities,
            count,
            entity_lineages_by_entity_id,
            value_lineages_by_entity_id,
            tables_by_adapter_name,
            entity_lineages_upstreams_by_entity_id,
            lineage_entities_by_tables,
        ) = self._prepare(category, req, valid_display_fields, limit, offset)
        rows = self._generate_rows(
            req,
            entities,
            entity_lineages_by_entity_id,
            value_lineages_by_entity_id,
            adapter_row_names,
            tables_by_adapter_name,
            entity_lineages_upstreams_by_entity_id,
            lineage_entities_by_tables,
            req_field,
            meta_fields,
        )
        return build_success({"count": count, "headers": headers, "raw": {"rows": rows}})


class CrossAdapterExportAPIView(CrossAdapterAnalyseBaseAPIView):
    def _generate_entities(self, category, req: CrossAdapterAnalyseRequest, meta_fields):
        #   生成表头
        headers, valid_display_fields, req_field, adapter_row_names = self._get_headers(req, meta_fields)
        field_header_ch_list = []
        for header in headers:
            field_header_ch_list.append(header["title"])
        buffer: StringIO = StringIO()
        writer = csv.writer(buffer)
        header = field_header_ch_list
        buffer.seek(0)
        writer.writerow(header)
        buffer.seek(0)
        line = buffer.read()
        yield line.encode("utf-8")

        buffer = None
        writer = None
        wrote_count = 0

        limit = 100
        offset = 0
        while True:
            (
                entities,
                _,
                entity_lineages_by_entity_id,
                value_lineages_by_entity_id,
                tables_by_adapter_name,
                entity_lineages_upstreams_by_entity_id,
                lineage_entities_by_tables,
            ) = self._prepare(category, req, valid_display_fields, limit, offset, False)
            if not entities:
                break
            offset += limit
            rows = self._generate_rows(
                req,
                entities,
                entity_lineages_by_entity_id,
                value_lineages_by_entity_id,
                adapter_row_names,
                tables_by_adapter_name,
                entity_lineages_upstreams_by_entity_id,
                lineage_entities_by_tables,
                req_field,
                meta_fields,
            )
            for row in rows:
                if buffer is None:
                    buffer: StringIO = StringIO()
                    writer = csv.writer(buffer)
                writer.writerow(row.values())
                wrote_count += 1
                if wrote_count > 100:
                    wrote_count = 0
                    buffer.seek(0)
                    line = buffer.read()
                    buffer = None
                    writer = None
                    yield line.encode("utf-8")
        if buffer:
            buffer.seek(0)
            line = buffer.read()
            yield line.encode("utf-8")

    def _after_post(self, category, req: CrossAdapterAnalyseRequest, meta_fields, limit, offset):
        res = StreamingHttpResponse(self._generate_entities(category, req, meta_fields))
        res["Content-Type"] = "application/octet-stream;charset=GB2312"
        now = datetime.now()
        file_name = f"跨适配器字段比较导出-{str(now)}.csv"
        res["Content-Disposition"] = f"attachment;filename={escape_uri_path(file_name)}"
        return res


class CrossAdapterMetaViewAPI(MetaViewAPI):
    def _is_selectable(self, field: MetaField):
        if field.type == MetaFieldType.LIST and field.children[0].type == MetaFieldType.OBJECT:
            return False
        elif field.type == MetaFieldType.OBJECT:
            return False
        return True
