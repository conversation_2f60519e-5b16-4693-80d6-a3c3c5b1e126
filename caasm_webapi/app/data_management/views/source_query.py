import json
from collections import defaultdict

from bson import ObjectId
from bson.json_util import dumps
from rest_framework.views import APIView

from caasm_persistence.handler.runtime import mongo_handler
from caasm_service.entity.adapter import Adapter
from caasm_service.entity.adapter_instance import AdapterInstance
from caasm_service.runtime import adapter_instance_service, adapter_service
from caasm_tool.util import extract, restore
from caasm_webapi.app.data_management.requests.source_query import (
    SourceQueryRequest,
    SourceQueryDetailRequest,
    SourceQueryCondition,
)
from caasm_webapi.app.data_management.serializers.source_query import SourceQuerySerializer, SourceQueryDetailSerializer
from caasm_webapi.util.response import build_failed, build_success


class _SourceQueryBase:
    @staticmethod
    def get_table(adapter_instance_id: str, fetch_type: str):
        adapter_instance: AdapterInstance = adapter_instance_service.get_adapter_instance(adapter_instance_id)
        if adapter_instance is None:
            return False, "未找到适配器实例"
        adapter: Adapter = adapter_service.get_adapter(name=adapter_instance.adapter_name)
        if adapter is None:
            return False, "未找到适配器"
        table_prefix = f"fetch_{fetch_type}_{adapter.name}_{str(adapter_instance.id)}"
        collections = mongo_handler.database().list_collection_names()
        latest_index = None
        for collection in collections:
            if collection.startswith(table_prefix):
                try:
                    index = int(collection.replace(table_prefix, "")[1:])
                    if latest_index is None:
                        latest_index = index
                    elif latest_index < index:
                        latest_index = index
                except ValueError:
                    pass
        if latest_index is None:
            return False, "未找到采集数据"
        return True, f"{table_prefix}_{latest_index}"


class SourceQueryAPIView(APIView, _SourceQueryBase):
    def post(self, request):
        serializer = SourceQuerySerializer(data=request.data)
        if serializer.is_valid():
            req: SourceQueryRequest = serializer.save()
            found, result = self.get_table(req.adapter_instance_id, req.fetch_type)
            if not found:
                return build_failed(-1, result)
            table_name = result
            #   加载所有可能的子字段
            all_sub_fields = set()
            if req.conditions:
                sub_fields = mongo_handler.find_distinct("key", table=table_name)
                all_sub_fields.update(sub_fields)
            condition_segments = []
            sub_condition_segments = []
            for condition in req.conditions:
                condition: SourceQueryCondition = condition
                sub_found = False
                for sub_field in all_sub_fields:
                    if condition.field.startswith(sub_field):
                        real_field_name = condition.field.replace(sub_field, "")
                        if real_field_name.startswith("."):
                            real_field_name = real_field_name[1:]
                        sub_condition_segments.append({f"record.{real_field_name}": condition.value})
                        sub_found = True
                        break
                if not sub_found:
                    condition_segments.append({f"record.{condition.field}": condition.value})
            if sub_condition_segments:
                sub_condition_segments.append({"type": "link"})
                cursor = mongo_handler.aggregate(
                    [
                        {"$match": {"$and": sub_condition_segments}},
                        {"$group": {"_id": None, "unique_values": {"$addToSet": "$id"}}},
                        {"$project": {"count": {"$size": "$unique_values"}}},
                    ],
                    table=table_name,
                )
                result = list(cursor)
                if result:
                    count = result[0]
                    if count["count"] > 1000:
                        return build_failed(-1, "检索结果过多，请缩小检索范围")
                    ids = mongo_handler.find_distinct("id", {"$and": sub_condition_segments}, table=table_name)
                    condition_segments.append({"id": {"$in": ids}})
            condition_segments.append({"type": "core"})
            if condition_segments:
                final_condition = {"$and": condition_segments}
            else:
                final_condition = None
            count = mongo_handler.count(condition=final_condition, table=table_name)
            records = mongo_handler.find_direct(
                condition=final_condition,
                fields=["internal.fetch_time", "_id"],
                table=table_name,
                offset=req.page_index * req.page_size,
                limit=req.page_size,
            )
            results = []
            for record in records:
                new_record = {"id": str(record["_id"]), "fetch_time": extract(record, "internal.fetch_time")}
                results.append(new_record)
            return build_success({"data": results, "count": count})
        return build_failed(-1, str(serializer.errors))


class SourceQueryDetailAPIView(APIView, _SourceQueryBase):
    def post(self, request):
        serializer = SourceQueryDetailSerializer(data=request.data)
        if serializer.is_valid():
            req: SourceQueryDetailRequest = serializer.save()
            found, result = self.get_table(req.adapter_instance_id, req.fetch_type)
            if not found:
                return build_failed(-1, result)
            table_name = result
            record = mongo_handler.get_direct({"_id": ObjectId(req.id)}, table=table_name)
            if record is None:
                return build_failed(-1, "未找到记录")
            #   找到子数据
            record_id = extract(record, "id")
            sub_records = defaultdict(list)
            if record_id:
                for sub_record in mongo_handler.find_direct({"id": record_id, "type": "link"}, table=table_name):
                    key = extract(sub_record, "key")
                    if key:
                        sub = extract(sub_record, "record")
                        sub_records[key].append(sub)
                for key, subs in sub_records.items():
                    restore(f"record.{key}", subs, record)
            return build_success({"raw": json.loads(dumps(extract(record, "record")))})
        return build_failed(-1, serializer.errors)
