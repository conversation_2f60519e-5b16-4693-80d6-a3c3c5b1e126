from caasm_tool.patch.router import DefaultRouter
from caasm_webapi.app.export.views import ExportRecordListAPIView, CancelExportRecordAPIView, DownloadExportFileAPIView

export_router = DefaultRouter("export/")
export_router.join_path("record/list/", ExportRecordListAPIView, name="ExportRecordListAPIView")
export_router.join_path("record/cancel/", CancelExportRecordAPIView, name="CancelExportRecordAPIView")
export_router.join_path("download/", DownloadExportFileAPIView, name="DownloadExportFileAPIView")
