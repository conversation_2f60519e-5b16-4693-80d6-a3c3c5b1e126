from caasm_tool.patch.router import DefaultRouter
from caasm_webapi.app.files.views.files import (
    FileUploadAPI,
    FileDownloadAPI,
    FileNameShowAPI,
    FileShowAPI,
    ImageUploadAPIView,
)

file_router = DefaultRouter()

file_router.join_path("fileName/<file_id>/", FileNameShowAPI, name="FileNameShowAPI")
file_router.join_path("fileUpload/", FileUploadAPI, name="FileUploadAPI")
file_router.join_path("imageUpload/", ImageUploadAPIView, name="ImageUploadAPIView")
file_router.join_path("fileDownload/<file_id>/", FileDownloadAPI, name="FileDown<PERSON><PERSON><PERSON>")
file_router.join_path("fileShow/<file_id>/", FileShowAPI, name="FileShowAPI")
