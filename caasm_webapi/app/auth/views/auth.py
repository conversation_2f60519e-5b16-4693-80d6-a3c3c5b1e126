from rest_framework.request import Request
from rest_framework.views import APIView

from caasm_config.config import caasm_config
from caasm_service.constants.auth import AuthenticationType
from caasm_service.runtime import sso_config_service, user_service
from caasm_webapi.app.auth.serializers.auth import AuthSerializer
from caasm_webapi.app.auth.serializers.oss import CaptchaValidateSerializer
from caasm_webapi.app.auth.views.sso.sso_base import SSOLoginBase
from caasm_webapi.auth import get_auth
from caasm_webapi.util.response import build_success, build_failed, ResponseCode, RESPONSE_MSG
from caasm_webapi.util.token import token_service, captcha_service
from caasm_webapi.util.tool import generate_random_string, get_ip, generate_captcha
from caasm_auth_manage.sso_config.base import SSOConfigBaseHandle
from caasm_tool import log


class AuthView(APIView):
    def post(self, request: Request):
        data = request.data
        serializer = AuthSerializer(data=data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)

        validated_data = serializer.validated_data

        auth_type = validated_data["auth_type"]
        settings = validated_data["settings"]
        settings["addr"] = get_ip(request)

        auth_instance = get_auth(auth_type)
        if not auth_instance:
            return build_failed(ResponseCode.REQUEST_ERROR, message="无效的认证方式")
        # 验证码校验
        captcha_code = settings.get("code", None)
        if captcha_code and not self._validate_captcha(settings["addr"], captcha_code):
            return build_failed(ResponseCode.REQUEST_ERROR, message="验证码错误")

        expire_time = caasm_config.AUTH_COOKIE_EXPIRE
        auth_flag, token, err_msg = auth_instance.login(settings)

        if not auth_flag:
            if not err_msg:
                err_msg = RESPONSE_MSG[ResponseCode.AUTH_FAILED]
            return build_failed(ResponseCode.REQUEST_ERROR, message=err_msg, audit_log="用户尝试登录")
        token_service.create_token(token, auth_type, expire_time)
        audit_log = "用户登录"
        response = build_success(token, audit_log=audit_log)
        response.set_cookie(caasm_config.AUTH_COOKIE_NAME, token, httponly=True, expires=expire_time)
        return response

    def _validate_captcha(self, addr: str, captcha_code: str) -> bool:
        captcha = captcha_service.get_captcha_content(addr)
        if captcha.lower() != captcha_code.lower():
            return False
        captcha_service.remove_captcha(addr)
        return True


class LogoutView(AuthView):
    def post(self, request: Request):
        token = request.COOKIES.get(caasm_config.AUTH_COOKIE_NAME)
        auth_type = token_service.get_token_auth_type(token)
        auth_instance = get_auth(auth_type)

        auth_config = sso_config_service.get_sso_config()
        if not auth_instance:
            return build_failed(ResponseCode.REQUEST_ERROR, message="no user info")
        data = {}
        if not auth_config or auth_config.authentication_type == AuthenticationType.DEFAULT:
            auth_instance.logout(token)
        else:
            sso_login_base = SSOLoginBase()
            data["redirect_url"] = sso_login_base.user_logout(token)
        audit_log = "用户登出"
        return build_success(audit_log=audit_log, data=data)


class CaptchaView(APIView):
    def get(self, request: Request):
        addr: str = get_ip(request)
        if not addr:
            return build_failed(ResponseCode.REQUEST_ERROR, message="系统异常")
        try:
            letter: str = generate_random_string(length=4)
            captcha_service.create_captcha(addr=addr, content=letter, expire=5 * 60)
            captcha_image = generate_captcha(letter)
            return build_success(data={"captcha": captcha_image})
        except Exception as e:
            log.error(f"Create a new captcha error:{str(e)}")
            return build_failed(code=ResponseCode.INVALID_DATA, message="配置出错")


class IsActiveCaptchaView(APIView):
    def get(self, request: Request):
        addr: str = get_ip(request)
        if not addr:
            return build_failed(ResponseCode.REQUEST_ERROR, message="系统异常")
        user_serializer = CaptchaValidateSerializer(data=request.query_params)
        if not user_serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message="参数异常")
        username: str = user_serializer.validated_data.get("username", None)
        data = {}
        data["is_active"] = False
        captcha, no_captcha_max_try, _, _ = SSOConfigBaseHandle(AuthenticationType.DEFAULT.value).get_auth_config()
        if not captcha:
            return build_success(data=data)
        try:
            if captcha and self._validate_max_try_login(username=username, try_login_num=no_captcha_max_try):
                data["is_active"] = True
            return build_success(data=data)
        except Exception as e:
            log.error(f"captcha error:{str(e)}")
            return build_failed(code=ResponseCode.INVALID_DATA, message="配置出错")

    def _validate_max_try_login(self, username: str, try_login_num: int) -> bool:
        user = None
        if username:
            user = user_service.get_user(username=username)
        if user:
            if user.try_login_count >= try_login_num:
                return True
            return False
        return False
