from rest_framework.request import Request
from rest_framework.views import APIView

from caasm_config.config import caasm_config
from caasm_service.constants.setting import SettingName
from caasm_service.runtime import user_service, role_service, setting_service
from caasm_tool.util import rsa_decrypt
from caasm_webapi.app.auth.serializers.user import (
    UserAddSerializer,
    UserModifySerializer,
    UserListSerializer,
    UserSerializer,
    UserDeleteSerializer,
    ModifyMyPasswordSerializer,
)
from caasm_webapi.auth import UsernameAuth
from caasm_webapi.util.response import build_success, build_failed, ResponseCode


class UserAddView(APIView):
    def post(self, request: Request):
        serializer = UserAddSerializer(data=request.data, context={"user": request.user})
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)

        validated_data = serializer.validated_data
        validated_data["is_super"] = False

        user_id = user_service.save_user(user_service.load_entity(**validated_data))

        if not user_id:
            return build_failed(ResponseCode.SYSTEM_ERROR, message="创建失败，请检查")
        audit_log = f"新增用户 【{validated_data['username']}】"
        return build_success(audit_log=audit_log)


class UserModifyView(APIView):
    def post(self, request: Request):
        serializer = UserModifySerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)

        validated_data = serializer.validated_data

        user_id = validated_data.pop("user_id")

        user = user_service.get_user(user_id=user_id, is_super=False)
        if not user:
            return build_success()
        user_mapper = user_service.dump_mapper(user)

        user_mapper.update(validated_data)

        user_instance = user_service.load_entity(**user_mapper)

        user_service.update_user(user_instance)
        self.flush_cache(user)
        if user_instance.role_codes:
            audit_log = f"给【{user.username}】分配【{'、'.join(user_instance.role_codes)}】角色"
        else:
            audit_log = f"修改用户【{user.username}】的信息"
        return build_success(audit_log=audit_log)

    @classmethod
    def flush_cache(cls, user):
        if not user.enabled:
            UsernameAuth.flush_user_info(user.id)
            return
        UsernameAuth.refresh_user_info(user, caasm_config.AUTH_COOKIE_EXPIRE)


class UserListView(APIView):
    permission_classes = []

    def get(self, request: Request):
        serializer = UserListSerializer(data=request.query_params)

        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)

        validate_data = serializer.validated_data
        keyword = validate_data.get("keyword", "")
        page_index = validate_data.get("page_index", 0)
        page_size = validate_data.get("page_size", 20)
        sort_fields = validate_data.get("sort_fields", [])
        bind_role_flag = validate_data.get("bind_role_flag")

        count = user_service.get_user_count(keyword=keyword, bind_role_flag=bind_role_flag, is_super=False)
        data = []
        if count:
            _cursor = user_service.find_user(
                keyword=keyword,
                offset=page_index * page_size,
                limit=page_size,
                sort_fields=sort_fields,
                is_super=False,
                bind_role_flag=bind_role_flag,
            )
            users = list(_cursor)
            role_codes = set()
            for user in users:
                role_codes = role_codes.union(user.role_codes)

            role_codes = list(role_codes)
            role_mapper = (
                {role.code: role for role in list(role_service.find_role(codes=role_codes, fields=["code", "name"]))}
                if role_codes
                else {}
            )

            for user in users:
                role_codes = user.role_codes
                roles = []

                for role_code in role_codes:
                    role = role_mapper.get(role_code)
                    if not role:
                        continue
                    roles.append({"name": role.name, "code": role_code})

                user.roles = roles

            data = UserSerializer(users, many=True).data

        result = {"total": count, "data": data}

        return build_success(result)


class UserDeleteView(APIView):
    def post(self, request):
        serializer = UserDeleteSerializer(data=request.data)

        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)

        validated_data = serializer.validated_data
        user_ids = validated_data.get("user_ids")
        if not user_ids:
            return build_success()
        user_info = user_service.find_user(user_ids=user_ids, fields=["username"])
        user_names = "、".join([user.username for user in user_info])
        user_service.delete_user(user_ids=user_ids, is_super=False)
        audit_log = f"删除用户【{user_names}】"
        return build_success(audit_log=audit_log)


class TotalUserAPI(APIView):
    def get(self, request):
        users = list(user_service.find_user(is_super=False))
        result = []

        for user in users:
            result.append({"user_id": str(user.id), "username": user.username})

        return build_success(result)


class UserInfoView(APIView):
    def get(self, request):
        if not hasattr(request, "user"):
            return build_success(data={})

        user_info = request.user
        user_id = user_info.user_id
        username = user_info.username
        roles = user_info.role_codes
        data = {"id": user_id, "username": username, "roles": roles}
        return build_success(data=data)


class ModifyMyPasswordView(APIView):
    def post(self, request):
        serializer = ModifyMyPasswordSerializer(data=request.data)

        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)
        validated_data = serializer.validated_data

        user_info = request.user
        user_id = user_info.user_id
        username = user_info.username
        user_info_data = user_service.get_user(user_id=user_id)

        private_key = setting_service.get_setting(SettingName.PRIVATE_KEY).value
        old_password = validated_data.get("old_password")
        password = validated_data.get("password")

        old_dec_password = rsa_decrypt(private_key, old_password)
        old_password_info = user_service.get_enc_password(old_dec_password)

        if old_password_info != user_info_data.password:
            return build_failed(ResponseCode.REQUEST_ERROR, message="旧密码输入错误")

        user_service.update_user_info_by_user_id_or_name(user_id=user_id, data={"password": password})
        audit_log = f"用户【{username}】修改了密码！"
        return build_success(audit_log=audit_log)
