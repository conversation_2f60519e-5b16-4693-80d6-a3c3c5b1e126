from rest_framework.views import APIView
from rest_framework.request import Request
from caasm_auth_manage.sso_config.base import SSOConfigBaseHandle
from caasm_service.constants.auth import AuthenticationStatus, AuthenticationType
from caasm_service.runtime import sso_config_service
from caasm_webapi.app.auth.serializers.oss import (
    SSOConfigDetailResponseSerializer,
    SSOSaveSerializer,
    SSOConfigUpdateSerializer,
)
from caasm_webapi.util.response import build_success, build_failed, ResponseCode


class SSOConfigView(APIView):
    """
    ssoconfig 获取接口接口
    """

    def get(self, request: Request):
        sso_config = SSOConfigBaseHandle()
        data = sso_config.handle()
        return build_success(data=data)


class SSOConfigDetailView(APIView):
    """
    获取生效的ossconfig
    """

    def get(self, request: Request):
        data = sso_config_service.get_sso_config()
        if not data:
            return build_success(data={"authentication_type": AuthenticationType.DEFAULT.value, "config": {}})
        result = {"authentication_type": data.authentication_type.value, "config": data.config}
        return build_success(SSOConfigDetailResponseSerializer(instance=result).data)


class SSOConfigSaverView(APIView):
    """
    单点登录保存接口
    """

    def post(self, request: Request):
        serializer = SSOSaveSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)

        validated_data = serializer.validated_data
        validated_data["status"] = AuthenticationStatus.ENABLE
        sso_config_service.delete_other_config()
        sso_config_service.save_sso_config(validated_data)
        return build_success()


class SSOConfigUpdateView(APIView):
    """
    单点登录配置更新接口
    """

    def post(self, request: Request):
        serializer = SSOConfigUpdateSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)

        validated_data = serializer.validated_data
        authentication_type = validated_data.get("authentication_type")
        config = validated_data.get("config")
        sso_config_service.update_sso_config(authentication_type=authentication_type, config=config)
        return build_success()
