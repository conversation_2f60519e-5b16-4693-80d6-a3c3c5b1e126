from rest_framework.serializers import Serializer
from rest_framework import serializers, fields
from caasm_service.constants.auth import AuthenticationType
from caasm_service.constants.setting import SettingName
from caasm_service.runtime import sso_config_service, temp_login_key_service, user_service, setting_service
from caasm_tool.patch.serializer import SerializerMixin
from caasm_tool.util import rsa_decrypt
from caasm_webapi.app.auth.serializers.user import UserAddSerializer


class SSOConfigBaseSerializer(SerializerMixin):
    default_roles = serializers.ListSerializer(child=serializers.CharField(), required=True, help_text="默认角色")


class DefaultAuthenticationSerializer(SerializerMixin):
    is_active_captcha = serializers.BooleanField(required=False, help_text="是否开启验证码")
    no_captcha_max_try = serializers.IntegerField(required=False, help_text="无验证码情况,登录尝试最大次数")
    captcha_max_try = serializers.IntegerField(required=False, help_text="有验证码情况,登录尝试最大次数")
    lockout_time = serializers.IntegerField(required=False, help_text="锁定时间")

    def validate(self, attrs):
        is_active_captcha = attrs.get("is_active_captcha", None)
        if is_active_captcha is None:
            raise serializers.ValidationError("参数is_active_captcha不能为空")
        no_captcha_max_try = attrs.get("no_captcha_max_try", None)
        if no_captcha_max_try is not None:
            if no_captcha_max_try < 1 or no_captcha_max_try > 5:
                raise serializers.ValidationError("参数no_captcha_max_try必须在1~5之间")
        captcha_max_try = attrs.get("captcha_max_try", None)
        if captcha_max_try is not None:
            if captcha_max_try < 1 or captcha_max_try > 5:
                raise serializers.ValidationError("参数captcha_max_try必须在1~5之间")
        lockout_time = attrs.get("lockout_time", None)
        if lockout_time is not None:
            if lockout_time < 5 or lockout_time > 30:
                raise serializers.ValidationError("参数lockout_time必须在5~30之间")
        return attrs


class TempUserListSerializer(SerializerMixin):
    login_key = serializers.CharField(required=True, help_text="用户")
    keyword = serializers.CharField(required=False, help_text="keyword")

    def validate(self, attrs):
        login_key = attrs.get("login_key")

        login_key_data = temp_login_key_service.get_temp_login_key(login_key=login_key)

        if not login_key_data:
            raise serializers.ValidationError("无效的用户")

        attrs["sso_key"] = login_key_data.get("sso_key")
        attrs["authentication_type"] = login_key_data.get("authentication_type")
        return attrs


class DefaultSSOUserBindASMUserSerializer(TempUserListSerializer):
    username = serializers.CharField(required=True, help_text="用户名称")
    password = serializers.CharField(required=True, help_text="验证密码")

    def validate(self, attrs):
        attrs = super(DefaultSSOUserBindASMUserSerializer, self).validate(attrs)
        username = attrs.get("username")
        enc_password = attrs.get("password")
        sso_key = attrs.get("sso_key")
        self.validate_user_info(username=username, sso_key=sso_key, attrs=attrs)
        self.validate_user_password(username=username, password=enc_password)
        return attrs

    def validate_user_password(self, username=None, password=None):
        private_content = setting_service.get_setting(SettingName.PRIVATE_KEY).value
        dec_password = rsa_decrypt(private_content, password)
        if not dec_password:
            raise serializers.ValidationError("用户信息认证失败！")
        req_dec_password = user_service.get_enc_password(dec_password)
        user_info = user_service.get_user(username=username)
        if user_info.password != req_dec_password:
            raise serializers.ValidationError("用户信息认证失败！")

    def validate_user_info(self, username=None, sso_key=None, attrs=None):
        user_info = user_service.get_user(username=username, is_super=False)
        if not user_info:
            raise serializers.ValidationError("用户信息认证失败！")

        if user_info.sso_keys and sso_key in user_info.sso_keys:
            raise serializers.ValidationError("用户信息认证失败")

        for _sso_key in user_info.sso_keys:
            if _sso_key.startswith(attrs.get("authentication_type")):
                raise serializers.ValidationError("该用户已绑定！")


class SSOUserADDASMUserSerializer(UserAddSerializer, TempUserListSerializer):
    def validate(self, attrs):
        result = super(SSOUserADDASMUserSerializer, self).validate(attrs)
        result = TempUserListSerializer.validate(self, attrs=result)
        config = sso_config_service.get_sso_config()
        result["role_codes"] = config.config.get("default_roles")
        return result


class CasSerializer(SSOConfigBaseSerializer):
    authentication_system_name = serializers.CharField(required=True, help_text="平台名称")
    authentication_jump_url = serializers.CharField(required=True, help_text="认证跳转URL")
    ticket_verification_url = serializers.CharField(required=True, help_text="Ticket验证URL")

    def validate(self, attrs):
        authentication_jump_url = attrs.get("authentication_jump_url")
        ticket_verification_url = attrs.get("ticket_verification_url")
        attrs["authentication_jump_url"] = "".join(authentication_jump_url.split())
        attrs["ticket_verification_url"] = "".join(ticket_verification_url.split())
        return attrs


class SSOSaveSerializer(SerializerMixin):
    authentication_type = serializers.ChoiceField(
        choices=AuthenticationType._value2member_map_, required=True, help_text="权限认证模型"
    )
    config = serializers.DictField(required=True)

    def validate(self, attrs):
        authentication_type = attrs.get("authentication_type")
        attrs["authentication_type"] = AuthenticationType(authentication_type)
        config = attrs.get("config")

        if authentication_type == AuthenticationType.CAS.value:
            serializer = CasSerializer(data=config)
            if not serializer.is_valid():
                raise serializers.ValidationError(serializer)
        if authentication_type == AuthenticationType.DEFAULT.value and config:
            serializer = DefaultAuthenticationSerializer(data=config)
            if not serializer.is_valid():
                raise serializers.ValidationError(serializer)
            attrs["config"] = serializer.validated_data
        return attrs


class CasAuthenticationSerializer(SerializerMixin):
    ticket = serializers.CharField(required=True, help_text="ticket参数")


class SSOConfigUpdateSerializer(SSOSaveSerializer):
    def validate(self, attrs):
        data = super(SSOConfigUpdateSerializer, self).validate(attrs)
        authentication_type = data.get("authentication_type")
        config = sso_config_service.get_sso_config(authentication_type=authentication_type)
        if not config:
            raise serializers.ValidationError("查找不到该类型的单点登录配置")
        return data


class SSOConfigDetailResponseSerializer(Serializer):
    authentication_type = fields.CharField()
    config = fields.DictField()


class CaptchaValidateSerializer(Serializer):
    username = fields.CharField(required=False, help_text="用户名")

    def validate(self, attrs):
        attrs["username"] = attrs.get("username", None)
        return attrs
