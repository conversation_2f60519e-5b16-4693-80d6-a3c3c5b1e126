import re

from bson import ObjectId
from rest_framework import serializers

from caasm_service.constants.setting import Setting<PERSON>ame
from caasm_service.runtime import setting_service, user_service, role_service
from caasm_tool import log
from caasm_tool.constants import DATETIME_FORMAT
from caasm_tool.patch.serializer import SerializerMixin
from caasm_tool.re_table import PASSWORD_RE
from caasm_tool.util import rsa_decrypt
from caasm_webapi.util.tool import Lock


class UserAddSerializer(SerializerMixin):
    _check_single_prop_mapper = {"username": "用户名称", "mobile": "手机号", "email": "邮箱"}

    username = serializers.CharField(max_length=32, help_text="用户名称", required=True)
    password = serializers.CharField(max_length=512, help_text="密码", required=True)
    email = serializers.EmailField(help_text="邮箱", required=False, allow_null=True, default="")
    mobile = serializers.CharField(max_length=24, help_text="手机号", required=False, allow_null=True, default="")
    enabled = serializers.BooleanField(help_text="是否启用", default=True, required=False)
    role_codes = serializers.ListField(child=serializers.CharField(), help_text="角色代码", required=False, allow_null=True)

    _username_regex = re.compile(r"[a-zA-Z][a-zA-Z0-9_]*")

    def validate(self, attrs):
        attrs = super(UserAddSerializer, self).validate(attrs)
        username = attrs.get("username")
        enc_password = attrs.get("password")
        private_key = setting_service.get_setting(SettingName.PRIVATE_KEY).value

        if username:
            if self._username_regex.fullmatch(username) is None:
                raise serializers.ValidationError("用户名必须为大小写英文字母开头，且只能包含大小写英文字母、数字和下划线")
            username_len = len(username)
            if username_len < 3 or username_len > 48:
                raise serializers.ValidationError("用户名长度必须不低于3，不高于48")

        if enc_password:
            dec_password = rsa_decrypt(private_key, enc_password)
            if not isinstance(dec_password, str):
                raise serializers.ValidationError("密码格式不正确")
            if not PASSWORD_RE.match(dec_password):
                raise serializers.ValidationError("密码需要满足：大小写字母、特殊字符及数字组合，长度8~20位!")
            attrs["password"] = user_service.get_enc_password(dec_password)

        self._check_single(**attrs)

        result_attrs = {attr_name: attr_value for attr_name, attr_value in attrs.items() if attr_value is not None}
        return result_attrs

    def _check_single(self, user_id="", **kwargs):
        user = user_service.get_user(user_id=user_id) if user_id else None
        with Lock() as lock:
            for (
                single_key,
                single_display_key,
            ) in self._check_single_prop_mapper.items():
                single_val = kwargs.get(single_key)
                self._check_single_common(user_id, user, lock, single_key, single_val, single_display_key)

    @classmethod
    def validate_role_codes(cls, role_codes):
        if not role_codes:
            return role_codes
        cursor = role_service.find_role(codes=role_codes, fields=["code"])
        return [i.code for i in cursor]

    @classmethod
    def _check_single_common(cls, user_id, user, lock, key, value, display_key):
        if not value:
            return

        current_user_id = getattr(user, "id", None)
        current_value = getattr(user, key, None)

        if current_user_id == user_id and value == current_value:
            return

        if not lock.acquire(value, key):
            raise serializers.ValidationError("请勿重复提交")
        count = user_service.get_user_count(**{key: value})

        if count:
            raise serializers.ValidationError(f"{display_key}已存在")


class UserModifySerializer(UserAddSerializer):
    user_id = serializers.CharField(max_length=32, help_text="用户ID", required=True)
    username = serializers.CharField(max_length=32, help_text="用户名称", required=False, allow_null=True)
    password = serializers.CharField(max_length=512, help_text="密码", required=False, allow_null=True)

    def validate_user_id(self, value):
        if value:
            try:
                value = ObjectId(value)
            except Exception as e:
                log.debug(f"user_id format error({e})")
                raise serializers.ValidationError("用户ID格式无效")
        return value


class UserDeleteSerializer(SerializerMixin):
    user_ids = serializers.ListField(
        child=serializers.CharField(),
        help_text="用户ID",
        required=True,
        allow_empty=False,
    )

    @classmethod
    def validate_user_ids(cls, value):
        user_ids = []
        for user_id in value:
            try:
                value = ObjectId(user_id)
            except Exception as e:
                pass
            else:
                user_ids.append(value)
        if not user_ids:
            raise serializers.ValidationError("用户ID不能为空")
        return user_ids


class UserListSerializer(SerializerMixin):
    keyword = serializers.CharField(max_length=64, help_text="关键字", required=False)
    page_index = serializers.IntegerField(help_text="页码", default=1, required=False)
    bind_role_flag = serializers.BooleanField(help_text="是否绑定角色", default=None, allow_null=True)
    page_size = serializers.IntegerField(help_text="每页最大数", required=False, default=100, max_value=200)
    sort_fields = serializers.ListField(help_text="排序字段", default=[], required=False)


class UserSerializer(SerializerMixin):
    user_id = serializers.CharField(help_text="用户ID", source="id")
    username = serializers.CharField(help_text="用户名称")
    email = serializers.EmailField(help_text="邮箱")
    mobile = serializers.CharField(help_text="手机号")
    enabled = serializers.BooleanField(help_text="状态")
    is_super = serializers.BooleanField(help_text="是否超管")
    roles = serializers.ListField(child=serializers.DictField(), default=list)
    role_desc = serializers.SerializerMethodField("get_role_desc", help_text="角色展示")
    create_time = serializers.DateTimeField(help_text="创建时间", format=DATETIME_FORMAT)
    update_time = serializers.DateTimeField(help_text="更新时间", format=DATETIME_FORMAT)

    def get_role_desc(self, obj):
        roles = obj.roles or []
        return ",".join([role["name"] for role in roles])


class ModifyMyPasswordSerializer(SerializerMixin):

    old_password = serializers.CharField(max_length=512, help_text="密码", required=True)
    new_password = serializers.CharField(max_length=512, help_text="密码", required=True)
    re_password = serializers.CharField(max_length=512, help_text="密码", required=True)

    def validate(self, attrs):
        new_password = attrs.get("new_password")
        re_password = attrs.get("re_password")

        private_key = setting_service.get_setting(SettingName.PRIVATE_KEY).value

        dec_password = rsa_decrypt(private_key, new_password)
        re_dec_password = rsa_decrypt(private_key, re_password)

        if not isinstance(dec_password, str):
            raise serializers.ValidationError("密码格式不正确")
        if not PASSWORD_RE.match(dec_password):
            raise serializers.ValidationError("密码需要满足：大小写字母、特殊字符及数字组合，长度8~20位!")

        if dec_password != re_dec_password:
            raise serializers.ValidationError("新设置密码不一致！")

        attrs["password"] = user_service.get_enc_password(dec_password)
        return attrs
