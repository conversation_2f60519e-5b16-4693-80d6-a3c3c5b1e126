import time

from rest_framework import serializers

from caasm_config.config import caasm_config
from caasm_service.constants.auth import AuthenticationType
from caasm_service.runtime import sso_config_service
from caasm_tool.constants import AuthType
from caasm_tool.patch.serializer import SerializerMixin


class AuthSerializer(SerializerMixin):
    timestamp = serializers.IntegerField(required=True, help_text="时间戳")
    auth_type = serializers.ChoiceField(choices=[AuthType.PASSWORD.value], default=AuthType.PASSWORD.value)
    settings = serializers.DictField(required=True)

    def validate_timestamp(self, value):
        now = int(time.time())

        diff = abs(now - value)

        if not (0 <= diff <= caasm_config.AUTH_TIMESTAMP_DIFF_SECOND):
            raise serializers.ValidationError("无效的时间戳")
        return value

    def validate(self, attrs):
        auth_settings_validate_mapper = {
            AuthType.PASSWORD.value: self._validate_password_settings,
        }
        auth_type = attrs.get("auth_type")
        settings = attrs.get("settings")

        auth_type_validate = auth_settings_validate_mapper[auth_type]
        auth_type_validate(settings)

        result = super(AuthSerializer, self).validate(attrs)
        return result

    @classmethod
    def _validate_password_settings(cls, settings):
        username = settings.get("username", "")
        password = settings.get("password", "")

        if not (username and isinstance(username, str)):
            raise serializers.ValidationError("用户名或者密码不正确")

        authentication_method = sso_config_service.get_sso_config()
        if authentication_method and authentication_method.authentication_type != AuthenticationType.DEFAULT.value:
            if username != caasm_config.DEFAULT_SUPER_USER:
                raise serializers.ValidationError("在【非系统默认认证模式】下只有【超级管理员】可以登录！")

        if not (password and isinstance(password, str)):
            raise serializers.ValidationError("用户名或者密码不正确")
