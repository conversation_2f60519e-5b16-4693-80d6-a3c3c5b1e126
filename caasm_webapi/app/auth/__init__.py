from caasm_tool.patch.router import DefaultRouter
from caasm_webapi.app.auth.views.auth import AuthView, LogoutView, CaptchaView, IsActiveCaptchaView
from caasm_webapi.app.auth.views.key import PublicKeyView
from caasm_webapi.app.auth.views.sso.sso_authentication import CasAuthenticationView, TempUserListView
from caasm_webapi.app.auth.views.sso.sso_base import (
    SSOUserBindASMUserView,
    SSOUserADDASMUserView,
    SSOOtherSystemLoginView,
)
from caasm_webapi.app.auth.views.sso.sso_config import (
    SSOConfigView,
    SSOConfigDetailView,
    SSOConfigUpdateView,
    SSOConfigSaverView,
)
from caasm_webapi.app.auth.views.user import (
    UserAddView,
    UserDeleteView,
    UserModifyView,
    UserListView,
    TotalUserAPI,
    UserInfoView,
    ModifyMyPasswordView,
)

auth_router = DefaultRouter()
auth_router.join_path("publicKey/", PublicKeyView, name="PublicKey")
auth_router.join_path("auth/", AuthView, name="AuthView")
auth_router.join_path("logout/", LogoutView, name="LogoutView")
auth_router.join_path("userInfo/", UserInfoView, name="UserInfoAPI")
auth_router.join_path("userAdd/", UserAddView, name="UserAddView")
auth_router.join_path("userList/", UserListView, name="UserListView")
auth_router.join_path("userDelete/", UserDeleteView, name="UserRemoveView")
auth_router.join_path("userModify/", UserModifyView, name="UserModifyView")
auth_router.join_path("user/total/", TotalUserAPI, name="TotalUserAPI")
auth_router.join_path("ssoConfig/list/", SSOConfigView, name="SSOConfigView")
auth_router.join_path("ssoConfig/save/", SSOConfigSaverView, name="SSOConfigSaverView")
auth_router.join_path("sso/ssoConfig/detail/", SSOConfigDetailView, name="SSOConfigDetailView")
auth_router.join_path("ssoConfig/update/", SSOConfigUpdateView, name="SSOConfigUpdateView")
auth_router.join_path("casAuthentication/", CasAuthenticationView, name="CasAuthenticationView")
auth_router.join_path("ssoUserBind/", SSOUserBindASMUserView, name="SSOUserBindASMUser")
auth_router.join_path("ssoUserADD/", SSOUserADDASMUserView, name="SSOUserADDASMUserView")
auth_router.join_path("tempUserList/", TempUserListView, name="TempUserListView")
auth_router.join_path("authenticationJumpUrl/", SSOOtherSystemLoginView, name="SSOOtherSystemLoginView")
auth_router.join_path("user/password/modify/", ModifyMyPasswordView, name="ModifyMyPasswordView")
auth_router.join_path("captcha/", CaptchaView, name="AuthCaptchaView")
auth_router.join_path("isActiveCaptcha/", IsActiveCaptchaView, name="IsActiveCaptchaView")


default_app_config = "caasm_webapi.app.auth.appconf.AuthAppConfig"
