from caasm_tool.patch.router import DefaultRouter
from caasm_webapi.app.vul.views import (
    ListVulFilesAPIView,
    CreateVulFileAPIView,
    DownloadVulFileAPIView,
    DeleteVulFileAPIView,
)

vul_router = DefaultRouter("vul")

vul_router.join_path("listVulFiles/", ListVulFilesAPIView, name="ListVulFiles")
vul_router.join_path("createVulFile/", CreateVulFileAPIView, name="CreateVulFile")
vul_router.join_path("downloadVulFile/<id_>/", DownloadVulFileAPIView, name="DownloadVulFile")
vul_router.join_path("deleteVulFile/<id_>/", DeleteVulFileAPIView, name="DeleteVulFile")
