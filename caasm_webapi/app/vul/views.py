import os.path
from urllib.parse import quote

from django.core.files.uploadedfile import InMemoryUploadedFile
from django.http import HttpResponse
from rest_framework.request import Request
from rest_framework.views import APIView

from caasm_service.runtime import vul_file_service
from caasm_webapi.app.vul.requests import ListVulFileRequest
from caasm_webapi.app.vul.serializers import (
    VulFileSerializer,
    ListVulFileRequestSerializer,
    CreateVulFileRequestSerializer,
)
from caasm_webapi.util.response import build_success, build_failed


class ListVulFilesAPIView(APIView):
    def post(self, request):
        serializer = ListVulFileRequestSerializer(data=request.data)
        if serializer.is_valid():
            req: ListVulFileRequest = serializer.save()
            return build_success(
                {
                    "count": vul_file_service.file_count(req.keyword),
                    "data": VulFileSerializer(
                        instance=vul_file_service.find_files(req.keyword, req.page_size, req.page_index - 1), many=True
                    ).data,
                }
            )
        else:
            return build_failed(str(serializer.error_messages))


class CreateVulFileAPIView(APIView):
    def post(self, request: Request):
        if "file" not in request.FILES:
            return build_failed(-1, "未上传文件或文件大小超出20MB")
        file: InMemoryUploadedFile = request.FILES["file"]
        _, ext_name = os.path.splitext(file.name)
        serializer = CreateVulFileRequestSerializer(data=request.data)
        if serializer.is_valid():
            name = request.data.get("name")
            if vul_file_service.exists_file(name):
                return build_failed(-1, "文件名称已存在")
            vul_file_service.save_vul_file(name, ext_name, request.user.username if request.user else "", file.read())
            return build_success({})
        else:
            return build_failed(str(serializer.error_messages))


class DownloadVulFileAPIView(APIView):
    def get(self, request, id_):
        file_name, content = vul_file_service.get_vul_file(id_)
        if file_name is None or content is None:
            return build_failed(-1, "文件不存在")
        mine_type = "application/octet-stream"
        response = HttpResponse(content)
        response["Content-Type"] = mine_type  # 设置头信息，告诉浏览器这是个文件
        response["Content-Disposition"] = f'filename="{quote(file_name)}"'
        return response


class DeleteVulFileAPIView(APIView):
    def get(self, request, id_):
        vul_file_service.delete_file(id_)
        return build_success({})
