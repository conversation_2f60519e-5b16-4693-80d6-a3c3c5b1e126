from caasm_tool.patch.router import DefaultRouter
from caasm_webapi.app.adapter import adapter_router
from caasm_webapi.app.alarm_manage import alarm_manage_router
from caasm_webapi.app.api_call_record import api_call_record_router
from caasm_webapi.app.audit import audit_router
from caasm_webapi.app.auth import auth_router
from caasm_webapi.app.business import business_router
from caasm_webapi.app.config import config_router
from caasm_webapi.app.data_management import data_management_router
from caasm_webapi.app.data_visualization import data_visualization_router
from caasm_webapi.app.export import export_router
from caasm_webapi.app.exposed_surface import exposed_surface_router
from caasm_webapi.app.fabric import fabric_router
from caasm_webapi.app.files import file_router
from caasm_webapi.app.meta_model import meta_model_router
from caasm_webapi.app.overview import overview_route
from caasm_webapi.app.permission import permission_router
from caasm_webapi.app.proxy import proxy_router
from caasm_webapi.app.query_engine import query_engine_router, query_engine_external_router
from caasm_webapi.app.snapshot import snapshot_router
from caasm_webapi.app.user import user_router
from caasm_webapi.app.variables import variable_router
from caasm_webapi.app.vul import vul_router
from caasm_webapi.app.vul_priority import vul_priority_router
from caasm_webapi.app.vulnerability import vulnerability_router

# 内部调用，提供给前端使用
INTERNAL_ROUTER = DefaultRouter()
_internal_routers = {
    adapter_router,
    overview_route,
    alarm_manage_router,
    auth_router,
    business_router,
    file_router,
    audit_router,
    variable_router,
    config_router,
    meta_model_router,
    data_visualization_router,
    query_engine_router,
    user_router,
    proxy_router,
    vulnerability_router,
    fabric_router,
    api_call_record_router,
    permission_router,
    snapshot_router,
    vul_priority_router,
    vul_router,
    exposed_surface_router,
    data_management_router,
    export_router,
}
for _internal_router in _internal_routers:
    INTERNAL_ROUTER.extend(_internal_router)

# 第三方调用，通过sdk调用
EXTERNAL_ROUTER = DefaultRouter("external")
_external_routers = [query_engine_external_router]
for _external_router in _external_routers:
    EXTERNAL_ROUTER.extend(_external_router)
