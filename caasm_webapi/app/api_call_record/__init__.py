from caasm_tool.patch.router import DefaultRouter
from caasm_webapi.app.api_call_record.views.api_call_record import (
    ApiCallRecordListAPI,
    ApiCallRecordRemoveDayGetAPI,
    ApiCallRecordRemoveDaySetAPI,
)

api_call_record_router = DefaultRouter("apiCallRecord")

api_call_record_router.join_path("list/", ApiCallRecordListAPI)
api_call_record_router.join_path("removeDay/info/", ApiCallRecordRemoveDayGetAPI)
api_call_record_router.join_path("removeDay/modify/", ApiCallRecordRemoveDaySetAPI)
