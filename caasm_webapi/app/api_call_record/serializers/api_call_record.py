from rest_framework import fields

from caasm_tool.constants import DATETIME_FORMAT
from caasm_tool.patch.serializer import SerializerMixin


class ApiCallRecordSerializer(SerializerMixin):
    page_size = fields.IntegerField(required=True, max_value=100, min_value=1, help_text="分页条数")
    page_index = fields.IntegerField(required=True, min_value=1, help_text="分页索引，从1开始")
    sort_fields = fields.ListField(child=fields.CharField(), default=["-create_time"])


class ApiCallRecordDetailSerializer(SerializerMixin):
    _ENABLED_MAPPER = {True: "成功", False: "失败"}

    url = fields.CharField()
    ip = fields.CharField()
    enabled = fields.BooleanField()
    enabled_display_name = fields.SerializerMethodField("get_enabled_display_name")
    status_code = fields.IntegerField()
    request_params = fields.DictField()
    create_time = fields.DateTimeField(format=DATETIME_FORMAT)
    update_time = fields.DateTimeField(format=DATETIME_FORMAT)

    def get_enabled_display_name(self, obj):
        return self._ENABLED_MAPPER.get(obj.enabled)


class ApiCallRecordRemoveDaySetSerializer(SerializerMixin):
    num = fields.IntegerField(required=True)
    unit = fields.ChoiceField(required=True, choices=["月", "天"])
