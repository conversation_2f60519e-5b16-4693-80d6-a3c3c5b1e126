from rest_framework import fields

from caasm_tool.patch.serializer import SerializerMixin


class OperationLogRequestSerializer(SerializerMixin):
    """
    操作日志启动
    """

    start_time = fields.CharField(help_text="开始时间", default=None, required=False)
    end_time = fields.CharField(help_text="结束时间", default=None, required=False)
    keyword = fields.CharField(help_text="关键字", required=False)
    page_size = fields.IntegerField(max_value=100, min_value=1, default=20, allow_null=True, help_text="分页条数")
    page_index = fields.IntegerField(min_value=1, default=1, allow_null=True, help_text="分页索引，从1开始")
    sort_fields = fields.ListField(child=fields.CharField(), default=["-time"])
