from caasm_tool.patch.router import DefaultRouter
from caasm_webapi.app.audit.view.operation_log import (
    OperationLogListAPIView,
    GetOperationLogConfigAPIView,
    UpdateOperationLogConfigAPIView,
)

audit_router = DefaultRouter()

audit_router.join_url(url="audit/operation/list/", view=OperationLogListAPIView, name="查询操作日志列表")
audit_router.join_url(url="audit/config/", view=GetOperationLogConfigAPIView, name="GetOperationLogConfigAPIView")
audit_router.join_url(
    url="audit/updateConfig/", view=UpdateOperationLogConfigAPIView, name="UpdateOperationLogConfigAPIView"
)
