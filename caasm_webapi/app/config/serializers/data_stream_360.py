from rest_framework import fields
from rest_framework.serializers import Serializer

from caasm_service.entity.data_stream_360 import DataStream360RecordEntity
from caasm_webapi.app.config.requests.data_stream_360 import DataStream360ConfigRequest


class DataStream360RealmSerializer(Serializer):
    config = fields.CharField(max_length=65535)

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        return validated_data["config"]


class DataStream360RecordResponseSerialize(Serializer):
    successful = fields.BooleanField()
    result = fields.SerializerMethodField()
    error = fields.CharField()
    create_time = fields.DateTimeField()

    def get_result(self, obj: DataStream360RecordEntity):
        if obj.result:
            return obj.result.encode().decode("unicode-escape").replace("\\", "")
        else:
            return ""


class UpdateDataStream360ConfigRequestSerializer(Serializer):
    url = fields.Char<PERSON>ield(required=True, max_length=1024)
    api_key = fields.Char<PERSON>ield(required=True, max_length=128)
    src_count = fields.IntegerField(required=True, max_value=999, min_value=1)
    dst_count = fields.IntegerField(required=True, max_value=999, min_value=1)

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        req = DataStream360ConfigRequest()
        req.url = validated_data["url"]
        req.api_key = validated_data["api_key"]
        req.src_count = validated_data["src_count"]
        req.dst_count = validated_data["dst_count"]
        return req


class UpdateDataCenterNamesRequestSerializer(Serializer):
    names = fields.ListField(child=fields.CharField(), default=list, max_length=3)

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        return {"names": validated_data["names"]}
