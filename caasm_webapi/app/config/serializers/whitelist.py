from IPy import IP
from rest_framework import serializers

from caasm_service.constants.setting import Setting<PERSON>ame
from caasm_service.constants.whitelist import ENABLED_CHOICES
from caasm_service.runtime import whitelist_service, setting_service
from caasm_tool.constants import DATETIME_FORMAT
from caasm_tool.patch.serializer import SerializerMixin


class ValidateCommon(object):
    def validate_address(self, address):
        if len(address.split(".")) != 4:
            raise serializers.ValidationError("白名单信息无效")
        if "-" in address:
            return self._validate_address_range(address)
        return self._validate_address_basic(address)

    def _validate_address_range(self, address):
        address_info = address.split("-")
        if len(address_info) != 2:
            raise serializers.ValidationError("白名单信息无效")

        start_address, end_point = address_info
        end_address = ".".join(start_address.split(".")[:3]) + f".{end_point}"
        return self._validate_address_basic(start_address) and self._validate_address_basic(end_address) and address

    @classmethod
    def _validate_address_basic(cls, address):
        try:
            IP(address)
        except Exception as e:
            raise serializers.ValidationError("白名单信息无效")
        return address


class WhitelistListSerializer(SerializerMixin):
    page_index = serializers.IntegerField(required=True, min_value=1)
    page_size = serializers.ChoiceField(required=True, choices=[20, 40, 60, 60])
    sort_fields = serializers.ListField(
        child=serializers.CharField(), allow_null=True, default=lambda: ["-update_time"]
    )


class WhitelistAddSerializer(SerializerMixin, ValidateCommon):
    address = serializers.CharField(required=True, help_text="值")
    enabled = serializers.BooleanField(required=True, help_text="是否启用")


class WhitelistModifySerializer(SerializerMixin, ValidateCommon):
    address = serializers.CharField(required=False, help_text="值", allow_null=True)
    enabled = serializers.BooleanField(required=False, help_text="是否启用", allow_null=True)
    id = serializers.CharField(min_length=24, max_length=24, required=True)


class WhitelistDeleteSerializer(SerializerMixin):
    ids = serializers.ListField(child=serializers.CharField(min_length=24, max_length=24, required=True), min_length=1)

    def validate(self, attrs):
        attrs = super(WhitelistDeleteSerializer, self).validate(attrs)
        ids = attrs["ids"]

        if setting_service.get_setting(SettingName.WHITELIST).value:
            if whitelist_service.get_whitelist_count(record_ids=ids) == whitelist_service.get_whitelist_count():
                raise serializers.ValidationError("开启白名单功能时，不能删除全部白名单记录")
        return attrs


class WhitelistDetailSerializer(SerializerMixin):
    id = serializers.CharField()
    address = serializers.CharField()
    enabled = serializers.BooleanField()
    enabled_display_name = serializers.SerializerMethodField("get_enabled_display_name")
    create_time = serializers.DateTimeField(format=DATETIME_FORMAT)
    update_time = serializers.DateTimeField(format=DATETIME_FORMAT)

    def get_enabled_display_name(self, obj):
        return ENABLED_CHOICES.get(obj.enabled, "")


class WhitelistSwitchSerializer(SerializerMixin):
    enabled = serializers.BooleanField(required=True)

    def validate_enabled(self, enabled):
        if not enabled:
            return enabled

        if not whitelist_service.get_whitelist_count(enabled=True):
            raise serializers.ValidationError("暂无可用的白名单，请先添加一条可用的白名单")
        return enabled


class WhitelistSwitchMultiSerializer(SerializerMixin):
    ids = serializers.ListField(child=serializers.CharField(min_length=24, max_length=24, required=True), min_length=1)
    enabled = serializers.BooleanField(required=True)

    def validate(self, attrs):
        attrs = super(WhitelistSwitchMultiSerializer, self).validate(attrs)

        ids = attrs["ids"]
        enabled = attrs["enabled"]

        if not enabled and setting_service.get_setting(SettingName.WHITELIST).value:
            if whitelist_service.get_whitelist_count(record_ids=ids) == whitelist_service.get_whitelist_count(
                enabled=True
            ):
                raise serializers.ValidationError("开启白名单功能时，不能禁用全部白名单记录")
        return attrs


class AccessKeyDownloadSerializer(SerializerMixin):
    password = serializers.CharField(min_length=32, required=True)
