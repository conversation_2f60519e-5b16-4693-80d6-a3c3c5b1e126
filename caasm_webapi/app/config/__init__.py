from caasm_tool.patch.router import DefaultRouter
from caasm_webapi.app.config.views.access_key import AccessKeyShowAPI, RefreshSecretKeyAPI, AccessKeyDownloadAPI
from caasm_webapi.app.config.views.export import UpdateExportLimitConfigAPIView, GetExportLimitConfigAPIView
from caasm_webapi.app.config.views.system_clean import (
    SourceCleanAPIView,
    WorkflowRestartAPIView,
    GetWorkflowMonitorExceededTimeAPIView,
    UpdateWorkflowMonitorExceededTimeAPIView,
)
from caasm_webapi.app.config.views.data_stream_360 import (
    DataStream360RealmAPIView,
    DataStream360RecordAPIView,
    DataStream360RecordTestAPIView,
    GetDataStream360ConfigAPIView,
    UpdateDataStream360ConfigAPIView,
    GetDataCenterNamesAPIView,
    UpdateDataCenterNamesAPIView,
)
from caasm_webapi.app.config.views.system_config import (
    SystemConfigDetailView,
    SystemConfigModifyView,
    SystemConfigLogoShowView,
    VersionView,
)
from caasm_webapi.app.config.views.system_status import SystemStatusAPIView, SystemCurrentStatusAPIView
from caasm_webapi.app.config.views.whitelist import (
    WhitelistModifyAPI,
    WhitelistListAPI,
    WhitelistDeleteAPI,
    WhitelistAddAPI,
    WhitelistSwitchInfoAPI,
    WhitelistSwitchModifyAPI,
    WhitelistSwitchMultiAPI,
)

config_router = DefaultRouter("systemConfig")

config_router.join_path("detail", SystemConfigDetailView, name="SystemConfigDetailView")
config_router.join_path("modify", SystemConfigModifyView, name="SystemConfigModifyView")
config_router.join_path("logoShow/<file_id>/", SystemConfigLogoShowView, name="SystemConfigLogoShowView")
config_router.join_path("version/", VersionView)

config_router.join_path("whitelist/list/", WhitelistListAPI)
config_router.join_path("whitelist/add/", WhitelistAddAPI)
config_router.join_path("whitelist/modify/", WhitelistModifyAPI)
config_router.join_path("whitelist/delete/", WhitelistDeleteAPI)
config_router.join_path("whitelist/switch/info/", WhitelistSwitchInfoAPI)
config_router.join_path("whitelist/switch/modify/", WhitelistSwitchModifyAPI)
config_router.join_path("whitelist/switch/multi/modify/", WhitelistSwitchMultiAPI)

config_router.join_path("accessKey/info/", AccessKeyShowAPI)
config_router.join_path("secretKey/refresh/", RefreshSecretKeyAPI)
config_router.join_path("accessKey/download/", AccessKeyDownloadAPI)
config_router.join_path("systemStatus/", SystemStatusAPIView, name="SystemStatusAPIView")
config_router.join_path("systemCurrentStatus/", SystemCurrentStatusAPIView, name="SystemCurrentStatusAPIView")

config_router.join_path("cleanSources/", SourceCleanAPIView, name="SourceCleanAPIView")
config_router.join_path("restartWorkflows/", WorkflowRestartAPIView, name="WorkflowRestartAPIView")
config_router.join_path(
    "workflowExceededTime/get/", GetWorkflowMonitorExceededTimeAPIView, name="GetWorkflowMonitorExceededTimeAPIView"
)
config_router.join_path(
    "workflowExceededTime/update/",
    UpdateWorkflowMonitorExceededTimeAPIView,
    name="UpdateWorkflowMonitorExceededTimeAPIView",
)
config_router.join_path("export/update/", UpdateExportLimitConfigAPIView, name="UpdateExportLimitConfigAPIView")
config_router.join_path("export/get/", GetExportLimitConfigAPIView, name="GetExportLimitConfigAPIView")

config_router.join_path("dataStream360/updateRealms/", DataStream360RealmAPIView, name="DataStream360RealmAPIView")
config_router.join_path("dataStream360/records/", DataStream360RecordAPIView, name="DataStream360RecordAPIView")
config_router.join_path("dataStream360/test/", DataStream360RecordTestAPIView, name="DataStream360RecordTestAPIView")
config_router.join_path(
    "dataStream360/config/get/", GetDataStream360ConfigAPIView, name="GetDataStream360ConfigAPIView"
)
config_router.join_path(
    "dataStream360/config/update/", UpdateDataStream360ConfigAPIView, name="UpdateDataStream360ConfigAPIView"
)
config_router.join_path(
    "dataStream360/dataCenterNames/get/", GetDataCenterNamesAPIView, name="GetDataCenterNamesAPIView"
)
config_router.join_path(
    "dataStream360/dataCenterNames/update/", UpdateDataCenterNamesAPIView, name="UpdateDataCenterNamesAPIView"
)
