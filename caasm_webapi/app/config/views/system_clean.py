from rest_framework.views import APIView

from caasm_manage.cleaner import clean_sources
from caasm_scheduler.jobs.snapshot_clear import clear_remnant_es_indices
from caasm_service.constants.setting import SettingName
from caasm_service.entity.setting import Setting
from caasm_service.runtime import setting_service
from caasm_webapi.app.config.serializers.system_clean import UpdateWorkflowExceededTimeSerializer
from caasm_webapi.util.response import build_success, build_failed
from caasm_workflow.cleaner import restart


class SourceCleanAPIView(APIView):
    def get(self, request):
        count, total_size = clean_sources()
        es_count, es_total_size = clear_remnant_es_indices()
        count += es_count
        total_size += es_total_size
        return build_success(
            {"count": count, "total_size": total_size, "es_count": es_count, "es_total_size": es_total_size}
        )


class WorkflowRestartAPIView(APIView):
    def get(self, request):
        restart(True)
        return build_success({})


class GetWorkflowMonitorExceededTimeAPIView(APIView):
    def get(self, request):
        setting = setting_service.get_setting(SettingName.WORKFLOW_EXCEEDED_TIME.value)
        if setting:
            hours = int(setting.value / 60 / 60)
        else:
            hours = 24
        return build_success({"hours": hours})


class UpdateWorkflowMonitorExceededTimeAPIView(APIView):
    def post(self, request):
        serializer = UpdateWorkflowExceededTimeSerializer(data=request.data)
        if serializer.is_valid():
            hours = serializer.save()
            seconds = hours * 60 * 60
            existing_setting: Setting = setting_service.get_setting(SettingName.WORKFLOW_EXCEEDED_TIME.value)
            if existing_setting:
                existing_setting.value = seconds
                setting_service.update(existing_setting)
            else:
                new_setting = setting_service.load_entity(name=SettingName.WORKFLOW_EXCEEDED_TIME.value, value=seconds)
                setting_service.save(new_setting)
            return build_success({})
        return build_failed(-1, str(serializer.errors))
