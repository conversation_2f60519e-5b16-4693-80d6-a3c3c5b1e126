from io import Bytes<PERSON>

from django.http import FileResponse
from rest_framework.views import APIView

from caasm_service.constants.setting import SettingName
from caasm_service.runtime import setting_service, user_service
from caasm_tool.util import get_random_string, rsa_decrypt
from caasm_webapi.app.config.serializers.whitelist import AccessKeyDownloadSerializer
from caasm_webapi.util.response import build_success, build_failed, ResponseCode


class AccessKeyShowAPI(APIView):
    def get(self, request):
        data = {
            "access_key": setting_service.get_setting(SettingName.ACCESS_KEY).value,
            "secret_key": "*" * 32,
        }
        return build_success(data)


class AccessKeyDownloadAPI(APIView):
    def get(self, request):
        serializer = AccessKeyDownloadSerializer(data=request.query_params)
        if not serializer.is_valid():
            return build_failed(code=ResponseCode.REQUEST_ERROR, message=serializer)

        private_key = setting_service.get_setting(SettingName.PRIVATE_KEY).value
        request_password = serializer.validated_data["password"]
        _password = rsa_decrypt(private_key, request_password)

        user = user_service.get_user(user_id=request._user.user_id)
        if user.password != user_service.get_enc_password(_password):
            return build_failed(code=ResponseCode.REQUEST_ERROR, message="密码输入不正确")

        writer = BytesIO()
        writer.write(f"accessKey: {setting_service.get_setting(SettingName.ACCESS_KEY).value}\n".encode("utf-8"))
        writer.write(f"secretKey: {setting_service.get_setting(SettingName.SECRET_KEY).value}\n".encode("utf-8"))
        writer.seek(0)
        response = FileResponse(writer)
        response["Content-Disposition"] = 'attachment;filename="AccessKey.txt"'
        return response


class RefreshSecretKeyAPI(APIView):
    def post(self, request):
        setting_service.modify_setting_simple(get_random_string(32), name=SettingName.SECRET_KEY)
        return build_success("*" * 32)
