import mimetypes
import os.path
import re

from django.core.files.uploadedfile import InMemoryUploadedFile
from rest_framework import status
from rest_framework.request import Request
from rest_framework.response import Response
from rest_framework.views import APIView

from caasm_config.config import caasm_config
from caasm_persistence.handler.runtime import mongo_handler
from caasm_service.constants.auth import AuthenticationType
from caasm_service.constants.setting import SettingName
from caasm_service.runtime import setting_service, sso_config_service
from caasm_webapi.app.config.serializers.system_config import SystemConfigSerializer
from caasm_webapi.util.response import build_success, ResponseCode, build_failed
from caasm_webapi.util.tool import build_file_response


class SystemConfigDetailView(APIView):
    def __init__(self, *args, **kwargs):
        super(SystemConfigDetailView, self).__init__(*args, **kwargs)
        self.clean_method = {"logo_id": self.clean_logo_id}

    def get(self, request: Request):
        result = {}
        setting = setting_service.get_setting(name=SettingName.API_SYSTEM_CONFIG)
        if not setting:
            return build_success(result)
        result = setting.value

        for key, value in result.items():
            clean_method = self.clean_method.get(key, self.clean_default)
            clean_value = clean_method(value)
            result[key] = clean_value
        auth_config = sso_config_service.get_sso_config()
        if not auth_config or auth_config.authentication_type == AuthenticationType.DEFAULT:
            result["authentication_type"] = "default"
        else:
            result["authentication_type"] = auth_config.authentication_type
            host_ip = caasm_config.HOST_IP
            cas_auth_api = caasm_config.CAS_AUTH_API
            service = host_ip + cas_auth_api
            authentication_jump_url = auth_config.config.get("authentication_jump_url").replace("{service}", service)
            result["url"] = authentication_jump_url
            result["name"] = f'{auth_config.config.get("authentication_system_name")}登录'
        return build_success(result)

    @classmethod
    def clean_logo_id(cls, logo):
        return caasm_config.API_URL_PREFIX + f"systemConfig/logoShow/{logo}/"

    @classmethod
    def clean_default(cls, value):
        return value


class SystemConfigModifyView(APIView):
    def post(self, request: Request):
        serializer = SystemConfigSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)

        validated_data = serializer.validated_data
        if not validated_data:
            return build_success()

        setting = setting_service.get_setting(name=SettingName.API_SYSTEM_CONFIG)

        setting.value.update(validated_data)
        setting_service.update(setting)
        return build_success()


class SystemConfigLogoShowView(APIView):
    def get(self, request, file_id):
        setting = setting_service.get_setting(name=SettingName.API_SYSTEM_CONFIG)
        if not setting:
            return Response(status=status.HTTP_404_NOT_FOUND)

        value = setting.value
        logo_id = value.get("logo_id")
        if logo_id != file_id:
            return Response(status=status.HTTP_404_NOT_FOUND)
        file = mongo_handler.get_file(file_id)
        import magic

        available_mime_types = {"image/jpeg", "image/png"}
        mine_type = magic.from_buffer(file.read(), mime=True)
        if mine_type not in available_mime_types:
            return build_failed(-1, "不支持该文件格式")
        file_ = mongo_handler.get_file(file_id)
        return build_file_response(file_)


class VersionView(APIView):
    _VERSION_RE = r"(v\d+.\d+.\d+_\d+.\d+.\d+)|(v\d+.\d+.\d+)"

    def get(self, request):
        version_path = caasm_config.VERSION
        if not os.path.exists(version_path):
            return build_success("")
        with open(version_path, "r") as f:
            content = f.read()
            search_result = re.search(self._VERSION_RE, content)
            result = content
            if search_result:
                result = search_result.group()
            return build_success(result)
