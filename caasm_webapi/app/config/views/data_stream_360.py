import json
import os

import shapely
from rest_framework.views import APIView

from caasm_scheduler.jobs.sync_data_stream_360 import sync_data_stream_360
from caasm_service.constants.setting import SettingName
from caasm_service.runtime import data_stream_360_realm_service, data_stream_360_record_service, setting_service
from caasm_webapi.app.config.requests.data_stream_360 import DataStream360ConfigRequest
from caasm_webapi.app.config.serializers.data_stream_360 import (
    DataStream360RealmSerializer,
    DataStream360RecordResponseSerialize,
    UpdateDataStream360ConfigRequestSerializer,
    UpdateDataCenterNamesRequestSerializer,
)
from caasm_webapi.util.response import build_failed, build_success


class DataStream360RealmAPIView(APIView):
    def post(self, request):
        serializer: DataStream360RealmSerializer = DataStream360RealmSerializer(data=request.data)
        if serializer.is_valid():
            config_str = serializer.save()
            try:
                config_dict = json.loads(config_str)
            except ValueError:
                return build_failed(-1, "JSON格式解析错误")
            total = config_dict.get("total")
            if total is None:
                return build_failed(-1, "数据结构错误，不包含total总数")
            realm_list = config_dict.get("list")
            if not realm_list:
                return build_failed(-1, "未包含有效的安全域数据")

            #   加载省级地理坐标
            provinces = []
            #   加载中国省份地利坐标
            with open(
                os.path.join(
                    os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))),
                    "caasm_script",
                    "data",
                    "china.geojson",
                )
            ) as fp:
                china = json.load(fp)
                for feature in china["features"]:
                    province_geometry = shapely.from_geojson(json.dumps(feature))
                    if not province_geometry.is_valid:
                        province_geometry = province_geometry.buffer(0)
                        if not province_geometry.is_valid:
                            continue
                    provinces.append({"geometry": province_geometry, "properties": feature["properties"]})

            count = 0
            realm_entities = []
            real_total = len(realm_list)
            for realm in realm_list:
                realm_entity = {}
                name: str = realm.get("name")
                if not name:
                    continue
                name_segment = name.split("_")
                if len(name_segment) == 2:
                    realm_entity["data_center"] = name_segment[0]
                    realm_entity["realm"] = name_segment[1]
                elif len(name_segment) == 1:
                    realm_entity["data_center"] = name_segment[0]
                else:
                    continue
                geo = realm.get("geo")
                if not geo:
                    continue
                longitude = geo.get("longitude")
                if not longitude:
                    continue
                if not isinstance(longitude, float) and not isinstance(longitude, int):
                    continue
                realm_entity["longitude"] = longitude
                latitude = geo.get("latitude")
                if not latitude:
                    continue
                if not isinstance(latitude, float) and not isinstance(latitude, int):
                    continue
                realm_entity["latitude"] = latitude
                #   富化地理坐标，直接简单笨办法
                province_name = None
                for province in provinces:
                    centroid = shapely.Point((longitude, latitude))
                    geometry = province["geometry"]
                    if shapely.contains(geometry, centroid):
                        province_name = province["properties"]["name"]
                        break
                if not province_name:
                    province_name = "未知"
                realm_entity["province"] = province_name
                ip_segments = []
                intranet_list = realm.get("intranet")
                if intranet_list:
                    for intranet in intranet_list:
                        valid = intranet.get("valid")
                        if not valid:
                            continue
                        content_type = intranet.get("contentType")
                        if not content_type:
                            continue
                        value = intranet.get("content")
                        if content_type == "subnetmask":
                            ip_segments.append({"cidr": value})
                        elif content_type == "couple":
                            value = str(value)
                            value_segments = value.split("-")
                            if len(value_segments) != 2:
                                continue
                            ip_segments.append(
                                {
                                    "start": value_segments[0],
                                    "end": value_segments[1],
                                }
                            )
                if ip_segments:
                    realm_entity["segments"] = ip_segments

                count += 1
                realm_entities.append(data_stream_360_realm_service.load_entity(**realm_entity))
            if realm_entities:
                data_stream_360_realm_service.drop()
                data_stream_360_realm_service.save_multi(realm_entities)
            return build_success({"message": f"声明共{total}条记录，实际共{real_total}条记录，成功导入{count}条记录"})
        return build_failed(-1, str(serializer.errors))


class DataStream360RecordAPIView(APIView):
    def get(self, request):
        return build_success(
            DataStream360RecordResponseSerialize(
                instance=data_stream_360_record_service.find(sort_fields=[("create_time", -1)]), many=True
            ).data
        )


class DataStream360RecordTestAPIView(APIView):
    def get(self, request):
        successful = sync_data_stream_360()
        return build_success({"successful": successful})


class SettingBase:
    @staticmethod
    def _get_value(setting_name):
        setting = setting_service.get_setting(setting_name)
        if setting:
            return setting.value
        else:
            return None

    @staticmethod
    def _upsert_setting(name, value):
        setting = setting_service.get_setting(name)
        if setting:
            setting.value = value
            setting_service.update(setting)
        else:
            setting_service.save(setting_service.load_entity(name=name, value=value))


class GetDataStream360ConfigAPIView(APIView, SettingBase):
    def get(self, request):
        url = self._get_value(SettingName.DATA_STREAM_360_URL.value) or ""
        api_key = self._get_value(SettingName.DATA_STREAM_360_APIKEY.value) or ""
        src_count = self._get_value(SettingName.DATA_STREAM_360_SRC_COUNT.value) or 10
        dst_count = self._get_value(SettingName.DATA_STREAM_360_DST_COUNT.value) or 10
        return build_success({"url": url, "api_key": api_key, "src_count": src_count, "dst_count": dst_count})


class UpdateDataStream360ConfigAPIView(APIView, SettingBase):
    def post(self, request):
        serializer = UpdateDataStream360ConfigRequestSerializer(data=request.data)
        if serializer.is_valid():
            req: DataStream360ConfigRequest = serializer.save()
            self._upsert_setting(SettingName.DATA_STREAM_360_URL.value, req.url)
            self._upsert_setting(SettingName.DATA_STREAM_360_APIKEY.value, req.api_key)
            self._upsert_setting(SettingName.DATA_STREAM_360_SRC_COUNT.value, req.src_count)
            self._upsert_setting(SettingName.DATA_STREAM_360_DST_COUNT.value, req.dst_count)
            return build_success({})
        return build_failed(-1, str(serializer.errors))


class GetDataCenterNamesAPIView(APIView, SettingBase):
    def get(self, request):
        return build_success({"names": self._get_value(SettingName.DATA_STREAM_360_DATA_CENTER_NAMES.value) or []})


class UpdateDataCenterNamesAPIView(APIView, SettingBase):
    def post(self, request):
        serializer = UpdateDataCenterNamesRequestSerializer(data=request.data)
        if serializer.is_valid():
            names = serializer.save()["names"]
            names = list(map(lambda v: v.strip(), names))
            self._upsert_setting(SettingName.DATA_STREAM_360_DATA_CENTER_NAMES.value, names)
            return build_success({})
        return build_failed(-1, str(serializer.errors))
