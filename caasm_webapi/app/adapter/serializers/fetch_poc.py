from rest_framework import serializers

from caasm_tool.constants import DATETIME_FORMAT
from caasm_tool.patch.serializer import SerializerMixin
from caasm_webapi.app.adapter.serializers.common_poc import CommonRecordListSerializer


class AdapterInstanceCheckConnectionSerializer(SerializerMixin):
    adapter_instance_id = serializers.CharField(required=True, max_length=24, min_length=24)
    fetch_type = serializers.CharField(required=True)


class AdapterInstanceFetchLogListSerializer(SerializerMixin):
    adapter_instance_id = serializers.CharField(required=True, max_length=24, min_length=24)
    page_index = serializers.IntegerField(required=True)
    page_size = serializers.IntegerField(required=True)
    sort_fields = serializers.ListField(
        child=serializers.CharField(),
        allow_null=True,
        allow_empty=True,
        required=False,
        default=lambda: [("-create_time", -1)],
    )
    min_id = serializers.<PERSON><PERSON><PERSON><PERSON>(required=False, allow_null=True)
    max_id = serializers.Char<PERSON>ield(required=False, allow_null=True)

    def validate(self, attrs):
        if attrs.get("max_id"):
            attrs["sort_fields"] = [("create_time", 1)]
        return attrs


class AdapterInstanceFetchRecordListSerializer(AdapterInstanceFetchLogListSerializer, CommonRecordListSerializer):
    ...


class AdapterInstanceFetchLogBasic(SerializerMixin):
    id = serializers.CharField()
    content = serializers.CharField()
    create_time = serializers.DateTimeField(format=DATETIME_FORMAT)
    update_time = serializers.DateTimeField(format=DATETIME_FORMAT)


class AdapterPocFetchSerializer(SerializerMixin):
    adapter_instance_id = serializers.CharField(required=True, max_length=24, min_length=24)
    fetch_type = serializers.CharField(required=True)
    max_record_id = serializers.CharField(required=False, max_length=24, min_length=24, allow_null=True)
