from caasm_tool.patch.serializer import SerializerMixin
from caasm_webapi.app.adapter.serializers.common_poc import CommonRecordListSerializer
from caasm_webapi.common import serializers


class AdapterPocMergeSerializer(SerializerMixin):
    adapter_name = serializers.CharField(required=True, min_length=1, max_length=50)
    fetch_type = serializers.CharField(required=True)


class AdapterMergeRecordListSerializer(AdapterPocMergeSerializer, CommonRecordListSerializer):
    page_index = serializers.IntegerField(required=True)
    page_size = serializers.IntegerField(required=True)
    sort_fields = serializers.ListField(
        child=serializers.CharField(), allow_null=True, allow_empty=True, required=False
    )
