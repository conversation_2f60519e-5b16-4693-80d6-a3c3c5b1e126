import logging
import traceback

from apscheduler.triggers.cron import CronTrigger
from rest_framework import serializers

from caasm_adapter.util.validator.manage import ValidateManager
from caasm_adapter.util.validator.pipeline.base import ValidatePipeline
from caasm_config.config import caasm_config
from caasm_service.constants.adapter import AdapterInstanceRunStatus, AdapterInstanceConnectionStatus
from caasm_service.constants.trigger import TriggerType
from caasm_service.runtime import adapter_instance_service, adapter_service
from caasm_tool.constants import DATETIME_FORMAT
from caasm_tool.patch.serializer import SerializerMixin, DynamicField
from caasm_tool.re_table import INT_RE

log = logging.getLogger()

ADAPTER_INSTANCE_RUN_STATUS_MAPPER = {
    AdapterInstanceRunStatus.INIT: "待采集",
    AdapterInstanceRunStatus.WAIT: "待采集",
    AdapterInstanceRunStatus.DOING: "采集中",
    AdapterInstanceRunStatus.SUCCESS: "采集成功",
    AdapterInstanceRunStatus.FAILED: "采集失败",
}

ADAPTER_INSTANCE_CONNECTION_STATUS = {
    AdapterInstanceConnectionStatus.UNKNOWN: "未知",
    AdapterInstanceConnectionStatus.FAILED: "失败",
    AdapterInstanceConnectionStatus.SUCCESS: "成功",
}


class AdapterRequestSerializer(SerializerMixin):
    type = serializers.CharField(help_text="适配器类型", required=False)
    adapter_name = serializers.CharField(help_text="适配器名称", required=False)
    keyword = serializers.CharField(help_text="关键字", required=False)
    adapter_inner_type = serializers.CharField(required=False, allow_null=True)


class AdapterInstanceListRequestSerializer(SerializerMixin):
    keyword = serializers.CharField(help_text="关键字", required=False)
    ancestor_adapter_name = serializers.CharField(help_text="适配器名称", required=False)
    enabled = serializers.BooleanField(help_text="实例状态", required=False, default=None, allow_null=True)
    run_status = serializers.CharField(help_text="采集状态", required=False)
    page_index = serializers.IntegerField(help_text="页码", required=False, default=1)
    connect_status = serializers.CharField(help_text="连接状态", required=False)
    page_size = serializers.IntegerField(help_text="每页最大小", required=False)
    sort_fields = serializers.ListField(
        child=serializers.CharField(), help_text="排序字段", required=False, default=["-update_time"]
    )


class ManufacturerSerializer(SerializerMixin):
    name = serializers.CharField(help_text="厂商名称")
    description = serializers.CharField(help_text="厂商描述")
    logo_id = serializers.CharField(help_text="logoId")


class AdapterValidateRule(SerializerMixin):
    name = serializers.CharField()
    error_hint = serializers.CharField()
    setting = serializers.SerializerMethodField("get_setting")

    def get_setting(self, obj):
        setting = obj.setting
        ValidatePipeline.load_dynamic(setting)
        return setting


class AdapterConnectionSerializer(SerializerMixin):
    name = serializers.CharField(help_text="参数名称")
    display_name = serializers.CharField(help_text="参数展示名称")
    description = serializers.CharField(help_text="描述信息")
    type = serializers.CharField(help_text="类型")
    required = serializers.BooleanField(help_text="是否必传")
    default = DynamicField(help_text="默认值")
    hidden = serializers.BooleanField(help_text="隐藏")
    validate_rules = serializers.ListField(child=AdapterValidateRule(), help_text="实例参数校验规则配置")


class AdapterSerializer(SerializerMixin):
    name = serializers.CharField(help_text="名称")
    display_name = serializers.CharField(help_text="展示名称")
    is_need_test_service = serializers.BooleanField(help_text="是否需要测试测试链接服务")
    virtual_instance_name = serializers.CharField(help_text="虚拟实例名称")
    description = serializers.CharField(help_text="描述信息")
    type = serializers.CharField(help_text="类型")
    adapter_inner_type = serializers.SerializerMethodField("get_adapter_inner_type", help_text="内部类型")
    logo_id = serializers.CharField(help_text="logoId")
    manufacturer = ManufacturerSerializer(help_text="厂商信息")
    version = serializers.CharField(help_text="版本")
    properties = serializers.ListField(child=serializers.CharField(), help_text="属性")
    priority = serializers.IntegerField(help_text="优先级")
    connection = serializers.SerializerMethodField("get_connection")
    adapter_instance_latest_fetch_status = serializers.CharField(help_text="适配器实例采集状态")

    def get_adapter_inner_type(self, adapter):
        if adapter.adapter_inner_type:
            return adapter.adapter_inner_type.value
        return None

    def get_connection(self, adapter):
        connections = adapter.connection

        new_connections = []
        for connection in connections:
            if connection.hidden:
                continue
            new_connections.append(connection)
        return AdapterConnectionSerializer(instance=new_connections, many=True).data


class AdapterInstanceTriggerAdd(SerializerMixin):
    value = serializers.DictField(help_text="触发器信息")
    type = serializers.SerializerMethodField("get_type", help_text="类型", required=False)

    def get_type(self, obj):
        return obj.type.value


class AdapterInstanceTriggerShow(SerializerMixin):
    value = serializers.SerializerMethodField("get_value", help_text="类型", required=False)
    type = serializers.SerializerMethodField("get_type", help_text="类型", required=False)

    def get_type(self, obj):
        return obj.type.value

    def get_value(self, obj):
        result = obj.value
        if not result:
            result = {"day": "*/1", "hour": "0", "minute": "0"}
        day = result.get("day", "*/1")
        _temp_time = int("".join(list(filter(str.isdigit, day)))) - 1
        result["day"] = f"*/{_temp_time}"
        return result


class AdapterInstanceSerializer(SerializerMixin):
    instance_id = serializers.CharField(help_text="实例ID", source="id")
    name = serializers.CharField(help_text="实例名称")
    adapter_name = serializers.CharField(help_text="适配器名称")
    enabled = serializers.SerializerMethodField("get_enabled")
    description = serializers.CharField(help_text="描述信息")
    last_sync_time = serializers.DateTimeField(
        format=DATETIME_FORMAT, help_text="最近采集时间", allow_null=None, default=None
    )
    debug = serializers.BooleanField(help_text="调试模式", default=False)
    create_time = serializers.DateTimeField(format=DATETIME_FORMAT, help_text="创建时间")
    logo_id = serializers.CharField(help_text="logoId")
    run_status_desc = serializers.SerializerMethodField("get_run_status_desc")
    run_status = serializers.SerializerMethodField("get_run_status")
    connect_status = serializers.SerializerMethodField("get_connect_status")
    connect_status_desc = serializers.SerializerMethodField("get_connect_status_desc")
    adapter_instance_connection = serializers.DictField(source="connection")
    trigger_desc = serializers.SerializerMethodField("get_trigger_desc")
    trigger = AdapterInstanceTriggerShow()
    trigger_type = serializers.CharField()
    proxy = serializers.CharField(help_text="代理地址", allow_null=True, required=False)
    properties = serializers.ListField(child=serializers.CharField())

    def get_enabled(self, obj):
        return "是" if obj.enabled else "否"

    def get_run_status_desc(self, obj):
        run_status = obj.run_status
        return ADAPTER_INSTANCE_RUN_STATUS_MAPPER.get(run_status, "待采集")

    def get_connect_status(self, obj):
        return obj.connect_status.value

    def get_connect_status_desc(self, obj):
        return ADAPTER_INSTANCE_CONNECTION_STATUS.get(obj.connect_status, "未知")

    def get_run_status(self, obj):
        return obj.run_status.value

    def get_trigger_desc(self, obj):
        if not obj.enabled:
            return ""
        value = obj.trigger.value
        trigger_desc = "每天0点"
        if value:
            day = value.get("day")
            hour = value.get("hour")
            minute = value.get("minute")

            if "*/" in day:
                _temp_time = int("".join(list(filter(str.isdigit, day)))) - 1
                if _temp_time:
                    trigger_desc = f"每间隔{_temp_time}天的"
                else:
                    trigger_desc = "每天"
            elif "-" in day:
                tmp_days = day.split(",")
                day_desc_list = []
                for tmp_day in tmp_days:
                    first_day, next_day = tmp_day.split("-")
                    day_desc_list.append(f"{first_day}号到{next_day}号")

                trigger_desc = f"每月" + ",".join(day_desc_list) + "的"
            else:
                trigger_desc = f"每月{day}号"

            trigger_desc += f"{hour}点{minute}分"
        return trigger_desc + "开始采集"


class AdapterInstanceAddSerializer(SerializerMixin):
    adapter_name = serializers.CharField(help_text="适配器名称", required=True)
    name = serializers.CharField(help_text="实例名称", required=True)
    description = serializers.CharField(help_text="描述信息", required=False, default="")
    trigger_type = serializers.CharField()
    trigger = AdapterInstanceTriggerAdd(required=False, allow_null=True, default=None)
    connection = serializers.DictField(required=False, default=dict)
    properties = serializers.ListField(child=serializers.CharField(), help_text="属性")
    enabled = serializers.BooleanField(help_text="是否有效", default=True, required=False)
    debug = serializers.BooleanField(help_text="调试模式", default=False, required=False)
    proxy = serializers.CharField(help_text="代理地址", default="", required=False, allow_null=True)

    def validate_proxy(self, proxy):
        local_proxies = caasm_config.SOCKS_PROXIES or []
        if proxy and proxy not in local_proxies:
            raise serializers.ValidationError("代理地址信息无效")
        return proxy

    def validate(self, attrs):
        adapter_instance_name = attrs["name"]
        count = adapter_instance_service.get_adapter_instance_count(name=adapter_instance_name)
        if count:
            raise serializers.ValidationError("适配器实例名称已经存在")
        return self._validate_core(attrs)

    def _validate_core(self, attrs):
        connection = attrs.get("connection") or {}
        trigger = attrs.get("trigger") or {}
        adapter_name = attrs.get("adapter_name")

        trigger_type = attrs.get("trigger_type")
        if trigger_type not in ["default", "customize"]:
            raise serializers.ValidationError("运行模式无效")

        adapter = adapter_service.get_adapter(name=adapter_name)
        if not adapter:
            raise serializers.ValidationError("适配器信息无效")

        connection = self._validate_connection(adapter, connection)
        if trigger_type == "default":
            trigger = {"type": "cron", "value": {}}
        else:
            trigger = self._validate_trigger(trigger)

        attrs["connection"] = connection
        attrs["trigger"] = trigger
        return attrs

    @classmethod
    def _validate_trigger(cls, trigger):
        if not trigger:
            return {}

        trigger_type = trigger.get("type", TriggerType.CRON.value)
        trigger_value = trigger.get("value") or {}
        trigger_value["day"] = trigger_value.pop("days")
        trigger_value.pop("hour_minute")
        day = trigger_value["day"]
        hour = trigger_value.get("hour", "0") or "0"
        minute = trigger_value.get("minute", "0") or "0"

        if trigger_type not in TriggerType.__members__.values():
            raise serializers.ValidationError("触发器类型无效")

        if not day:
            raise serializers.ValidationError("触发器日期参数无效")

        _temp = int("".join(list(filter(str.isdigit, day))))
        if _temp >= 30:
            raise serializers.ValidationError(f"目前支持最大间隔时间为0-29天")

        day = f"*/{int(_temp) + 1}"
        trigger_value["day"] = day
        if not (INT_RE.match(hour) and 0 <= int(hour) <= 23):
            raise serializers.ValidationError("触发器小时参数无效")

        if not (INT_RE.match(minute) and 0 <= int(minute) <= 59):
            raise serializers.ValidationError("触发器分钟参数无效")

        new_trigger_value = {"hour": hour, "minute": minute, "day": day}

        try:
            CronTrigger(**trigger_value)
        except Exception as e:
            log.debug(traceback.format_exc())
            raise serializers.ValidationError("触发器参数无效")
        trigger["type"] = trigger_type
        trigger["value"] = new_trigger_value
        return trigger

    @classmethod
    def _validate_connection(cls, adapter, connection):
        adapter_connections = adapter.connection

        new_connection = {}
        for adapter_connection in adapter_connections:
            adapter_connection_name = adapter_connection.name
            adapter_connection_type = adapter_connection.type
            adapter_connection_default = adapter_connection.default
            adapter_connection_validate_rules = adapter_connection.validate_rules

            if adapter_connection.required and adapter_connection_name not in connection:
                raise serializers.ValidationError(f"缺少必传参数:{adapter_connection_name}")

            connection_value = connection.get(adapter_connection_name)
            if connection_value is None:
                connection_value = adapter_connection_default

            new_connection[adapter_connection_name] = connection_value

            if connection_value is not None:
                _ret = ValidateManager().validate(
                    adapter_connection_type,
                    connection_value,
                    adapter_connection_validate_rules,
                )
                if not _ret.flag:
                    raise serializers.ValidationError(f"参数{adapter_connection.display_name}校验失败: {_ret.msg}")

                new_connection[adapter_connection_name] = connection_value
        return new_connection


class AdapterInstanceTestSerializer(SerializerMixin):
    instance_id = serializers.CharField(max_length=24, min_length=24, required=False)
    adapter_name = serializers.CharField(help_text="适配器名称", required=True)
    name = serializers.CharField(help_text="实例名称", required=True)
    description = serializers.CharField(help_text="描述信息", required=False, default="")
    connection = serializers.DictField(required=True)
    enabled = serializers.BooleanField(help_text="是否有效", default=True, required=False)
    debug = serializers.BooleanField(help_text="调试模式", default=False, required=False)
    proxy = serializers.CharField(help_text="代理地址", allow_null=True, required=False)

    def validate(self, attrs):
        instance_id = attrs.get("instance_id")
        if not instance_id:
            return attrs

        adapter_instance = adapter_instance_service.get_adapter_instance(adapter_instance_id=instance_id)

        if not adapter_instance:
            raise serializers.ValidationError("适配器信息无效")

        tmp_connection = attrs.get("connection", {})
        for conn_name, conn_val in adapter_instance.connection.items():
            tmp_val = tmp_connection.get(conn_name)
            if not tmp_val:
                tmp_val = conn_val

            tmp_connection[conn_name] = tmp_val

            attrs = {k: v for k, v in attrs.items() if v}
            attrs["connection"] = tmp_connection
        return attrs


class AdapterConnectionRequestSerializer(SerializerMixin):
    model = serializers.CharField()
    data = AdapterInstanceTestSerializer()


class AdapterRunTimeRequestSerializer(SerializerMixin):
    day = serializers.CharField()
    hour = serializers.CharField()
    minute = serializers.CharField()
    trigger_type = serializers.CharField()

    def validate(self, attrs):
        trigger_type = attrs.get("trigger_type")
        day = attrs.get("day")
        hour = attrs.get("hour")
        minute = attrs.get("minute")

        if trigger_type not in ["default", "customize"]:
            raise serializers.ValidationError(f"参数{trigger_type}校验失败")
        if trigger_type == "default":
            return {"day": "*/1", "hour": "0", "minute": "0"}

        if not day or not day.startswith("*/"):
            raise serializers.ValidationError(f"参数{day}检验失败")

        _temp = int("".join(list(filter(str.isdigit, day))))
        if _temp >= 30:
            raise serializers.ValidationError(f"目前支持最大间隔时间为0-29天")

        attrs["day"] = f"*/{int(_temp) + 1}"
        if not (INT_RE.match(hour) and 0 <= int(hour) <= 23):
            raise serializers.ValidationError("触发器小时参数无效")

        if not (INT_RE.match(minute) and 0 <= int(minute) <= 59):
            raise serializers.ValidationError("触发器分钟参数无效")
        return attrs


class AdapterInstanceModifySerializer(AdapterInstanceAddSerializer):
    instance_id = serializers.CharField(max_length=24, min_length=24, required=True)
    name = serializers.CharField(help_text="实例名称", required=False)
    adapter_name = serializers.CharField(help_text="适配器名称", required=False)

    def validate(self, attrs):
        instance_id = attrs.get("instance_id")

        adapter_instance = adapter_instance_service.get_adapter_instance(adapter_instance_id=instance_id)

        if not adapter_instance:
            raise serializers.ValidationError("适配器信息无效")

        name = attrs.get("name") or adapter_instance.name
        if name != adapter_instance.name:
            if adapter_instance_service.get_adapter_instance_count(name=name):
                raise serializers.ValidationError("适配器名称已存在")
        enabled = attrs.get("enabled") or False

        # 更新conn
        tmp_connection = attrs.get("connection", {})
        for conn_name, conn_val in adapter_instance.connection.items():
            tmp_val = tmp_connection.get(conn_name)
            if not tmp_val:
                tmp_val = conn_val

            tmp_connection[conn_name] = tmp_val

        new_attrs = {k: v for k, v in attrs.items() if v}
        if "proxy" in attrs:
            new_attrs["proxy"] = attrs["proxy"]
        new_attrs["connection"] = tmp_connection
        new_attrs["enabled"] = enabled
        instance_mapper = adapter_instance_service.dump_mapper(adapter_instance)
        instance_mapper.update(new_attrs)

        return self._validate_core(instance_mapper)


class AdapterInstanceDeleteSerializer(SerializerMixin):
    instance_ids = serializers.ListField(
        child=serializers.CharField(min_length=24, max_length=24),
        allow_empty=False,
        help_text="实例ID",
    )


class AdapterConnectionStatusSerializer(SerializerMixin):
    status = serializers.BooleanField()
    reason = serializers.CharField()


class AdapterRunTimeResponseSerializer(SerializerMixin):
    currentTime = serializers.CharField()
    nextRunTime = serializers.ListField(child=serializers.CharField())


class AdapterIdSerializer(SerializerMixin):
    instance_id = serializers.CharField()

    def create(self, validated_data):
        return validated_data["instance_id"]
