from rest_framework import serializers

from caasm_meta_data.constants import CATEGORY_TRANSLATE
from caasm_service.constants.adapter import ADAPTER_FETCH_STATUS_MAPPER
from caasm_tool.constants import DATETIME_FORMAT
from caasm_tool.patch.serializer import SerializerMixin


class FetchRecordSerializer(SerializerMixin):
    adapter_display_name = serializers.CharField(help_text="适配器名称")
    adapter_name = serializers.CharField(help_text="适配器标识")
    status = serializers.SerializerMethodField("get_status")
    status_desc = serializers.SerializerMethodField("get_status_desc")
    fetch_type = serializers.SerializerMethodField("get_fetch_type")
    fetch_type_desc = serializers.SerializerMethodField("get_fetch_type_desc")
    adapter_instance_name = serializers.CharField(help_text="连接实例名称")
    start_time = serializers.DateTimeField(help_text="开始时间", format=DATETIME_FORMAT)
    finish_time = serializers.DateTimeField(help_text="结束时间", format=DATETIME_FORMAT)
    count = serializers.IntegerField(help_text="发现条数", source="fetch_count")
    err_info = serializers.CharField(help_text="错误信息", default="")

    def get_status(self, obj):
        return obj.status.value

    def get_status_desc(self, obj):
        return ADAPTER_FETCH_STATUS_MAPPER.get(obj.status, "未知")

    def get_fetch_type(self, obj):
        return obj.fetch_type

    def get_fetch_type_desc(self, obj):
        return CATEGORY_TRANSLATE.get(obj.fetch_type, "未知")


class FetchHistoryQuerySerializer(SerializerMixin):
    start_time = serializers.CharField(help_text="开始时间", required=False, allow_null=True)
    finish_time = serializers.CharField(help_text="结束时间", required=False, allow_null=True)
    adapter_display_name = serializers.CharField(help_text="适配器名称", required=False)
    status = serializers.CharField(help_text="采集状态", required=False)
    adapter_instance_name = serializers.CharField(help_text="适配器实例名称", required=False)
    record_id = serializers.CharField(max_length=24, min_length=24, required=False, allow_null=True)
    sort_fields = serializers.ListField(
        child=serializers.CharField(required=False),
        help_text="排序字段",
        required=False,
        default=lambda: ["-start_time"],
    )
    page_index = serializers.IntegerField(help_text="页码", min_value=0)
    page_size = serializers.IntegerField(help_text="页数", required=True, max_value=200)
