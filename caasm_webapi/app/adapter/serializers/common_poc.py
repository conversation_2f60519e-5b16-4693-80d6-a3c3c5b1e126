from rest_framework import serializers

from caasm_persistence.handler.runtime import mongo_handler
from caasm_tool.patch.serializer import SerializerMixin


class CommonRecordListSerializer(SerializerMixin):
    query = serializers.DictField(required=False, allow_null=True)
    fetch_type = serializers.CharField(required=True)

    def validate_query(self, query):
        result = self._clean_query(query)
        return result

    @classmethod
    def _clean_query(cls, query, deep=0):
        if not query:
            return {}
        result = {}
        for key, val in query.items():
            if key == "$oid" and deep:
                return mongo_handler._build_id(val)
            elif isinstance(val, dict):
                result[key] = cls._clean_query(val, deep=1)
            else:
                result[key] = val

        return result
