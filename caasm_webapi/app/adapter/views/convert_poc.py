from rest_framework.views import APIView

from caasm_meta_data.constants import CATEGORY_TRANSLATE
from caasm_service.constants.adapter import Adapter<PERSON>onvertStatus, ADAPTER_CONVERT_STATUS_MAPPER
from caasm_service.runtime import adapter_service, adapter_instance_service, convert_service
from caasm_webapi.app.adapter.serializers.convert_poc import (
    AdapterPocConvertSerializer,
    AdapterConvertRecordListSerializer,
)
from caasm_webapi.app.adapter.views.common_poc import CommonRecordListView
from caasm_webapi.util.response import build_success, ResponseCode, build_failed
from caasm_workflow.sdk.workflow import workflow_sdk


class AdapterConvertPocRecordView(APIView):
    _ADAPTER_SORT_FIELDS = [("update_time", -1)]
    _CONVERT_SORT_FIELDS = [("update_time", -1), ("latest", -1)]

    def get(self, request):
        adapters = list(adapter_service.find_adapter(sort_fields=self._ADAPTER_SORT_FIELDS, is_biz_useful=True))

        result = []

        convert_cursor = convert_service.find_convert_record(data_deleted=False, sort_fields=self._CONVERT_SORT_FIELDS)
        convert_record_mapper = {}
        for convert_record in convert_cursor:
            _key = f"{convert_record.adapter_name}-{convert_record.fetch_type}"
            if _key in convert_record_mapper:
                continue
            convert_record_mapper[_key] = convert_record.status

        for adapter in adapters:
            adapter_name = adapter.name
            adapter_display_name = adapter.display_name
            adapter_instances = adapter_instance_service.find_adapter_instance(adapter_name=adapter_name)

            fetch_type_mapper = adapter.fetch_setting.fetch_type_mapper
            fetch_types = fetch_type_mapper.keys()

            adapter_instance_show = ",".join([adapter_instance.name for adapter_instance in adapter_instances])

            if not adapter_instance_show:
                continue

            for fetch_type in fetch_types:
                _fetch_type_display_name = CATEGORY_TRANSLATE[fetch_type]
                _convert_record_key = f"{adapter.name}-{fetch_type}"
                _convert_status = convert_record_mapper.get(_convert_record_key, AdapterConvertStatus.WAIT)
                _convert_status_display_name = ADAPTER_CONVERT_STATUS_MAPPER[_convert_status]

                _detail = {
                    "fetch_type": fetch_type,
                    "fetch_type_display_name": _fetch_type_display_name,
                    "adapter_display_names": adapter_instance_show,
                    "adapter_display_name": adapter_display_name,
                    "adapter_name": adapter_name,
                    "convert_status": _convert_status,
                    "convert_status_display_name": _convert_status_display_name,
                }
                result.append(_detail)

        return build_success(result)


class AdapterConvertStartView(APIView):
    _PLAYBOOK = "adapter_convert"

    def post(self, request):
        serializer = AdapterPocConvertSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)

        v = serializer.validated_data
        params = {"adapter_name": v["adapter_name"], "category": v["fetch_type"]}
        workflow_sdk.start_workflow(self._PLAYBOOK, params)

        return build_success()


class AdapterConvertRecordListView(CommonRecordListView):
    def get_table(self, request):
        serializer = AdapterConvertRecordListSerializer(data=request.data)
        if not serializer.is_valid():
            return self.build_table_response(build_failed(ResponseCode.REQUEST_ERROR, message=serializer))

        query_data = serializer.save()

        adapter_name = query_data["adapter_name"]
        fetch_type = query_data["fetch_type"]
        page_index = query_data.get("page_index")
        page_size = query_data.get("page_size")
        query = query_data.get("query")

        convert_records = list(
            convert_service.find_convert_record(
                adapter_name=adapter_name,
                data_deleted=False,
                sort_fields=self._DEFAULT_SORT_FIELDS,
                limit=1,
                fetch_type=fetch_type,
            )
        )
        if not convert_records:
            return self.build_table_response(self.build_data())
        convert_record = convert_records[0]

        convert_table = convert_service.build_convert_data_table(
            adapter_name=convert_record.adapter_name,
            index=convert_record.index,
            fetch_type=fetch_type,
        )
        return self.build_table_response(
            table_name=convert_table, query=query, page_size=page_size, page_index=page_index
        )
