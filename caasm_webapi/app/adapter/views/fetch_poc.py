import logging

from rest_framework.views import APIView

from caasm_adapter.fetcher.auth import Auth<PERSON>and<PERSON>
from caasm_meta_data.constants import CATEGORY_TRANSLATE
from caasm_service.constants.adapter import Adapter<PERSON>etch<PERSON>tatus, ADAPTER_FETCH_STATUS_MAPPER
from caasm_service.runtime import (
    adapter_instance_fetch_poc_log_service,
    adapter_instance_service,
    adapter_service,
    fetch_service,
)
from caasm_tool.util import deduplicate
from caasm_webapi.app.adapter.serializers.fetch_poc import (
    AdapterInstanceFetchLogListSerializer,
    AdapterInstanceFetchLogBasic,
    AdapterPocFetchSerializer,
    AdapterInstanceFetchRecordListSerializer,
    AdapterInstanceCheckConnectionSerializer,
)
from caasm_webapi.app.adapter.views.common_poc import CommonRecordListView
from caasm_webapi.util.response import build_failed, ResponseCode, build_success
from caasm_workflow.sdk.workflow import workflow_sdk

log = logging.getLogger()


class AdapterFetchLogListView(APIView):
    def get(self, request):
        serializer = AdapterInstanceFetchLogListSerializer(data=request.query_params)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)

        request_data = serializer.save()

        page_size = request_data.get("page_size")
        sort_fields = request_data.get("sort_fields")
        adapter_instance_id = request_data.get("adapter_instance_id")
        max_id = request_data.get("max_id")
        min_id = request_data.get("min_id")
        fetch_type = request_data.get("fetch_type")

        total = adapter_instance_fetch_poc_log_service.get_poc_log_content(
            adapter_instance_id=adapter_instance_id, max_id=max_id, min_id=min_id, fetch_type=fetch_type
        )

        if not total:
            return build_success({"total": total, "data": []})

        poc_logs = adapter_instance_fetch_poc_log_service.find_poc_log(
            adapter_instance_id,
            limit=page_size,
            sort_fields=sort_fields,
            max_id=max_id,
            min_id=min_id,
            fetch_type=fetch_type,
        )

        data = AdapterInstanceFetchLogBasic(instance=poc_logs, many=True).data
        return build_success({"total": total, "data": data})


class AdapterFetchStartView(APIView):
    def post(self, request):
        serializer = AdapterPocFetchSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)

        request_data = serializer.save()

        adapter_instance = adapter_instance_service.get_adapter_instance(request_data["adapter_instance_id"])
        if not adapter_instance:
            return build_failed(ResponseCode.NOT_FOUND)

        params = {
            "adapter_name": adapter_instance.adapter_name,
            "adapter_instance_id": adapter_instance.id,
            "category": request_data["fetch_type"],
            "system_call": False,
        }
        workflow_sdk.start_workflow("adapter_instance_fetch", params=params)
        return build_success()


class AdapterInstanceFetchPocRecordView(APIView):
    _DEFAULT_SORT_FIELDS = [("update_time", -1)]
    _DEFAULT_FETCH_SORT_FIELDS = [("update_time", -1), ("latest", -1)]

    def get(self, request):
        adapter_instances = list(adapter_instance_service.find_adapter_instance(sort_fields=self._DEFAULT_SORT_FIELDS))
        adapter_names = deduplicate([adapter_instance.adapter_name for adapter_instance in adapter_instances])

        adapter_mapper = {}
        fetch_record_mapper = {}

        if adapter_names:
            adapters = adapter_service.find_adapter(names=adapter_names)
            adapter_mapper = {adapter.name: adapter for adapter in adapters}

            fetch_records = fetch_service.find_fetch_record(
                adapter_names=adapter_names,
                data_deleted=False,
                sort_fields=self._DEFAULT_FETCH_SORT_FIELDS,
            )
            for fetch_record in fetch_records:
                _key = self._build_key(fetch_record.adapter_instance_id, fetch_record.fetch_type)
                if _key in fetch_record_mapper:
                    continue
                fetch_record_mapper[_key] = fetch_record.status

        result = []
        for adapter_instance in adapter_instances:
            adapter_name = adapter_instance.adapter_name
            adapter = adapter_mapper.get(adapter_name)
            if not adapter:
                continue

            adapter_display_name = adapter.display_name
            fetch_type_mapper = adapter.fetch_setting.fetch_type_mapper
            fetch_types = fetch_type_mapper.keys()
            adapter_instance_id = str(adapter_instance.id)

            for fetch_type in fetch_types:
                _fetch_type_display_name = CATEGORY_TRANSLATE[fetch_type]
                _fetch_record_key = self._build_key(adapter_instance_id, fetch_type)
                _fetch_status = fetch_record_mapper.get(_fetch_record_key, AdapterFetchStatus.INIT)
                _fetch_status_display_name = ADAPTER_FETCH_STATUS_MAPPER[_fetch_status]

                _detail = {
                    "fetch_type": fetch_type,
                    "fetch_type_display_name": _fetch_type_display_name,
                    "adapter_instance_id": adapter_instance_id,
                    "adapter_instance_display_name": adapter_instance.name,
                    "adapter_display_name": adapter_display_name,
                    "adapter_name": adapter_name,
                    "fetch_status": _fetch_status,
                    "fetch_status_display_name": _fetch_status_display_name,
                }
                result.append(_detail)

        return build_success(result)

    @classmethod
    def _build_key(cls, adapter_instance_id, category):
        return f"{adapter_instance_id}-{category}"


class AdapterFetchRecordListView(CommonRecordListView):
    def get_table(self, request):
        serializer = AdapterInstanceFetchRecordListSerializer(data=request.data)
        if not serializer.is_valid():
            return self.build_table_response(build_failed(ResponseCode.REQUEST_ERROR, message=serializer))

        query_data = serializer.save()

        adapter_instance_id = query_data["adapter_instance_id"]
        fetch_type = query_data["fetch_type"]
        page_index = query_data.get("page_index")
        page_size = query_data.get("page_size")
        query = query_data.get("query")

        fetch_records = list(
            fetch_service.find_fetch_record(
                adapter_instance_id=adapter_instance_id,
                data_deleted=False,
                sort_fields=self._DEFAULT_SORT_FIELDS,
                limit=1,
                fetch_type=fetch_type,
            )
        )
        if not fetch_records:
            return self.build_table_response(self.build_data())
        fetch_record = fetch_records[0]

        fetch_table = fetch_service.build_fetch_data_table(
            adapter_name=fetch_record.adapter_name,
            adapter_instance_id=fetch_record.adapter_instance_id,
            index=fetch_record.index,
            fetch_type=fetch_type,
        )
        return self.build_table_response(
            table_name=fetch_table, query=query, page_size=page_size, page_index=page_index
        )


class AdapterFetchConnectionCheckAPI(APIView):
    def get(self, request):
        serializer = AdapterInstanceCheckConnectionSerializer(data=request.query_params)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)

        request_data = serializer.save()

        adapter_instance_id = request_data.get("adapter_instance_id")
        fetch_type = request_data.get("fetch_type")
        adapter_instance = adapter_instance_service.get_adapter_instance(adapter_instance_id=adapter_instance_id)
        if not adapter_instance:
            return build_failed(ResponseCode.NOT_FOUND)

        adapter_name = adapter_instance.adapter_name
        try:
            auth = AuthHandler(adapter_instance.id, fetch_type, adapter_name)
            auth.initialize()
            auth.handle()
            auth.finish()
        except Exception as e:
            log.warning(f"Auth error{e}")
        return build_success()


class AdapterPocLogClearView(APIView):
    _size = 1000

    def post(self, request):
        serializer = AdapterPocFetchSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)

        request_data = serializer.save()
        request_data["min_eq_id"] = request_data.pop("max_record_id", None)
        while adapter_instance_fetch_poc_log_service.get_poc_log_content(**request_data):
            adapter_instance_fetch_poc_log_service.delete_logs(**request_data, limit=self._size)

        return build_success()
