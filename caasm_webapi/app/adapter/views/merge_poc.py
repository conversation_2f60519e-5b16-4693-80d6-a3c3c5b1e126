from rest_framework.views import APIView

from caasm_meta_data.constants import CATEG<PERSON>Y_TRANSLATE
from caasm_service.constants.adapter import Adapter<PERSON>ergeStatus, ADAPTER_MERGE_STATUS_MAPPER
from caasm_service.runtime import adapter_service, adapter_instance_service, merge_service
from caasm_webapi.app.adapter.serializers.merge_poc import AdapterPocMergeSerializer, AdapterMergeRecordListSerializer
from caasm_webapi.app.adapter.views.common_poc import CommonRecordListView
from caasm_webapi.util.response import build_success, ResponseCode, build_failed
from caasm_workflow.sdk.workflow import workflow_sdk


class AdapterMergePocRecordView(APIView):
    _ADAPTER_SORT_FIELDS = [("update_time", -1)]
    _MERGE_SORT_FIELDS = [("update_time", -1), ("latest", -1)]

    def get(self, request):
        adapters = list(adapter_service.find_adapter(sort_fields=self._ADAPTER_SORT_FIELDS, is_biz_useful=True))

        result = []

        cur = merge_service.find_merge_record(data_deleted=False, sort_fields=self._MERGE_SORT_FIELDS)
        merge_record_mapper = {}
        for merge_record in cur:
            _key = f"{merge_record.adapter_name}-{merge_record.fetch_type}"
            if _key in merge_record_mapper:
                continue
            merge_record_mapper[_key] = merge_record.status

        for adapter in adapters:
            adapter_name = adapter.name
            adapter_display_name = adapter.display_name
            adapter_instances = adapter_instance_service.find_adapter_instance(adapter_name=adapter_name)

            fetch_type_mapper = adapter.fetch_setting.fetch_type_mapper
            fetch_types = fetch_type_mapper.keys()

            adapter_instance_show = ",".join([adapter_instance.name for adapter_instance in adapter_instances])

            if not adapter_instance_show:
                continue

            for fetch_type in fetch_types:
                _fetch_type_display_name = CATEGORY_TRANSLATE[fetch_type]
                _merge_record_key = f"{adapter.name}-{fetch_type}"
                _merge_status = merge_record_mapper.get(_merge_record_key, AdapterMergeStatus.WAIT)
                _merge_status_display_name = ADAPTER_MERGE_STATUS_MAPPER[_merge_status]

                _detail = {
                    "fetch_type": fetch_type,
                    "fetch_type_display_name": _fetch_type_display_name,
                    "adapter_display_names": adapter_instance_show,
                    "adapter_display_name": adapter_display_name,
                    "adapter_name": adapter_name,
                    "merge_status": _merge_status,
                    "merge_status_display_name": _merge_status_display_name,
                }
                result.append(_detail)

        return build_success(result)


class AdapterMergeStartView(APIView):
    _PLAYBOOK = "adapter_merge"

    def post(self, request):
        serializer = AdapterPocMergeSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)
        request_data = serializer.save()

        params = {"adapter_name": request_data["adapter_name"], "category": request_data["fetch_type"]}
        workflow_sdk.start_workflow(self._PLAYBOOK, params=params)

        return build_success()


class AdapterMergeRecordListView(CommonRecordListView):
    def get_table(self, request):
        serializer = AdapterMergeRecordListSerializer(data=request.data)
        if not serializer.is_valid():
            return self.build_table_response(build_failed(ResponseCode.REQUEST_ERROR, message=serializer))

        query_data = serializer.save()

        adapter_name = query_data["adapter_name"]
        fetch_type = query_data["fetch_type"]
        page_index = query_data.get("page_index")
        page_size = query_data.get("page_size")
        query = query_data.get("query")

        merge_records = list(
            merge_service.find_merge_record(
                adapter_name=adapter_name,
                data_deleted=False,
                sort_fields=self._DEFAULT_SORT_FIELDS,
                limit=1,
                fetch_type=fetch_type,
            )
        )
        if not merge_records:
            return self.build_table_response(self.build_data())
        merge_record = merge_records[0]

        merge_table = merge_service.build_merge_data_table(
            adapter_name=merge_record.adapter_name,
            index=merge_record.index,
            fetch_type=fetch_type,
        )
        return self.build_table_response(
            table_name=merge_table, query=query, page_size=page_size, page_index=page_index
        )
