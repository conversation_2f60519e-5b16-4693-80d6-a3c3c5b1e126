import datetime
import logging
from collections import defaultdict
from typing import <PERSON>ple

from apscheduler.triggers.cron import CronTrigger
from django.http import HttpResponse
from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_page
from rest_framework.request import Request
from rest_framework.views import APIView

from caasm_adapter.base.base import Response
from caasm_config.config import caasm_config
from caasm_database.util import get_database_parser
from caasm_file.util import get_file_parser
from caasm_ftp.util import get_ftp_parser
from caasm_manage.steps.auth import default_test_auth, default_test_connection
from caasm_service.constants.adapter import AdapterInnerType, AdapterFetchStatus
from caasm_service.constants.setting import SettingName
from caasm_service.entity.adapter import Adapter
from caasm_service.entity.adapter_instance import AdapterInstance
from caasm_service.runtime import (
    adapter_service,
    manufacturer_service,
    adapter_instance_service,
    meta_model_service,
    setting_service,
    fetch_service,
    convert_visualization_service,
)
from caasm_smb.util import get_smb_parser
from caasm_tool.constants import DATETIME_FORMAT, DATETIME_FORMAT_3
from caasm_tool.util import generate_random_string, load_entry
from caasm_webapi.app.adapter.serializers.adapter import (
    AdapterSerializer,
    AdapterRequestSerializer,
    AdapterInstanceListRequestSerializer,
    AdapterInstanceSerializer,
    AdapterInstanceAddSerializer,
    AdapterInstanceModifySerializer,
    AdapterInstanceDeleteSerializer,
    AdapterConnectionRequestSerializer,
    AdapterConnectionStatusSerializer,
    AdapterRunTimeRequestSerializer,
    AdapterRunTimeResponseSerializer,
    AdapterIdSerializer,
)
from caasm_webapi.util.response import build_success, build_failed, ResponseCode

log = logging.getLogger()


class AdapterListAPI(APIView):
    _fields = [
        "name",
        "display_name",
        "description",
        "type",
        "adapter_inner_type",
        "logo_id",
        "manufacturer_id",
        "version",
        "properties",
        "priority",
        "fetch_setting",
        "connection",
    ]
    _sort_fields = [("priority", -1), ("create_time", -1)]

    def get(self, request):
        serializer = AdapterRequestSerializer(data=request.query_params)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)
        validated_data = serializer.validated_data

        keyword = validated_data.get("keyword", "")
        type = validated_data.get("type", "")
        adapter_name = validated_data.get("adapter_name")
        adapter_inner_type = validated_data.get("adapter_inner_type")

        cursor = adapter_service.find_adapter(
            keyword=keyword, type=type, fields=self._fields, name=adapter_name, adapter_inner_type=adapter_inner_type
        )
        adapters = list(cursor)

        manufacturer_ids = set()
        for adapter in adapters:
            manufacturer_ids.add(adapter.manufacturer_id)
        manufacturer_ids = list(manufacturer_ids)
        manufacturer_mapper = {}
        if manufacturer_ids:
            manufacturers = manufacturer_service.find_manufacturer(manufacturer_ids=manufacturer_ids)
            manufacturer_mapper = {manufacturer.id: manufacturer for manufacturer in manufacturers}

        for adapter in adapters:
            adapter.manufacturer = manufacturer_mapper.get(adapter.manufacturer_id)

        adapter_names = [adapter.name for adapter in adapters]
        fetch_records = fetch_service.find_fetch_record(
            adapter_names=adapter_names, latest=True, fields=["status", "adapter_name"]
        )

        adapter_fetch_status_mapper = defaultdict(list)
        for fetch_record in fetch_records:
            adapter_fetch_status_mapper[fetch_record.adapter_name].append(fetch_record.status)

        for adapter in adapters:
            adapter_instance_latest_fetch_status_list = adapter_fetch_status_mapper.get(
                adapter.name, AdapterFetchStatus.INIT
            )
            # 采集中 反正采集失败，有后续

            new_adapter_instance_latest_fetch_status_list = []
            for adapter_instance_latest_fetch_status in adapter_instance_latest_fetch_status_list:
                if adapter_instance_latest_fetch_status == AdapterFetchStatus.FETCHING:
                    adapter_instance_latest_fetch_status = adapter_instance_latest_fetch_status.INIT

                new_adapter_instance_latest_fetch_status_list.append(adapter_instance_latest_fetch_status)

            if AdapterFetchStatus.SUCCESS in new_adapter_instance_latest_fetch_status_list:
                new_adapter_instance_latest_fetch_status = AdapterFetchStatus.SUCCESS
            elif AdapterFetchStatus.FAILED in new_adapter_instance_latest_fetch_status_list:
                new_adapter_instance_latest_fetch_status = AdapterFetchStatus.FAILED
            else:
                new_adapter_instance_latest_fetch_status = AdapterFetchStatus.INIT

            adapter.adapter_instance_latest_fetch_status = new_adapter_instance_latest_fetch_status.value

            ## 给前端返回一个虚拟的实例名称
            adapter.virtual_instance_name = generate_random_string(adapter.display_name)

        adapter_mapper = defaultdict(list)
        for adapter in adapters:
            adapter.is_need_test_service = adapter.fetch_setting.is_need_test_service
            adapter_mapper[adapter.type].append(adapter)

        result = []
        for adapter_type in caasm_config.ADAPTER_TYPES:
            result.extend(adapter_mapper[adapter_type])

        data = AdapterSerializer(instance=result, many=True).data
        return build_success(data)


class AdapterIconView(APIView):
    @method_decorator(cache_page(600))
    def get(self, request: Request, adapter_name: str):
        use_default = True
        file_id = None
        adapter = adapter_service.get_adapter(adapter_name)
        if adapter:
            use_default = False
            file_id = adapter.logo_id

        if use_default or not file_id:
            file_id = setting_service.get_setting(SettingName.ADAPTER_DEFAULT_LOGO).value

        file_fd = setting_service.get_file(file_id)

        content = file_fd.read()
        return HttpResponse(content, content_type="image/png")


class AdapterInstanceListAPI(APIView):
    def get(self, request):
        serializer = AdapterInstanceListRequestSerializer(data=request.query_params)

        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)

        validated_data = serializer.validated_data

        keyword = validated_data.get("keyword")
        enabled = validated_data.get("enabled")
        run_status = validated_data.get("run_status")
        page_index = validated_data.get("page_index")
        page_size = validated_data.get("page_size")
        sort_fields = validated_data.get("sort_fields")
        connect_status = validated_data.get("connect_status")
        ancestor_adapter_name = validated_data.get("ancestor_adapter_name")

        if page_size is None:
            offset = 0
            limit = 10000
        else:
            offset = page_index * page_size
            limit = page_size

        count = adapter_instance_service.get_adapter_instance_count(
            run_status=run_status,
            keyword=keyword,
            enabled=enabled,
            ancestor_adapter_name=ancestor_adapter_name,
            connect_status=connect_status,
        )
        data = []
        if count:
            adapter_instance_cursor = adapter_instance_service.find_adapter_instance(
                run_status=run_status,
                keyword=keyword,
                enabled=enabled,
                ancestor_adapter_name=ancestor_adapter_name,
                sort_fields=sort_fields,
                connect_status=connect_status,
                offset=offset,
                limit=limit,
            )
            adapter_instances = list(adapter_instance_cursor)

            adapter_names = set()
            for adapter_instance in adapter_instances:
                adapter_names.add(adapter_instance.adapter_name)
            adapter_names = list(adapter_names)
            adapter_mapper = {}
            if adapter_names:
                adapters = adapter_service.find_adapter(names=adapter_names)
                adapter_mapper = {adapter.name: adapter for adapter in adapters}

            for adapter_instance in adapter_instances:
                adapter_name = adapter_instance.adapter_name
                adapter = adapter_mapper.get(adapter_name, None)
                adapter_instance.adapter_display_name = adapter.display_name if adapter else None
                adapter_instance.logo_id = adapter.logo_id if adapter else None

            data = AdapterInstanceSerializer(instance=adapter_instances, many=True).data

        return build_success({"total": count, "data": data})


class AdapterInstanceAddAPI(APIView):
    SHELL_ADAPTER_MAPPER = {
        "caasm_database": get_database_parser,
        "caasm_file": get_file_parser,
        "caasm_ftp": get_ftp_parser,
        "caasm_smb": get_smb_parser,
        "caasm_auto_file": None,
    }

    def post(self, request):
        serializer = AdapterInstanceAddSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)

        validate_data = serializer.validated_data
        validate_data["adapter_name"] = validate_data.pop("adapter_name")
        adapter_instance = adapter_instance_service.load_entity(**validate_data)
        adapter_instance.ancestor_adapter_name = adapter_instance.adapter_name

        build_res = self._build_virtual_adapter(adapter_instance)
        if build_res:
            return build_res

        adapter_instance.is_complex = (
            True if adapter_instance.ancestor_adapter_name in self.SHELL_ADAPTER_MAPPER else False
        )
        adapter_instance_service.save(adapter_instance)
        return build_success()

    @classmethod
    def _build_virtual_adapter(cls, adapter_instance):
        ancestor_adapter_name = adapter_instance.ancestor_adapter_name
        if ancestor_adapter_name not in cls.SHELL_ADAPTER_MAPPER:
            return

        connection = adapter_instance.connection
        virtual_adapter_name = connection.get("adapter_flag")
        if not virtual_adapter_name:
            virtual_adapter_name = f"{ancestor_adapter_name}_{datetime.datetime.now().strftime(DATETIME_FORMAT_3)}"

        ans = cls.create_virtual_adapter(virtual_adapter_name, adapter_instance, connection, ancestor_adapter_name)
        if ans:
            return ans

        adapter_instance.adapter_name = virtual_adapter_name
        adapter_instance.connection["adapter_flag"] = virtual_adapter_name

    @classmethod
    def create_virtual_adapter(cls, virtual_adapter_name, adapter_instance, connection, adapter_virtual_name):
        res = cls.handle_virtual_adapter_common(
            virtual_adapter_name, adapter_instance, connection, adapter_virtual_name
        )
        if not adapter_service.save(adapter_service.load_entity(**res)):
            return build_failed(ResponseCode.REQUEST_ERROR, message="适配器创建失败")

    @classmethod
    def handle_virtual_adapter_common(cls, virtual_adapter_name, adapter_instance, connection, ancestor_adapter_name):
        adapter = adapter_service.get_adapter(ancestor_adapter_name, fields=["fetch_setting", "connection", "type"])

        adapter_mapper = adapter_service.dump_mapper(adapter)

        fetch_setting_mapper = adapter_mapper["fetch_setting"]
        connections = adapter_mapper["connection"]

        parser_name = connection.get("parser")
        parser = cls.get_parser(ancestor_adapter_name, parser_name) if parser_name else None
        category = parser.get_category() if parser else connection.get("category")
        data_type = parser.get_data_type() if parser else connection.get("data_type")
        model_id = meta_model_service.get_meta_model(name=parser.get_model_name()).id if parser else None
        fetch_setting_mapper["fetch_type_mapper"] = {category: [data_type]}

        virtual_adapter_mapper = {
            "name": virtual_adapter_name,
            "display_name": adapter_instance.name,
            "properties": adapter_instance.properties,
            "priority": connection.get("priority"),
            "adapter_inner_type": AdapterInnerType.VIRTUAL if parser else AdapterInnerType.PROXY,
            "fetch_setting": fetch_setting_mapper,
            "connection": connections,
            "logo_id": connection.get("logo_id"),
            "type": adapter.type,
            "is_biz_useful": True,
            "model_id": model_id,
        }
        if not parser:
            cls.create_demo(ancestor_adapter_name, virtual_adapter_name, data_type, connection)
        return virtual_adapter_mapper

    @classmethod
    def create_demo(cls, adapter_name, virtual_adapter_name, data_type, connection):
        demo_path = f"{adapter_name}.fetch:get_demo"
        demo = load_entry(demo_path, entry_params={"connection": connection})
        convert_visualization_service.delete_one({"fetch_type": data_type, "adapter_name": virtual_adapter_name})
        convert_visualization_service.save_convert_visualization(virtual_adapter_name, data_type, demo)

    @classmethod
    def get_parser(cls, ancestor_adapter_name, parser_name):
        parser_method = cls.SHELL_ADAPTER_MAPPER.get(ancestor_adapter_name)
        if not parser_method:
            return None
        return parser_method(parser_name)


class AdapterInstanceModifyAPI(AdapterInstanceAddAPI):
    _FIELDS = ["is_complex", "adapter_name"]

    def post(self, request):
        serializer = AdapterInstanceModifySerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)

        request_data = serializer.validated_data

        adapter_instance_id = request_data.pop("instance_id")
        adapter_instance = adapter_instance_service.get_adapter_instance(adapter_instance_id, fields=self._FIELDS)
        if not adapter_instance:
            return build_success()

        if adapter_instance.is_complex:
            old_adapter_name = adapter_instance.adapter_name
            new_adapter_name = request_data["connection"]["adapter_flag"]
            new_adapter_instance = adapter_instance_service.load_entity(**request_data)
            if adapter_instance.adapter_name != new_adapter_name:
                request_data["adapter_name"] = new_adapter_name
                adapter_service.delete_adapter(old_adapter_name)

                self._build_virtual_adapter(new_adapter_instance)

            else:
                res = self.handle_virtual_adapter_common(
                    new_adapter_instance.adapter_name,
                    new_adapter_instance,
                    new_adapter_instance.connection,
                    new_adapter_instance.ancestor_adapter_name,
                )
                adapter_service.modify_adapter(res, old_adapter_name)

        adapter_instance_service.modify_adapter_instance(adapter_instance_id=adapter_instance_id, values=request_data)
        return build_success()


class AdapterInstanceDeleteAPI(APIView):
    def post(self, request):
        serializer = AdapterInstanceDeleteSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)

        adapter_instance_ids = serializer.validated_data.get("instance_ids")

        if not adapter_instance_ids:
            return build_success()

        adapter_instances = adapter_instance_service.find_adapter_instance(adapter_instance_ids=adapter_instance_ids)

        need_remove_adapter_names = []
        for adapter_instance in adapter_instances:
            if adapter_instance.is_complex:
                need_remove_adapter_names.append(adapter_instance.adapter_name)

        if need_remove_adapter_names:
            adapter_service.delete_adapter(names=list(set(need_remove_adapter_names)))
        adapter_instance_service.delete_adapter_instance(adapter_instance_ids=adapter_instance_ids)
        return build_success()


class AdapterTypeListAPI(APIView):
    types = [{"label": i, "value": i} for i in caasm_config.ADAPTER_TYPES]

    def get(self, request):
        return build_success(self.types)


class AdapterDefaultTypeListAPI(APIView):
    types = [{"label": i, "value": i} for i in caasm_config.ADAPTER_TYPES]

    def get(self, request):
        return build_success(self.types)


class AdapterExistInstanceListAPI(APIView):
    def get(self, request):
        result = []
        ancestor_adapter_names = adapter_instance_service.find_adapter_instance_distinct("ancestor_adapter_name")
        if ancestor_adapter_names:
            adapters = adapter_service.find_adapter(names=ancestor_adapter_names, fields=["name", "display_name"])
            for adapter in adapters:
                result.append({"label": adapter.display_name, "value": adapter.name})
        return build_success(result)


class AdapterConnectionTestAPI(APIView):
    def post(self, request):
        serializer = AdapterConnectionRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)

        validate_data = serializer.validated_data
        test_data = validate_data.pop("data")
        model = validate_data.pop("model")
        if model == "connection":
            connect_result = default_test_connection(**test_data)
            connect_reason = ""
            if not connect_result:
                connect_reason = "地址不可达，请检查IP与对应端口"
            data = AdapterConnectionStatusSerializer(instance={"status": connect_result, "reason": connect_reason}).data
            return build_success(data)
        if model == "auth":
            auth_test_response: Response = default_test_auth(**test_data)
            flag = auth_test_response.flag
            auth_test = auth_test_response.data
            if flag:
                auth_test, auth_reason = flag, auth_test
            else:
                auth_reason = "权限校验失败"
            data = AdapterConnectionStatusSerializer(instance={"status": auth_test, "reason": auth_reason}).data
            return build_success(data)
        return build_failed(ResponseCode.REQUEST_ERROR, message="请输入合法参数")


class AdapterRunTimeAPI(APIView):
    def post(self, request):
        serializer = AdapterRunTimeRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, serializer)

        validate_data = serializer.validated_data
        day = validate_data.pop("day")
        hour = validate_data.pop("hour")
        minute = validate_data.pop("minute")

        now = datetime.datetime.now()
        times = 2
        next_run_time = None
        next_run_time_list = []
        while times:
            if not next_run_time:
                next_run_time = now
            next_run_time = CronTrigger(day=day, hour=hour, minute=minute).get_next_fire_time(
                next_run_time, next_run_time
            )
            next_run_time_list.append(next_run_time.strftime(DATETIME_FORMAT))
            times -= 1
        instance = {"currentTime": now.strftime(DATETIME_FORMAT), "nextRunTime": next_run_time_list}
        data = AdapterRunTimeResponseSerializer(instance).data
        return build_success(data)


class AdapterFetchTypeAPIView(APIView):
    def post(self, request):
        serializer = AdapterIdSerializer(data=request.data)
        if serializer.is_valid():
            instance_id = serializer.save()
            adapter_instance: AdapterInstance = adapter_instance_service.get_adapter_instance(instance_id)
            if not adapter_instance:
                return build_failed(-1, "适配器实例不存在")
            adapter: Adapter = adapter_service.get_adapter(adapter_instance.adapter_name)
            if not adapter:
                return build_failed(-1, "适配器不存在")
            result = [{"label": item, "value": item} for item in adapter.fetch_setting.fetch_type_mapper.keys()]
            return build_success({"fetch_types": result})
        return build_failed(-1, serializer.errors)
