from caasm_tool.patch.router import DefaultRouter
from caasm_webapi.app.adapter.views.adapter import (
    AdapterIconView,
    AdapterListAPI,
    AdapterInstanceListAPI,
    AdapterInstanceAddAPI,
    AdapterTypeListAPI,
    AdapterInstanceModifyAPI,
    AdapterInstanceDeleteAPI,
    AdapterExistInstanceListAPI,
    AdapterDefaultTypeListAPI,
    AdapterConnectionTestAPI,
    AdapterRunTimeAPI,
    AdapterFetchTypeAPIView,
)
from caasm_webapi.app.adapter.views.convert_poc import (
    AdapterConvertPocRecordView,
    AdapterConvertStartView,
    AdapterConvertRecordListView,
)
from caasm_webapi.app.adapter.views.fabric_poc import AdapterFabricPocRecordView, AdapterFabricStartView
from caasm_webapi.app.adapter.views.fetch import AdapterFetchHistoryListAPI
from caasm_webapi.app.adapter.views.fetch_poc import (
    AdapterFetchLogListView,
    AdapterInstanceFetchPocRecordView,
    AdapterFetchStartView,
    AdapterFetchRecordListView,
    AdapterFetchConnectionCheckAPI,
    AdapterPocLogClearView,
)
from caasm_webapi.app.adapter.views.merge_poc import (
    AdapterMergePocRecordView,
    AdapterMergeStartView,
    AdapterMergeRecordListView,
)

adapter_router = DefaultRouter()

adapter_router.join_path("adapter/", AdapterListAPI, name="AdapterListAPI")
adapter_router.join_path("adapterInstanceList/", AdapterInstanceListAPI, name="AdapterInstanceListAPI")
adapter_router.join_path("adapterInstanceAdd/", AdapterInstanceAddAPI, name="adapterInstanceAdd")
adapter_router.join_path("adapterInstanceModify/", AdapterInstanceModifyAPI, name="AdapterInstanceModifyAPI")
adapter_router.join_path("adapterInstanceDelete/", AdapterInstanceDeleteAPI, name="AdapterInstanceDeleteAPI")
adapter_router.join_path("adapterInstance/getFetchTypes/", AdapterFetchTypeAPIView, name="AdapterFetchTypeAPIView")
adapter_router.join_path("adapterIcons/<adapter_name>/", AdapterIconView, name="AdapterIcons")
adapter_router.join_path("adapterType/", AdapterTypeListAPI, name="AdapterTypeListAPI")
adapter_router.join_path("adapterDefaultType/", AdapterDefaultTypeListAPI, name="AdapterTypeListAPI")
adapter_router.join_path(
    "adapterExistInstance/",
    AdapterExistInstanceListAPI,
    name="AdapterExistInstanceListAPI",
)
adapter_router.join_path(
    "adapterFetchHistory/",
    AdapterFetchHistoryListAPI,
    name="AdapterFetchHistoryListAPI",
)
adapter_router.join_path("adapterTestConnection/", AdapterConnectionTestAPI)
adapter_router.join_path("adapterNextRunTime/", AdapterRunTimeAPI)

adapter_router.join_path("adapter/fetch/pocLogs/", AdapterFetchLogListView)
adapter_router.join_path("adapter/fetch/pocRecord/", AdapterInstanceFetchPocRecordView)
adapter_router.join_path("adapter/fetch/pocStart/", AdapterFetchStartView)
adapter_router.join_path("adapter/fetch/pocData/", AdapterFetchRecordListView)
adapter_router.join_path("adapter/connection/pocCheck/", AdapterFetchConnectionCheckAPI)
adapter_router.join_path("adapter/fetch/pocClear/", AdapterPocLogClearView)

adapter_router.join_path("adapter/merge/pocRecord/", AdapterMergePocRecordView)
adapter_router.join_path("adapter/merge/pocStart/", AdapterMergeStartView)
adapter_router.join_path("adapter/merge/pocData/", AdapterMergeRecordListView)

adapter_router.join_path("adapter/convert/pocRecord/", AdapterConvertPocRecordView)
adapter_router.join_path("adapter/convert/pocStart/", AdapterConvertStartView)
adapter_router.join_path("adapter/convert/pocData/", AdapterConvertRecordListView)

adapter_router.join_path("adapter/fabric/pocRecord/", AdapterFabricPocRecordView)
adapter_router.join_path("adapter/fabric/pocStart/", AdapterFabricStartView)

default_app_config = "caasm_webapi.app.adapter.appconf.CaasmAdapterConfig"
