from rest_framework import fields
from rest_framework.serializers import Serializer

from caasm_webapi.common.serializers import ResponseSerializer


class OrderOperationLogSerializer(Serializer):
    """工单操作日志序列化器"""

    operation_type = fields.CharField(required=True, help_text="操作类型")
    operator = fields.CharField(required=True, help_text="操作人")
    operation_time = fields.CharField(required=True, help_text="操作时间")
    content = fields.CharField(required=True, help_text="操作内容")
    details = fields.DictField(required=False, default=dict, help_text="操作详情")


class OrderOperationLogSyncSerializer(Serializer):
    """工单操作日志同步序列化器"""

    order_id = fields.CharField(required=True, help_text="工单ID")
    logs = fields.ListField(child=OrderOperationLogSerializer(), required=True, help_text="操作日志列表")


class OrderOperationLogQuerySerializer(Serializer):
    """工单操作日志查询序列化器"""

    order_id = fields.CharField(required=True, help_text="工单ID")
    limit = fields.IntegerField(required=False, default=None, help_text="返回的日志数量限制")


class OrderOperationLogResponseSerializer(ResponseSerializer):
    """工单操作日志响应序列化器"""

    data = fields.ListField(child=OrderOperationLogSerializer(), help_text="操作日志列表")


class OrderBatchQuerySerializer(Serializer):
    """工单批量查询序列化器"""

    order_ids = fields.ListField(child=fields.CharField(), required=True, help_text="工单ID列表")


class OrderTimelineItemSerializer(Serializer):
    """工单时间轴项目序列化器"""

    time = fields.CharField(required=True, help_text="时间")
    person = fields.CharField(required=True, help_text="操作人")
    status = fields.CharField(required=True, help_text="状态")
    operation = fields.CharField(required=True, help_text="操作")


class OrderTimelineDaySerializer(Serializer):
    """工单时间轴日期序列化器"""

    date = fields.CharField(required=True, help_text="日期")
    items = fields.ListField(child=OrderTimelineItemSerializer(), required=True, help_text="时间轴项目列表")


class OrderDetailDataSerializer(Serializer):
    """工单详细数据序列化器"""

    systemName = fields.CharField(required=False, allow_null=True, help_text="系统名称")
    userCode = fields.CharField(required=False, allow_null=True, help_text="用户代码")
    userName = fields.CharField(required=False, allow_null=True, help_text="用户名")
    senderName = fields.CharField(required=False, allow_null=True, help_text="发送人名称")
    email = fields.CharField(required=False, allow_null=True, help_text="邮箱")
    phone = fields.CharField(required=False, allow_null=True, help_text="电话")
    createTime = fields.CharField(required=False, allow_null=True, help_text="创建时间")
    closeTime = fields.CharField(required=False, allow_null=True, help_text="关闭时间")
    expectedTime = fields.CharField(required=False, allow_null=True, help_text="预期完成时间")


class OrderDetailSerializer(Serializer):
    """工单详情序列化器"""

    id = fields.CharField(required=True, help_text="工单ID")
    title = fields.CharField(required=True, help_text="工单标题")
    data = OrderDetailDataSerializer(required=True, help_text="工单详细数据")
    timeline = fields.ListField(child=OrderTimelineDaySerializer(), required=True, help_text="工单时间轴")


class OrderBatchQueryResponseSerializer(ResponseSerializer):
    """工单批量查询响应序列化器"""

    data = fields.ListField(child=OrderDetailSerializer(), help_text="工单详情列表")
