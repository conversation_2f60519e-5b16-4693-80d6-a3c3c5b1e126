from rest_framework import fields
from rest_framework import serializers
from rest_framework.serializers import Serializer

from caasm_charts_manage.charts_manage.util.enum import ChartPresentation, Sorting, ChartModelCategoryEnum, CountMethod
from caasm_service.entity.data_stream_360 import DataStream360Entity
from caasm_service.runtime import overview_chart_service, overview_chart_category_service, overview_service


class ChartConfigRequestSerializer(Serializer):
    chart_type = serializers.ChoiceField(choices=ChartPresentation._value2member_map_, help_text="图表类型", required=True)


class CustomizeConfigSerializer(Serializer):
    pass


class DefaultConfigSerializer(Serializer):
    field = serializers.CharField()
    tag = serializers.CharField()
    order = serializers.ChoiceField(choices=Sorting._value2member_map_, help_text="排序方式")
    top = serializers.IntegerField()


class SegmentationCountSerializer(Serializer):
    field = serializers.CharField()
    tag = serializers.Char<PERSON><PERSON>()
    method = serializers.ChoiceField(choices=CountMethod._value2member_map_, help_text="计数方式")


class FieldSegmentationSerializer(Serializer):
    default = DefaultConfigSerializer(required=False)
    count = SegmentationCountSerializer()


class ChartInfoModuleRequestSerializer(Serializer):
    type = serializers.CharField()
    option = serializers.DictField()


class ChartOptionalSerializer(Serializer):
    type = serializers.CharField()


class ChartInfoOptionRequestSerializer(Serializer):
    """
    选项
    {
      "type": "dimension",
      "name": "类别轴/维度",
      "optional": {
        "type": "field",
        "optional": [
          {
            "type": "content",
            "option": {
              "field": "base.asset_type_display_name",
              "limit": 10
            }
          }
        ],
        "renderKey": "field_segmentation"
      },
      "tabsValue": "field"
    }
    ==> ==>  ==>
    ==> ==>  ==>
    {
      "type": "dimension",
      "name": "类别轴/维度",
      "optional": {
        "type": "field",
        "renderKey": "",
        "option" :{
            "field": "base.asset_type_display_name",
            "limit": 10
        }
      }
    }

    """

    type = serializers.ChoiceField(choices=ChartModelCategoryEnum._value2member_map_, help_text="图标配置模式")
    optional = serializers.DictField()

    def validate(self, attrs):
        model_type = attrs.get("type")
        optional = attrs.get("optional")
        model_optional = optional.get("optional")
        if len(model_optional) != 1:
            raise serializers.ValidationError(f"该{model_type}配置 不正确")
        attrs["optional"]["option"] = attrs["optional"]["optional"][0]["option"]
        del attrs["optional"]["optional"]
        return attrs


class SortFieldsSerializer(Serializer):
    field_name = serializers.CharField()
    sorting = serializers.CharField()


class AsqlChartSerializer(Serializer):
    chart_name = serializers.CharField(required=True)
    asql = serializers.CharField(required=True)


class CountAndTimeLineChartSerializer(Serializer):
    base_asql = serializers.CharField(required=False)
    days = serializers.IntegerField(required=False, default=0, allow_null=True)
    end_date = serializers.CharField(required=False)
    count_field = serializers.CharField(required=False)
    timeline_field = serializers.CharField(required=False)


class VulHistoryTimeLineChartSerializer(Serializer):
    base_asql = serializers.CharField(required=False)


class VulDateHistoryTimeLineChartSerializer(Serializer):
    base_asql = serializers.CharField(required=False)
    end_date = serializers.CharField(required=False)
    days = serializers.IntegerField(required=False, default=0, allow_null=True)


class VulDealCountChartSerializer(Serializer):
    base_asql = serializers.CharField(required=False)
    group_field = serializers.CharField(required=False)
    limit = serializers.IntegerField(required=False, default=3, allow_null=True)
    other_field = serializers.CharField(required=False)
    other_values = serializers.ListField(child=serializers.CharField(required=False), required=False)


class ChartInfoRequestionSerializer(Serializer):
    """
    传递chart信息及必要参数
    """

    base_date = serializers.ListSerializer(child=serializers.CharField(), max_length=2, required=False)
    type = serializers.ChoiceField(choices=ChartPresentation._value2member_map_, help_text="图表类型", required=True)
    asql = serializers.CharField(required=False)

    optional = serializers.ListSerializer(child=ChartInfoOptionRequestSerializer())

    page_size = serializers.IntegerField(required=False, default=20, allow_null=True)
    page_index = serializers.IntegerField(required=False, default=1, allow_null=True)
    field_sorting_list = serializers.ListSerializer(
        child=SortFieldsSerializer(), required=False, default=[], allow_null=True
    )

    def validate(self, attr):
        """
        这里主要做 验证
        不做 数据格式化
        """
        asql = attr.get("asql")
        optional = attr.get("optional")

        if optional:
            attr["chart_config"] = [
                {"type": item.get("optional").get("render_key"), "config": item.get("optional").get("option")}
                for item in optional
            ]
        return attr


class SaverChartDetailSerializer(ChartInfoRequestionSerializer):
    """
    保存chart详情
    """

    chart_name = serializers.CharField()
    category_tree_id = serializers.CharField()
    description = serializers.CharField(required=False)
    front_end_setting = serializers.DictField(required=False)

    def validate_chart_name(self, value):
        chart_info = overview_chart_service.get_chart_info(chart_name=value)
        if chart_info:
            raise serializers.ValidationError("图表名称重复，请重新输入")
        return value

    def validate_category_tree_id(self, value):
        category_tree = overview_chart_category_service.get_chart_category(category_id=value)
        if not category_tree:
            raise serializers.ValidationError("该图表收藏分类不存在，请检查正确分类")
        return value


class UpdateChartDetailSerializer(SaverChartDetailSerializer):
    """
    保存chart详情
    """

    chart_id = serializers.CharField()

    def validate_chart_name(self, value):
        return value

    def validate_chart_id(self, value):
        data = overview_chart_service.get_chart_info(chart_id=value)
        if not data:
            raise serializers.ValidationError("该图表不存在")
        return value


class SpaceChartSaverSerializer(Serializer):
    """
    空间图表信息
    """

    chart = serializers.CharField()
    chart_instance_name = serializers.CharField(required=False)
    horizontal_index = fields.IntegerField(help_text="水平序号")
    vertical_index = fields.IntegerField(help_text="垂直序号")
    width = fields.IntegerField(help_text="图表占宽")
    height = fields.IntegerField(help_text="图表占高")


class SpaceChartDetailSaverSerializer(Serializer):
    """
    空间图表 保存
    """

    space_id = serializers.CharField()
    charts = serializers.ListSerializer(child=SpaceChartSaverSerializer())

    def validate(self, attrs):
        space_id = attrs.get("space_id")
        data = overview_service.get_owner_space(space_id=space_id)
        if not data:
            raise serializers.ValidationError("该仪表盘不存在")
        return attrs


class ChartQuerySerializer(Serializer):
    space_id = serializers.CharField(required=False)
    chart_id = serializers.CharField()
    page_size = serializers.IntegerField(required=False, default=20)
    page_index = serializers.IntegerField(required=False, default=1)
    field_sorting_list = serializers.ListSerializer(
        child=serializers.JSONField(), required=False, default=[], source="field_sorting_list[]"
    )


class ChartRelativeTime(Serializer):
    time_type = serializers.CharField(default="time_point")

    def validate(self, attrs):
        time_type = attrs.get("time_type")

        if time_type not in ["period", "time_point"]:
            raise serializers.ValidationError("时间类型错误")

        return attrs


class ChartConfigsExportSerializer(Serializer):
    chart_ids = serializers.ListSerializer(child=serializers.CharField(), required=True)

    def validate(self, attrs):
        chart_ids = attrs.get("chart_ids")
        data = overview_chart_service.find_chart_info()
        all_chart_ids = []
        for chart in data:
            all_chart_ids.append(str(chart.id))
        for chart_id in chart_ids:
            if chart_id not in all_chart_ids:
                raise serializers.ValidationError("存在未知图表")
        return attrs


class ChartConfigsImportSerializer(Serializer):
    file = serializers.FileField()


class ChartDataStreamSerializer(Serializer):
    request_url = serializers.CharField(default="https://10.1.196.100/__rest/__api/discover/search?locale=zh_cn")
    api_key = serializers.CharField(default="50d5687015e7c86b9e51f095f367f6ab")


class ChartDataStream360RequestSerializer(Serializer):
    province = fields.CharField(required=False, default=None)

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        return {"province": validated_data["province"]}


class ChartDataStreamResponseSerializer(Serializer):
    src = fields.SerializerMethodField()
    src_data_center = fields.CharField()
    src_realm = fields.CharField()
    dst = fields.SerializerMethodField()
    dst_data_center = fields.CharField()
    dst_realm = fields.CharField()
    count = fields.IntegerField()

    def get_src(self, obj: DataStream360Entity):
        names = [obj.src_data_center]
        if obj.src_realm:
            names.append(obj.src_realm)
        return "_".join(names)

    def get_dst(self, obj: DataStream360Entity):
        names = [obj.dst_data_center]
        if obj.dst_realm:
            names.append(obj.dst_realm)
        return "_".join(names)


class DataCenterStreamRequestSerializer(Serializer):
    data_center = fields.CharField(required=True, max_length=32)

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        return validated_data["data_center"]


class BusinessCountChartSerializer(serializers.Serializer):
    """业务统计图表序列化器"""

    data_center = serializers.CharField(required=False, help_text="数据中心名称，用于过滤业务数据", default="")


class InterceptAlarmDisplaySerializer(Serializer):
    """告警拦截记录显示序列化器"""

    province = fields.CharField(required=False, default=None)

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        return {"province": validated_data["province"]}
