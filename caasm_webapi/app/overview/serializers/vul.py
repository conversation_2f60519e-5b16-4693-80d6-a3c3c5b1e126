from datetime import datetime, timezone
from rest_framework import fields
from rest_framework.serializers import Serializer

from caasm_webapi.common.serializers import ResponseSerializer
from caasm_service.constants.vul_cycle import (
    VulnLevelEnum,
    VulnStatusEnum,
    VulnStatusMapper,
    VulnLevelMapper,
    VulnStatusToEnumMapper,
    VulnLevelToEnumMapper,
)


class VulLifecycleSerializer(Serializer):
    id = fields.CharField(help_text="漏洞ID", required=False)
    name = fields.CharField(help_text="漏洞名称")
    level = fields.ChoiceField(choices=[level for level in VulnLevelMapper.values()], help_text="问题等级")
    score = fields.FloatField(help_text="威胁分值")
    host = fields.CharField(help_text="受影响主机")
    description = fields.CharField(help_text="详细描述")
    solution = fields.CharField(help_text="修复建议")
    cve_id = fields.Char<PERSON>ield(help_text="CVE编号")
    seen_time = fields.DateTimeField(help_text="发现日期")
    status = fields.ChoiceField(choices=[status for status in VulnStatusMapper.values()], help_text="状态")
    cnnvd_id = fields.CharField(help_text="cnnvd编号", required=False)
    cnvd_id = fields.CharField(help_text="cnvd编号", required=False)
    order_ids = fields.ListField(child=fields.CharField(), help_text="工单编号列表", required=False)
    business_name = fields.CharField(help_text="业务系统名称", required=False)
    port = fields.IntegerField(help_text="端口", required=False)
    protocal = fields.CharField(help_text="协议", required=False)
    update_time = fields.DateTimeField(help_text="更新时间", format="%Y-%m-%d %H:%M:%S")
    create_time = fields.DateTimeField(help_text="创建时间", format="%Y-%m-%d %H:%M:%S")
    file_ids = fields.ListField(child=fields.DictField(), help_text="附件列表", required=False)

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        pass


class VulLifecycleResponseSerializer(ResponseSerializer):
    data = fields.ListField(child=VulLifecycleSerializer(help_text="漏洞生命周期定义"), help_text="漏洞生命周期列表")


class TimeQuerySerializer(Serializer):
    create_time_start = fields.IntegerField(help_text="创建时间起始（时间戳）", required=False)
    create_time_end = fields.IntegerField(help_text="创建时间结束（时间戳）", required=False)

    def validate_seen_time(self, value):
        if value:
            try:
                datetime.strptime(value, "%Y-%m-%d")
            except ValueError:
                raise fields.ValidationError("发现日期格式错误,应为YYYY-MM-DD")
        return value

    def validate_create_time_start(self, value):
        if value:
            # 将时间戳转换为datetime对象
            try:
                return datetime.fromtimestamp(value, tz=timezone.utc)
            except (ValueError, TypeError, OverflowError):
                raise fields.ValidationError("创建时间起始格式错误,应为有效的时间戳")
        return value

    def validate_create_time_end(self, value):
        if value:
            # 将时间戳转换为datetime对象
            try:
                return datetime.fromtimestamp(value, tz=timezone.utc)
            except (ValueError, TypeError, OverflowError):
                raise fields.ValidationError("创建时间结束格式错误,应为有效的时间戳")
        return value


class VulLifecycleQuerySerializer(TimeQuerySerializer):
    name = fields.CharField(help_text="漏洞名称", required=False)
    level_group = fields.ListField(
        child=fields.ChoiceField(choices=[level for level in VulnLevelMapper.values()], required=True),
        help_text="威胁等级组",
        required=False,
        default=[],
    )
    score = fields.FloatField(help_text="威胁分值", required=False)
    host = fields.CharField(help_text="受影响主机", required=False)
    status_group = fields.ListField(
        child=fields.ChoiceField(
            choices=[status for status in VulnStatusMapper.values()], help_text="状态", required=True
        ),
        help_text="状态组",
        required=False,
        default=[],
    )
    order_id = fields.CharField(help_text="工单编号", required=False)
    business_name = fields.CharField(help_text="业务系统名称", required=False)
    keyword = fields.CharField(
        help_text="漏洞名称模糊查询，支持模糊查询的字段包括:name,host,order_ids,business_name",
        required=False,
        default=None,
    )
    page_size = fields.IntegerField(max_value=100, min_value=1, default=None, allow_null=False, help_text="分页条数")
    page_index = fields.IntegerField(min_value=1, default=None, allow_null=False, help_text="分页索引,从1开始")

    def validate(self, attrs):
        page_size = attrs.get("page_size", None)
        page_index = attrs.get("page_index", None)
        if page_size is not None and page_index is not None:
            attrs["page_index"] = (page_index - 1) * page_size
        return super().validate(attrs)

    def validate_status_group(self, value):
        if value:
            new_value = []
            for status in value:
                new_value.append(VulnStatusToEnumMapper[status].value)
            value = new_value
        return value

    def validate_level_group(self, value):
        if value is not None:
            new_value = []
            for level in value:
                new_value.append(VulnLevelToEnumMapper[level].value)
            value = new_value
        return value


class VulMonthlyStatSerializer(Serializer):
    month = fields.CharField(help_text="统计月份,格式:YYYY-MM")
    count = fields.IntegerField(help_text="漏洞数量", default=0)

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        pass


class VulMonthlyStatsQuerySerializer(TimeQuerySerializer):
    months = fields.IntegerField(min_value=1, max_value=24, default=6, help_text="要统计的月份数量，默认为6个月，最大为24个月")


class VulTypeStatSerializer(Serializer):
    type = fields.CharField(help_text="漏洞类型名称")
    count = fields.IntegerField(help_text="漏洞数量", default=0)

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        pass


class VulTypeStatsQuerySerializer(TimeQuerySerializer):
    pass


class VulLevelStatSerializer(Serializer):
    level = fields.CharField(help_text="漏洞威胁等级")
    count = fields.IntegerField(help_text="漏洞数量", default=0)
    name = fields.CharField(help_text="漏洞威胁等级名称")

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        pass


class VulLevelStatsQuerySerializer(TimeQuerySerializer):
    pass


class VulStatusSummarySerializer(Serializer):
    total = fields.IntegerField(help_text="漏洞总数")
    unfixed = fields.IntegerField(help_text="未修复总数")
    fixed = fields.IntegerField(help_text="已修复总数")
    reproduced = fields.IntegerField(help_text="复现漏洞总数")

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        pass


class VulStatusSummaryQuerySerializer(TimeQuerySerializer):
    pass


class VulBusinessSummarySerializer(Serializer):
    business_name = fields.CharField(help_text="业务系统名称")
    total = fields.IntegerField(help_text="漏洞总数")
    unfixed = fields.IntegerField(help_text="未修复总数")
    fixed = fields.IntegerField(help_text="已修复总数")
    reproduced = fields.IntegerField(help_text="复现总数")
    high_risk = fields.IntegerField(help_text="高危漏洞总数")
    medium_risk = fields.IntegerField(help_text="中危漏洞总数")
    low_risk = fields.IntegerField(help_text="低危漏洞总数")
    order_ids = fields.ListField(child=fields.CharField(), help_text="工单ID数组")

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        pass


class VulBusinessSummaryQuerySerializer(TimeQuerySerializer):
    keyword = fields.CharField(help_text="业务系统名称关键字", required=False)


class VulLifecycleBatchUpdateSerializer(Serializer):
    """批量更新漏洞状态的序列化器"""

    ids = fields.ListField(child=fields.CharField(), help_text="漏洞ID列表", required=True)
    status = fields.ChoiceField(
        choices=[status for status in VulnStatusMapper.values()], help_text="要更新的状态", required=True
    )
    file_ids = fields.ListField(child=fields.DictField(), help_text="漏洞文件ID", required=False, default=None)

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        return validated_data

    def validate_status(self, value):
        return VulnStatusToEnumMapper[value].value


class VulNetworkLevelStatSerializer(Serializer):
    """内外网漏洞威胁等级统计序列化器"""

    external_high_fixed = fields.IntegerField(help_text="外网高危已修复漏洞数量")
    external_high_unfixed = fields.IntegerField(help_text="外网高危未修复漏洞数量")
    external_medium_fixed = fields.IntegerField(help_text="外网中危已修复漏洞数量")
    external_medium_unfixed = fields.IntegerField(help_text="外网中危未修复漏洞数量")
    external_low_fixed = fields.IntegerField(help_text="外网低危已修复漏洞数量")
    external_low_unfixed = fields.IntegerField(help_text="外网低危未修复漏洞数量")
    internal_high_fixed = fields.IntegerField(help_text="内网高危已修复漏洞数量")
    internal_high_unfixed = fields.IntegerField(help_text="内网高危未修复漏洞数量")
    internal_medium_fixed = fields.IntegerField(help_text="内网中危已修复漏洞数量")
    internal_medium_unfixed = fields.IntegerField(help_text="内网中危未修复漏洞数量")


class VulNetworkLevelStatQuerySerializer(TimeQuerySerializer):
    """内外网漏洞威胁等级统计查询序列化器"""

    ...
