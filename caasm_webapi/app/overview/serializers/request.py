from dataclasses import dataclass
from typing import Optional, List, Dict, Any
from rest_framework import serializers


@dataclass
class BaseQueryConfig:
    """基础查询配置"""

    category: str
    base_date: List[str] = None
    base_query: Optional[str] = None
    page_size: int = 20
    page_index: int = 1
    sort_fields: List[str] = None

    def __post_init__(self):
        if self.base_date is None:
            self.base_date = []
        if self.sort_fields is None:
            self.sort_fields = []


@dataclass
class CountQueryConfig(BaseQueryConfig):
    """计数查询配置"""

    field: str = ""
    count_method: str = "value_count"
    file: Optional[str] = None
    time_comparison: Dict[str, Any] = None

    def __post_init__(self):
        super().__post_init__()
        if self.time_comparison is None:
            self.time_comparison = {"time": None}


@dataclass
class GroupQueryConfig(BaseQueryConfig):
    """分组查询配置"""

    chart_type: str = ""
    f_category: str = ""
    metric_type: str = ""
    group_field: str = ""
    group_limit: int = 10
    order: str = "desc"

    def __post_init__(self):
        super().__post_init__()


class QueryFactory:
    """查询条件工厂类"""

    @staticmethod
    def create_count_query(category: str, field: str, base_query: Optional[str] = None) -> Dict[str, Any]:
        """创建计数查询条件"""
        config = CountQueryConfig(category=category, base_query=base_query, field=field)
        return {
            "category": config.category,
            "base_date": config.base_date,
            "base_query": config.base_query,
            "chart_type": "text",
            "metric_type": "count",
            "count_info": {"field": config.field, "count_method": config.count_method, "file": config.file},
            "time_comparison": config.time_comparison,
            "page_size": config.page_size,
            "page_index": config.page_index,
            "sort_fields": config.sort_fields,
        }

    @staticmethod
    def create_group_query(
        category: str,
        group_field: str,
        group_limit: int,
        base_query: Optional[str] = None,
        chart_type: str = "histogram",
    ) -> Dict[str, Any]:
        """创建分组查询条件"""
        config = GroupQueryConfig(
            category=category,
            base_query=base_query,
            chart_type=chart_type,
            f_category="group",
            metric_type="group",
            group_field=group_field,
            group_limit=group_limit,
        )
        return {
            "category": config.category,
            "base_date": config.base_date,
            "base_query": config.base_query,
            "chart_type": config.chart_type,
            "f_category": config.f_category,
            "metric_type": config.metric_type,
            "count_info": {
                "field": "base.entity_id",
                "count_method": "value_count",
                "aql_filter": None,
                "order": config.order,
            },
            "group_fields": [{"field": config.group_field, "limit": config.group_limit}],
            "page_size": config.page_size,
            "page_index": config.page_index,
            "sort_fields": config.sort_fields,
        }


class BusinessRealmSerializer(serializers.Serializer):
    """业务领域查询序列化器"""

    data_centers = serializers.CharField(required=False, help_text="数据中心名称，多个数据中心用逗号分隔")
