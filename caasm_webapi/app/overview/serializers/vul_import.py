from rest_framework import serializers


class VulFileImportSerializer(serializers.Serializer):
    """漏洞文件导入序列化器"""

    order_ids = serializers.ListField(child=serializers.CharField(), required=True, help_text="工单ID列表")
    business_name = serializers.CharField(required=False, help_text="业务系统名称", default=None)
    name = serializers.CharField(required=False, help_text="文件名称，不包含扩展名")
    internal = serializers.BooleanField(required=False, help_text="是否为内部漏洞", default=None)
    other_vuln_state = serializers.BooleanField(required=False, help_text="是否设置其他漏洞状态", default=False)

    def validate(self, attrs):
        internal = attrs.get("internal", None)
        if internal is not None:
            attrs["internal"] = not internal
        else:
            attrs["internal"] = True
        return super().validate(attrs)
