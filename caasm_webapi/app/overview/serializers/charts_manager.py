from rest_framework.serializers import Serializer
from rest_framework import serializers

from caasm_service.runtime import overview_chart_service
from caasm_tool.constants import DATETIME_FORMAT


class ChartInfoRequestSerializer(Serializer):
    page_index = serializers.IntegerField(required=False, default=0)
    page_size = serializers.IntegerField(required=False, default=20, max_value=200)
    sort_fields = serializers.ListField(
        child=serializers.CharField(),
        help_text="排序字段",
        required=False,
        default=[("update_time", -1), ("chart_name", 1)],
    )
    category_id = serializers.Char<PERSON>ield(required=False)
    keyword = serializers.CharField(max_length=256, required=False, help_text="关键字")


class ModifyChartInfoRequestSerializer(Serializer):
    chart_id = serializers.Char<PERSON>ield()
    chart_name = serializers.Char<PERSON>ield(required=False)
    description = serializers.Char<PERSON><PERSON>(required=False)

    def validate(self, attrs):
        chart_id = attrs.get("chart_id")
        chart_name = attrs.get("chart_name")

        chart_info = overview_chart_service.get_chart_info(chart_id=chart_id)
        if not chart_info:
            raise serializers.ValidationError("该图表不存在")
        if chart_name:
            old_name = chart_info.chart_name
            if chart_name == old_name:
                return attrs
            chart_info = overview_chart_service.get_chart_info(chart_name=chart_name)
            if chart_info:
                raise serializers.ValidationError("图表名称重复，请重新输入")

        return attrs


class CopyChartInfoRequestSerializer(Serializer):
    chart_id = serializers.CharField()
    chart_name = serializers.CharField(required=False)
    description = serializers.CharField(required=False)

    def validate(self, attrs):
        chart_id = attrs.get("chart_id")
        data = overview_chart_service.get_chart_info(chart_id=chart_id)
        if not data:
            raise serializers.ValidationError("没找到对应的图表信息")
        chart_name = attrs.get("chart_name")
        if chart_name:
            chart_info = overview_chart_service.get_chart_info(chart_name=chart_name)
            if chart_info:
                raise serializers.ValidationError("图表名称重复，请重新输入")

        return attrs


class DeleteChartInfoRequestSerializer(Serializer):
    chart_ids = serializers.ListSerializer(child=serializers.CharField())


class ChartInfoResponseSerializer(Serializer):
    chart_id = serializers.CharField()
    category_tree_id = serializers.CharField()
    chart_name = serializers.CharField()
    creator = serializers.CharField()
    internal = serializers.BooleanField()
    description = serializers.CharField(required=False)
    chart_type = serializers.CharField()
    front_end_setting = serializers.DictField(required=False)
    category = serializers.CharField()
    base_query = serializers.CharField()
    create_time = serializers.DateTimeField(help_text="创建时间", format=DATETIME_FORMAT)
    update_time = serializers.DateTimeField(help_text="更新时间", format=DATETIME_FORMAT)
