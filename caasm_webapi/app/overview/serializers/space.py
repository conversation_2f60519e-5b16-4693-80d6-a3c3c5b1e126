import logging
from rest_framework import serializers
from rest_framework import fields
from rest_framework.serializers import Serializer

from caasm_service.constants.overview import DashboardSpaceDisplayEnum
from caasm_service.runtime import overview_service
from caasm_tool.patch.serializer import SerializerMixin
from caasm_webapi.common.serializers import ResponseSerializer

log = logging.getLogger()


class SpaceChooseSerializer(Serializer):
    label = fields.CharField()
    value = fields.CharField()


class SpaceChartSerializer(Serializer):
    chart_id = fields.CharField(source="chart", help_text="图表ID")
    horizontal_index = fields.IntegerField(help_text="水平序号")
    vertical_index = fields.IntegerField(help_text="垂直序号")
    width = fields.IntegerField(help_text="图表占宽")
    height = fields.IntegerField(help_text="图表占高")

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        pass


class SpaceSerializer(Serializer):
    id = fields.CharField(help_text="仪表盘空间ID")
    name = fields.CharField(help_text="仪表盘名称")
    charts = fields.ListField(child=SpaceChartSerializer(help_text="图表配置"), help_text="空间中包含的图表")
    index = fields.IntegerField(help_text="空间序号")
    is_default = fields.BooleanField(help_text="是否是默认空间")
    role_desc = fields.ListField(child=fields.CharField())
    ownership = fields.CharField()
    file_id = fields.CharField(required=False)
    frontend_validate_name = fields.CharField(required=False)

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        pass


class SpaceResponseSerializer(ResponseSerializer):
    data = fields.ListField(child=SpaceSerializer(help_text="仪表盘空间定义"), help_text="仪表盘空间列表")


class SpaceAddRequestSerializer(SerializerMixin):
    name = fields.CharField(required=True)
    role_codes = fields.ListField(child=fields.CharField(required=False), required=False)
    space_ownership = fields.ChoiceField(
        choices=[value for key, value in enumerate(DashboardSpaceDisplayEnum._value2member_map_.keys())]
    )
    file_id = fields.CharField(required=False)

    def validate(self, attrs):
        name = attrs.get("name")

        if overview_service.get_owner_space(space_name=name):
            raise serializers.ValidationError("仪表盘名称重复")
        return attrs


class SpaceModifyRequestSerializer(SpaceAddRequestSerializer):
    space_id = fields.CharField(required=True)

    def validate(self, attrs):
        space_id = attrs.get("space_id")
        name = attrs.get("name")

        data = overview_service.get_owner_space(space_id=space_id)
        if not data:
            raise serializers.ValidationError("该仪表盘已删除")
        space_id_name = data.name
        if space_id_name != name:
            if overview_service.get_owner_space(space_name=name):
                raise serializers.ValidationError("仪表盘名称重复")
        return attrs


class SpaceViewRequestSerializer(SerializerMixin):
    keyword = fields.CharField(help_text="关键字", required=False)
    page_size = fields.IntegerField(max_value=100, min_value=1, default=20, allow_null=True, help_text="分页条数")
    page_index = fields.IntegerField(min_value=1, default=1, allow_null=True, help_text="分页索引，从1开始")
    sort_fields = fields.ListField(child=fields.CharField(), default=["-time"])


class SpaceSpaceIDRequestSerializer(SerializerMixin):
    space_id = fields.CharField(required=True)


class SpaceCopyRequestSerializer(SpaceSpaceIDRequestSerializer):
    space_name = fields.CharField(required=False)


class SequencedSpacesSaveSerializer(SerializerMixin):
    spaces_sequence = fields.ListField(child=fields.CharField())


class IsSequenceSpacesSerializer(SerializerMixin):
    is_sequence = fields.BooleanField()
