from bson import ObjectId
from drf_yasg.utils import swagger_auto_schema
from rest_framework.views import APIView

import caasm_tool.util
from caasm_service.constants.overview import OverviewCategoryEnum

from caasm_service.runtime import overview_service, role_service, dashboard_user_setting_service

from caasm_webapi.app.overview.serializers.space import (
    SpaceResponseSerializer,
    SpaceSerializer,
    SpaceAddRequestSerializer,
    SpaceModifyRequestSerializer,
    SpaceViewRequestSerializer,
    SpaceSpaceIDRequestSerializer,
    SequencedSpacesSaveSerializer,
    SpaceChooseSerializer,
    SpaceCopyRequestSerializer,
    IsSequenceSpacesSerializer,
)

from rest_framework.request import Request
from rest_framework.response import Response

from caasm_webapi.app.overview.views.base import OverviewBaseAPIView
from caasm_webapi.util.response import build_failed, ResponseCode, build_success


class SpaceAPIView(OverviewBaseAPIView):
    def get(self, request: Request, overview_category):
        self.check_category(overview_category)
        serializer = SpaceViewRequestSerializer(data=request.query_params)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)
        validated_data = serializer.validated_data
        page_index = validated_data.get("page_index")
        page_size = validated_data.get("page_size")
        keyword = validated_data.get("keyword")
        user_id = request.user.user_id
        role_codes = request.user.role_codes
        count = overview_service.get_space_count(
            user_id=user_id, category=overview_category, role_codes=role_codes, keyword=keyword
        )
        if not count:
            return build_success({"total": count, "data": []})
        space_entities = overview_service.find_owner_space(
            user_id=user_id,
            category=overview_category,
            role_codes=role_codes,
            page_index=page_index,
            page_size=page_size,
            keyword=keyword,
            sort_field=[("index", 1), ("update_time", 1)],
        )
        role_info = {item.code: item.name for item in role_service.find_role()}
        result = []
        for space in space_entities:
            space_data = {
                "id": space.id,
                "name": space.name,
                "charts": space.charts,
                "index": space.index,
                "is_default": space.is_default,
                "ownership": space.ownership.value,
                "file_id": space.file_id or "",
                "frontend_validate_name": space.frontend_validate_name,
            }
            role_desc_list = []
            for item in space.role_codes:
                if item in role_info.keys():
                    role_desc_list.append(role_info.get(item))
            space_data["role_desc"] = role_desc_list
            result.append(space_data)
        serializer = SpaceSerializer(instance=result, many=True)
        return build_success({"total": count, "data": serializer.data})


class AddSpaceAPIView(OverviewBaseAPIView):
    @swagger_auto_schema(
        operation_summary="新增仪表盘空间",
        operation_description="新增仪表盘空间",
        responses={200: SpaceResponseSerializer()},
    )
    def post(self, request: Response, overview_category):
        self.check_category(overview_category)
        serializer = SpaceAddRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)

        validated_data = serializer.validated_data
        name = validated_data.get("name")
        validated_data["ownership"] = validated_data.pop("space_ownership")
        validated_data["is_default"] = False
        validated_data["user_id"] = request.user.user_id
        validated_data["category"] = overview_category
        if overview_service.get_space_count(space_name=name, category=overview_category):
            return build_failed(ResponseCode.REQUEST_ERROR, message="名称重复，请修改名称")
        overview_service.add_new_space(overview_service.load_entity(**validated_data))
        return build_success()


class ModifySpaceAPIView(OverviewBaseAPIView):
    @swagger_auto_schema(
        operation_summary="修改仪表盘空间",
        operation_description="修改仪表盘空间",
        responses={200: SpaceResponseSerializer()},
    )
    def post(self, request: Request, overview_category):
        self.check_category(overview_category)
        serializer = SpaceModifyRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)
        validated_data = serializer.validated_data
        space_id = validated_data.pop("space_id")
        validated_data["ownership"] = validated_data.pop("space_ownership")
        validated_data["is_default"] = False
        validated_data["user_id"] = request.user.user_id
        validated_data["category"] = overview_category
        name = validated_data["name"]
        data = overview_service.get_owner_space(space_id=space_id)
        space_id_name = data.name
        if space_id_name != name:
            if overview_service.get_owner_space(space_name=name):
                return build_failed(ResponseCode.REQUEST_ERROR, message="名称重复，请修改名称")
        overview_service.update_space(space_id=space_id, values=validated_data)
        return build_success()


class DeleteSpaceAPIView(OverviewBaseAPIView):
    @swagger_auto_schema(
        operation_summary="删除仪表盘空间",
        operation_description="删除仪表盘空间",
        responses={200: SpaceResponseSerializer()},
    )
    def post(self, request: Request, overview_category):
        self.check_category(overview_category)
        serializer = SpaceSpaceIDRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)
        validated_data = serializer.validated_data
        space_id = validated_data.get("space_id")
        space_data = overview_service.get_owner_space(space_id=space_id)
        if not space_data:
            return build_failed(ResponseCode.REQUEST_ERROR, message="该仪表盘不存在")
        if space_data.charts:
            return build_failed(ResponseCode.REQUEST_ERROR, message="请先删除仪表盘中的图表")
        overview_service.delete_space(space_id=space_id)
        return build_success()


class CopySpaceAPIView(OverviewBaseAPIView):
    @swagger_auto_schema(
        operation_summary="复制仪表盘空间",
        operation_description="复制仪表盘空间",
        responses={200: SpaceResponseSerializer()},
    )
    def post(self, request: Request, overview_category):
        self.check_category(overview_category)
        serializer = SpaceCopyRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)
        validated_data = serializer.validated_data
        space_id = validated_data.get("space_id")
        space_name = validated_data.get("space_name")
        space_data = overview_service.get_owner_space(space_id=space_id)
        if not space_data:
            return build_failed(ResponseCode.REQUEST_ERROR, message="该源仪表盘不存在")
        _data = {
            "name": space_name or caasm_tool.util.generate_random_string(space_data.name),
            "charts": space_data.charts,
            "index": space_data.index,
            "is_default": False,
            "category": space_data.category,
            "ownership": space_data.ownership,
            "create_time": caasm_tool.util.get_now(),
            "update_time": caasm_tool.util.get_now(),
        }
        overview_service.add_new_space(_data)
        return build_success()


class ReleaseSpacesListAPIView(OverviewBaseAPIView):
    @swagger_auto_schema(
        operation_summary="获取已经发布的仪表盘空间",
        operation_description="修改仪表盘空间发布状态",
        responses={200: SpaceResponseSerializer()},
    )
    def get(self, request: Request, overview_category):
        """
        这里单独拎一个接口是因为 这个要有客户设定的排序
        """
        self.check_category(overview_category)
        serializer = IsSequenceSpacesSerializer(data=request.query_params)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)
        validated_data = serializer.validated_data
        is_sequence = validated_data.get("is_sequence")
        user_id = request.user.user_id
        role_codes = request.user.role_codes
        space_ids = []

        space_data = overview_service.find_owner_space(
            user_id=user_id, role_codes=role_codes, category=overview_category
        )
        space_data = list(space_data)
        temp_result_map = {}
        for item in space_data:
            temp_result_map[item.id] = item
        if is_sequence:
            default_space = overview_service.find_owner_space(
                is_default=True, category=overview_category, sort_field=[("index", -1)]
            )
            default_space_ids = [item.id for item in default_space]
            space_ids.extend(default_space_ids)
            data = dashboard_user_setting_service.get_user_dashboard_setting(
                user_id=user_id, category=overview_category
            )
            if data:
                if all(item in data.spaces_sequence for item in space_ids):
                    space_ids = data.spaces_sequence
                else:
                    space_ids = space_ids + [item for item in data.spaces_sequence if item not in space_ids]
            space_data = []
            for id in space_ids:
                if id not in temp_result_map.keys():
                    continue
                space_data.append(temp_result_map[id])
        if not space_data:
            return build_success()

        result = []
        role_info = {item.code: item.name for item in role_service.find_role()}
        for space in space_data:
            _space_data = {
                "id": space.id,
                "name": space.name,
                "charts": space.charts,
                "index": space.index,
                "is_default": space.is_default,
                "ownership": space.ownership.value,
                "frontend_validate_name": space.frontend_validate_name,
            }
            role_desc_list = []
            for item in space.role_codes:
                if item in role_info.keys():
                    role_desc_list.append(role_info.get(item))
            _space_data["role_desc"] = role_desc_list
            result.append(_space_data)
        serializer = SpaceSerializer(instance=result, many=True)
        return build_success(serializer.data)


class SaveSequencedSpacesAPIView(OverviewBaseAPIView):
    @swagger_auto_schema(
        operation_summary="保存客户的排列顺序",
        operation_description="保存客户的排列顺序",
        responses={200: SpaceResponseSerializer()},
    )
    def post(self, request: Request, overview_category):
        self.check_category(overview_category)
        serializer = SequencedSpacesSaveSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)
        validated_data = serializer.validated_data
        _spaces_sequence = validated_data.get("spaces_sequence")
        spaces_sequence = [ObjectId(item) for item in _spaces_sequence]
        user_id = request.user.user_id
        data = dashboard_user_setting_service.get_user_dashboard_setting(user_id=user_id, category=overview_category)
        if not data:
            data = {
                "user_id": ObjectId(user_id),
                "spaces_sequence": spaces_sequence,
                "category": OverviewCategoryEnum.DASHBOARD,
            }
            dashboard_user_setting_service.save_user_setting(dashboard_user_setting_service.load_entity(**data))
        else:
            dashboard_user_setting_service.update_user_setting(
                user_id=user_id,
                values={"spaces_sequence": spaces_sequence},
                category=overview_category,
            )
        return build_success()


class DashBoardRolesAPIView(APIView):
    @swagger_auto_schema(
        operation_summary="获取角色列表",
        operation_description="获取角色列表",
        responses={200: SpaceResponseSerializer()},
    )
    def get(self, request: Request):
        result = []
        role_data = role_service.find_role()
        for role in role_data:
            result.append({"label": role.name, "value": role.code})
        serializer = SpaceChooseSerializer(instance=result, many=True)
        return build_success(serializer.data)
