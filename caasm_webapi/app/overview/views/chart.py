import datetime
import json
import logging
import os
from collections import defaultdict
from io import BytesIO

import requests
import shapely
from django.http import FileResponse
from rest_framework.request import Request
from rest_framework.views import APIView
from shapely import MultiPolygon, box, LineString, Point

from caasm_aql.tool import parse_aql
from caasm_charts_manage.chart_config.chart_config_import import ChartConfigImportHandler
from caasm_charts_manage.chart_config.manager import get_chart_config
from caasm_charts_manage.charts_manage.manager import ChartManager
from caasm_charts_manage.charts_manage.util.enum import MetricType
from caasm_config.config import caasm_config
from caasm_meta_data.constants import Category
from caasm_service.constants.setting import SettingName
from caasm_service.entity.data_stream_360 import DataStream360RealmEntity, DataStream360Entity
from caasm_service.entity.intercept_alarm import InterceptAlarmEntity
from caasm_service.runtime import (
    user_asset_aql_collect_service,
    retrieve_statement_service,
    overview_chart_service,
    overview_service,
    entity_service,
    snapshot_record_service,
    data_stream_360_service,
    data_stream_360_realm_service,
    setting_service,
    intercept_alarm_service,
)
from caasm_tool.constants import DATE_FORMAT_1
from caasm_webapi.app.overview.serializers.chart import (
    ChartConfigRequestSerializer,
    ChartInfoRequestionSerializer,
    SaverChartDetailSerializer,
    SpaceChartDetailSaverSerializer,
    ChartQuerySerializer,
    UpdateChartDetailSerializer,
    ChartRelativeTime,
    ChartConfigsExportSerializer,
    ChartConfigsImportSerializer,
    AsqlChartSerializer,
    CountAndTimeLineChartSerializer,
    VulDealCountChartSerializer,
    VulHistoryTimeLineChartSerializer,
    VulDateHistoryTimeLineChartSerializer,
    ChartDataStreamSerializer,
    ChartDataStream360RequestSerializer,
    ChartDataStreamResponseSerializer,
    DataCenterStreamRequestSerializer,
    BusinessCountChartSerializer,
    InterceptAlarmDisplaySerializer,
)
from caasm_webapi.app.overview.views.base import OverviewBaseAPIView
from caasm_webapi.util.response import build_failed, build_success, ResponseCode
from caasm_webapi.util.tool import get_user_id
from caasm_webapi.app.overview.serializers.request import QueryFactory


log = logging.getLogger()


class ChartBaseView(APIView):
    def pre_post(self, validated_data, category, data):
        """ "
        主要用于 格式化 数据
        """
        data["category"] = category
        data["base_date"] = validated_data.get("base_date")
        data["base_query"] = validated_data.get("asql")
        data["chart_type"] = validated_data.get("type")
        self.handle_chart_config(validated_data.get("chart_config"), data)
        data["page_size"] = validated_data.get("page_size")
        data["page_index"] = validated_data.get("page_index")
        data["sort_fields"] = validated_data.get("field_sorting_list")

    def handle_chart_config(self, chart_config, data):
        """
        处理chart_config
        """
        if not isinstance(chart_config, list):
            raise ValueError(f"{chart_config} type is {type(chart_config)},not a validate type ")
        if not len(chart_config):
            raise ValueError(f"{chart_config} not a validate chart config!")
        group_fields = []
        for i in range(0, len(chart_config)):
            if 0 == i and chart_config[i].get("type") == MetricType.GROUP.value:
                data["f_category"] = MetricType.GROUP.value
                data["metric_type"] = MetricType.GROUP.value
                group_fields.append(chart_config[i].get("config"))
            elif 0 == i and chart_config[i].get("type") == MetricType.COUNT.value:
                data["metric_type"] = MetricType.COUNT.value
                data["count_info"] = chart_config[0].get("config")
            elif 0 == i and chart_config[i].get("type") == MetricType.TIME.value:
                data["f_category"] = MetricType.TIME.value
                data["metric_type"] = MetricType.GROUP.value
                data["time"] = chart_config[i].get("config")
            elif 0 == i and chart_config[i].get("type") == MetricType.TABLE.value:
                data["metric_type"] = MetricType.TABLE.value
                data["table"] = chart_config[i].get("config")
            elif 0 == i and chart_config[i].get("type") == MetricType.ASQL.value:
                data["metric_type"] = MetricType.ASQL.value
                data["asql"] = chart_config[i].get("config")

            elif 1 == i and chart_config[i].get("type") == MetricType.GROUP.value:
                if chart_config[i].get("config").get("field"):
                    data["s_category"] = MetricType.GROUP.value
                    group_fields.append(chart_config[i].get("config"))
                else:
                    if data["f_category"] == MetricType.TIME.value:
                        data["metric_type"] = MetricType.TIME.value
                        data["s_category"] = MetricType.COUNT.value
            elif 1 == i and chart_config[i].get("type") == MetricType.TIME.value:
                data["s_category"] = MetricType.TIME.value
                data["time"] = chart_config[i].get("config", {})
            elif 1 == i and chart_config[i].get("type") == MetricType.COUNT.value:
                data["count_info"] = chart_config[i].get("config")
                if chart_config[0].get("type") == MetricType.ASQL.value:
                    status = chart_config[0].get("config", {}).get("percentage", "no")
                    data["count_info"]["percentage"] = True if status == "yes" else False
            elif 1 == i and chart_config[i].get("type") == MetricType.TIME_COMPARISON.value:
                data["time_comparison"] = chart_config[i].get("config")
            elif 1 == i and chart_config[i].get("type") == MetricType.ASQL.value:
                if data["asql"]:
                    data.get("asql", {}).get("querys", []).extend(chart_config[i].get("config", {}).get("querys", []))

            elif 2 == i and chart_config[i].get("type") == MetricType.COUNT.value:
                data["count_info"] = chart_config[i].get("config")

        if group_fields:
            data["group_fields"] = group_fields


class ChartConfigAPIView(APIView):
    """
    主要是解决 chart 的创建配置洁面，用于给前端返回使用
    """

    def get(self, request):
        serializer = ChartConfigRequestSerializer(data=request.query_params)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)
        validate_data = serializer.validated_data
        chart_type = validate_data.get("chart_type")
        try:
            result = get_chart_config(chart_type=chart_type)
        except Exception as e:
            import traceback

            log.error(f"find chart fail! {e},detail is {traceback.format_exc()}")
            return build_failed(ResponseCode.REQUEST_ERROR, message=f"图表类型查询失败!")
        return build_success(result)


class ChartConfigHandleAPIView(ChartBaseView):
    """
    主要是为了处理 chart的配置信息
    获取chart的展现方式
    """

    def post(self, request, category):
        serializer = ChartInfoRequestionSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)
        validated_data = serializer.validated_data
        data = {}
        self.pre_post(validated_data, category, data)
        manager = ChartManager(**data)
        result = manager.handle()
        return build_success({"chart_type": data.get("chart_type"), "result": result})


class AsqlChart(APIView):
    def post(self, request, overview_category):
        serializer = AsqlChartSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)
        validated_data = serializer.validated_data
        chart_name = validated_data["chart_name"]
        asql = validated_data["asql"]

        query = {
            "category": f"{overview_category}",
            "base_date": [],
            "base_query": f"{asql}",
            "chart_type": "text",
            "metric_type": "count",
            "count_info": {"field": "base.entity_id", "count_method": "value_count", "file": None},
            "time_comparison": {"time": None},
            "page_size": 20,
            "page_index": 1,
            "sort_fields": [],
        }
        count_info = ChartManager(**query).handle()
        return build_success(data={"chart_name": chart_name, "result": count_info})


class StatisticsCountChartView(APIView):
    def build_search_query(self, category=None, base_aql=None, group_field=None, limit=None):
        return {
            "category": category,
            "base_date": [],
            "base_query": f"{base_aql}",
            "chart_type": "bar",
            "f_category": "group",
            "metric_type": "group",
            "count_info": {
                "field": "base.entity_id",
                "count_method": "value_count",
                "order": "desc",
                "aql_filter": None,
            },
            "group_fields": [{"field": f"{group_field}", "limit": limit}],
            "page_size": 20,
            "page_index": 1,
            "sort_fields": [],
        }

    def post(self, request, overview_category):
        serializer = VulDealCountChartSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)
        validated_data = serializer.validated_data

        base_aql = validated_data.get("base_asql")
        group_field = validated_data.get("group_field")
        limit = validated_data.get("limit")
        other_field = validated_data.get("other_field")
        other_values = validated_data.get("other_values")

        query = self.build_search_query(
            category=overview_category, base_aql=base_aql, group_field=group_field, limit=limit
        )
        manager = ChartManager(**query)
        result = manager.handle()
        result = self.enrich_other_info(result, other_field, other_values, category=overview_category)
        return build_success({"result": result})

    def enrich_other_info(self, result, other_field, other_values, category):

        for item in result:
            asql = item["asql"]
            asqls = asql.split(";") or []
            item["table"] = [
                {"value": "url", "count": item["name"], "asql": item["asql"], "category": category},
                {"value": "总数", "count": item["count"], "asql": item["asql"], "category": category},
            ]
            for value in other_values:
                asqls.append(f"$.{other_field} = '{value}'")
                _asql = " and ".join(asqls)
                query = parse_aql(_asql, category=category, date=None)
                count = entity_service.get_count(category=category, date=None, condition=query)
                item["table"].append({"value": value, "count": count, "asql": ";".join(asqls), "category": category})

        return result


class VulDateHisttoryChart(APIView):
    def post(self, request):
        serializer = VulDateHistoryTimeLineChartSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)
        validated_data = serializer.validated_data
        base_asql = validated_data.get("base_asql")
        end_date = validated_data.get("end_date")
        days = validated_data.get("days")
        given_date = datetime.datetime.strptime(end_date, "%Y-%m-%d")
        result_date = given_date - datetime.timedelta(days=days)
        display_date = result_date.strftime("%Y-%m-%d")
        given_date_timestamp = given_date.timestamp() * 1000
        result_date_timestamp = result_date.timestamp() * 1000

        query = {
            "category": "asset_vul_instance",
            "base_date": [],
            "base_query": base_asql,
            "chart_type": "line",
            "f_category": "time",
            "metric_type": "time",
            "time": {
                "time": {
                    "type": "absolute",
                    "label": f"{display_date} ~ {end_date}",
                    "value": [result_date_timestamp, given_date_timestamp],
                },
                "step_size": 1,
                "limit": days,
            },
            "s_category": "count",
            "count_info": {
                "field": "base.entity_id",
                "count_method": "value_count",
                "order": "desc",
                "aql_filter": None,
            },
            "page_size": 20,
            "page_index": 1,
            "sort_fields": [],
        }

        manager = ChartManager(**query)
        result = manager.handle()

        for i in range(1, len(result)):
            previous_count = result[i - 1]["count"]
            current_count = result[i]["count"]
            change = current_count - previous_count
            result[i]["change"] = change

        return build_success(data={"result": result})


class VulHistoryTimeLineChart(APIView):
    def get_total_count(self, base_asql=None):
        query = {
            "category": "asset_vul_instance",
            "base_date": [],
            "base_query": f"{base_asql}",
            "chart_type": "text",
            "metric_type": "count",
            "count_info": {"field": "base.entity_id", "count_method": "value_count", "file": None},
            "time_comparison": {"time": None},
            "page_size": 20,
            "page_index": 1,
            "sort_fields": [],
        }
        manager = ChartManager(**query)
        result = manager.handle()
        return result

    def get_timeline_result(self, base_asql=None):
        query = {
            "category": "asset_vul_instance",
            "base_date": [],
            "base_query": f"{base_asql}",
            "chart_type": "line",
            "f_category": "time",
            "metric_type": "time",
            "time": {"time": {"type": "relative", "label": "近七天", "value": "7"}, "step_size": 1, "limit": 10},
            "s_category": "count",
            "count_info": {
                "field": "base.entity_id",
                "count_method": "value_count",
                "order": "desc",
                "aql_filter": None,
            },
            "page_size": 20,
            "page_index": 1,
            "sort_fields": [],
        }
        manager = ChartManager(**query)
        result = manager.handle()
        return result

    def post(self, request):
        """
        漏洞历史时间线图
        """
        serializer = VulHistoryTimeLineChartSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)
        validated_data = serializer.validated_data
        base_asql = validated_data.get("base_asql")

        total_count = self.get_total_count(base_asql)
        timeline_result = self.get_timeline_result(base_asql)
        return build_success(data={"total_count": total_count, "timeline_result": timeline_result})


class VulHistoryTableChart(APIView):
    def post(self, request):
        serializer = VulHistoryTimeLineChartSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)
        validated_data = serializer.validated_data
        base_asql = validated_data.get("base_asql")
        query = {
            "category": "asset_vul_instance",
            "base_date": [],
            "base_query": base_asql,
            "chart_type": "bar",
            "f_category": "group",
            "metric_type": "group",
            "count_info": {
                "field": "base.entity_id",
                "count_method": "value_count",
                "order": "desc",
                "aql_filter": None,
            },
            "group_fields": [{"field": "asset_vul_instance.business_name", "limit": 3}],
            "page_size": 20,
            "page_index": 1,
            "sort_fields": [],
        }
        result = ChartManager(**query).handle()
        date = snapshot_record_service.get_latest_useful_date_of_category(Category.ASSET_VUL_INSTANCE)
        for item in result:
            aql = item.get("asql")
            low_count_asql = f"{aql} and $.asset_vul_instance.priority = '低'"
            mid_count_asql = f"{aql} and $.asset_vul_instance.priority = '中'"
            high_count_asql = f"{aql} and $.asset_vul_instance.priority = '高'"
            low_query = parse_aql(low_count_asql, category=Category.ASSET_VUL_INSTANCE, date=date)
            low_count = entity_service.get_count(category=Category.ASSET_VUL_INSTANCE, date=date, condition=low_query)
            mid_query = parse_aql(mid_count_asql, category=Category.ASSET_VUL_INSTANCE, date=date)
            mid_count = entity_service.get_count(category=Category.ASSET_VUL_INSTANCE, date=date, condition=mid_query)
            high_query = parse_aql(high_count_asql, category=Category.ASSET_VUL_INSTANCE, date=date)
            high_count = entity_service.get_count(category=Category.ASSET_VUL_INSTANCE, date=date, condition=high_query)
            item["low_count"] = low_count
            item["mid_count"] = mid_count
            item["high_count"] = high_count
            item["low_count_asql"] = low_count_asql
            item["mid_count_asql"] = mid_count_asql
            item["high_count_asql"] = high_count_asql
        return build_success(data=result)


class CountAndDateLineChart(APIView):
    def get_all_count(self, base_asql=None, days=None, count_field=None, category=None, end_date=None):

        now = current_date = datetime.datetime.strptime(end_date, "%Y-%m-%d")
        seven_days_ago = now - datetime.timedelta(days=days)
        seven_days_ago_str = seven_days_ago.strftime("%Y-%m-%d")

        _asql = f"$.{count_field} > '{seven_days_ago_str}'"
        if base_asql:
            _asql = f"{_asql} and {base_asql}"
        query = parse_aql(_asql, category=category, date=None)

        count = entity_service.get_count(category=category, date=None, condition=query)
        return {"count": count, "asql": _asql}

    def get_timeline_count(self, base_asql=None, category=None, timeline_field=None, days=None, end_date=None):

        current_date = datetime.datetime.strptime(end_date, "%Y-%m-%d")
        # 获取最近七天的日期
        dates = []
        for i in range(days):
            day = (current_date - datetime.timedelta(days=i)).strftime("%Y-%m-%d")
            dates.append(day)
        result = []
        for day in dates:
            _asql = f"$.{timeline_field} = '{day}'"
            if base_asql:
                _asql = f"{_asql}"
            _asql = f"{_asql} and {base_asql}"
            query = parse_aql(_asql, category=category, date=None)

            count = entity_service.get_count(category=Category.VUL_INSTANCE_UNIQUE, date=None, condition=query)

            result.append({"date": day, "count": count, "asql": _asql})
        return result

    def post(self, request, overview_category):
        serializer = CountAndTimeLineChartSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)
        validated_data = serializer.validated_data
        base_asql = validated_data.get("base_asql")
        days = validated_data.get("days", 7)
        end_date = validated_data.get("end_date", datetime.datetime.now().date().strftime(DATE_FORMAT_1))
        timeline_field = validated_data.get("timeline_field")
        count_field = validated_data.get("count_field")

        count = self.get_all_count(
            base_asql=base_asql, days=days, count_field=count_field, category=overview_category, end_date=end_date
        )
        line_count = self.get_timeline_count(
            end_date=end_date, category=overview_category, base_asql=base_asql, days=days, timeline_field=timeline_field
        )

        return build_success(data={"count": count, "timeline": line_count, "category": overview_category})


class GetBusinessCountChart(APIView):
    def get(self, request):
        serializer = BusinessCountChartSerializer(data=request.query_params)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer.errors)

        # 获取数据中心参数(字符串)
        data_center = serializer.validated_data.get("data_center", "")

        # 构建查询条件
        if data_center == "所有分公司":
            base_query = f"business.data_centers.regex('.*分公司')"
        elif data_center:
            base_query = f"business.data_centers = '{data_center}'"
        else:
            base_query = None

        # 使用工厂函数创建查询
        count_query = QueryFactory.create_count_query(
            category="business", field="base.entity_id", base_query=base_query
        )

        importance_query = QueryFactory.create_group_query(
            category="business", group_field="business.magnitude", group_limit=4, base_query=base_query
        )

        level_query = QueryFactory.create_group_query(
            category="business", group_field="business.grade_protection_level", group_limit=3, base_query=base_query
        )

        department_query = QueryFactory.create_group_query(
            category="business",
            group_field="asset_base.ownership.department.name",
            group_limit=10,
            base_query=base_query,
        )

        return build_success(
            data={
                "count_info": ChartManager(**count_query).handle(),
                "importance_info": ChartManager(**importance_query).handle(),
                "level_info": ChartManager(**level_query).handle(),
                "department_info": ChartManager(**department_query).handle(),
            }
        )


class GetBusinessByRealm(APIView):
    def get(self, request: Request):
        # 参数校验
        serializer = BusinessCountChartSerializer(data=request.query_params)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer.errors)

        # 获取数据中心参数
        data_centers_str = serializer.validated_data.get("data_center", "")

        # 构建基础查询条件
        data_center_conditions = None
        if data_centers_str:
            if data_centers_str == "所有分公司":
                data_center_conditions = {"regexp": {"business.data_centers": ".*分公司"}}
            else:
                data_center_conditions = {"term": {"business.data_centers": data_centers_str}}

        # 构建聚合查询
        search_body = {
            "size": 0,
            "aggs": {
                "counts_by_realm": {"terms": {"field": "business.realms", "size": 10, "order": {"_count": "desc"}}}
            },
        }

        # 添加过滤条件
        if data_center_conditions:
            search_body["query"] = {"bool": {"must": data_center_conditions}}

        # 执行聚合查询
        aggregations = entity_service.search(
            **search_body,
            table=entity_service.get_table(Category.BUSINESS),
        )

        result = []
        for bucket in aggregations.body["aggregations"]["counts_by_realm"]["buckets"]:
            count = bucket["doc_count"]
            key = bucket["key"]
            result.append(
                {
                    "asql": f"business.realms = '{key}'",
                    "count": count,
                    "isPercentage": False,
                    "name": key,
                    "proportionCount": 0,
                }
            )

        if not result:
            result = [
                {
                    "asql": "business.realms = '测试系统1'",
                    "count": 1000,
                    "isPercentage": False,
                    "name": "测试系统1",
                    "proportionCount": 0,
                },
                {
                    "asql": "business.realms = '测试系统2'",
                    "count": 800,
                    "isPercentage": False,
                    "name": "测试系统2",
                    "proportionCount": 0,
                },
                {
                    "asql": "business.realms = '测试系统3'",
                    "count": 500,
                    "isPercentage": False,
                    "name": "测试系统3",
                    "proportionCount": 0,
                },
            ]

        return build_success(result)


class AqlCollectListAPIView(APIView):
    def get(self, request):
        user_id = get_user_id(request)
        count = user_asset_aql_collect_service.get_user_asset_aql_collect_count(user_id=user_id)
        if not count:
            return build_success([])
        cur = user_asset_aql_collect_service.find_user_asset_aql_collect(user_id=user_id)
        user_asset_aql_collect_list = list(cur)

        _sids = [i.retrieve_statement_id for i in user_asset_aql_collect_list]
        statement_mapper = retrieve_statement_service.find_retrieve_aql_to_mapper(retrieve_ids=_sids) if _sids else {}
        result = []
        for item in user_asset_aql_collect_list:
            aql_info = statement_mapper.get(item.retrieve_statement_id)
            if not aql_info:
                continue
            result.append({"value": str(item.id), "label": item.name, "aql": aql_info.aql, "type": item.aql_type})
        return build_success(result)


class SaverChartDetailAPIView(ChartBaseView):
    def post(self, request, category):
        serializer = SaverChartDetailSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)
        validated_data = serializer.validated_data
        data = {}
        data["creator_id"] = request._user.user_id
        data["front_end_setting"] = validated_data["front_end_setting"]
        data["chart_name"] = validated_data.get("chart_name")
        data["category_tree_id"] = validated_data.get("category_tree_id")
        data["description"] = validated_data.get("description", "")
        self.pre_post(validated_data, category, data)
        manager = ChartManager(**data)
        data["result"] = manager.handle()
        overview_chart_service.add_chart_info(overview_chart_service.load_entity(**data))
        return build_success()


class UpdateChartDetailAPIView(ChartBaseView):
    def post(self, request, category):
        serializer = UpdateChartDetailSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)
        validated_data = serializer.validated_data
        chart_id = validated_data.get("chart_id")
        chart_name = validated_data.get("chart_name")
        temp_data = overview_chart_service.get_chart_info(chart_id=chart_id)
        if temp_data.chart_name != chart_name:
            name_info = overview_chart_service.get_chart_info(chart_name=chart_name)
            if name_info:
                return build_failed(ResponseCode.REQUEST_ERROR, message="图表名称与其他图表重复")
        data = {}
        self.pre_post(validated_data, category, data)
        data["chart_name"] = chart_name
        data["category_tree_id"] = validated_data.get("category_tree_id")
        data["description"] = validated_data.get("description", "")
        data["creator_id"] = request._user.user_id
        data["front_end_setting"] = validated_data.get("front_end_setting")
        manager = ChartManager(**data)
        data["result"] = manager.handle()
        overview_chart_service.update_chart_info_by_id(chart_id=chart_id, value=data)
        return build_success()


class SpaceChartSaverAPIView(OverviewBaseAPIView):
    def post(self, request, overview_category):
        self.check_category(overview_category)
        serializer = SpaceChartDetailSaverSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)
        validated_data = serializer.validated_data
        space_id = validated_data.get("space_id")
        charts = validated_data.get("charts")
        overview_service.update_space(space_id=space_id, values={"charts": charts})
        return build_success()


class ChartQueryAPIView(APIView):
    """
    单个图表查询接口
    """

    def get(self, request):
        serializer = ChartQuerySerializer(data=request.query_params)

        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)
        validate_data = serializer.validated_data
        space_id = validate_data.get("space_id")
        chart_id = validate_data.get("chart_id")
        page_size = validate_data.get("page_size")
        page_index = validate_data.get("page_index")
        field_sorting_list = request.query_params.getlist("field_sorting_list[]") or []
        sort_fields = [json.loads(item) for item in field_sorting_list]

        data = overview_chart_service.get_chart_info(chart_id=chart_id)
        if not data:
            return build_success({})
        time = None
        if data.time:
            time = data.time if data.time.get("time") else None
        chart_name = data.chart_name
        base_query_mapper = caasm_config.CHAT_BASE_AQL_MAPPING
        if isinstance(base_query_mapper, dict) and base_query_mapper.get(chart_name):
            base_query = base_query_mapper[chart_name]
        else:
            base_query = data.base_query
        if data.result:
            temp_result = data.result
        else:
            time = None
            if data.time:
                time = data.time if data.time.get("time") else None

        json_data = overview_chart_service.convert_data_to_json(data=data, many=False)
        json_data["base_query"] = base_query
        json_data["page_size"] = page_size
        json_data["page_index"] = page_index
        json_data["sort_fields"] = sort_fields
        json_data["time"] = time
        manager = ChartManager(**json_data)
        temp_result = manager.handle()

        if space_id:
            space_data = overview_service.get_owner_space(space_id=space_id)
            for temp in space_data.charts:
                if temp.chart != chart_id:
                    continue
                chart_name = temp.chart_instance_name if temp.chart_instance_name else chart_name
        result = {
            "chart_id": chart_id,
            "chart_name": chart_name,
            "description": data.description,
            "create_time": data.create_time,
            "update_time": data.update_time,
            "chart_type": data.chart_type.value,
            "category": data.category,
            "result": temp_result,
            "base_query": data.base_query or "",
            "front_end_setting": data.front_end_setting,
        }

        return build_success(result)


class ChartConfigsExport(APIView):
    def post(self, request):
        serializer = ChartConfigsExportSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)
        validated_data = serializer.validated_data
        chart_ids = validated_data.get("chart_ids")
        data = overview_chart_service.find_chart_info(chart_ids=chart_ids)
        try:
            result = overview_chart_service.convert_data_to_json(data=data, many=True)
        except Exception as e:
            return build_failed(ResponseCode.REQUEST_ERROR, message="数据内容错误！")
        for item in result:
            item.pop("_id")
            item.pop("creator_id")
            item.pop("category_tree_id")
        writer = BytesIO()

        writer.write(f"{result}\n".encode("utf-8"))
        writer.seek(0)
        response = FileResponse(writer)
        response["Content-Disposition"] = 'attachment;filename="chart_config.json"'
        return response


class ChartConfigsImport(APIView):
    def post(self, request):
        serializer = ChartConfigsImportSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)
        validated_data = serializer.validated_data
        file_info = validated_data.get("file")
        builder = ChartConfigImportHandler()
        try:
            builder.execute(file_info.read(), request.user.user_id)
            return build_success()
        except Exception as e:
            return build_failed(ResponseCode.REQUEST_ERROR, message=e)


class GetRelativeTimeView(APIView):
    def get(self, request):
        serializer = ChartRelativeTime(data=request.query_params)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)
        validate_data = serializer.validated_data

        time_type = validate_data.get("time_type")
        result = []
        if time_type == "period":
            result = [
                {"label": "近三天", "value": "3"},
                {"label": "近七天", "value": "7"},
                {"label": "近两周", "value": "14"},
                {"label": "近三周", "value": "21"},
                {"label": "近一个月", "value": "30"},
                {"label": "近两个月", "value": "60"},
            ]
        elif time_type == "time_point":
            result = [
                {"label": "三天前", "value": "3"},
                {"label": "七天前", "value": "7"},
                {"label": "两周前", "value": "14"},
                {"label": "三周前", "value": "21"},
                {"label": "一个月前", "value": "30"},
                {"label": "两个月前", "value": "60"},
            ]
        return build_success(data=result)


class GetDataStreamAPIView(APIView):
    def post(self, request):
        serializer = ChartDataStreamSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)
        validated_data = serializer.validated_data
        request_url = validated_data["request_url"]
        api_key = validated_data["api_key"]

        json_data = {
            "dimensions": [
                {
                    "type": "term",
                    "field": "src_address_company",
                    "top": {"type": "metric", "metric": "count", "field": "id", "order": "desc", "value": 10},
                },
                {
                    "type": "term",
                    "field": "dst_address_company",
                    "top": {"type": "metric", "metric": "count", "field": "id", "order": "desc", "value": 10},
                },
                {"type": "metric", "metric": "count", "field": "id"},
            ],
            "chartType": "AggTablePanel",
            "chartFlag": True,
            "source": "event",
            "filter": "",
            "timeFilter": "发生时间 >=now-3m AND 发生时间 <=now",
            "mode": "bi",
        }

        response = requests.post(request_url, json=json_data, headers={"x-api-key": f"{api_key}"}, verify=False)
        result = response.json()
        return build_success(data={"result": result})


class DataStreamAPIView(APIView):
    def post(self, request):
        serializer = ChartDataStream360RequestSerializer(data=request.data)
        if serializer.is_valid():
            province_dict = serializer.save()
            province = province_dict["province"]
            return build_success(
                ChartDataStreamResponseSerializer(
                    instance=data_stream_360_service.find_streams(province=province), many=True
                ).data
            )

        return build_failed(-1, str(serializer.errors))


class DataStreamDisplayAPIView(APIView):
    @staticmethod
    def _get_full_name(data_center, realm):
        names = []
        if data_center:
            names.append(data_center)
        if realm:
            names.append(realm)
        return "_".join(names)

    def _get_coordinates(self, data_center, realm, realm_mapper):
        full_name = self._get_full_name(data_center, realm)
        if full_name in realm_mapper:
            return realm_mapper[full_name]
        if data_center in realm_mapper:
            return realm_mapper[data_center]
        return None

    @staticmethod
    def _extend_line(point1, point2, length):
        # 创建一个从 point1 到 point2 的线段
        line = LineString([point1, point2])

        # 计算线段的方向向量
        dx = point2.x - point1.x
        dy = point2.y - point1.y

        # 计算线段的长度
        line_length = line.length

        # 计算延长线的比例
        scale = (line_length + length) / line_length

        # 计算新的点的位置
        new_x = point1.x + dx * scale
        new_y = point1.y + dy * scale

        # 创建新的延长线
        extended_line = LineString([point1, point2, (new_x, new_y)])

        return extended_line

    def post(self, request):
        serializer = ChartDataStream360RequestSerializer(data=request.data)
        if serializer.is_valid():
            province_dict = serializer.save()
            province = province_dict["province"]
            realm_mapper = {}
            for realm in data_stream_360_realm_service.find():
                realm: DataStream360RealmEntity = realm
                realm_mapper[self._get_full_name(realm.data_center, realm.realm)] = [realm.longitude, realm.latitude]
                realm_mapper[realm.data_center] = [realm.longitude, realm.latitude]
            lines = []
            nodes = {}
            point_names = {}
            province_mapper = {}
            for stream in data_stream_360_service.find_streams(province=province):
                stream: DataStream360Entity = stream
                src_coordinates = self._get_coordinates(stream.src_data_center, stream.src_realm, realm_mapper)
                if not src_coordinates:
                    continue
                if province:
                    if stream.src_province != province:
                        point_names[self._get_full_name(stream.src_data_center, stream.src_realm)] = src_coordinates
                dst_coordinates = self._get_coordinates(stream.dst_data_center, stream.dst_realm, realm_mapper)
                if not dst_coordinates:
                    continue
                if province:
                    if stream.src_province != province:
                        point_names[self._get_full_name(stream.src_data_center, stream.src_realm)] = src_coordinates
                src_full_name = self._get_full_name(stream.src_data_center, stream.src_realm)
                dst_full_name = self._get_full_name(stream.dst_data_center, stream.dst_realm)
                province_mapper[src_full_name] = stream.src_province
                province_mapper[dst_full_name] = stream.dst_province
                lines.append(
                    [
                        {"name": src_full_name, "coords": src_coordinates},
                        {"name": dst_full_name, "coords": dst_coordinates},
                    ]
                )
                nodes[src_full_name] = {"name": src_full_name, "value": src_coordinates}
                nodes[dst_full_name] = {"name": dst_full_name, "value": dst_coordinates}

            fake_nodes = []
            if province:
                fake_point_mapper = {}
                province_bbox = None
                centroid = None
                #   加载中国省份地利坐标
                with open(
                    os.path.join(
                        os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))),
                        "caasm_script",
                        "data",
                        "china.geojson",
                    )
                ) as fp:
                    china = json.load(fp)
                    for feature in china["features"]:
                        if province != feature["properties"]["name"]:
                            continue
                        province_geometry: MultiPolygon = shapely.from_geojson(json.dumps(feature))
                        if not province_geometry.is_valid:
                            province_geometry = province_geometry.buffer(0)
                            if not province_geometry.is_valid:
                                continue
                        """Returns minimum bounding region (minx, miny, maxx, maxy)"""
                        bounds = list(province_geometry.bounds)
                        bounds[0] -= 0.5
                        bounds[1] -= 0.2
                        bounds[2] += 0.5
                        bounds[3] += 0.2
                        province_bbox = box(*bounds)
                        centroid: Point = province_bbox.centroid
                        province_bbox = province_bbox.boundary
                        break
                if province_bbox:
                    province_bbox: LineString = province_bbox
                    for key, value in nodes.items():
                        name = value["name"]
                        if name in province_mapper and province_mapper[name] == province:
                            continue
                        line = self._extend_line(centroid, Point(*value["value"]), 10)
                        intersection_point: LineString = province_bbox.intersection(line)
                        if not intersection_point.is_empty:
                            fake_point_mapper[key] = intersection_point.coords[0]
                new_lines = []
                for line in lines:
                    new_line = []
                    for point in line:
                        name = point["name"]
                        if name in fake_point_mapper:
                            new_line.append(fake_point_mapper[name])
                        else:
                            new_line.append(point["coords"])
                    new_lines.append(new_line)
                lines = new_lines
                for name, node in nodes.items():
                    if name in fake_point_mapper:
                        node["value"] = fake_point_mapper[name]
                        fake_nodes.append(fake_point_mapper[name])
            else:
                new_lines = []
                for line in lines:
                    new_line = []
                    for point in line:
                        new_line.append(point["coords"])
                    new_lines.append(new_line)
                lines = new_lines

            return build_success(
                {
                    "lines": lines,
                    "nodes": list(nodes.values()),
                    "fake_nodes": fake_nodes,
                }
            )
        return build_failed(-1, str(serializer.errors))


class DataStreamDataCenterAPIView(APIView):
    def post(self, request):
        serializer = ChartDataStream360RequestSerializer(data=request.data)
        if serializer.is_valid():
            province_dict = serializer.save()
            province = province_dict["province"]
            data_center_mapper = {}
            data_center_names_setting = setting_service.get_setting(SettingName.DATA_STREAM_360_DATA_CENTER_NAMES.value)
            if data_center_names_setting:
                data_center_names = data_center_names_setting.value or []
            else:
                data_center_names = []
            for data_center_name in data_center_names:
                data_center_mapper[data_center_name] = 0
            for stream in data_stream_360_service.find_streams(province=province):
                stream: DataStream360Entity = stream
                if stream.dst_data_center == stream.src_data_center:
                    continue
                if stream.src_data_center in data_center_names:
                    data_center_mapper[stream.src_data_center] += stream.count
                if stream.dst_data_center in data_center_names:
                    data_center_mapper[stream.dst_data_center] += stream.count
            data_centers = []
            for k, v in data_center_mapper.items():
                data_centers.append(
                    {
                        "name": k,
                        "count": v,
                    }
                )
            return build_success({"data_centers": data_centers})
        return build_failed(-1, str(serializer.errors))


class DataStreamRealmAPIView(APIView):
    def post(self, request):
        serializer = DataCenterStreamRequestSerializer(data=request.data)
        if serializer.is_valid():
            data_center = serializer.save()
            streams_by_realm = defaultdict(lambda: defaultdict(int))
            for stream in data_stream_360_service.find_streams(data_center=data_center):
                stream: DataStream360Entity = stream
                if stream.src_realm and stream.dst_realm:
                    streams_by_realm[stream.src_realm][stream.dst_realm] += stream.count
            streams = []
            for src, dst_count in streams_by_realm.items():
                for dst, count in dst_count.items():
                    if src != dst:
                        streams.append({"src": src, "dst": dst, "count": count})
            return build_success(streams)
        return build_failed(-1, str(serializer.errors))


class GetBusinessByDataCenter(APIView):
    """
    根据business.data_centerses字段进行聚合查询，将包含"XX分公司"的值合并为一类
    """

    def get(self, request: Request):
        # 使用ElasticSearch聚合查询business.data_centerses字段
        aggregations = entity_service.search(
            size=0,
            aggs={
                "counts_by_data_center": {
                    "terms": {"field": "business.data_centers", "size": 30, "order": {"_count": "desc"}}
                }
            },
            table=entity_service.get_table(Category.BUSINESS),
        )

        result = []
        merged_branch_count = 0

        # 处理聚合结果，将包含"XX分公司"的合并为一类
        for bucket in aggregations.body["aggregations"]["counts_by_data_center"]["buckets"]:
            count = bucket["doc_count"]
            key = bucket["key"]

            # 检查是否包含"分公司"关键词
            if "分公司" in key:
                merged_branch_count += count
            else:
                result.append(
                    {
                        "asql": f"business.data_centers = '{key}'",
                        "count": count,
                        "isPercentage": False,
                        "name": key,
                        "proportionCount": 0,
                    }
                )

        # 如果存在"XX分公司"类型的数据，添加合并项
        if merged_branch_count > 0:
            result.append(
                {
                    "asql": "business.data_centers LIKE '%分公司%'",
                    "count": merged_branch_count,
                    "isPercentage": False,
                    "name": "所有分公司",
                    "proportionCount": 0,
                }
            )

        return build_success(result)


class InterceptAlarmDisplayAPIView(APIView):
    """告警拦截记录显示视图"""

    @staticmethod
    def _get_full_name(data_center, realm):
        names = []
        if data_center:
            names.append(data_center)
        if realm:
            names.append(realm)
        return "_".join(names)

    def _get_coordinates(self, data_center, realm, realm_mapper):
        full_name = self._get_full_name(data_center, realm)
        if full_name in realm_mapper:
            return realm_mapper[full_name]
        if data_center in realm_mapper:
            return realm_mapper[data_center]
        return None

    def post(self, request):
        serializer = InterceptAlarmDisplaySerializer(data=request.data)
        if serializer.is_valid():
            province = serializer.validated_data.get("province")

            realm_mapper = {}
            for realm in data_stream_360_realm_service.find():
                realm: DataStream360RealmEntity = realm
                realm_mapper[self._get_full_name(realm.data_center, realm.realm)] = [realm.longitude, realm.latitude]
                realm_mapper[realm.data_center] = [realm.longitude, realm.latitude]
            # 构建安全域映射
            lines = []
            nodes = {}
            point_names = {}
            province_mapper = {}
            for alarm in intercept_alarm_service.find_intercept_alarm_records(src_province=province):
                alarm: InterceptAlarmEntity = alarm
                src_full_name = self._get_full_name(alarm.src_data_center, alarm.src_realm)
                dst_full_name = self._get_full_name(alarm.dst_data_center, alarm.dst_realm)
                src_coordinates = self._get_coordinates(alarm.src_data_center, alarm.src_realm, realm_mapper)
                dst_coordinates = self._get_coordinates(alarm.dst_data_center, alarm.dst_realm, realm_mapper)
                if not src_full_name or not dst_full_name or not src_coordinates or not dst_coordinates:
                    continue
                if province:
                    if alarm.src_province != province:
                        point_names[self._get_full_name(alarm.src_data_center, alarm.src_realm)] = src_coordinates
                lines.append(
                    [
                        {"name": src_full_name, "coords": src_coordinates},
                        {"name": dst_full_name, "coords": dst_coordinates},
                    ]
                )
                nodes[src_full_name] = {"name": src_full_name, "value": src_coordinates}
                nodes[dst_full_name] = {"name": dst_full_name, "value": dst_coordinates}
                province_mapper[src_full_name] = alarm.src_province
                province_mapper[dst_full_name] = alarm.dst_province
            new_lines = []
            fake_nodes = []

            if province:
                fake_point_mapper = {}
                province_bbox = None
                centroid = None
                #   加载中国省份地利坐标
                with open(
                    os.path.join(
                        os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))),
                        "caasm_script",
                        "data",
                        "china.geojson",
                    )
                ) as fp:
                    china = json.load(fp)
                    for feature in china["features"]:
                        if province != feature["properties"]["name"]:
                            continue
                        province_geometry: MultiPolygon = shapely.from_geojson(json.dumps(feature))
                        if not province_geometry.is_valid:
                            province_geometry = province_geometry.buffer(0)
                            if not province_geometry.is_valid:
                                continue
                        bounds = list(province_geometry.bounds)
                        bounds[0] -= 0.5
                        bounds[1] -= 0.2
                        bounds[2] += 0.5
                        bounds[3] += 0.2
                        province_bbox = box(*bounds)
                        centroid: Point = province_bbox.centroid
                        province_bbox = province_bbox.boundary
                        break
                if province_bbox:
                    province_bbox: LineString = province_bbox
                    for key, value in nodes.items():
                        name = value["name"]
                        if name in province_mapper and province_mapper[name] == province:
                            continue
                        line = self._extend_line(centroid, Point(*value["value"]), 10)
                        intersection_point: LineString = province_bbox.intersection(line)
                        if not intersection_point.is_empty:
                            fake_point_mapper[key] = intersection_point.coords[0]
                new_lines = []
                for line in lines:
                    new_line = []
                    for point in line:
                        name = point["name"]
                        if name in fake_point_mapper:
                            new_line.append(fake_point_mapper[name])
                        else:
                            new_line.append(point["coords"])
                    new_lines.append(new_line)
                lines = new_lines
                for name, node in nodes.items():
                    if name in fake_point_mapper:
                        node["value"] = fake_point_mapper[name]
                        fake_nodes.append(fake_point_mapper[name])
            else:
                for line in lines:
                    new_line = []
                    for point in line:
                        new_line.append(point["coords"])
                    new_lines.append(new_line)
            lines = new_lines
            return build_success(
                {
                    "lines": lines,
                    "nodes": list(nodes.values()),
                    "fake_nodes": fake_nodes,
                }
            )
        return build_failed(-1, str(serializer.errors))

    @staticmethod
    def _extend_line(point1, point2, length):
        # 创建一个从 point1 到 point2 的线段
        line = LineString([point1, point2])

        # 计算线段的方向向量
        dx = point2.x - point1.x
        dy = point2.y - point1.y

        # 计算线段的长度
        line_length = line.length

        # 计算延长线的比例
        scale = (line_length + length) / line_length

        # 计算新的点的位置
        new_x = point1.x + dx * scale
        new_y = point1.y + dy * scale

        # 创建新的延长线
        extended_line = LineString([point1, point2, (new_x, new_y)])

        return extended_line
