from datetime import datetime
from enum import Enum
from typing import List, Optional, Dict, Any, Tuple
import time
import os
import json
import uuid
import xlrd
import csv
import tempfile
import zipfile
import tempfile
from io import StringIO, BytesIO
from openpyxl import load_workbook

from rest_framework.views import APIView
from rest_framework.request import Request
from rest_framework.parsers import MultiPartParser, FormParser
from django.core.files.uploadedfile import InMemoryUploadedFile
from django.http import StreamingHttpResponse
from django.utils.encoding import escape_uri_path
from openpyxl import Workbook
from openpyxl.styles import Alignment, Border, Side, PatternFill
from openpyxl.utils import get_column_letter

from caasm_service.runtime import vul_lifecycle_service, vul_file_service, order_service
from caasm_service.constants.order import OrderStatusEnum, OrderPriorityEnum, SupportImportVulnFile
from caasm_webapi.app.overview.serializers.vul import (
    VulLifecycleQuerySerializer,
    VulLifecycleSerializer,
    VulMonthlyStatSerializer,
    VulMonthlyStatsQuerySerializer,
    VulTypeStatSerializer,
    VulTypeStatsQuerySerializer,
    VulLevelStatSerializer,
    VulLevelStatsQuerySerializer,
    VulStatusSummarySerializer,
    VulStatusSummaryQuerySerializer,
    VulBusinessSummarySerializer,
    VulBusinessSummaryQuerySerializer,
    VulLifecycleBatchUpdateSerializer,
    VulNetworkLevelStatSerializer,
    VulNetworkLevelStatQuerySerializer,
)
from caasm_service.constants.vul_cycle import (
    VulnLevelEnum,
    VulnOriginEnum,
    VulnStatusEnum,
    VulnLevelMapper,
    VulnStatusMapper,
)
from caasm_webapi.app.overview.serializers.vul_import import VulFileImportSerializer
from caasm_service.entity.vul_lifecycle import VulLifecycleEntity
from caasm_webapi.util.response import build_success, ResponseCode, build_failed
from caasm_tool.constants import DATETIME_FORMAT
from caasm_tool.util import get_now


def timestamp_to_datetime(timestamp: Optional[int]) -> Optional[datetime]:
    """将时间戳转换为datetime对象

    Args:
        timestamp: 时间戳，单位为秒

    Returns:
        datetime对象或None
    """
    if timestamp is None:
        return None
    return datetime.fromtimestamp(timestamp)


class VulLifecycleListAPIView(APIView):
    _sort_fields = [("status", 1), ("level", 1), ("update_time", -1), ("_id", -1)]

    def post(self, request: Request):
        serializer = VulLifecycleQuerySerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)

        validated_data = serializer.validated_data
        # 处理分页
        page_index = validated_data.pop("page_index", 0)
        page_size = validated_data.pop("page_size", 20)
        # 处理时间戳参数
        create_time_start = validated_data.pop("create_time_start", None)
        create_time_end = validated_data.pop("create_time_end", None)

        # 将时间戳参数添加回查询条件
        if create_time_start is not None:
            validated_data["create_time_start"] = create_time_start
        if create_time_end is not None:
            validated_data["create_time_end"] = create_time_end

        count = vul_lifecycle_service.get_vul_lifecycle_count(**validated_data)
        if not count:
            return build_success({"total": count, "data": []})

        vul_entities: List[VulLifecycleEntity] = vul_lifecycle_service.find_vul_lifecycle(
            sort_fields=self._sort_fields,
            page_index=page_index,
            page_size=page_size,
            **validated_data,
        )

        result = []
        for vul in vul_entities:
            vul_data = {
                "id": vul.id,
                "name": vul.name,
                "level": VulnLevelMapper[vul.level] if vul.level is not None else None,
                "score": vul.score,
                "host": vul.host,
                "description": vul.description,
                "solution": vul.solution,
                "cve_id": vul.cve_id,
                "seen_time": vul.seen_time,
                "status": VulnStatusMapper[vul.status] if vul.status is not None else None,
                "cnnvd_id": vul.cnnvd_id,
                "cnvd_id": vul.cnvd_id,
                "order_ids": vul.order_ids,
                "business_name": vul.business_name,
                "port": vul.port,
                "protocal": vul.protocal,
                "update_time": vul.update_time,
                "create_time": vul.create_time,
                "file_ids": vul.file_ids,
            }
            result.append(vul_data)

        serializer = VulLifecycleSerializer(instance=result, many=True)
        return build_success({"total": count, "data": serializer.data})


class VulMonthlyStatsAPIView(APIView):
    """获取近N个月漏洞数量统计，默认为6个月"""

    def get(self, request: Request):
        # 解析请求参数
        serializer = VulMonthlyStatsQuerySerializer(data=request.query_params)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)

        # 获取月份参数
        months = serializer.validated_data.get("months", 6)
        create_time_start = serializer.validated_data.get("create_time_start")
        create_time_end = serializer.validated_data.get("create_time_end")

        # 获取漏洞统计数据
        stats = vul_lifecycle_service.get_monthly_vul_stats(
            months=months, create_time_start=create_time_start, create_time_end=create_time_end
        )
        result_serializer = VulMonthlyStatSerializer(instance=stats, many=True)

        return build_success(result_serializer.data)


class VulTypeStatsAPIView(APIView):
    """获取各个漏洞类型的聚合数量统计，并按照统计值从大到小排序"""

    def get(self, request: Request):
        # 解析请求参数
        serializer = VulTypeStatsQuerySerializer(data=request.query_params)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)

        create_time_start = serializer.validated_data.get("create_time_start")
        create_time_end = serializer.validated_data.get("create_time_end")

        # 获取漏洞类型统计数据
        stats = vul_lifecycle_service.get_vul_type_stats(
            create_time_start=create_time_start, create_time_end=create_time_end
        )
        if len(stats) > 10:
            stats = stats[0:10]
        result_serializer = VulTypeStatSerializer(instance=stats, many=True)

        return build_success(result_serializer.data)


class VulLevelStatsAPIView(APIView):
    """获取各个漏洞威胁等级的聚合数量统计，并按照统计值从大到小排序"""

    def get(self, request: Request):
        # 解析请求参数
        serializer = VulLevelStatsQuerySerializer(data=request.query_params)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)

        create_time_start = serializer.validated_data.get("create_time_start")
        create_time_end = serializer.validated_data.get("create_time_end")

        # 获取漏洞威胁等级统计数据
        stats = vul_lifecycle_service.get_vul_level_stats(
            create_time_start=create_time_start, create_time_end=create_time_end
        )
        result_serializer = VulLevelStatSerializer(instance=stats, many=True)

        return build_success(result_serializer.data)


class VulStatusSummaryAPIView(APIView):
    """获取漏洞状态汇总统计，包括总数、未修复总数、已修复总数和复现漏洞总数"""

    def get(self, request: Request):
        # 解析请求参数
        serializer = VulStatusSummaryQuerySerializer(data=request.query_params)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)

        create_time_start = serializer.validated_data.get("create_time_start")
        create_time_end = serializer.validated_data.get("create_time_end")

        # 获取漏洞状态汇总统计数据
        summary = vul_lifecycle_service.get_vul_status_summary(
            create_time_start=create_time_start, create_time_end=create_time_end
        )
        result_serializer = VulStatusSummarySerializer(instance=summary)

        return build_success(result_serializer.data)


class VulBusinessSummaryAPIView(APIView):
    """按照业务系统聚合，统计每个业务系统下的漏洞总数、未修复总数、已修复总数和复现总数"""

    def post(self, request: Request):
        # 解析请求参数
        serializer = VulBusinessSummaryQuerySerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)

        # 获取按业务系统聚合的漏洞统计数据
        summary = vul_lifecycle_service.get_vul_business_summary(**serializer.validated_data)
        result_serializer = VulBusinessSummarySerializer(instance=summary, many=True)

        return build_success(result_serializer.data)


class VulNetworkLevelStatAPIView(APIView):
    """统计已修复和未修复漏洞，按照内外网和威胁等级（高危/中危）进行分类"""

    def get(self, request: Request):
        # 解析请求参数
        serializer = VulNetworkLevelStatQuerySerializer(data=request.query_params)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)

        create_time_start = serializer.validated_data.get("create_time_start")
        create_time_end = serializer.validated_data.get("create_time_end")

        # 获取内外网漏洞威胁等级统计数据
        stats = vul_lifecycle_service.get_vul_network_level_stat(
            create_time_start=create_time_start, create_time_end=create_time_end
        )
        data = self.format_bar(data=stats)
        return build_success(data)

    def format_bar(self, data) -> dict:
        return {
            "categories": ["外网高危", "外网中危", "外网低危", "内网高危", "内网中危"],
            "series": [
                {
                    "name": "已修复",
                    "type": "bar",
                    "data": [
                        data["external_high_fixed"],
                        data["external_medium_fixed"],
                        data["external_low_fixed"],
                        data["internal_high_fixed"],
                        data["internal_medium_fixed"],
                    ],
                },
                {
                    "name": "未修复",
                    "type": "bar",
                    "data": [
                        data["external_high_unfixed"],
                        data["external_medium_unfixed"],
                        data["external_low_unfixed"],
                        data["internal_high_unfixed"],
                        data["internal_medium_unfixed"],
                    ],
                },
            ],
        }


class VulFileImportAPIView(APIView):
    _header_mapping = {
        "漏洞名称": "name",
        "漏洞等级": "level",
        "威胁分值": "score",
        "受影响主机": "host",
        "详细描述": "description",
        "修复建议": "solution",
        "CVE编号": "cve_id",
        "发现日期": "seen_time",
        "状态": "status",
        "CNNVD编号": "cnnvd_id",
        "CNVD编号": "cnvd_id",
    }

    # 远程漏洞sheet中的表头映射
    _remote_vul_header_mapping = {
        "漏洞名称": "name",
        "危险等级": "level",
        "CVSS评分": "score",
        "详细描述": "description",
        "解决办法": "solution",
        "CVE编号": "cve_id",
        "发现时间": "seen_time",
        "CNNVD编号": "cnnvd_id",
        "CNVD编号": "cnvd_id",
        "端口": "port",
        "协议": "protocal",
        "漏洞类型": "name",
        "风险等级": "level",
        "CVE编码": "cve_id",
        "CNNVD编码": "cnnvd_id",
        "CNVD编码": "cnvd_id",
        "漏洞描述": "description",
        "解决方案": "solution",
        "威胁评分": "score",
    }

    """导入漏洞文件并解析漏洞内容"""
    parser_classes = (MultiPartParser, FormParser)

    def post(self, request: Request):
        # 检查是否上传了文件
        if "file" not in request.FILES:
            return build_failed(ResponseCode.REQUEST_ERROR, message="未上传文件或文件大小超出限制")

        # 获取上传的文件
        file: InMemoryUploadedFile = request.FILES["file"]
        file_content = file.read()
        file_name, ext_name = os.path.splitext(file.name)

        # 验证请求参数
        serializer = VulFileImportSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer.errors)

        validated_data = serializer.validated_data
        order_ids = validated_data.get("order_ids", [])
        business_name = validated_data.get("business_name", None)
        name = validated_data.get("name", f"vul_import_{uuid.uuid4().hex[:8]}")
        internal = validated_data.get("internal", True)
        other_vuln_state = validated_data.get("other_vuln_state", False)

        # 检查工单是否存在
        for order_id in order_ids:
            order = order_service.get_order(order_id=order_id)
            if not order:
                order_entity = order_service.load_entity(order_id=order_id, business_system=business_name)
                order_service.save(order_entity)

        try:
            # 解析文件内容
            try:
                # 根据文件扩展名选择解析方法
                if ext_name.lower() == SupportImportVulnFile.XLSX.value:
                    # 解析Excel格式
                    try:
                        # 打开Excel文件
                        workbook = xlrd.open_workbook(file_contents=file_content)
                        # 解析Excel数据
                        vul_data = self._parse_excel_data(workbook)
                        # 处理漏洞数据
                        result = self._process_vul_data(
                            vul_data,
                            order_ids,
                            business_name,
                            internal,
                            other_vuln_state=other_vuln_state,
                            vul_origin=VulnOriginEnum.NORMAL,
                        )
                        return build_success(result)
                    except ImportError:
                        return build_failed(ResponseCode.INVALID_DATA, message="服务器未安装xlrd库，无法解析Excel文件")
                    except Exception as e:
                        return build_failed(ResponseCode.INVALID_DATA, message=f"解析Excel文件失败: {str(e)}")
                elif ext_name.lower() == SupportImportVulnFile.XLS.value:
                    # 解析XLS格式
                    try:
                        # 打开XLS文件
                        workbook = xlrd.open_workbook(file_contents=file_content, formatting_info=True)

                        # 检查是否是特殊格式的XLS文件（如**********.xls）
                        if self._is_special_xls_format(file_name):
                            # 从文件名中提取主机名
                            host = file_name
                            # 解析特殊格式的XLS数据
                            vul_data = self._parse_special_xls_data(workbook, host)
                        else:
                            # 使用标准Excel解析方法
                            vul_data = self._parse_excel_data(workbook)

                        # 处理漏洞数据
                        result = self._process_vul_data(
                            vul_data,
                            order_ids,
                            business_name,
                            internal,
                            other_vuln_state=other_vuln_state,
                            vul_origin=VulnOriginEnum.VULNSCAN,
                        )
                        return build_success(result)
                    except ImportError:
                        return build_failed(ResponseCode.INVALID_DATA, message="服务器未安装xlrd库，无法解析XLS文件")
                    except Exception as e:
                        return build_failed(ResponseCode.INVALID_DATA, message=f"解析XLS文件失败: {str(e)}")
                elif ext_name.lower() == SupportImportVulnFile.ZIP.value:
                    # 解析ZIP文件
                    try:
                        # 处理ZIP文件
                        result = self._process_zip_file(
                            file_content,
                            order_ids,
                            business_name,
                            internal,
                            other_vuln_state=other_vuln_state,
                            vul_origin=VulnOriginEnum.VULNSCAN,
                        )
                        return build_success(result)
                    except Exception as e:
                        return build_failed(ResponseCode.INVALID_DATA, message=f"解析ZIP文件失败: {str(e)}")
                else:
                    # 其他格式的文件处理
                    return build_failed(ResponseCode.REQUEST_ERROR, message="不支持的文件格式，目前仅支持JSON、XLSX、XLS和ZIP格式")
            except json.JSONDecodeError:
                return build_failed(ResponseCode.REQUEST_ERROR, message="JSON格式解析错误")
            except Exception as e:
                return build_failed(ResponseCode.INVALID_DATA, message=f"解析文件失败: {str(e)}")
        except Exception as e:
            return build_failed(ResponseCode.INVALID_DATA, message=f"保存文件失败: {str(e)}")

    def _is_special_xls_format(self, file_name):
        """检查是否是特殊格式的XLS文件（如**********.xls）

        Args:
            file_name: 文件名（不含扩展名）

        Returns:
            bool: 是否是特殊格式的XLS文件
        """
        # 检查文件名是否符合IP地址格式
        import re

        ip_pattern = re.compile(r"^(\d{1,3}\.){3}\d{1,3}$")
        return bool(ip_pattern.match(file_name))

    def _parse_special_xls_data(self, workbook, host):
        """解析特殊格式的XLS文件数据

        Args:
            workbook: xlrd工作簿对象
            host: 主机名（从文件名中提取）

        Returns:
            list: 解析后的漏洞数据列表
        """
        # 查找名为"远程漏洞"的工作表
        remote_vul_sheet = None
        for i in range(workbook.nsheets):
            sheet = workbook.sheet_by_index(i)
            if sheet.name == "远程漏洞":
                remote_vul_sheet = sheet
                break

        # 如果没有找到"远程漏洞"工作表，尝试使用第一个工作表
        if not remote_vul_sheet and workbook.nsheets > 0:
            remote_vul_sheet = workbook.sheet_by_index(0)

        # 如果没有找到任何工作表，返回空列表
        if not remote_vul_sheet:
            return []

        # 获取合并单元格信息
        merged_cells = remote_vul_sheet.merged_cells if hasattr(remote_vul_sheet, "merged_cells") else []

        # 获取表头（第一行）
        headers = []
        for col_idx in range(remote_vul_sheet.ncols):
            # 获取表头值，支持合并单元格
            header_value = self._get_xlrd_merged_cell_value(
                remote_vul_sheet, 1, col_idx, merged_cells, workbook.datemode
            )
            headers.append(header_value)

        # 解析数据行
        vul_data = []
        for row_idx in range(2, remote_vul_sheet.nrows):
            row_data = {"host": host}  # 添加主机名
            for col_idx in range(remote_vul_sheet.ncols):
                if col_idx >= len(headers):
                    continue

                header = headers[col_idx]
                # 获取单元格值，支持合并单元格
                value = self._get_xlrd_merged_cell_value(
                    remote_vul_sheet, row_idx, col_idx, merged_cells, workbook.datemode
                )

                # 将Excel表头映射到漏洞字段
                field_name = self._remote_vul_header_mapping.get(header)
                if field_name and value is not None:
                    row_data[field_name] = value

            # 处理漏洞等级映射
            if "level" in row_data:
                level_value = row_data["level"]
                # 将文本等级映射到系统定义的等级
                if isinstance(level_value, str):
                    if "高" in level_value:
                        row_data["level"] = VulnLevelEnum.HIGH.value
                    elif "中" in level_value:
                        row_data["level"] = VulnLevelEnum.MEDIUM.value
                    elif "低" in level_value:
                        row_data["level"] = VulnLevelEnum.LOW.value
                    else:
                        row_data["level"] = VulnLevelEnum.LOW.value

            # 设置默认状态为未修复
            if "status" not in row_data:
                row_data["status"] = VulnStatusEnum.UNFIXED.value

            # 只添加非空行
            if len(row_data) > 1:  # 至少包含主机名和其他一个字段
                vul_data.append(row_data)

        return vul_data

    def _parse_excel_data(self, workbook):
        """解析Excel文件数据

        Args:
            workbook: xlrd工作簿对象

        Returns:
            list: 解析后的漏洞数据列表
        """
        # 获取第一个工作表
        sheet = workbook.sheet_by_index(0)

        # 获取合并单元格信息
        merged_cells = sheet.merged_cells if hasattr(sheet, "merged_cells") else []

        # 获取表头（第一行）
        headers = []
        for col_idx in range(sheet.ncols):
            # 获取表头值，支持合并单元格
            header_value = self._get_xlrd_merged_cell_value(sheet, 0, col_idx, merged_cells, workbook.datemode)
            headers.append(header_value)

        # 解析数据行
        vul_data = []
        for row_idx in range(1, sheet.nrows):  # 从第二行开始，跳过表头
            row_data = {}
            for col_idx in range(sheet.ncols):
                header = headers[col_idx]
                # 获取单元格值，支持合并单元格
                value = self._get_xlrd_merged_cell_value(sheet, row_idx, col_idx, merged_cells, workbook.datemode)

                # 将Excel表头映射到漏洞字段
                if not self._header_mapping.get(header):
                    continue
                field_name = self._map_header_to_field(header)
                if field_name and value is not None:
                    row_data[field_name] = value

            # 只添加非空行
            if row_data:
                vul_data.append(row_data)

        return vul_data

    def _get_xlrd_merged_cell_value(self, sheet, row_idx, col_idx, merged_cells, datemode):
        """获取xlrd工作表中单元格的值，支持合并单元格

        Args:
            sheet: xlrd工作表对象
            row_idx: 行索引 (0-based)
            col_idx: 列索引 (0-based)
            merged_cells: 合并单元格信息列表
            datemode: 工作簿的日期模式

        Returns:
            单元格的值
        """
        # 检查单元格是否在合并区域内
        for crange in merged_cells:
            rlo, rhi, clo, chi = crange  # 合并单元格范围：起始行，结束行，起始列，结束列
            if row_idx >= rlo and row_idx < rhi and col_idx >= clo and col_idx < chi:
                # 如果在合并区域内，返回左上角单元格的值
                value = sheet.cell_value(rlo, clo)
                # 处理日期类型
                if sheet.cell_type(rlo, clo) == xlrd.XL_CELL_DATE:
                    try:
                        date_tuple = xlrd.xldate_as_tuple(value, datemode)
                        value = datetime(*date_tuple).strftime("%Y-%m-%d")
                    except Exception:
                        # 如果日期转换失败，保留原始值
                        pass
                return value

        # 如果不在任何合并区域内，直接返回单元格的值
        value = sheet.cell_value(row_idx, col_idx)
        # 处理日期类型
        if sheet.cell_type(row_idx, col_idx) == xlrd.XL_CELL_DATE:
            try:
                date_tuple = xlrd.xldate_as_tuple(value, datemode)
                value = datetime(*date_tuple).strftime("%Y-%m-%d")
            except Exception:
                # 如果日期转换失败，保留原始值
                pass
        return value

    def _map_header_to_field(self, header):
        """将Excel表头映射到漏洞字段

        Args:
            header: Excel表头名称

        Returns:
            str: 对应的漏洞字段名，如果没有对应关系则返回None
        """
        # 返回映射后的字段名，如果没有对应关系则返回原表头
        return self._header_mapping.get(header, header)

    def _parse_xlsx_with_merged_cells(self, workbook):
        """解析xlsx文件数据，处理合并单元格

        Args:
            workbook: openpyxl工作簿对象

        Returns:
            list: 解析后的漏洞数据列表
        """
        # 获取第一个工作表
        sheet = workbook.active

        # 获取合并单元格信息
        merged_cells = sheet.merged_cells.ranges

        # 获取表头（第一行）
        headers = []
        for col_idx in range(1, sheet.max_column + 1):
            cell_value = self._get_merged_cell_value(sheet, 1, col_idx)
            if cell_value:
                headers.append(cell_value)
            else:
                headers.append(f"Column_{col_idx}")  # 为空表头提供默认值

        # 解析数据行
        vul_data = []
        for row_idx in range(2, sheet.max_row + 1):  # 从第二行开始，跳过表头
            row_data = {}
            for col_idx in range(1, sheet.max_column + 1):
                if col_idx > len(headers):
                    continue

                header = headers[col_idx - 1]
                value = self._get_merged_cell_value(sheet, row_idx, col_idx)

                # 将Excel表头映射到漏洞字段
                if not self._header_mapping.get(header):
                    continue
                field_name = self._map_header_to_field(header)
                if field_name and value is not None:
                    row_data[field_name] = value

            # 只添加非空行
            if row_data:
                vul_data.append(row_data)

        return vul_data

    def _get_merged_cell_value(self, sheet, row, column):
        """获取合并单元格的值

        Args:
            sheet: openpyxl 工作表对象
            row: 行号 (1-based)
            column: 列号 (1-based)

        Returns:
            单元格的值
        """
        # 先检查指定的单元格是否在合并区域内
        for merged_range in sheet.merged_cells.ranges:
            if row in range(merged_range.min_row, merged_range.max_row + 1) and column in range(
                merged_range.min_col, merged_range.max_col + 1
            ):
                # 如果在合并区域内，返回合并区域左上角单元格的值
                return sheet.cell(merged_range.min_row, merged_range.min_col).value

        # 如果不在任何合并区域内，直接返回单元格的值
        return sheet.cell(row, column).value

    def _process_zip_file(
        self,
        file_content,
        order_ids,
        business_name,
        internal=False,
        other_vuln_state=False,
        vul_origin=VulnOriginEnum.VULNSCAN,
    ):
        """处理ZIP文件，提取并解析其中的XLS文件

        Args:
            file_content: ZIP文件内容
            order_ids: 工单ID列表
            business_name: 业务系统名称

        Returns:
            dict: 处理结果统计
        """
        # 创建一个临时目录用于解压文件
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建一个临时文件来保存ZIP内容
            zip_path = os.path.join(temp_dir, "temp.zip")
            with open(zip_path, "wb") as f:
                f.write(file_content)

            # 打开ZIP文件
            with zipfile.ZipFile(zip_path, "r") as zip_file:
                # 获取所有XLS和XLSX文件
                excel_files = [f for f in zip_file.namelist() if f.lower().endswith((".xls", ".xlsx"))]

                if not excel_files:
                    return {
                        "total": 0,
                        "success": 0,
                        "failed": 0,
                        "vul_ids": [],
                        "message": "ZIP文件中没有找到Excel文件",
                    }

                # 用于存储所有Excel文件解析出的漏洞数据
                all_vul_data = []

                # 处理每个Excel文件
                for excel_file in excel_files:
                    try:
                        # 提取文件名（不含路径和扩展名）和扩展名
                        file_name, ext_name = os.path.splitext(os.path.basename(excel_file))
                        ext_name = ext_name.lower()

                        # 读取Excel文件内容
                        excel_content = zip_file.read(excel_file)

                        # 根据文件扩展名选择解析方法
                        if ext_name == SupportImportVulnFile.XLS.value:
                            # 处理XLSX文件
                            # 打开XLS文件
                            workbook = xlrd.open_workbook(file_contents=excel_content, formatting_info=True)

                            # 检查是否是特殊格式的XLS文件（如**********.xls）
                            if self._is_special_xls_format(file_name):
                                # 从文件名中提取主机名
                                host = file_name
                                # 解析特殊格式的XLS数据
                                vul_data = self._parse_special_xls_data(workbook, host)
                            else:
                                # 使用标准Excel解析方法
                                vul_data = self._parse_excel_data(workbook)
                            # 将解析出的漏洞数据添加到总列表中
                            all_vul_data.extend(vul_data)
                    except Exception as e:
                        print(f"解析ZIP文件中的Excel文件 {excel_file} 失败: {str(e)}")

                # 处理所有漏洞数据
                result = self._process_vul_data(
                    all_vul_data,
                    order_ids,
                    business_name,
                    internal,
                    other_vuln_state=other_vuln_state,
                    vul_origin=vul_origin,
                )
                result["processed_files"] = len(excel_files)

                return result

    def _process_vul_data(
        self,
        vul_data,
        order_ids,
        business_system,
        internal,
        other_vuln_state: bool = False,
        vul_origin: VulnOriginEnum = None,
    ):
        """处理漏洞数据"""
        # 根据实际数据格式进行处理
        # 这里假设vul_data是一个包含多个漏洞信息的列表
        if not isinstance(vul_data, list):
            vul_data = [vul_data]

        success_count = 0
        failed_count = 0
        vul_ids = []

        for item in vul_data:
            try:
                # 构建漏洞实体数据
                vul_entity_data = {
                    "name": item.get("name", ""),
                    "level": item.get("level", VulnLevelEnum.LOW.value),
                    "score": float(item.get("score", 0)) if item.get("score") else 0,
                    "host": item.get("host", ""),
                    "description": item.get("description", ""),
                    "solution": item.get("solution", ""),
                    "cve_id": item.get("cve_id", None),
                    "seen_time": item.get("seen_time", datetime.now().strftime(DATETIME_FORMAT)),
                    "status": item.get("status", VulnStatusEnum.UNFIXED.value),
                    "cnnvd_id": item.get("cnnvd_id", None),
                    "cnvd_id": item.get("cnvd_id", None),
                    "order_ids": order_ids,
                    "business_name": business_system,
                    "protocal": item.get("protocal", None) if item.get("protocal", None) else None,
                    "internal": internal,
                    "origin": vul_origin.value,
                }
                port = item.get("port", None)
                if port:
                    try:
                        port = int(port)
                    except Exception:
                        port = None
                else:
                    port = None
                vul_entity_data["port"] = port

                # 检查是否已存在相同的漏洞记录
                vulns: List[VulLifecycleEntity] = vul_lifecycle_service.find_vul_lifecycle(
                    name=vul_entity_data["name"],
                    host=vul_entity_data["host"],
                    business_name=vul_entity_data["business_name"],
                    port=vul_entity_data["port"],
                    protocal=vul_entity_data["protocal"],
                )
                vulns = list(vulns)
                if len(vulns) > 0:
                    # 如果存在相同的漏洞记录，更新其状态和工单ID
                    vuln = vulns[0]
                    if vuln.status == VulnStatusEnum.FIXED:
                        orig_order_ids: list = vuln.order_ids
                        orig_order_ids.extend(order_ids)
                        orig_order_ids = list(set(orig_order_ids))
                        vul_lifecycle_service.update_by_id(
                            id=vuln.id,
                            values={"status": VulnStatusEnum.REPRODUCED.value, "order_ids": orig_order_ids},
                        )
                        vul_ids.append(vuln.id)
                        success_count += 1
                        continue

                # 创建漏洞实体
                vul_entity = vul_lifecycle_service.load_entity(**vul_entity_data)

                # 保存漏洞实体
                saved_entity = vul_lifecycle_service.save(vul_entity)
                if saved_entity and saved_entity.flag:
                    vul_ids.append(saved_entity.inserted_id)
                    success_count += 1
                else:
                    failed_count += 1
            except Exception as e:
                failed_count += 1
                print(f"处理漏洞数据失败: {str(e)}")

        # 更新同一个工单的其他漏洞状态
        if other_vuln_state:
            other_vulns_res = vul_lifecycle_service.update_vuln_multi(
                condition=vul_lifecycle_service.build_vul_lifecycle_condition(
                    not_in_ids=vul_ids,
                    business_name=business_system,
                    order_id=order_ids,
                    origin=vul_origin.value,
                ),
                status=VulnStatusEnum.FIXED.value,
            )

        return {"total": len(vul_data), "success": success_count, "failed": failed_count, "vul_ids": vul_ids}


class VulLifecycleBatchUpdateAPIView(APIView):
    """批量更新漏洞状态的API视图"""

    def post(self, request: Request):
        # 解析请求参数
        serializer = VulLifecycleBatchUpdateSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer.errors)

        validated_data = serializer.validated_data
        ids = validated_data.get("ids", [])
        status = validated_data.get("status", None)
        file_ids = validated_data.get("file_ids", None)

        if not ids:
            return build_failed(ResponseCode.REQUEST_ERROR, message="漏洞ID列表不能为空")

        # 批量更新漏洞状态
        try:

            result = vul_lifecycle_service.update_vuln_multi(
                condition=vul_lifecycle_service.build_vul_lifecycle_condition(ids=ids),
                status=status,
                file_ids=file_ids,
            )
            if not result.flag:
                raise Exception("更新漏洞失败")
        except Exception as e:
            return build_failed(ResponseCode.REQUEST_ERROR, message=f"更新漏洞失败: {str(e)}")
        return build_success()


class VulLifecycleExportAPIView(APIView):
    """漏洞生命周期数据导出接口

    基于VulLifecycleListAPIView的查询参数，导出漏洞数据为Excel文件
    """

    def post(self, request: Request):
        """处理POST请求，导出漏洞数据"""
        serializer = VulLifecycleQuerySerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)

        validated_data = serializer.validated_data
        # 处理时间戳参数
        create_time_start = validated_data.pop("create_time_start", None)
        create_time_end = validated_data.pop("create_time_end", None)

        # 将时间戳参数添加回查询条件
        if create_time_start is not None:
            validated_data["create_time_start"] = create_time_start
        if create_time_end is not None:
            validated_data["create_time_end"] = create_time_end

        # 查询所有符合条件的漏洞数据（不分页）
        vul_entities: List[VulLifecycleEntity] = vul_lifecycle_service.find_vul_lifecycle(**validated_data)
        vul_entities = list(vul_entities)
        # 生成Excel文件
        return self._generate_excel_response(vul_entities)

    def _generate_excel_response(self, vul_entities: List[VulLifecycleEntity]):
        """生成Excel文件并返回下载响应

        Args:
            vul_entities: 漏洞实体列表

        Returns:
            StreamingHttpResponse: 文件下载响应
        """
        # 创建工作簿和工作表
        wb = Workbook()
        ws = wb.active
        ws.title = "漏洞数据"

        # 定义表头
        headers = [
            "漏洞ID",
            "漏洞名称",
            "威胁等级",
            "威胁分值",
            "受影响主机",
            "详细描述",
            "修复建议",
            "CVE编号",
            "发现日期",
            "状态",
            "CNNVD编号",
            "CNVD编号",
            "工单ID",
            "业务系统",
        ]

        # 写入表头
        for col_idx, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col_idx)
            cell.value = header
            # 设置表头样式
            cell.alignment = Alignment(horizontal="center", vertical="center")
            cell.font = cell.font.copy(bold=True)
            cell.fill = PatternFill(start_color="DDEBF7", end_color="DDEBF7", fill_type="solid")
            # 添加边框
            cell.border = Border(
                left=Side(style="thin"), right=Side(style="thin"), top=Side(style="thin"), bottom=Side(style="thin")
            )

        # 写入数据
        for row_idx, vul in enumerate(vul_entities, 2):
            # 将工单ID列表转换为逗号分隔的字符串
            order_ids_str = ", ".join(vul.order_ids) if vul.order_ids else ""

            # 准备行数据
            row_data = [
                vul.id,
                vul.name,
                vul.level,
                vul.score,
                vul.host,
                vul.description,
                vul.solution,
                vul.cve_id,
                vul.seen_time,
                vul.status,
                vul.cnnvd_id,
                vul.cnvd_id,
                order_ids_str,
                vul.business_name,
            ]

            # 写入行数据
            for col_idx, value in enumerate(row_data, 1):
                cell = ws.cell(row=row_idx, column=col_idx)
                if isinstance(value, VulnStatusEnum):
                    value = VulnStatusMapper[value]
                elif isinstance(value, VulnLevelEnum):
                    value = VulnLevelMapper[value]
                elif isinstance(value, Enum):
                    value = value.value
                cell.value = str(value)
                # 添加边框
                cell.border = Border(
                    left=Side(style="thin"), right=Side(style="thin"), top=Side(style="thin"), bottom=Side(style="thin")
                )

        # 调整列宽
        for col_idx, _ in enumerate(headers, 1):
            column_letter = get_column_letter(col_idx)
            # 设置列宽为自动适应内容
            ws.column_dimensions[column_letter].width = 15

        # 冻结首行
        ws.freeze_panes = "A2"

        with tempfile.NamedTemporaryFile(delete=False, suffix=".xlsx") as tmp:
            wb.save(tmp.name)
            tmp.flush()

            # 读取临时文件内容
            tmp.seek(0)
            file_content = tmp.read()

        # 创建响应
        response = StreamingHttpResponse(
            content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        )

        # 设置文件名
        now = datetime.now()
        file_name = f"漏洞数据导出_{now.strftime('%Y%m%d_%H%M%S')}.xlsx"
        response["Content-Disposition"] = f'attachment; filename="{escape_uri_path(file_name)}"'

        # 写入文件内容
        response.streaming_content = [file_content]

        return response
