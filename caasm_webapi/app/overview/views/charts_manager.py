from rest_framework.views import APIView
from caasm_service.runtime import overview_chart_service, user_service, overview_chart_category_service
from caasm_webapi.app.overview.serializers.charts_manager import (
    ChartInfoRequestSerializer,
    ChartInfoResponseSerializer,
    ModifyChartInfoRequestSerializer,
    DeleteChartInfoRequestSerializer,
    CopyChartInfoRequestSerializer,
)
from caasm_webapi.util.response import build_failed, build_success, ResponseCode


class ChartInfoListAPIView(APIView):
    """
    用户获取具体分类的图表信息
    """

    def find_all_children_category_ids(self, parent_id=None, data=None, cache=None):
        if cache is None:
            cache = []
        result = []
        for item in data:
            if item.get("category_id") in cache:
                continue
            if item.get("parent_id") == parent_id:
                cache.append(item.get("category_id"))
                result.append(item.get("category_id"))
                result.extend(self.find_all_children_category_ids(item.get("category_id"), data, cache))

        return result

    def get(self, request):
        serializer = ChartInfoRequestSerializer(data=request.query_params)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)
        validate_data = serializer.validated_data
        keyword = validate_data.get("keyword")
        page_index = validate_data.get("page_index")
        page_size = validate_data.get("page_size")
        sort_fields = validate_data.get("sort_fields")
        category_tree_id = validate_data.get("category_id")
        temp_tree_data = overview_chart_category_service.find_chart_category()
        temp_data = [
            {"category_id": str(item.id), "category_name": item.category_name, "parent_id": item.parent_id}
            for item in temp_tree_data
        ]
        category_ids = self.find_all_children_category_ids(parent_id=category_tree_id, data=temp_data)
        category_ids.append(category_tree_id)

        count = overview_chart_service.get_chart_info_count(category_tree_ids=category_ids, keyword=keyword)
        if not count:
            return build_success({"count": 0, "data": []})

        data = list(
            overview_chart_service.find_chart_info(
                category_tree_ids=category_ids,
                page_index=page_index - 1,
                page_size=page_size,
                keyword=keyword,
                sort_fields=sort_fields,
            )
        )
        creator_ids = set([item.creator_id for item in data])
        creator_infos = user_service.find_user(user_ids=list(creator_ids))
        creator_map = {item.id: item.username for item in creator_infos}
        result = []
        for item in data:
            chart_type = item.chart_type.value
            temp_dict = {
                "chart_id": str(item.id),
                "category_tree_id": item.category_tree_id,
                "chart_name": item.chart_name,
                "creator": creator_map.get(item.creator_id, "未知"),
                "description": item.description,
                "create_time": item.create_time,
                "update_time": item.update_time,
                "internal": item.internal,
                "chart_type": chart_type,
                "category": item.category,
                "base_query": item.base_query or "",
                "front_end_setting": item.front_end_setting,
            }
            result.append(temp_dict)
        return build_success({"count": count, "data": ChartInfoResponseSerializer(instance=result, many=True).data})


class ModifyChartInfoAPIView(APIView):
    """
    修改图表信息,这里指的简要信息 例如图表的名称以及描述信息
    """

    def post(self, request):
        serializer = ModifyChartInfoRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)
        validated_data = serializer.validated_data
        chart_id = validated_data.pop("chart_id")
        overview_chart_service.update_chart_info_by_id(chart_id=chart_id, value=validated_data)
        return build_success()


class DeleteChartInfoAPIView(APIView):
    """
    删除图表信息
    """

    def post(self, request):
        serializer = DeleteChartInfoRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)
        validated_data = serializer.validated_data
        chart_ids = validated_data.get("chart_ids")
        ## todo 这里需要注意的是 如果图表被仪表盘引用，能不能删除，是否要解除关系等等
        overview_chart_service.delete_chart_info(chart_ids=chart_ids)
        return build_success()


class CopyChartInfoAPIView(APIView):
    """
    用于拷贝图表信息
    """

    def post(self, request):
        serializer = CopyChartInfoRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)
        validated_data = serializer.validated_data
        chart_id = validated_data.get("chart_id")

        chart_info = overview_chart_service.get_chart_info(chart_id=chart_id)
        copy_chart_info = {
            "chart_name": validated_data.get("chart_name") or f"{chart_info.chart_name}_copy",
            "description": validated_data.get("description") or chart_info.description,
            "creator_id": request._user.user_id,
            "category_tree_id": chart_info.category_tree_id,
            "chart_type": chart_info.chart_type,
            "metric_type": chart_info.metric_type,
            "category": chart_info.category,
            "f_category": chart_info.f_category,
            "s_category": chart_info.s_category,
            "base_query": chart_info.base_query,
            "count_info": vars(chart_info.count_info) if chart_info.count_info else None,
            "group_fields": [vars(item) for item in chart_info.group_fields] if chart_info.group_fields else None,
            "asql": vars(chart_info.asql) if chart_info.asql else None,
            "time": chart_info.time if chart_info.time else None,
            "table": vars(chart_info.table) if chart_info.table else None,
            "front_end_setting": chart_info.front_end_setting,
        }

        overview_chart_service.save(entity=overview_chart_service.load_entity(**copy_chart_info))
        return build_success()
