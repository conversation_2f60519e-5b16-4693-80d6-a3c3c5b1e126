from datetime import datetime
import re
from collections import defaultdict
from rest_framework.views import APIView
from rest_framework.request import Request

from caasm_service.runtime import order_service
from caasm_webapi.app.overview.serializers.order import (
    OrderOperationLogSyncSerializer,
    OrderOperationLogQuerySerializer,
    OrderOperationLogSerializer,
    OrderBatchQuerySerializer,
    OrderDetailSerializer,
)
from caasm_webapi.util.response import build_success, ResponseCode, build_failed
from caasm_service.entity.order import OrderEntity


class OrderOperationLogSyncAPIView(APIView):
    """工单操作日志同步API视图"""

    def post(self, request: Request):
        """同步工单操作日志"""
        # 解析请求参数
        serializer = OrderOperationLogSyncSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer.errors)

        validated_data = serializer.validated_data
        order_id = validated_data.get("order_id")
        logs = validated_data.get("logs")

        # 检查工单是否存在
        order = order_service.get_order(order_id=order_id)
        if not order:
            return build_failed(ResponseCode.REQUEST_ERROR, message=f"工单 {order_id} 不存在")

        # 同步操作日志
        result = order_service.sync_operation_logs(order_id=order_id, logs=logs)
        if not result:
            return build_failed(ResponseCode.INVALID_DATA, message="同步操作日志失败")

        return build_success({"order_id": order_id, "success": True})


class OrderBatchQueryAPIView(APIView):
    """工单批量查询API视图"""

    def post(self, request: Request):
        """批量查询工单信息"""
        # 解析请求参数
        serializer = OrderBatchQuerySerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer.errors)

        validated_data = serializer.validated_data
        order_ids = validated_data.get("order_ids")

        # 查询工单信息
        order_details = []
        for order_id in order_ids:
            # 获取工单基本信息
            order = order_service.get_order(order_id=order_id)
            if not order:
                continue

            # 获取工单操作日志
            logs = order_service.get_operation_logs(order_id=order_id)

            # 构建工单详情
            order_detail = self._build_order_detail(order, logs)
            order_details.append(order_detail)

        # 返回结果
        return build_success(order_details)

    def _build_order_detail(self, order: OrderEntity, logs):
        """构建工单详情"""
        # 构建基本信息
        order_detail = {
            "id": order.order_id,
            "title": order.title or f"工单{order.order_id}",
            "data": {
                "systemName": order.business_system or "",
                "userCode": order.creator_id or "",
                "userName": order.creator or "",
                "senderName": "",
                "email": getattr(order, "creator_email", "") or "",
                "phone": getattr(order, "creator_phone", "") or getattr(order, "creator_mobile", "") or "",
                "createTime": self._format_datetime(order.create_time) if order.create_time else "",
                "closeTime": self._format_datetime(order.finish_time) if order.finish_time else "",
                "expectedTime": "",
            },
            "timeline": self._build_timeline(logs),
        }
        return order_detail

    def _build_timeline(self, logs):
        """构建时间轴"""
        if not logs:
            return []

        # 按日期分组
        date_groups = defaultdict(list)
        for log in logs:
            # 解析操作时间
            operation_time = log.get("operation_time", "")
            if not operation_time:
                continue

            try:
                # 尝试解析日期和时间
                dt = datetime.strptime(operation_time, "%Y-%m-%d %H:%M:%S")
                date_str = dt.strftime("%Y-%m-%d")
                time_str = dt.strftime("%H:%M:%S")
            except ValueError:
                # 如果解析失败，尝试使用正则表达式提取
                date_match = re.search(r"(\d{4}-\d{2}-\d{2})", operation_time)
                time_match = re.search(r"(\d{2}:\d{2}:\d{2})", operation_time)

                if date_match and time_match:
                    date_str = date_match.group(1)
                    time_str = time_match.group(1)
                else:
                    # 如果仍然无法解析，则跳过
                    continue

            # 构建时间轴项目
            timeline_item = {
                "time": time_str,
                "person": log.get("operator", ""),
                "status": log.get("details", {}).get("action_code", ""),
                "operation": log.get("details", {}).get("inc_processing", ""),
            }

            date_groups[date_str].append(timeline_item)

        # 构建时间轴
        timeline = []
        for date_str, items in sorted(date_groups.items(), reverse=True):
            # 按时间排序
            sorted_items = sorted(items, key=lambda x: x["time"], reverse=True)

            timeline.append({"date": date_str, "items": sorted_items})

        return timeline

    def _format_datetime(self, dt):
        """格式化日期时间"""
        if not dt:
            return ""

        try:
            if isinstance(dt, str):
                dt = datetime.strptime(dt, "%Y-%m-%d %H:%M:%S")

            return dt.strftime("%Y年%m月%d日 %H:%M:%S")
        except (ValueError, TypeError):
            return str(dt)
