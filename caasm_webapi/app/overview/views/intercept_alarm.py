from rest_framework.views import APIView

from caasm_service.entity.intercept_alarm import InterceptAlarmEntity
from caasm_service.runtime import intercept_alarm_service
from caasm_webapi.app.alarm_manage.serializers.alarm import InterceptAlarmListSerializer
from caasm_webapi.util.response import build_failed, ResponseCode, build_success


class InterceptAlarmListAPIView(APIView):
    """拦截告警列表API视图"""

    _sort_fields = [("-update_time", -1)]

    def get(self, request):
        """获取拦截告警列表"""
        serializer = InterceptAlarmListSerializer(data=request.query_params)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)

        validated_data = serializer.validated_data

        # 获取查询参数
        src_province = validated_data.get("src_province", None)
        dst_province = validated_data.get("dst_province", None)
        src_ip = validated_data.get("src_ip", None)
        dst_ip = validated_data.get("dst_ip", None)
        keyword = validated_data.get("keyword", None)
        page_index = validated_data.get("page_index", 0)
        page_size = validated_data.get("page_size", 20)

        # 获取总数
        total = intercept_alarm_service.get_intercept_alarm_count(
            src_province=src_province,
            dst_province=dst_province,
            src_ip=src_ip,
            dst_ip=dst_ip,
            keyword=keyword,
        )

        # 获取列表数据
        records = intercept_alarm_service.find_intercept_alarm_records(
            src_province=src_province,
            dst_province=dst_province,
            src_ip=src_ip,
            dst_ip=dst_ip,
            keyword=keyword,
            page_index=page_index,
            page_size=page_size,
            sort_fields=self._sort_fields,
        )

        # 转换为列表
        result = []
        for record in records:
            record: InterceptAlarmEntity = record
            result.append(
                {
                    "id": str(record.id),
                    "src_ip": record.src_ip,
                    "src_group_relationship_name": record.src_group_relationship_name,
                    "dst_ip": record.dst_ip,
                    "dst_port": record.dst_port,
                    "dst_group_relationship_name": record.dst_group_relationship_name,
                    "count": record.count,
                    "reason": record.reason,
                    "src_data_center": record.src_data_center,
                    "src_realm": record.src_realm,
                    "dst_data_center": record.dst_data_center,
                    "dst_realm": record.dst_realm,
                    "create_time": record.create_time,
                    "update_time": record.update_time,
                    "src_province": record.src_province,
                    "dst_province": record.dst_province,
                }
            )

        return build_success({"data": result, "total": total})
