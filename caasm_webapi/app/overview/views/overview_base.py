from rest_framework.views import APIView

from caasm_service.constants.overview import OverviewCategoryEnum
from caasm_webapi.util.response import build_failed, ResponseCode


class OverviewBaseAPIView(APIView):
    @classmethod
    def check_category(cls, category):
        """
        检查分类信息
        """
        try:
            OverviewCategoryEnum(category)
        except Exception as e:
            return build_failed(ResponseCode.REQUEST_ERROR, message="请求URL错误")
