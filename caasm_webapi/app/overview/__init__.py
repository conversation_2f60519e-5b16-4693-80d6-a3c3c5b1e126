from caasm_tool.patch.router import DefaultRouter
from caasm_webapi.app.overview.views.chart import (
    ChartConfigAPIView,
    ChartConfigHandleAPIView,
    AqlCollectListAPIView,
    InterceptAlarmDisplayAPIView,
    SaverChartDetailAPIView,
    SpaceChartSaverAPIView,
    ChartQueryAPIView,
    UpdateChartDetailAPIView,
    GetRelativeTimeView,
    ChartConfigsExport,
    ChartConfigsImport,
    GetBusinessCountChart,
    AsqlChart,
    CountAndDateLineChart,
    StatisticsCountChartView,
    VulDateHisttoryChart,
    VulHistoryTimeLineChart,
    VulHistoryTableChart,
    GetDataStreamAPIView,
    DataStreamAPIView,
    DataStreamDisplayAPIView,
    DataStreamDataCenterAPIView,
    DataStreamRealmAPIView,
    GetBusinessByRealm,
    GetBusinessByDataCenter,
)
from caasm_webapi.app.overview.views.chart_category import (
    AddChartCategoryAPIView,
    ChartCategoryAPIView,
    ModifyChartCategoryAPIView,
    DeleteChartCategoryAPIView,
)
from caasm_webapi.app.overview.views.charts_manager import (
    ChartInfoListAPIView,
    ModifyChartInfoAPIView,
    DeleteChartInfoAPIView,
    CopyChartInfoAPIView,
)
from caasm_webapi.app.overview.views.icon import IconListAPIView, ImageIconAPIView, IconAddAPIView

from caasm_webapi.app.overview.views.spaces import (
    SpaceAPIView,
    AddSpaceAPIView,
    ModifySpaceAPIView,
    DeleteSpaceAPIView,
    CopySpaceAPIView,
    ReleaseSpacesListAPIView,
    SaveSequencedSpacesAPIView,
    DashBoardRolesAPIView,
)
from caasm_webapi.app.overview.views.vul import (
    VulLifecycleListAPIView,
    VulMonthlyStatsAPIView,
    VulTypeStatsAPIView,
    VulLevelStatsAPIView,
    VulStatusSummaryAPIView,
    VulBusinessSummaryAPIView,
    VulFileImportAPIView,
    VulLifecycleBatchUpdateAPIView,
    VulLifecycleExportAPIView,
    VulNetworkLevelStatAPIView,
)
from caasm_webapi.app.overview.views.order import (
    OrderOperationLogSyncAPIView,
    OrderBatchQueryAPIView,
)
from caasm_webapi.app.overview.views.intercept_alarm import InterceptAlarmListAPIView


overview_route = DefaultRouter("overview/")

## 大屏 和 仪表盘 共用
overview_route.join_path("<overview_category>/spaces/", SpaceAPIView, name="空间管理空间列表")
overview_route.join_path("<overview_category>/addSpaces/", AddSpaceAPIView, name="空间管理新增空间")
overview_route.join_path("<overview_category>/modifySpaces/", ModifySpaceAPIView, name="modify_spaces")
overview_route.join_path("<overview_category>/deleteSpaces/", DeleteSpaceAPIView, name="delete_space")
overview_route.join_path("<overview_category>/copySpaces/", CopySpaceAPIView, name="copy_space")
overview_route.join_path("<overview_category>/releaseSpaces/", ReleaseSpacesListAPIView, name="仪表盘顺序")
overview_route.join_path("<overview_category>/saverSequencedSpaces/", SaveSequencedSpacesAPIView, name="保存空间顺序")
overview_route.join_path("<overview_category>/spaceChart/saver/", SpaceChartSaverAPIView, name="保存空间图表")
# 图表
overview_route.join_url("chartCategory/add/", AddChartCategoryAPIView, name="添加图表分类")
overview_route.join_url("chartCategory/list/", ChartCategoryAPIView, name="展示分类节点信息")
overview_route.join_url("chartCategory/modify/", ModifyChartCategoryAPIView, name="修改分类节点信息")
overview_route.join_url("chartCategory/delete/", DeleteChartCategoryAPIView, name="删除分类节点信息")
overview_route.join_url("chartManager/list", ChartInfoListAPIView, name="图表信息列表")
overview_route.join_url("chartManager/modify/", ModifyChartInfoAPIView, name="修改图表信息")
overview_route.join_url("chartManager/delete/", DeleteChartInfoAPIView, name="删除图表信息")
overview_route.join_url("chartManager/copy/", CopyChartInfoAPIView, name="拷贝图表数据")
overview_route.join_url("chartConfig/list/", ChartConfigAPIView, name="图表配置详情")
overview_route.join_path("chartInfo/<category>/detail/", ChartConfigHandleAPIView, name="图表es计算后返回数据")
overview_route.join_path("chartInfo/<category>/saver/", SaverChartDetailAPIView, name="图表保存")
overview_route.join_path("chartInfo/<category>/update/", UpdateChartDetailAPIView, name="图表更新")
overview_route.join_url("chartInfo/chart/", ChartQueryAPIView, name="图表信息")
overview_route.join_url("chartConfig/aqlCollect/", AqlCollectListAPIView, name="收藏列表")
overview_route.join_url("iconManage/list/", IconListAPIView, name="图表管理")
overview_route.join_path("icon/<id>", ImageIconAPIView, name="图标详情")
overview_route.join_url("icon/add", IconAddAPIView, name="新增图标")
overview_route.join_url("relativeTime/", GetRelativeTimeView, name="获取相对时间")
# overview_route.join_url("getDataStream/", GetDataStreamAPIView, name="GetDataStreamAPIView")
overview_route.join_url("getDataStream360/", DataStreamAPIView, name="DataStreamAPIView")
overview_route.join_url("getDataStream360Display/", DataStreamDisplayAPIView, name="DataStreamDisplayAPIView")
overview_route.join_url("getDataStream360DataCenter/", DataStreamDataCenterAPIView, name="DataCenterStreamAPIView")
overview_route.join_url("getDataStream360Realm/", DataStreamRealmAPIView, name="DataStreamRealmAPIView")
overview_route.join_url("roleChoose/", DashBoardRolesAPIView, name="role")
overview_route.join_url("chartExport/", ChartConfigsExport, name="export")
overview_route.join_url("chartImport/", ChartConfigsImport, name="import")
overview_route.join_url("businessCountChart/", GetBusinessCountChart, name="business")
overview_route.join_url("businessCountByRealmChart/", GetBusinessByRealm, name="GetBusinessByRealm")
overview_route.join_url("businessCountByDataCenterChart/", GetBusinessByDataCenter, name="GetBusinessByDataCenter")
overview_route.join_path("<overview_category>/asql/chart/", AsqlChart, name="asql")
overview_route.join_path("<overview_category>/countTimeline/chart/", CountAndDateLineChart, name="countTimeline")
overview_route.join_path("<overview_category>/statisticsCount/chart/", StatisticsCountChartView, name="statistics")
overview_route.join_url("vulDateHistory/chart/", VulDateHisttoryChart, name="vulDateHistory")
overview_route.join_url("vulHistoryTimeLine/chart/", VulHistoryTimeLineChart, name="VulHistoryTimeLineChart")
overview_route.join_url("vulHistoryTable/chart/", VulHistoryTableChart, name="VulHistoryTableChart")
overview_route.join_url("interceptAlarmDisplay/", InterceptAlarmDisplayAPIView, name="告警拦截数据展示")
# 漏洞大屏# 拦截告警
overview_route.join_path("intercept_alarm/list/", InterceptAlarmListAPIView, name="InterceptAlarmListAPIView")

overview_route.join_url("vuln/lifecycle/list/", VulLifecycleListAPIView, name="漏洞生命周期")
overview_route.join_url("vuln/lifecycle/export/", VulLifecycleExportAPIView, name="导出漏洞数据")
overview_route.join_url("vuln/monthly/stats/", VulMonthlyStatsAPIView, name="漏洞月度统计")
overview_route.join_url("vuln/type/stats/", VulTypeStatsAPIView, name="漏洞类型统计")
overview_route.join_url("vuln/level/stats/", VulLevelStatsAPIView, name="漏洞威胁等级统计")
overview_route.join_url("vuln/status/summary/", VulStatusSummaryAPIView, name="漏洞状态汇总统计")
overview_route.join_url("vuln/business/summary/", VulBusinessSummaryAPIView, name="按业务系统聚合的漏洞统计")
overview_route.join_url("vuln/network/level/stats/", VulNetworkLevelStatAPIView, name="内外网漏洞威胁等级统计")
overview_route.join_url("vuln/file/import/", VulFileImportAPIView, name="导入漏洞文件并解析")
overview_route.join_url("vuln/lifecycle/batch_update/", VulLifecycleBatchUpdateAPIView, name="批量更新漏洞状态")
# 工单操作日志
overview_route.join_url("order/logs/sync/", OrderOperationLogSyncAPIView, name="同步工单操作日志")
overview_route.join_url("order/batch/query/", OrderBatchQueryAPIView, name="批量查询工单信息")
default_app_config = "caasm_webapi.app.overview.appconf.CaasmDashboardConfig"
