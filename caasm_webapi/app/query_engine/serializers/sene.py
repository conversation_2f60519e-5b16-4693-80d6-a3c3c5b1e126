from rest_framework import fields

from caasm_tool.patch.serializer import SerializerMixin


class AqlSceneTreeQuerySerializer(SerializerMixin):
    sort_fields = fields.ListField(child=fields.CharField(), default=["-create_time"])
    keyword = fields.CharField(max_length=4097, default="")


class AqlSceneListQuerySerializer(SerializerMixin):
    main_scene = fields.CharField(max_length=4097, default="")
    sub_scene = fields.CharField(max_length=4097, default="")
    page_size = fields.IntegerField(max_value=100, min_value=1, default=20, allow_null=True, help_text="分页条数")
    page_index = fields.IntegerField(min_value=1, default=1, allow_null=True, help_text="分页索引，从1开始")
    keyword = fields.Char<PERSON>ield(max_length=4097, default="")
    sort_fields = fields.ListField(child=fields.Char<PERSON><PERSON>(max_length=128), default=["-enabled", "priority"])
