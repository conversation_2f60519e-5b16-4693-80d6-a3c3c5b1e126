from rest_framework import fields
from rest_framework import serializers
from rest_framework.serializers import Serializer

from caasm_aql.base import AsqlType
from caasm_service.entity.meta_model import MetaFieldType
from caasm_webapi.app.query_engine.requests.asql import (
    ComplexFieldRequest,
    ComplexFieldQueryRequest,
    FieldOperationRequest,
    WizardToBatchRequest,
)
from caasm_webapi.common.serializers import ResponseSerializer
from caasm_webapi.util.serializer import SnapshotDateSerializer


class AsqlToWizardSerializer(Serializer):
    asql = fields.CharField(required=True, max_length=1024, help_text="ASQL语句")

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        return validated_data["asql"]


class AsqlPlusWizardSerializer(Serializer):
    asql = fields.CharField(required=True, max_length=4096, help_text="ASQL+语句")

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        return validated_data["asql"]


class WizardToAsqlSerializer(Serializer):
    wizard = fields.JSONField(required=True, help_text="向导结构化数据")

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        return validated_data["wizard"]


class WizardToBatchSerializer(Serializer):
    mode = fields.ChoiceField(choices=["field_mode", "match_mode"], required=True)
    option = fields.ChoiceField(choices=["full_match", "match"], default=None, allow_null=True)
    field_names = fields.ListField(child=fields.CharField(), default=list)
    value = fields.CharField(max_length=4096, required=True)

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        req = WizardToBatchRequest()
        req.mode = validated_data["mode"]
        req.option = validated_data.get("option")
        req.value = validated_data["value"]
        req.field_names = validated_data.get("field_names")
        return req


class BatchFilterResponseSerializer(Serializer):
    name = fields.CharField()
    modes = fields.ListField(child=fields.JSONField())


class FieldRequestSerializer(SnapshotDateSerializer):
    entity_type = fields.CharField(required=False, allow_null=True, help_text="实体类型", default="all")


class ChoiceSerializer(Serializer):
    key = fields.CharField(help_text="可选值的键")
    value = fields.CharField(help_text="可选值的显示文字")

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        pass


class FieldDetailSerializer(Serializer):
    label = fields.CharField()
    value = fields.CharField()

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        pass


class FieldInfoSerializer(Serializer):
    full_name = fields.CharField(help_text="字段完整名称")
    display_name = fields.CharField(help_text="字段显示名称")
    data_type = fields.CharField(help_text="字段数据类型")
    is_complex = fields.BooleanField(help_text="字段是否为复杂表格字段")
    choices = fields.ListField(child=ChoiceSerializer(), required=False, default=list, help_text="可选值")
    details = fields.ListField(child=FieldDetailSerializer(), default=list, help_text="字段细节")

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        pass


class ClassifiedFieldSerializer(Serializer):
    often_used_fields = fields.ListField(
        child=FieldInfoSerializer(help_text="字段"),
    )
    fields = fields.ListField(child=FieldInfoSerializer(required=True, help_text="字段"), help_text="全字段列表")

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        pass


class ClassifiedFieldResponseSerializer(ResponseSerializer):
    data = ClassifiedFieldSerializer(help_text="字段信息")


class FieldOperationRequestSerializer(Serializer):
    # data_type = fields.CharField(required=False, allow_null=False, help_text="类型名称")
    field_name = fields.CharField(required=False, allow_null=False, help_text="字段名称")

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        req = FieldOperationRequest()
        # req.data_type = validated_data.get("data_type")
        req.field_name = validated_data.get("field_name")
        return req

    def validate(self, attrs):
        if "data_type" in attrs:
            try:
                MetaFieldType(attrs.get("data_type"))
            except Exception as e:
                raise serializers.ValidationError("该类型不存在")
        return attrs


class FieldOperatorSerializer(Serializer):
    name = fields.CharField(required=True, allow_null=False, help_text="操作符")
    display_name = fields.CharField(required=True, allow_null=False, help_text="显示名称")
    description = fields.CharField(required=True, allow_null=False, help_text="显示名称")
    type = fields.CharField(required=False)
    auto_complete = fields.BooleanField(required=False, default=True, help_text="是否支持自动完成")

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        pass


class FieldMethodParamSerializer(Serializer):
    name = fields.CharField(required=True, allow_null=False, help_text="参数名")
    display_name = fields.CharField(required=True, allow_null=False, help_text="参数显示名")
    description = fields.CharField(help_text="参数说明")
    data_type = fields.CharField(help_text="参数数据类型")
    required = fields.BooleanField(help_text="参数是否必要")
    default = fields.JSONField(help_text="参数默认值", default=None)

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        pass


class FieldMethodSerializer(Serializer):
    name = fields.CharField(required=True, allow_null=False, help_text="函数名")
    display_name = fields.CharField(required=True, allow_null=False, help_text="函数显示名称")
    params = fields.ListField(child=FieldMethodParamSerializer(), help_text="参数列表", default=list())
    description = fields.CharField(required=True, allow_null=False, help_text="显示名称")
    type = fields.CharField(required=False)
    auto_complete = fields.BooleanField(required=False, default=True, help_text="是否支持自动完成")

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        pass


class FieldOperationSerializer(Serializer):
    operators = fields.ListField(
        child=FieldOperatorSerializer(required=True, allow_null=False, help_text="允许的操作符"),
        help_text="允许的操作符集合",
    )
    methods = fields.ListField(
        child=FieldMethodSerializer(required=True, allow_null=False, help_text="允许的函数"),
        help_text="允许的函数集合",
    )

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        pass


class FieldOperationResponseSerializer(ResponseSerializer):
    data = FieldOperationSerializer(required=True, help_text="结果")


class ComplexFieldRequestSerializer(Serializer):
    field = fields.CharField(required=True, allow_null=False, help_text="复杂字段名称")
    entity_type = fields.CharField(required=False, allow_null=False, help_text="资产类型", default="all")

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        request = ComplexFieldRequest()
        request.field = validated_data["field"]
        request.entity_type = validated_data["entity_type"]
        return request


class ComplexFieldResponseSerializer(ResponseSerializer):
    fields = fields.ListField(child=fields.JSONField(), help_text="复杂字段下的字段列表")


class AdapterSerializer(Serializer):
    name = fields.CharField(help_text="适配器名称")
    display_name = fields.CharField(help_text="适配器显示名称")

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        pass


class AdaptersSerializer(Serializer):
    adapters = fields.ListField(child=AdapterSerializer(), help_text="适配器列表")

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        pass


class AdapterResponseSerializer(ResponseSerializer):
    data = AdapterSerializer(help_text="适配器列表", many=True)


class ComplexFieldQuerySerializer(Serializer):
    asset_id = fields.CharField(required=True, help_text="要查询的资产ID")
    field = fields.CharField(required=True, help_text="要查询的复杂字段")
    keyword = fields.CharField(required=False, help_text="查询关键字")

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        request = ComplexFieldQueryRequest()
        request.asset_id = validated_data["asset_id"]
        request.field = validated_data["field"]
        request.keyword = validated_data.get("keyword")
        return request


class QuickSearchParamSerializer(Serializer):
    name = fields.CharField()
    description = fields.CharField()
    display_name = fields.CharField()
    type = fields.CharField()
    required = fields.BooleanField(default=False)
    default = fields.JSONField(default=None)
    validate_rules = fields.ListField(default=list)

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        pass


class QuickSearchSerializer(Serializer):
    aql = fields.CharField(source="asql")
    category = fields.CharField()
    scene_name = fields.CharField(source="display_name")
    params_type = fields.CharField(source="params_type.value")
    inner = fields.BooleanField()
    aql_type = fields.CharField(source="asql_type.value")
    is_support = fields.BooleanField(default=True)
    params = fields.ListField(child=QuickSearchParamSerializer())

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        pass


class FilterSerializer(Serializer):
    type = fields.ChoiceField(
        choices=[AsqlType.ASQL.value, AsqlType.FULLTEXT.value, AsqlType.FIELD.value, AsqlType.BATCH.value],
        required=True,
    )
    asql = fields.CharField(max_length=10240, required=True)

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        return {"type": validated_data["type"], "asql": validated_data["asql"]}


class FiltersSerializer(Serializer):
    filters = fields.ListField(child=FilterSerializer(required=True), required=True)

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        return validated_data["filters"]
