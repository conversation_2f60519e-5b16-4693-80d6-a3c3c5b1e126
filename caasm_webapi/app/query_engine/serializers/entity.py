from uuid import uuid4

from rest_framework import fields
from rest_framework.serializers import Serializer

from caasm_aql.aql import (
    AqlSorting,
    AqlFieldSorting,
    AsqlOption,
    AqlAddition,
    ChildRetrieve,
    AqlRelatedEntityOption,
)
from caasm_aql.base import AsqlType
from caasm_render.loaders.constants import CardType
from caasm_tool.patch.serializer import SerializerMixin
from caasm_webapi.app.query_engine.requests.entity import (
    EntitiesRequest,
    ExportRequest,
    AqlRequest,
    EntityRequest,
    EntityDetailRequest,
    EntityAdapterDetailRequest,
    EntityCategoryDetailRequest,
    UserDefaultEntityFieldsRequest,
    EntityFieldsRequest,
)
from caasm_webapi.app.query_engine.serializers.asql import FilterSerializer
from caasm_webapi.common.serializers import ResponseSerializer
from caasm_webapi.util.serializer import SnapshotDateSerializer

CARD_TYPE_MAPPER = {
    CardType.BASIC: "基础信息",
    CardType.VUL: "漏洞信息",
    CardType.ADAPTER: "适配器",
    CardType.FABRIC: "融合信息",
    CardType.GRAPH: "图谱举证",
    CardType.SCENES: "场景举证",
    CardType.ASSET_PORTRAIT: "资产画像",
    CardType.ASSET_VUL_INSTANCE: "漏洞数据",
    CardType.VUL_INSTANCE_UNIQUE: "漏洞信息",
    CardType.ENTITIES: "关联实体",
    CardType.ENTITY_GRAPH: "实体关系图",
    CardType.CHANGE: "变化记录",
}


class AqlAqlFieldSortingSerializer(Serializer):
    field_name = fields.CharField(max_length=256, help_text="要排序的字段名称")
    sorting = fields.ChoiceField(choices=[sorting.value for sorting in AqlSorting], help_text="字段升降序")

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data) -> AqlFieldSorting:
        sorting = AqlFieldSorting()
        sorting.field_name = validated_data["field_name"]
        sorting.sorting = AqlSorting(validated_data["sorting"])
        return sorting


class EntitiesRequestSerializer(Serializer):
    asset_ids = fields.ListField(
        child=fields.CharField(required=True, help_text="资产ID"),
        required=False,
        help_text="资产ID列表",
    )
    entity_ids = fields.ListField(
        child=fields.CharField(required=True, help_text="资产ID"),
        required=False,
        help_text="实体ID列表",
    )

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        request: EntitiesRequest = EntitiesRequest()
        request.entity_ids = validated_data.get("asset_ids") or validated_data.get("entity_ids")
        return request


class AqlOptionBaseSerializer(Serializer):
    field_list = fields.ListField(
        child=fields.CharField(max_length=256),
        allow_null=True,
        default=None,
        help_text="要显示的字段名称列表",
    )
    field_sorting_list = fields.ListField(
        child=AqlAqlFieldSortingSerializer(),
        default=None,
        allow_null=True,
        help_text="字段排序列表",
    )
    entity_type = fields.CharField(required=False, allow_null=True, help_text="资产模型")

    page_size = fields.IntegerField(max_value=100, min_value=1, default=20, allow_null=True, help_text="分页条数")
    page_index = fields.IntegerField(min_value=1, default=1, allow_null=True, help_text="分页索引，从1开始")

    @classmethod
    def _create(cls, validated_data):
        field_list = validated_data.get("field_list")
        option = AsqlOption()
        option.page_size = validated_data.get("page_size", 20)
        option.page_index = validated_data.get("page_index", 1)
        option.field_list = field_list
        option.entity_type = validated_data.get("entity_type")
        field_sorting_list = validated_data.get("field_sorting_list")

        if field_sorting_list:
            _sort_serializer = AqlAqlFieldSortingSerializer(data=field_sorting_list, many=True)
            if _sort_serializer.is_valid():
                option.field_sorting_list = _sort_serializer.save()

        return option

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        pass


class AqlOptionSerializer(AqlOptionBaseSerializer):
    _default_fields = ["base.adapter_count", "base.entity_id", "base.adapters"]

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data) -> AsqlOption:
        option = super()._create(validated_data)
        if option.field_list:
            option.field_list.extend(self._default_fields)
        else:
            option.field_list = self._default_fields.copy()
        return option


class ExportOptionSerializer(AqlOptionBaseSerializer):
    limit = fields.IntegerField(required=False, allow_null=True, help_text="导出上限数量")

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data) -> AsqlOption:
        return super(ExportOptionSerializer, self)._create(validated_data)


class ExportRequestSerializer(SnapshotDateSerializer):
    aql = fields.CharField(
        max_length=4096, required=False, default="", allow_null=True, help_text="查询语句", allow_blank=True
    )
    filters = fields.ListField(child=FilterSerializer(required=True), required=False, default=list)
    option = ExportOptionSerializer(required=False, allow_null=True, help_text="导出选项")
    childs_retrieve_info = fields.ListField(required=False, default=list, help_text="关键字检索")

    def create(self, validated_data):
        #   todo: 后面统一改asql
        asql: str = validated_data["aql"]
        filters = validated_data["filters"]
        serializer = ExportOptionSerializer(data=validated_data.get("option", {}))
        serializer.is_valid()
        option: AsqlOption = serializer.save()
        childs_retrieve = []
        for child_retrieve in validated_data.get("childs_retrieve_info", []):
            ser = ChildRetrieveInfoSerializer(data=child_retrieve)
            if ser.is_valid():
                child = ser.save()
                childs_retrieve.append(child)

        return ExportRequest(asql, filters, option, validated_data["date"], childs_retrieve_info=childs_retrieve)


class ExportTaskRequestSerializer(ExportRequestSerializer):
    name = fields.CharField(required=True, max_length=128)

    def create(self, validated_data):
        req = super().create(validated_data)
        req.name = validated_data["name"]
        return req


class AqlAdditionSerializer(Serializer):
    field = fields.CharField(help_text="额外字段名称")
    value = fields.JSONField(help_text="额外值")

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        addition = AqlAddition()
        addition.field = validated_data["field"]
        addition.value = validated_data["value"]
        return addition


class ChildRetrieveInfoSerializer(Serializer):
    """
    二级目录检索
    """

    field = fields.CharField(help_text="字段名称", required=False, default="", allow_null=True)
    keyword = fields.CharField(help_text="关键字", required=False, default="", allow_null=True)
    aql = fields.CharField(max_length=4096, required=False, default="", allow_null=True, help_text="查询日期")

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        child = ChildRetrieve()
        child.field = validated_data["field"]
        child.aql = validated_data["aql"]
        child.keyword = validated_data["keyword"]
        return child


class AqlRelatedEntityOptionSerializer(Serializer):
    parent_category = fields.CharField(required=True, help_text="关联实体分类")
    entity_id = fields.CharField(required=True, help_text="关联实体ID")
    field = fields.CharField(required=True, help_text="关联实体字段")
    field_names = fields.ListField(
        required=False, child=fields.CharField(help_text="字段名称"), help_text="默认显示字段", default=list
    )

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        option = AqlRelatedEntityOption()
        option.parent_category = validated_data["parent_category"]
        option.entity_id = validated_data["entity_id"]
        option.field = validated_data["field"]
        option.field_names = validated_data.get("field_names")
        return option


class AqlRequestSerializer(SnapshotDateSerializer):
    aql = fields.CharField(max_length=4096, required=False, default="", allow_null=True, help_text="查询日期")
    filters = fields.ListField(child=FilterSerializer(required=True), required=False, default=list)
    aql_type = fields.ChoiceField(
        choices=[type_.value for type_ in AsqlType], default=AsqlType.ASQL, help_text="AQL语句类型"
    )
    option = AqlOptionSerializer(required=False, allow_null=True, help_text="视图选项")
    additions = fields.ListField(
        child=AqlAdditionSerializer(),
        required=False,
        allow_null=True,
        default=list,
        help_text="额外条件",
    )
    childs_retrieve_info = fields.ListField(required=False, default=[])
    related_entity_option = AqlRelatedEntityOptionSerializer(required=False)

    def create(self, validated_data) -> AqlRequest:
        aql: str = validated_data["aql"]
        filters = validated_data["filters"]
        serializer = AqlOptionSerializer(data=validated_data.get("option", {}))
        serializer.is_valid()
        option: AsqlOption = serializer.save()
        additions = list()
        childs_retrieve = list()
        for addition_dict in validated_data.get("additions", []):
            ser = AqlAdditionSerializer(data=addition_dict)
            if ser.is_valid():
                addition = ser.save()
                additions.append(addition)
        for child_retrieve in validated_data.get("childs_retrieve_info", []):
            ser = ChildRetrieveInfoSerializer(data=child_retrieve)
            if ser.is_valid():
                child = ser.save()
                childs_retrieve.append(child)
        related_entity_option = None
        if "related_entity_option" in validated_data:
            related_serializer = AqlRelatedEntityOptionSerializer(data=validated_data["related_entity_option"])
            if related_serializer.is_valid():
                related_entity_option = related_serializer.save()
        return AqlRequest(
            aql,
            filters,
            option,
            validated_data["date"],
            additions,
            childs_retrieve,
            AsqlType(validated_data["aql_type"]),
            related_entity_option=related_entity_option,
        )


class MetaFieldSerializerBase(Serializer):
    description = fields.CharField(read_only=True, help_text="字段描述")
    data_type = fields.SerializerMethodField(read_only=True, help_text="数据类型")

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        pass

    def get_data_type(self, meta_field):
        return meta_field.setting.data_type.value


class MetaFieldSerializer(MetaFieldSerializerBase):
    name = fields.CharField(read_only=True, help_text="字段名称")
    display_name = fields.CharField(read_only=True, help_text="字段显示名称")


class MetaFieldFullSerializer(Serializer):
    name = fields.CharField(source="full_name", read_only=True, help_text="字段完全名称")
    # display_name = fields.SerializerMethodField(read_only=True, help_text='字段完全显示名称')
    display_name = fields.CharField(help_text="字段完全显示名称")
    description = fields.CharField(read_only=True, default="", help_text="字段描述")
    data_type = fields.CharField(read_only=True, help_text="数据类型")

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        pass

    # def get_display_name(self, meta_field: MetaField):
    #     return meta_field.full_display_name if meta_field.full_display_name else meta_field.display_name


class EntitiesResultSerializer(Serializer):
    entities = fields.ListField(source="rows", child=fields.JSONField(), help_text="Aql查询实体结果列表")
    count = fields.IntegerField(help_text="查询到的实体总数")

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        pass


class EntitiesResponseSerializer(ResponseSerializer):
    data = EntitiesResultSerializer(help_text="查询结果")


class EntitiesFieldsResultSerializer(Serializer):
    default_fields = fields.ListField(child=fields.CharField(required=True, allow_null=False), help_text="默认显示字段列表")
    pk_field = fields.CharField(help_text="视图主键字段")
    charts = fields.ListField(
        child=fields.CharField(),
        required=False,
        allow_null=True,
        help_text="图表ID列表，用于从图表接口请求内容",
    )
    fields = fields.ListField(child=fields.DictField(), help_text="当前资产库字段简单列表，供查询视图筛选字段使用")

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        pass


class EntitiesFieldsResponseSerializer(ResponseSerializer):
    data = EntitiesFieldsResultSerializer()


class EntitiesDetailItemSerializer(Serializer):
    name = fields.CharField(required=True, help_text="数据项名称")
    display_name = fields.CharField(required=True, help_text="数据项显示名称")
    value = fields.JSONField(required=True, help_text="数据项值")

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        pass


class EntitiesDetailGroupSerializer(Serializer):
    display_name = fields.CharField(required=True, help_text="数据分组显示名称")
    items = fields.ListField(child=EntitiesDetailItemSerializer(help_text="数据项"), help_text="数据分组包含的数据项")

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        pass


class EntityRequestSerializer(SnapshotDateSerializer):
    entity_id = fields.CharField(required=True, allow_null=False, help_text="实体ID")

    def create(self, validated_data):
        request = EntityRequest()
        request.entity_id = validated_data.get("entity_id")
        request.date = validated_data.get("date")
        return request


class EntityDetailRequestSerializer(EntityRequestSerializer):
    render_content = fields.BooleanField(required=False, default=True, help_text="是否直接渲染所有内容")

    def create(self, validated_data):
        request = EntityDetailRequest()
        request.entity_id = validated_data.get("entity_id")
        request.render_content = validated_data.get("render_content", True)
        request.date = validated_data.get("date")
        return request


class EntityCategoryDetailRequestSerializer(EntityRequestSerializer):
    category = fields.CharField(required=False, help_text="请求渲染的子分类")

    def create(self, validated_data):
        request = EntityCategoryDetailRequest()
        request.entity_id = validated_data["entity_id"]
        request.category = validated_data.get("category")
        request.date = validated_data.get("date")
        return request


class EntityAdapterDetailRequestSerializer(EntityRequestSerializer):
    adapter_name = fields.CharField(required=False, help_text="请求渲染的适配器数据")

    def create(self, validated_data):
        request = EntityAdapterDetailRequest()
        request.entity_id = validated_data["entity_id"]
        request.adapter_name = validated_data.get("adapter_name", "")
        return request


class EntityDetailSerializer(Serializer):
    groups = fields.ListField(child=EntitiesDetailGroupSerializer(help_text="数据分组"), help_text="详情包含的数据分组")

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        pass


class AdapterDetailSerializer(Serializer):
    display_name = fields.CharField(required=True, help_text="适配器名称")
    groups = fields.ListField(child=EntitiesDetailGroupSerializer(help_text="数据分组"), help_text="适配器详情包含的数据分组")

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        pass


class EntityDetailResultSerializer(Serializer):
    asset = EntityDetailSerializer(required=True, help_text="资产详情数据")
    adapters = fields.ListField(child=AdapterDetailSerializer(help_text="适配器资产数据"), help_text="适配器资产数据")

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        pass


class EntityDetailResponseSerializer(ResponseSerializer):
    data = EntityDetailResultSerializer(required=True, help_text="资产详情数据")


class EntitySupportCardRequestSerializer(SnapshotDateSerializer):
    entityId = fields.CharField(required=True, min_length=1, max_length=40)


class EntityCardDetailRequestSerializer(SnapshotDateSerializer):
    entity_id = fields.CharField(required=True, min_length=1, max_length=40)
    card_type = fields.ChoiceField(required=True, choices=CARD_TYPE_MAPPER)
    card_name = fields.CharField(required=False)
    setting = fields.DictField(required=False, default=dict, allow_null=True)
    asql = fields.CharField(required=False, default=None, allow_null=True)


class AqlFieldValueGroupRequestSerializer(SerializerMixin):
    aql = fields.CharField(required=False)
    field_name = fields.CharField(required=True)
    keyword = fields.CharField(required=False)
    max_fetch = fields.IntegerField(min_value=1, max_value=100, required=False, default=20)
    additions = fields.ListField(child=AqlAdditionSerializer(), required=False, allow_null=True, default=list)


class EntitySupportCardSerializer(SerializerMixin):
    name = fields.SerializerMethodField("get_name")
    card_name = fields.SerializerMethodField("get_card_name")
    display_name = fields.SerializerMethodField("get_display_name")
    config = fields.JSONField(required=False, default=dict)

    def get_name(self, obj):
        if isinstance(obj, dict):
            return obj.get("card_type")
        else:
            return obj.card_type

    def get_card_name(self, obj):
        if isinstance(obj, dict):
            return obj.get("card_name") or obj.get("card_type")
        else:
            return obj.card_name or obj.card_type

    def get_display_name(self, obj):
        if isinstance(obj, dict):
            return obj.get("display_name") or CARD_TYPE_MAPPER.get(obj.get("card_type"))
        else:
            return obj.display_name or CARD_TYPE_MAPPER.get(obj.card_type)


class EntityAdapterMenuResponseSerializer(SerializerMixin):
    name = fields.CharField()
    display_name = fields.CharField()


class UpdateUserDefaultEntityFieldsRequestSerializer(Serializer):
    field_names = fields.ListField(child=fields.CharField(required=True), required=True, help_text="默认字段名称列表")

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        req = UserDefaultEntityFieldsRequest()
        req.field_names = validated_data["field_names"]
        return req


class EntityFieldsRequestSerializer(Serializer):
    category = fields.CharField(required=False, help_text="请求的子实体类型")

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        req = EntityFieldsRequest()
        req.category = validated_data["category"]
        return req


class EntityFieldsSerializer(Serializer):
    label = fields.CharField(required=True, help_text="字段列表名称", source="name")
    value = fields.UUIDField(default=uuid4)
    field_names = fields.ListField(child=fields.CharField(required=True), required=True, help_text="默认字段名称列表")

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        pass


class JoinExportFieldsSerializer(Serializer):
    aql = fields.CharField(required=False, allow_null=True, default="", allow_blank=True)
    filters = fields.ListField(child=FilterSerializer(required=True), required=False, default=list)
    aql_type = fields.CharField(required=False, default="asql")
    asset = fields.ListField(child=fields.CharField(required=True), required=False, help_text="资产需要导出的列表")
    vul = fields.ListField(child=fields.CharField(required=True), required=False, help_text="漏洞情报需要导出的列表")
    vul_instance_unique = fields.ListField(
        child=fields.CharField(required=True), required=True, help_text="漏洞实例需要导出的列表"
    )


class EntityTypeSerializer(Serializer):
    entity_type = fields.CharField(required=True)

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        return validated_data["entity_type"]
