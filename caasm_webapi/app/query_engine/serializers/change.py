from rest_framework import fields
from rest_framework.serializers import Serializer

from caasm_service.entity.change import CHANGE_TYPE_MAPPING
from caasm_tool.patch.serializer import SerializerMixin
from caasm_webapi.app.query_engine.requests.change import ChangeRequest


class ChangeRequestSerializer(SerializerMixin):
    page_size = fields.IntegerField(max_value=100, min_value=1, default=20, allow_null=True, help_text="分页条数")
    page_index = fields.IntegerField(min_value=1, default=1, allow_null=True, help_text="分页索引，从1开始")
    entity_id = fields.CharField(required=True)

    def create(self, validated_data):
        req = ChangeRequest()
        req.page_index = validated_data["page_index"]
        req.page_size = validated_data["page_size"]
        req.entity_id = validated_data["entity_id"]
        return req


class ChangeSerializer(Serializer):
    field_name = fields.CharField(default="")
    change_type = fields.SerializerMethodField()
    child_change_type = fields.SerializerMethodField()
    new_display = fields.CharField(default="")
    original_display = fields.CharField(default="")
    changed_datetime = fields.DateTimeField()

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        pass

    def get_change_type(self, obj):
        return CHANGE_TYPE_MAPPING.get(obj.change_type) or ""

    def get_child_change_type(self, obj):
        return CHANGE_TYPE_MAPPING.get(obj.child_change_type) or ""
