from rest_framework.views import APIView

from caasm_service.runtime import change_service
from caasm_tool.util import extract
from caasm_webapi.app.query_engine.requests.change import ChangeRequest
from caasm_webapi.app.query_engine.serializers.change import ChangeRequestSerializer, ChangeSerializer
from caasm_webapi.util.response import build_failed, build_success


class ChangeAPIView(APIView):
    _headers = [
        {"title": "变化", "key": "change_type", "data_type": "string"},
        {
            "title": "子实体变化",
            "key": "child_change_type",
            "data_type": "string",
        },
        {"title": "新值", "key": "new_display", "data_type": "string"},
        {"title": "原值", "key": "original_display", "data_type": "string"},
        {"title": "字段", "key": "field_name", "data_type": "string"},
        {"title": "变化时间", "key": "changed_datetime", "data_type": "string"},
    ]

    def post(self, request, category):
        serializer = ChangeRequestSerializer(data=request.data)
        if serializer.is_valid():
            req: ChangeRequest = serializer.save()
            changes = change_service.find_change_details(
                category=category, entity_id=req.entity_id, offset=req.page_size * req.page_index, limit=req.page_size
            )
            count = change_service.change_details_count(category=category, entity_id=req.entity_id)
            change_serializer = ChangeSerializer(instance=changes, many=True)
            changes = change_serializer.data
            rows = []
            for change in changes:
                row = []
                for header in self._headers:
                    field_name = extract(header, "key")
                    if field_name in change:
                        v = change[field_name]
                    else:
                        v = ""
                    row.append({"key": field_name, "value": v})
                rows.append(row)
            return build_success({"headers": self._headers, "rows": rows, "count": count})

        return build_failed(-1, str(serializer.errors))
