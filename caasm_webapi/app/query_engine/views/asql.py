from copy import deepcopy
from typing import Set

from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_page
from marshmallow import ValidationError, RAISE
from rest_framework.request import Request
from rest_framework.views import APIView

from caasm_aql.aql import AqlLogicQuery
from caasm_aql.aql_resolver import AqlResolver
from caasm_aql.asql_batch import AsqlBatchLogicQuery, AsqlBatchResolver
from caasm_aql.asql_field import AsqlFieldLogicQuery, AsqlFieldResolver
from caasm_aql.asql_plus import AsqlPlusResolver, AsqlPlusLogicalQuery
from caasm_aql.query_builders.runtime import manager
from caasm_aql.schemas import AqlLogicalGroupQuerySchema, AqlInventoryTargetSchema, AsqlFieldLogicalGroupQuerySchema
from caasm_render.query.base import BaseQuery
from caasm_render.runtime import render_manager
from caasm_service.entity.meta_model import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, T<PERSON><PERSON>_NAME_MAPPER
from caasm_service.runtime import adapter_service, adapter_instance_service, quick_search_service, entity_service
from caasm_webapi.app.query_engine.requests.asql import ComplexFieldRequest, FieldOperationRequest, WizardToBatchRequest
from caasm_webapi.app.query_engine.responses.asql import FieldOperations
from caasm_webapi.app.query_engine.serializers.asql import (
    AsqlToWizardSerializer,
    WizardToAsqlSerializer,
    FieldRequestSerializer,
    FieldOperationRequestSerializer,
    FieldOperationSerializer,
    ComplexFieldRequestSerializer,
    ClassifiedFieldSerializer,
    QuickSearchSerializer,
    AsqlPlusWizardSerializer,
    FiltersSerializer,
    WizardToBatchSerializer,
)
from caasm_webapi.util.response import build_failed, ResponseCode, build_success


class AsqlPlusToFiltersAPIView(APIView, BaseQuery):
    def post(self, request: Request, category):
        serializer = AsqlPlusWizardSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(-1, str(serializer.errors))
        asql: str = serializer.save()
        resolver = AsqlPlusResolver(as_fulltext=True)
        try:
            query = resolver.resolve_logic_query(asql)
        except ValueError as exc:
            return build_failed(-1, str(exc))
        if query is None:
            return build_success()
        else:
            field_mapper = self.find_field_to_mapper(category)
            return build_success([q.to_dict(field_mapper) for q in query.queries])


class FiltersToAsqlPlusAPIView(APIView, BaseQuery):
    def post(self, request: Request, category):
        serializer = FiltersSerializer(data=request.data)
        if serializer.is_valid():
            return build_success(AsqlPlusLogicalQuery.from_list(serializer.save()))
        return build_failed(-1, str(serializer.errors))


class AsqlToWizardAPIView(APIView, BaseQuery):
    def post(self, request: Request, category):
        serializer = AsqlToWizardSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)
        asql: str = serializer.save()
        resolver = AqlResolver()
        try:
            query = resolver.resolve_logic_query(asql)
        except ValueError as exc:
            return build_failed(ResponseCode.REQUEST_ERROR, message=exc.args)
        if query is None:
            return build_success()
        else:
            schema = AqlLogicalGroupQuerySchema()
            builder_data = schema.dump(query)
            return build_success(builder_data)


class WizardToFieldFilterAPIView(APIView, BaseQuery):
    def post(self, request: Request, category):
        serializer = WizardToAsqlSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(str(serializer.errors))
        wizard: dict = serializer.save()
        schema = AsqlFieldLogicalGroupQuerySchema()
        try:
            wizard = {
                "group": {"not_boolean": False, "operand": "and", "items": [{"type": "target", "target": wizard}]}
            }
            query: AsqlFieldLogicQuery = schema.load(wizard, partial=False, unknown=RAISE)
        except ValidationError as exc:
            return build_failed(ResponseCode.SYSTEM_ERROR, message=str(exc.messages))
        field_mapper = self.find_field_to_mapper(category)
        return build_success(query.to_dict(field_mapper))


class WizardToAsqlAPIView(APIView, BaseQuery):
    def post(self, request: Request, category):
        serializer = WizardToAsqlSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(str(serializer.errors))
        wizard: dict = serializer.save()
        schema = AqlLogicalGroupQuerySchema()
        try:
            query: AqlLogicQuery = schema.load(wizard, partial=False, unknown=RAISE)
        except ValidationError as exc:
            return build_failed(ResponseCode.SYSTEM_ERROR, message=str(exc.messages))
        return build_success(query.to_asql())


class WizardToAsqlFilterAPIView(APIView, BaseQuery):
    def post(self, request: Request, category):
        serializer = WizardToAsqlSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(str(serializer.errors))
        wizard: dict = serializer.save()
        schema = AqlLogicalGroupQuerySchema()
        try:
            query: AqlLogicQuery = schema.load(wizard, partial=False, unknown=RAISE)
        except ValidationError as exc:
            return build_failed(ResponseCode.SYSTEM_ERROR, message=str(exc.messages))
        return build_success(query.to_dict())


class BatchWizardBase:
    _FILTERS = {
        "asset": {
            "modes": [
                {
                    "name": "field_mode",
                    "display_name": "IP检索",
                    "options": [
                        {
                            "name": "full_match",
                            "display_name": "精确匹配",
                            "selected": True,
                        },
                        {
                            "name": "match",
                            "display_name": "模糊匹配",
                            "selected": False,
                        },
                    ],
                    "fields": [
                        {
                            "name": "network.priority_addr",
                            "display_name": "网络-主IP",
                            "selected": True,
                        },
                        {
                            "name": "network.ips.addr",
                            "display_name": "网络-IP地址列表-IP地址",
                            "selected": True,
                        },
                        {
                            "name": "kube_pod.pod_ip",
                            "display_name": "容器云Pod-Pod IP",
                            "selected": True,
                        },
                    ],
                    "selected": True,
                },
                {
                    "name": "match_mode",
                    "display_name": "全字段全文检索（支持完整IP，不支持IP片段）",
                    "selected": False,
                },
            ],
            "value": "",
        },
        "domain_unique": {
            "modes": [
                {
                    "name": "field_mode",
                    "display_name": "域名检索",
                    "options": [
                        {
                            "name": "full_match",
                            "display_name": "精确匹配",
                            "selected": True,
                        },
                        {
                            "name": "match",
                            "display_name": "模糊匹配",
                            "selected": False,
                        },
                    ],
                    "fields": [
                        {
                            "name": "domain_unique.name",
                            "display_name": "域名-域名",
                            "selected": True,
                        }
                    ],
                    "selected": True,
                },
                {
                    "name": "match_mode",
                    "display_name": "全字段全文检索（支持完整IP，不支持IP片段）",
                    "selected": False,
                },
            ],
            "value": "",
        },
        "internet_ip_unique": {
            "modes": [
                {
                    "name": "field_mode",
                    "display_name": "IP检索",
                    "options": [
                        {
                            "name": "full_match",
                            "display_name": "精确匹配",
                            "selected": True,
                        },
                        {
                            "name": "match",
                            "display_name": "模糊匹配",
                            "selected": False,
                        },
                    ],
                    "fields": [
                        {
                            "name": "internet_ip_unique.ip",
                            "display_name": "互联网IP-IP地址",
                            "selected": True,
                        },
                        {
                            "name": "internet_ip_unique.ipv4",
                            "display_name": "互联网IP-IPv4地址",
                            "selected": True,
                        },
                        {
                            "name": "internet_ip_unique.ipv6",
                            "display_name": "互联网IP-IPv6地址",
                            "selected": True,
                        },
                    ],
                    "selected": True,
                },
                {
                    "name": "match_mode",
                    "display_name": "全字段全文检索（支持完整IP，不支持IP片段）",
                    "selected": False,
                },
            ],
            "value": "",
        },
    }
    _DEFAULT_FILTER = {
        "modes": [
            {
                "name": "match_mode",
                "display_name": "全字段全文检索（支持完整IP，不支持IP片段）",
                "selected": False,
            },
        ],
        "value": "",
    }


class BatchWizardAPIView(APIView, BatchWizardBase):
    def post(self, request, category):
        #   todo: 先写死，在下一个迭代改成资产类型相关
        return build_success(self._FILTERS.get(category, self._DEFAULT_FILTER))


class BatchFilterToWizardAPIView(APIView, BaseQuery, BatchWizardBase):
    def post(self, request, category):
        data = request.data
        resolver = AsqlBatchResolver()
        try:
            query: AsqlBatchLogicQuery = resolver.resolve_logic_query(data["asql"])
        except ValueError as exc:
            return build_failed(ResponseCode.REQUEST_ERROR, message=exc.args)
        if query is None:
            return build_success({})
        else:
            if "*" in query.fields:
                selected_mode_name = "match_mode"
            else:
                selected_mode_name = "field_mode"
            result = deepcopy(self._FILTERS.get(category, self._DEFAULT_FILTER))
            for mode in result["modes"]:
                mode_name = mode["name"]
                if mode_name == selected_mode_name:
                    mode["selected"] = True
                    for field in mode.get("fields") or []:
                        field_name = field["name"]
                        if field_name in query.fields:
                            field["selected"] = True
                if mode_name == "field_mode":
                    if query.op == "=":
                        selected_option = "full_match"
                    else:
                        selected_option = "match"
                    for option in mode.get("options") or []:
                        option_name = option["name"]
                        if option_name == selected_option:
                            option["selected"] = True
                            break
            result["value"] = "\n".join(query.keywords)
            return build_success(result)


class WizardToBatchFilterAPIView(APIView, BaseQuery):
    def post(self, request, category):
        serializer = WizardToBatchSerializer(data=request.data)
        if serializer.is_valid():
            req: WizardToBatchRequest = serializer.save()
            value = req.value
            query = AsqlBatchLogicQuery()
            query.keywords = value
            mode_name = req.mode
            if mode_name == "field_mode":
                option_name = req.option
                if option_name == "full_match":
                    op = "="
                else:
                    op = "match"
                query.op = op
                query.fields = req.field_names
                if not req.field_names:
                    return build_failed(-1, "必须选中至少一个字段")
            else:
                query.fields = ["*"]
                query.op = "="
            return build_success(query.to_dict())
        return build_failed(-1, str(serializer.errors))


class FieldFilterToWizardAPIView(APIView, BaseQuery):
    def post(self, request: Request, category):
        serializer = AsqlToWizardSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)
        asql: str = serializer.save()
        resolver = AsqlFieldResolver()
        try:
            query = resolver.resolve_logic_query(asql)
        except ValueError as exc:
            return build_failed(ResponseCode.REQUEST_ERROR, message=exc.args)
        if query is None:
            return build_success()
        else:
            schema = AqlInventoryTargetSchema()
            builder_data = schema.dump(query.logical_group.items[0].target)
            return build_success(builder_data)


class ClassifiedFieldAPI(APIView, BaseQuery):
    def post(self, request, category):
        serializer = FieldRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)
        data = serializer.save()
        meta_view = self.get_meta_view(category)
        if not meta_view:
            return build_failed(ResponseCode.INVALID_DATA)

        field_mapper = self.find_field_to_mapper(category, date=data["date"])

        field_result = self.clean_field(field_mapper)
        often_used_result = self.clean_often_used_field(field_mapper, meta_view.often_used_fields)

        serializer_instance = {"fields": field_result, "often_used_fields": often_used_result}
        result = ClassifiedFieldSerializer(instance=serializer_instance).data
        return build_success(data=result)

    def clean_field(self, field_mapper):
        result = []
        for field_name, field in field_mapper.items():
            result.append(self._format_field(field))
        return result

    def clean_often_used_field(self, field_mapper, often_used_fields):
        result = []

        for often_used_field in often_used_fields:
            field = field_mapper.get(often_used_field)
            if not field:
                continue
            tmp_field_res = self._format_field(field)
            result.append(tmp_field_res)
        return result

    @classmethod
    def _format_field(cls, field):
        field_type = field.type.value
        full_display_name = field.full_display_name
        full_name = field.full_name
        is_complex = field.is_complex
        description = field.description
        choices = []

        if field_type == MetaFieldType.ENUM:
            pass

        details = [
            {
                "label": "字段名称",
                "value": full_name,
            },
            {
                "label": "显示名称",
                "value": full_display_name,
            },
            {
                "label": "字段类型",
                "value": TYPE_NAME_MAPPER.get(field_type, ""),
            },
            {"label": "字段描述", "value": description},
        ]

        res = {
            "data_type": field_type,
            "full_name": full_name,
            "display_name": full_display_name,
            "choices": choices,
            "is_complex": is_complex,
            "details": details,
        }
        return res


class DataTypeOperationsAPIView(APIView, BaseQuery):
    def post(self, request: Request, category):
        serializer = FieldOperationRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)
        req: FieldOperationRequest = serializer.save()
        operations = FieldOperations()
        meta_field_mapper = self.find_field_to_mapper(category)
        meta_field: MetaField = meta_field_mapper.get(req.field_name)
        if meta_field is not None:
            data_type = meta_field.type

            for i in manager.operator_manager.find_operator(data_type):
                available = True
                if meta_field:
                    from caasm_aql.query_builders.operator import Operator

                    i: Operator = i
                    unavailable_sub_data_types: Set = i.unavailable_sub_data_types(meta_field.type)
                    for sub_field in meta_field.children:
                        sub_field: MetaField = sub_field
                        if sub_field.type in unavailable_sub_data_types:
                            available = False
                            continue
                if not available:
                    continue
                _detail = {
                    "name": i.get_operator.value,
                    "display_name": i.get_display_name(),
                    "description": i.description,
                    "type": i.type,
                    "auto_complete": i.auto_complete,
                }
                operations.operators.append(_detail)

            for method in manager.method_manager.find_method(data_type):
                _params = []
                for _param in method.params:
                    _param_detail = {
                        "name": _param.name,
                        "display_name": _param.display_name,
                        "description": _param.description,
                        "data_type": _param.data_type.value,
                        "required": _param.required,
                        "default": _param.default,
                    }
                    _params.append(_param_detail)

                _detail = {
                    "name": method.name,
                    "display_name": method.display_name,
                    "params": _params,
                    "description": method.description,
                    "type": method.type,
                    "auto_complete": method.auto_complete,
                }
                operations.methods.append(_detail)

        return build_success(FieldOperationSerializer(instance=operations).data)


class AsqlComplexFieldAPIView(APIView, BaseQuery):
    def post(self, request: Request, category):
        serializer = ComplexFieldRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)
        req: ComplexFieldRequest = serializer.save()
        field_name = req.field
        field_model = render_manager.default_query.find_field_to_mapper(category).get(field_name)
        if not field_model:
            return build_failed(ResponseCode.REQUEST_ERROR, message="该字段在模型表中不存在")
        if not field_model.is_complex:
            return build_failed(ResponseCode.REQUEST_ERROR, message="字段不是复杂字段")
        fields = list()
        for child_field in field_model.children[0].children:
            child_field: MetaField = child_field

            sub_type = ""
            data_type = child_field.type
            children = child_field.children
            if data_type == MetaFieldType.LIST:
                sub_type = TYPE_NAME_MAPPER.get(children[0].type)
            details = [
                {
                    "label": "字段名称",
                    "value": child_field.name,
                },
                {
                    "label": "显示名称",
                    "value": child_field.display_name,
                },
                {
                    "label": "字段类型",
                    "value": TYPE_NAME_MAPPER.get(data_type.value, ""),
                },
                {"label": "描述信息", "value": child_field.description},
            ]
            if sub_type:
                details.insert(3, {"label": "元素类型", "value": sub_type})
            field_info = {
                "name": child_field.name,
                "display_name": child_field.display_name,
                "data_type": child_field.type.value,
                "details": details,
            }
            if child_field.type == MetaFieldType.ENUM:
                field_info["choices"] = [
                    {"key": str(key), "value": value} for key, value in child_field.rules[0].setting.items()
                ]
            fields.append(field_info)
        return build_success(fields)


class AsqlAdapterAPI(APIView, BaseQuery):
    def get(self, request, category):
        result = list()
        ancestor_adapter_names = adapter_instance_service.find_adapter_instance_distinct("ancestor_adapter_name")
        if ancestor_adapter_names:
            adapters = adapter_service.find_adapter(names=ancestor_adapter_names, fields=["name", "display_name"])
            for adapter in adapters:
                result.append({"display_name": adapter.display_name, "name": adapter.name})
        return build_success(result)


class QuickSearchApiView(APIView, BaseQuery):
    def get(self, request, category):
        quick_search = quick_search_service.find_quick_searches(category=category, limit=10000)
        return build_success(QuickSearchSerializer(instance=quick_search, many=True).data)


class BucketView(APIView):
    _EMPTY = ["", [], {}, None]

    def get(self, request, category):
        table = entity_service.get_table(category)
        if not table:
            return build_success([])
        field = request.query_params.get("field")
        condition = request.query_params.get("condition", {"match_all": {}})
        if not field:
            return build_success([])

        records = entity_service.find_distinct(field, condition=condition, table=table)
        return build_success([{"label": record, "value": record} for record in records if record not in self._EMPTY])
