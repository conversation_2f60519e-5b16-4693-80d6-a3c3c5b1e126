from datetime import datetime
from typing import List

from caasm_aql.aql import AsqlOption, AqlAddition, ChildRetrieve, AqlRelatedEntityOption
from caasm_aql.base import AsqlType


class AqlRequest:
    def __init__(
        self,
        aql: str,
        filters: list,
        option: AsqlOption,
        date: datetime = None,
        additions: List[AqlAddition] = None,
        childs_retrieve_info: List[ChildRetrieve] = None,
        aql_type: AsqlType = None,
        related_entity_option: AqlRelatedEntityOption = None,
    ):
        self.aql: str = aql
        self.filters: str = filters
        self.option: AsqlOption = option
        self.date: datetime = date
        self.additions: List[AqlAddition] = additions or list()
        self.childs_retrieve_info: List[ChildRetrieve] = childs_retrieve_info or list()
        self.aql_type: AsqlType = aql_type or AsqlType.ASQL
        self.related_entity_option: AqlRelatedEntityOption = related_entity_option


class ExportRequest(AqlRequest):
    def __init__(
        self,
        aql: str,
        filters: list,
        option: AsqlOption,
        date: datetime = None,
        additions: List[AqlAddition] = None,
        childs_retrieve_info: List[ChildRetrieve] = None,
        aql_type: AsqlType = None,
        related_entity_option: AqlRelatedEntityOption = None,
    ):
        super().__init__(aql, filters, option, date, additions, childs_retrieve_info, aql_type, related_entity_option)
        self.name = None


class EntitiesRequest:
    def __init__(self):
        self.entity_ids = list()


class EntityRequest:
    def __init__(self):
        self.entity_id: str = None
        self.date = None


class EntityDetailRequest(EntityRequest):
    def __int__(self):
        super(EntityDetailRequest, self).__init__()
        self.render_content = True


class EntityCategoryDetailRequest(EntityRequest):
    def __int__(self):
        super(EntityCategoryDetailRequest, self).__init__()
        self.category = None


class EntityAdapterDetailRequest(EntityRequest):
    def __int__(self):
        super(EntityAdapterDetailRequest, self).__init__()
        self.adapter_name = None


class UserDefaultEntityFieldsRequest:
    def __init__(self):
        self.field_names = None


class EntityFieldsRequest:
    def __init__(self):
        self.category = None
