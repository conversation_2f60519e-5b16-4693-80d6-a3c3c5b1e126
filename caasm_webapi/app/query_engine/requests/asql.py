from typing import Optional, List


class ComplexFieldRequest:
    def __init__(self):
        self.field: str = None
        self.entity_type: Optional[str] = None


class ComplexFieldQueryRequest:
    def __init__(self):
        self.asset_id: Optional[str] = None
        self.field: Optional[str] = None
        self.keyword: Optional[str] = None


class FieldOperationRequest:
    def __init__(self):
        self.field_name: str = None
        # self.data_type: str = None


class WizardToBatchRequest:
    def __init__(self):
        self.mode: str = ""
        self.option: str = ""
        self.field_names: List[str] = []
        self.value = ""
