from caasm_tool.patch.router import DefaultRouter
from caasm_webapi.app.query_engine.views.asql import (
    AsqlAdapterAPI,
    ClassifiedFieldAPI,
    DataTypeOperationsAPIView,
    WizardToAsqlAPIView,
    AsqlToWizardAPIView,
    AsqlComplexFieldAPIView,
    QuickSearchApiView,
    AsqlPlusToFiltersAPIView,
    FiltersToAsqlPlusAPIView,
    WizardToFieldFilterAPIView,
    FieldFilterToWizardAPIView,
    WizardToAsqlFilterAPIView,
    BatchWizardAPIView,
    WizardToBatchFilterAPIView,
    BatchFilterToWizardAPIView,
    BucketView,
)
from caasm_webapi.app.query_engine.views.change import ChangeAPIView
from caasm_webapi.app.query_engine.views.entity import (
    MetaViewAPI,
    AqlQueryAPI,
    DetailAPI,
    EntityCardAPI,
    EntityCardDetailAPI,
    EntityTitleAPI,
    EntityCategoryDetailAPI,
    EntityFabricMenuAPI,
    CategoryImageAPI,
    EntityAdapterMenuAPI,
    ExportAPIView,
    AqlFieldGroupAPI,
    UpdateUserDefaultEntityFieldsAPIView,
    UpdateSystemDefaultEntityFieldsAPIView,
    MetaEntityFieldsAPIView,
    AqlQuerySimpleAPI,
    ExportMetaViewAPI,
    JoinExportViewAPI,
    JoinExportTableViewAPI,
    MetaEntityTypeFieldsAPIView,
    NewExportAPIView,
    NewExportNameAPIView,
)
from caasm_webapi.app.query_engine.views.graph import (
    VerticesOfVertexAPI,
    EntityGraphQueryAPI,
    EntityGraphAggregatedQueryAPI,
    GraphVertexTypesAPI,
    GraphVerticesOfTypeAPI,
)
from caasm_webapi.app.query_engine.views.graph_icon import GraphIconAPIView
from caasm_webapi.app.query_engine.views.os_icon import OSIconAPI
from caasm_webapi.app.query_engine.views.scene import SceneTreeAPI, GraphSceneAPI, AqlSceneAPI
from caasm_webapi.app.query_engine.views.unique import UniqueOperationAPIView

query_engine_router = DefaultRouter()

query_engine_router.join_path("<category>/metaView/", MetaViewAPI, name="MetaViewAPI")
query_engine_router.join_path("<category>/adapters/", AsqlAdapterAPI, name="AsqlAdapterAPIView")
query_engine_router.join_path("<category>/classifiedFields/", ClassifiedFieldAPI, name="ClassifiedFieldAPI")
query_engine_router.join_path("<category>/toAsql/", WizardToAsqlAPIView, name="WizardToAsqlAPIView")
query_engine_router.join_path(
    "<category>/asql/dataTypeOperations/", DataTypeOperationsAPIView, name="InventoryFieldAPIView"
)
query_engine_router.join_path(
    "<category>/wizardToAsqlFilter/", WizardToAsqlFilterAPIView, name="WizardToAsqlFilterAPIView"
)
query_engine_router.join_path("<category>/asql/complexFields/", AsqlComplexFieldAPIView, name="AsqlComplexFieldAPIView")
query_engine_router.join_path("<category>/aql/", AqlQueryAPI, name="AqlQueryAPI")
query_engine_router.join_path("<category>/aql/group/", AqlFieldGroupAPI, name="AqlFieldGroupAPI")
query_engine_router.join_path("<category>/toWizard/", AsqlToWizardAPIView, name="AsqlToWizardAPIView")
query_engine_router.join_path(
    "<category>/fieldFilterToWizard/", FieldFilterToWizardAPIView, name="FieldFilterToWizardAPIView"
)
query_engine_router.join_path(
    "<category>/asqlPlusToFilters/", AsqlPlusToFiltersAPIView, name="AsqlPlusToFiltersAPIView"
)
query_engine_router.join_path(
    "<category>/filtersToAsqlPlus/", FiltersToAsqlPlusAPIView, name="FiltersToAsqlPlusAPIView"
)
query_engine_router.join_path(
    "<category>/wizardToFieldFilter/", WizardToFieldFilterAPIView, name="WizardToFieldFilterAPIView"
)
query_engine_router.join_path("<category>/batchWizard/", BatchWizardAPIView, name="BatchWizardAPIView")
query_engine_router.join_path(
    "<category>/wizardToBatchFilter/", WizardToBatchFilterAPIView, name="WizardToBatchFilterAPIView"
)
query_engine_router.join_path(
    "<category>/batchFilterToWizard/", BatchFilterToWizardAPIView, name="BatchFilterToWizardAPIView"
)
query_engine_router.join_path("<category>/detail/", DetailAPI, name="DetailAPI")
query_engine_router.join_path("<category>/cards/", EntityCardAPI, name="EntityCardAPI")
query_engine_router.join_path("<category>/cardDetail/", EntityCardDetailAPI, name="EntityCardDetailAPI")
query_engine_router.join_path("<category>/title/", EntityTitleAPI, name="EntityTitleAPI")
query_engine_router.join_path("<category>/categoryDetail/", EntityCategoryDetailAPI, name="EntityCategoryDetailAPI")
query_engine_router.join_path("<category>/adapterMenu/", EntityAdapterMenuAPI, name="EntityAdapterMenuAPI")
query_engine_router.join_path("<category>/fabricMenu/", EntityFabricMenuAPI, name="EntityFabricMenuAPI")
query_engine_router.join_path("<category>/<name>/icon/", CategoryImageAPI, name="CategoryImageAPI")
query_engine_router.join_path("<category>/export/", ExportAPIView, name="ExportAPIView")
query_engine_router.join_path("<category>/graph/verticesOfVertex/", VerticesOfVertexAPI, name="VerticesOfVertexAPI")
query_engine_router.join_path("<category>/graph/query/", EntityGraphQueryAPI, name="EntityGraphQueryAPI")

query_engine_router.join_path("<category>/sceneTree/", SceneTreeAPI, name="SceneTreeAPI")
query_engine_router.join_path("<category>/sceneGraph/", GraphSceneAPI, name="SceneTreeAPI")
query_engine_router.join_path("<category>/scene/", AqlSceneAPI, name="AqlSceneAPI")

query_engine_router.join_path("<category>/graph/vertexTypes/", GraphVertexTypesAPI, name="GraphVertexTypesAPI")
query_engine_router.join_path("<category>/graph/verticesOfType/", GraphVerticesOfTypeAPI, name="GraphVerticesOfTypeAPI")
query_engine_router.join_path(
    "<category>/graph/aggregatedQuery/", EntityGraphAggregatedQueryAPI, name="EntityGraphAggregatedQueryAPI"
)
query_engine_router.join_path("osIcon/<os_name>", OSIconAPI, name="os_icon")
query_engine_router.join_path("graphIcon/<name>/", GraphIconAPIView, name="graph_icon")
query_engine_router.join_path(
    "<category>/updateUserDefaultEntityFields/",
    UpdateUserDefaultEntityFieldsAPIView,
    name="update_user_default_entity_fields",
)
query_engine_router.join_path(
    "<category>/updateSystemDefaultEntityFields/",
    UpdateSystemDefaultEntityFieldsAPIView,
    name="update_system_default_entity_fields",
)
query_engine_router.join_path("<category>/metaEntityFields/", MetaEntityFieldsAPIView, name="meta_entity_fields")

query_engine_router.join_path(
    "<category>/metaTypeFields/", MetaEntityTypeFieldsAPIView, name="MetaEntityTypeFieldsAPIView"
)

query_engine_router.join_path("<category>/join/export/metaView/", ExportMetaViewAPI, name="MetaViewAPI")

query_engine_router.join_path("<category>/join/export/data/", JoinExportViewAPI, name="JoinExportViewAPI")

query_engine_router.join_path("<category>/join/aql/", JoinExportTableViewAPI, name="JoinExportTableViewAPI")

query_engine_router.join_path("<category>/quickSearch/", QuickSearchApiView, name="QuickSearchApiView")

# 第三方调用
query_engine_external_router = DefaultRouter()
query_engine_external_router.join_path("<category>/aql/", AqlQuerySimpleAPI, name="AqlQuerySimpleAPI")

# 唯一实体操作
query_engine_router.join_path("<category>/operation/", UniqueOperationAPIView, name="UniqueOperationAPIView")

# 变化
query_engine_router.join_path("<category>/changes/", ChangeAPIView, name="ChangeAPIView")

# 导出
query_engine_router.join_path("<category>/exportName/", NewExportNameAPIView, name="NewExportNameAPIView")
query_engine_router.join_path("<category>/exportTask/", NewExportAPIView, name="NewExportAPIView")

# bucket
query_engine_router.join_path("<category>/bucket/", BucketView, name="BucketView")
