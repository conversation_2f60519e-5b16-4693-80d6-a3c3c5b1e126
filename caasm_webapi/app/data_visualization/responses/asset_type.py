from caasm_service.entity.asset_type import AssetType


class AssetTypeResponse:
    def __init__(self, asset_type: AssetType, meta_model):
        self.id = asset_type.id
        self.name = asset_type.name
        self.display_name = asset_type.display_name
        self.model_id = asset_type.model_id
        self.model_display_name = meta_model.display_name if meta_model else None
        self.internal = asset_type.internal
        self.description = asset_type.description
        self.create_time = asset_type.create_time
        self.update_time = asset_type.update_time
