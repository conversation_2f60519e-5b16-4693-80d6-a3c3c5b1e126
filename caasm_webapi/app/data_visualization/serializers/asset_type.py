from rest_framework import serializers, fields
from rest_framework.serializers import Serializer

from caasm_service.runtime import meta_model_service
from caasm_tool.constants import DATETIME_FORMAT
from caasm_tool.patch.serializer import SerializerMixin
from caasm_webapi.app.data_visualization.requests.asset_type import (
    AssetTypeRequest,
    AssetTypeUpdateRequest,
    AssetTypeCreateRequest,
)
from caasm_webapi.common.serializers import ResponseSerializer


class AssetTypeTotalRequestSerializer(SerializerMixin):
    modelId = serializers.Char<PERSON>ield(min_length=1, max_length=200, required=False)

    def validate_modelId(self, model_id):
        if not meta_model_service.get_meta_model_count(model_id=model_id):
            raise serializers.ValidationError("无效的模型名称")
        return model_id


class AssetTypeTotalResponseSerializer(SerializerMixin):
    label = serializers.CharField(source="display_name")
    value = serializers.Char<PERSON><PERSON>(source="id")


class AssetTypesRequestSerializer(SerializerMixin):
    page_size = fields.IntegerField(required=False, default=20, help_text="页面大小")
    page_index = fields.IntegerField(required=False, default=1, help_text="分页序号，从1开始")
    keyword = fields.CharField(required=False)

    def update(self, instance, validated_data):
        pass


class AssetTypeCreateRequestSerializer(Serializer):
    display_name = fields.CharField(help_text="资产类型显示名称", required=True)
    model_id = fields.CharField(help_text="资产类型关联的模型ID", required=True)
    description = fields.CharField(help_text="资产类型描述信息", required=False, allow_null=True)

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        req = AssetTypeCreateRequest()
        req.display_name = validated_data["display_name"]
        req.model_id = validated_data["model_id"]
        req.description = validated_data.get("description")
        return req


class AssetTypeSerializer(AssetTypeCreateRequestSerializer):
    id = fields.CharField(help_text="资产类型ID")
    model_display_name = fields.CharField(help_text="资产类型关键的模型显示名称")
    internal = fields.BooleanField(help_text="是否为内置模型")
    create_time = fields.DateTimeField(help_text="创建时间", format=DATETIME_FORMAT)
    update_time = fields.DateTimeField(help_text="修改时间", format=DATETIME_FORMAT)

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        pass


class AssetTypesResponseSerializer(ResponseSerializer):
    data = AssetTypeSerializer(many=True)


class AssetTypeRequestSerializer(Serializer):
    asset_type_id = fields.CharField(help_text="资产类型ID")

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        request = AssetTypeRequest()
        request.asset_type_id = validated_data["asset_type_id"]
        return request


class AssetTypeResponseSerializer(ResponseSerializer):
    data = AssetTypeSerializer()


class AssetTypeUpdateRequestSerializer(Serializer):
    asset_type_id = fields.CharField(help_text="资产类型ID")
    model_id = fields.CharField(help_text="模型ID")
    display_name = fields.CharField(help_text="展示名称")
    description = fields.CharField(help_text="描述", allow_null=True, required=False)

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        req = AssetTypeUpdateRequest()
        req.asset_type_id = validated_data["asset_type_id"]
        req.model_id = validated_data["model_id"]
        req.display_name = validated_data["display_name"]
        req.description = validated_data.get("description", "")
        return req
