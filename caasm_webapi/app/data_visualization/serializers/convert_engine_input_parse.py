from functools import lru_cache
from typing import List

import yaml

from caasm_service.entity.convert_func import BaseData
from caasm_service.runtime import convert_func_service
from caasm_tool.util import hump2underline


class ConvertEngineInputParser(object):
    _IGNORE_VALUES = ["?"]
    _input_params_filter_fields = []
    _not_need_padding_title_funcs = ["replace", "enter"]
    _not_need_padding_method_params = {"split": ["sep"], "translate": ["default"], "add_list_object": ["size"]}
    _is_output_root_key = "is_output_root"

    def __init__(self):
        self._input_parse = {
            "add_up": self.add_up_input_parse,
            "compute_size": self.compute_size_input_parse,
            "rename": self.rename_input_parse,
            "add": self.add_input_parse,
            "int": self.int_input_parse,
            "str": self.str_input_parse,
            "multiply": self.multiply_input_parse,
            "translate": self.translate_input_parse,
            "for": self.for_input_parse,
            "filter_ipv4": self.filter_ipv4_input_parse,
            "filter_empty": self.filter_empty_input_parse,
            "filter_equal": self.filter_equal_input_parse,
            "drop": self.drop_input_parse,
            "unwind": self.unwind_input_parse,
            "datetime": self.datetime_input_parse,
            "add_list_object": self.add_list_object_input_parse,
            "replace": self.replace_input_parse,
            "fuzzy_replace": self.replace_input_parse,
            "lower": self.lower_input_parse,
            "json": self.json_input_parse,
            "upper": self.upper_input_parse,
            "extract": self.extract_input_parse,
            "split": self.split_input_parse,
            "deduplicate": self.deduplicate_input_parse,
            "division": self.division_input_parse,
            "timestamp_to_datetime": self.timestamp_to_datetime_input_parse,
            "enter": self.enter_input_parse,
            "float": self.float_input_parse,
            "list": self.common_parse,
            "flatten": self.common_parse,
            "strip": self.common_parse,
            "list_get": self.list_get_parse,
            "convert_list_object": self.convert_list_object_parse,
        }

    def parse_input(self, method_name, action_node_params, input_params, title, for_ignore_title):
        action_node_params = [i.replace(for_ignore_title, "") for i in action_node_params]
        method = self._get_func(method_name)

        input_param_define_list: List[BaseData] = [i for i in method.input.data if i.show]

        real_input_params = {k: v for k, v in input_params.items() if k not in self._input_params_filter_fields}

        result = {}

        for input_param_define in input_param_define_list:
            input_param_define_name = hump2underline(input_param_define.name)
            tmp_value = real_input_params.get(input_param_define_name)

            if method_name in self._not_need_padding_title_funcs:
                result[input_param_define_name] = tmp_value
                continue

            if input_param_define_name in self._not_need_padding_method_params.get(method_name, []):
                result[input_param_define_name] = tmp_value
                continue

            if not input_params.get(self._is_output_root_key) and not input_param_define.direct:
                try:
                    tmp_value = title + "." + tmp_value if title else tmp_value
                    tmp_value = tmp_value.replace(for_ignore_title, "")
                except Exception:
                    pass
            result[input_param_define_name] = tmp_value

        self._input_parse[method_name](result, action_node_params, input_params)

        return result

    @classmethod
    def parse_output(cls, method_name, input_params, title, for_ignore_title):
        method = cls._get_func(method_name)
        output_data_batch = method.output.data
        output_params = []
        title = title.replace(for_ignore_title, "")
        for output_data in output_data_batch:
            output_info = input_params[hump2underline(output_data.name)]
            if not input_params.get(cls._is_output_root_key):
                if title and not output_info.replace("${", "").startswith(title + "."):
                    output_info = title + "." + output_info
            output_params.append(output_info)
        return output_params

    @classmethod
    def rename_input_parse(cls, result, action_node_params, input_params):
        result["src_field"] = action_node_params[0]

    @classmethod
    def list_get_parse(cls, result, action_node_params, input_params):
        result["index"] = cls._get_real_value(input_params["index"])
        result["src_field"] = action_node_params[0]

    @classmethod
    def convert_list_object_parse(cls, result, action_node_params, input_params):
        cls.common_parse(result, action_node_params, input_params)
        result["child_field"] = cls._get_real_value(input_params["child_field"])

    @classmethod
    def add_input_parse(cls, result, action_node_params, input_params):
        values = cls._find_real_value(result.get("values"))

        for action_node_param in action_node_params:
            values.append(cls._build_dynamic_params(action_node_param))
        result["values"] = values

    @classmethod
    def int_input_parse(cls, result, action_node_params, input_params):
        return cls.common_parse(result, action_node_params, input_params)

    @classmethod
    def str_input_parse(cls, result, action_node_params, input_params):
        return cls.common_parse(result, action_node_params, input_params)

    @classmethod
    def float_input_parse(cls, result, action_node_params, input_params):
        return cls.common_parse(result, action_node_params, input_params)

    @classmethod
    def add_up_input_parse(cls, result, action_node_params, input_params):
        values = result.get("values") or []

        for action_node_param in action_node_params:
            values.append(cls._build_dynamic_params(action_node_param))
        result["values"] = values

    @classmethod
    def multiply_input_parse(cls, result, action_node_params, input_params):
        action_node_params = [cls._build_dynamic_params(action_node_params[0])]
        values = input_params["values"]
        input_params["values"] = cls._find_real_value(values)
        result["values"] = cls._find_real_value(values)
        return cls.common_parse(result, action_node_params, input_params)

    @classmethod
    def compute_size_input_parse(cls, result, action_node_params, input_params):
        result["src_field"] = action_node_params[0]

    @classmethod
    def translate_input_parse(cls, result, action_node_params, input_params):
        new_values = [cls._get_real_value_by_dict(i) for i in (result.get("values") or [])]
        default = result.get("default")
        result.update({"values": new_values, "field": action_node_params[0], "default": cls._get_real_value(default)})
        result["field"] = action_node_params[0]
        input_params["field"] = action_node_params[0]

    @classmethod
    def for_input_parse(cls, result, action_node_params, input_params):
        return cls.common_parse(result, action_node_params, input_params)

    @classmethod
    def filter_ipv4_input_parse(cls, result, action_node_params, input_params):
        return cls.common_parse(result, action_node_params, input_params)

    @classmethod
    def filter_empty_input_parse(cls, result, action_node_params, input_params):
        return cls.filter_ipv4_input_parse(result, action_node_params, input_params)

    @classmethod
    def filter_equal_input_parse(cls, result, action_node_params, input_params):
        result["value"] = cls._get_real_value(result["value"])
        return cls.filter_ipv4_input_parse(result, action_node_params, input_params)

    @classmethod
    def drop_input_parse(cls, result, action_node_params, input_params):
        result["field"] = action_node_params[0]

    @classmethod
    def unwind_input_parse(cls, result, action_node_params, input_params):
        return cls.common_parse(result, action_node_params, input_params)

    @classmethod
    def datetime_input_parse(cls, result, action_node_params, input_params):
        return cls.common_parse(result, action_node_params, input_params)

    @classmethod
    def add_list_object_input_parse(cls, result, action_node_params, input_params):
        size = result.get("size")
        mapping = result.get("mapping")

        new_size = cls._get_real_value(size)
        new_mapping = [cls._get_real_value_by_dict(i) for i in mapping]
        tmp_info = {"size": new_size, "mapping": new_mapping}

        result.update(tmp_info)
        input_params.update(tmp_info)

    @classmethod
    def replace_input_parse(cls, result, action_node_params, input_params):
        return cls.common_parse(result, action_node_params, input_params)

    @classmethod
    def lower_input_parse(cls, result, action_node_params, input_params):
        return cls.common_parse(result, action_node_params, input_params)

    @classmethod
    def json_input_parse(cls, result, action_node_params, input_params):
        return cls.common_parse(result, action_node_params, input_params)

    @classmethod
    def upper_input_parse(cls, result, action_node_params, input_params):
        return cls.common_parse(result, action_node_params, input_params)

    @classmethod
    def extract_input_parse(cls, result, action_node_params, input_params):
        return cls.common_parse(result, action_node_params, input_params)

    @classmethod
    def split_input_parse(cls, result, action_node_params, input_params):
        cls.common_parse(result, action_node_params, input_params)
        input_params["dst_field"] = result["dst_field"]

    @classmethod
    def deduplicate_input_parse(cls, result, action_node_params, input_params):
        return cls.common_parse(result, action_node_params, input_params)

    @classmethod
    def division_input_parse(cls, result, action_node_params, input_params):
        action_node_params = [cls._build_dynamic_params(action_node_params[0])]
        values = input_params["values"]
        input_params["values"] = cls._find_real_value(values)
        result["values"] = cls._find_real_value(values)
        return cls.common_parse(result, action_node_params, input_params)

    @classmethod
    def timestamp_to_datetime_input_parse(cls, result, action_node_params, input_params):
        return cls.common_parse(result, action_node_params, input_params)

    @classmethod
    def enter_input_parse(cls, result, action_node_params, input_params):
        field = action_node_params[0]
        result["field"] = cls._build_dynamic_params(field)
        input_params["field"] = cls._build_dynamic_params(field)
        return

    @classmethod
    def common_parse(cls, result, action_node_params, input_params):
        result["field"] = action_node_params[0]
        input_params["field"] = action_node_params[0]

    @classmethod
    def _build_dynamic_params(cls, params):
        if params.startswith("${"):
            return params
        return f"${{{params}}}"

    @classmethod
    def _get_real_value(cls, value):
        if value in cls._IGNORE_VALUES:
            return value
        try:
            value = yaml.safe_load(value)
        except Exception:
            pass
        return value

    @classmethod
    def _find_real_value(cls, values):
        if not values:
            return []
        return [cls._get_real_value(i) for i in values]

    @classmethod
    def _get_real_value_by_dict(cls, value):
        result = {}
        for key, val in value.items():
            real_key = cls._get_real_value(key)
            real_val = cls._get_real_value(val)

            result[real_key] = real_val
        return result

    @classmethod
    @lru_cache()
    def _get_func(cls, method_name):
        method = convert_func_service.get_convert_func(name=method_name)
        return method


convert_engine_input_parser = ConvertEngineInputParser()
