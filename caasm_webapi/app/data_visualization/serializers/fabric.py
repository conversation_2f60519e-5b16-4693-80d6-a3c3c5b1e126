from rest_framework import serializers

from caasm_service.entity.meta_model import Meta<PERSON>ieldType, TYPE_NAME_MAPPER
from caasm_tool.constants import DATETIME_FORMAT
from caasm_tool.patch.serializer import SerializerMixin


class FabricRequestListSerializer(SerializerMixin):
    page_index = serializers.IntegerField(required=False, default=1)
    page_size = serializers.IntegerField(required=False, default=20, max_value=200)
    sort_fields = serializers.ListField(child=serializers.CharField(), default=list, required=False)
    keyword = serializers.CharField(max_length=256, required=False, help_text="关键字")
    internal = serializers.BooleanField(required=False, default=None, help_text="是否内置", allow_null=True)


class FabricResponseListSerializer(SerializerMixin):
    fabric_config_id = serializers.CharField(required=False)
    asset_type_id = serializers.Char<PERSON>ield()
    model_id = serializers.CharField()
    internal = serializers.BooleanField()
    asset_type = serializers.CharField()
    description = serializers.CharField()
    model_display_name = serializers.CharField()
    is_modify = serializers.BooleanField()
    modify_time = serializers.CharField()
    modify_username = serializers.CharField()


class FabricFieldListSerializer(SerializerMixin):
    asset_type_id = serializers.CharField()


class FabricFieldPoricyListSerializer(SerializerMixin):
    asset_type_id = serializers.CharField()
    keyword = serializers.CharField(help_text="keyword", required=False)
    page_index = serializers.IntegerField(required=False, default=1)
    page_size = serializers.IntegerField(required=False, default=20, max_value=200)
    sort_fields = serializers.ListField(child=serializers.CharField(), default=list, required=False)


class FabricModelVisualizationModifySerializer(SerializerMixin):
    asset_type_id = serializers.CharField(help_text="资产类型ID", required=True)


class UniqueIdentificationPolicySerializer(SerializerMixin):
    policy_label = serializers.CharField()
    policy_value = serializers.CharField()
    policy = serializers.ListSerializer(child=serializers.CharField())


class AdapterConfidenceSerializer(SerializerMixin):
    adapter_name = serializers.CharField()
    confidence = serializers.CharField()


class FieldGlobalPolicySerializer(SerializerMixin):
    label = serializers.CharField()
    value = serializers.CharField()
    policy_description = serializers.CharField(required=False)


class FieldPolicySerializer(SerializerMixin):
    field_id = serializers.CharField()
    field_adapter_confidence = serializers.ListSerializer(child=AdapterConfidenceSerializer())
    field_policy = serializers.ListSerializer(child=FieldGlobalPolicySerializer())


class FabricModelAssetTypeDeleteSerializer(SerializerMixin):
    fabric_config_id = serializers.CharField()


class SaveFabricFieldPolicySerializer(SerializerMixin):
    asset_type_id = serializers.CharField()
    full_name = serializers.CharField()
    adapter_confidence = serializers.ListSerializer(child=AdapterConfidenceSerializer())
    fabric_policy = serializers.ListSerializer(child=FieldGlobalPolicySerializer())


class FabricPolicySerializer(SerializerMixin):
    name = serializers.CharField(required=False)
    src_field = serializers.CharField(required=False, allow_null=True)
    dst_field = serializers.CharField(required=False, allow_null=True)
    setting = serializers.DictField(required=False)


class FabricModelSaveVisualizationSerializer(SerializerMixin):
    asset_type_id = serializers.CharField()
    fabric_policy = FabricPolicySerializer(required=False)
    adapter_confidence = serializers.ListSerializer(child=AdapterConfidenceSerializer())
    field_global_policy = serializers.ListSerializer(child=FieldGlobalPolicySerializer(), allow_null=True)


class FabricModelShowResponseVisualizationSerializer(SerializerMixin):
    asset_type = serializers.CharField()
    fabric_policy = FabricPolicySerializer()
    model_display_name = serializers.CharField()
    adapter_confidence = serializers.ListSerializer(child=AdapterConfidenceSerializer())
    field_global_policy = serializers.ListSerializer(child=FieldGlobalPolicySerializer())


class FabricFieldPolicySerializer(SerializerMixin):
    type = serializers.SerializerMethodField("get_type", help_text="类型")
    type_desc = serializers.SerializerMethodField("get_type_desc", help_text="类型描述")
    name = serializers.CharField(help_text="字段名称")
    full_name = serializers.CharField(help_text="字段全名称")
    description = serializers.CharField(help_text="描述信息")
    internal = serializers.BooleanField(help_text="内置字段")
    internal_desc = serializers.SerializerMethodField("get_internal_desc", help_text="内置描述")
    update_time = serializers.DateTimeField(format=DATETIME_FORMAT)
    adapter_confidence = serializers.ListSerializer(child=AdapterConfidenceSerializer())
    fabric_policy = serializers.ListSerializer(child=FieldGlobalPolicySerializer())

    def get_type(self, meta_field):
        return meta_field.get("type").value

    def get_internal_desc(self, meta_field):
        return self.internal_mapper.get(meta_field.get("internal"))

    def get_type_desc(self, meta_field):
        type = meta_field.get("type")
        type_desc = TYPE_NAME_MAPPER.get(meta_field.get("type"))
        if type == MetaFieldType.LIST:
            child = meta_field.children[0]
            child_type_desc = TYPE_NAME_MAPPER.get(child.type, "")
            type_desc = type_desc + ":" + child_type_desc

        return type_desc

    internal_mapper = {True: "是", False: "否"}


class FabricAddFieldDefaultPolicySerializer(SerializerMixin):
    adapter_confidence = serializers.ListSerializer(child=AdapterConfidenceSerializer())
    field_global_policy = serializers.ListSerializer(child=FieldGlobalPolicySerializer())


class DeleteFabricFieldPolicySerializer(SerializerMixin):
    asset_type_id = serializers.CharField()
    full_name = serializers.CharField()
