import copy
import json
import logging
from collections import defaultdict

from bson import ObjectId
from rest_framework import serializers

from caasm_config.config import caasm_config
from caasm_service.entity.meta_model import MetaFieldType, MetaModelType
from caasm_service.runtime import (
    adapter_service,
    convert_visualization_service,
    meta_model_service,
    convert_visualization_relation_service,
    asset_type_service,
)
from caasm_tool.patch.serializer import SerializerMixin, DynamicField
from caasm_tool.util import left_replace, compute_md5, deduplicate
from caasm_webapi.app.data_visualization.serializers.convert_engine_input_parse import convert_engine_input_parser

log = logging.getLogger()


class ConvertVisualizationCommon(object):
    @classmethod
    def parse_field(cls, demo, result, parent_path=""):
        if not demo:
            return

        for field, value in demo.items():
            if parent_path:
                path = parent_path + "." + field
            else:
                path = field

            sub_field_schema = {"field": field, "path": path}
            sub_field_schema.update(cls.__parse_loop(value, parent_path=path))
            result.append(sub_field_schema)

    @classmethod
    def __parse_loop(cls, value, parent_path):
        if isinstance(value, int):
            return {"type": MetaFieldType.INT}
        elif isinstance(value, str):
            return {"type": MetaFieldType.STRING}
        elif isinstance(value, float):
            return {"type": MetaFieldType.FLOAT}
        elif isinstance(value, dict):
            sub_fields = []
            result = {"type": MetaFieldType.OBJECT, "sub_fields": sub_fields}
            cls.parse_field(value, sub_fields, parent_path=parent_path)
            return result
        elif isinstance(value, list):
            sub_fields = []
            result = {"type": MetaFieldType.LIST, "sub_fields": sub_fields}
            path = parent_path + "." + "element"
            if not value:
                sub_fields.append({"type": MetaFieldType.ANY})
            else:
                sub_fields.append(cls.__parse_loop(value[0], path))
            sub_fields[0].update({"field": "element", "path": path})
        else:
            result = {"type": MetaFieldType.ANY, "sub_fields": []}
        return result


class ConvertVisualizationShowSerializer(SerializerMixin):
    adapter_name = serializers.CharField(required=True, help_text="适配器名称")
    fetch_type = serializers.CharField(required=True, help_text="采集类型")
    internal = serializers.BooleanField(required=True, help_text="是否内置")
    model_id = serializers.CharField(required=True, help_text="模型ID", max_length=24, min_length=24)

    def validate_adapter_name(self, adapter_name):
        if not adapter_service.get_adapter_count(adapter_name):
            raise serializers.ValidationError("适配器信息无效")
        return adapter_name

    def validate_model_id(self, model_id):
        if not meta_model_service.get_meta_model_count(model_id=model_id, model_type=MetaModelType.APPLICATION):
            raise serializers.ValidationError("模型信息无效")
        return ObjectId(model_id)


class ConvertVisualizationClearSerializer(SerializerMixin):
    id = serializers.CharField(help_text="画布ID", min_length=24, max_length=24, required=True)


class ConvertVisualizationRuleImportSerializer(SerializerMixin, ConvertVisualizationCommon):
    file = serializers.FileField(max_length=100 * 1024)
    id = serializers.CharField(help_text="画布ID", min_length=24, max_length=24, required=False)

    def validate(self, attrs):
        _file = attrs["file"]
        _id = attrs.get("id")

        try:
            result = json.load(_file)
        except Exception as e:
            log.warning(f"Json dump error({e})")
            raise serializers.ValidationError("文件格式不正确")
        else:
            canvas = result.get("canvas")
            rules = result.get("rules")
            asset_type = result.get("asset_type")
            fetch_type = result.get("fetch_type")
            model_name = result.get("model_name")
            adapter_name = result.get("adapter_name")
            internal = result.get("internal")

            if not isinstance(canvas, dict):
                raise serializers.ValidationError("画布数据无效")

            if not isinstance(rules, list):
                raise serializers.ValidationError("规则信息无效")

            asset_type_id = self._check_asset_type(asset_type)
            if not asset_type_id:
                raise serializers.ValidationError("资产类型无效")

            model_id = self._check_model(model_name)
            if not model_id:
                raise serializers.ValidationError("模型信息无效")

            if not adapter_service.get_adapter_count(adapter_name):
                raise serializers.ValidationError("模型民称无效")
            if _id and not convert_visualization_relation_service.get_convert_visualization_relation_count(
                relation_id=_id
            ):
                raise serializers.ValidationError("数据无效")

            attrs = {
                "canvas": canvas,
                "rules": rules,
                "asset_type_id": asset_type_id,
                "fetch_type": fetch_type,
                "internal": internal,
                "model_id": model_id,
                "adapter_name": adapter_name,
                "id": _id,
                "modify_flag": True,
            }
        return attrs

    @classmethod
    def _check_asset_type(cls, asset_type):
        asset_type_object = asset_type_service.get_asset_type(name=asset_type)
        return asset_type_object.id if asset_type_object else ""

    @classmethod
    def _check_model(cls, model_name):
        meta_model = meta_model_service.get_meta_model(name=model_name)
        return meta_model.id if meta_model else ""


class ConvertVisualizationModifySerializer(ConvertVisualizationClearSerializer, ConvertVisualizationCommon):
    def __init__(self, *args, **kwargs):
        super(ConvertVisualizationModifySerializer, self).__init__(*args, **kwargs)
        self._fields = []
        self._node_mapper = {}
        self._field_node_path_mapper = defaultdict(list)
        self._field_mapper = {}
        self._handle_id_set = set()
        self._for_enter_mapper = {}
        self._ignore_node_types = ("target",)

    asset_type_id = serializers.CharField(help_text="资产类型", required=True)
    canvas = serializers.DictField(help_text="画布数据", required=True)

    def validate_asset_type_id(self, asset_type_id):
        if not asset_type_service.get_asset_type_count(asset_type_id=asset_type_id):
            raise serializers.ValidationError("无效的资产类型")
        return ObjectId(asset_type_id)

    def validate(self, attrs):
        attrs["rules"] = self._parse_rules(attrs)
        attrs["modify_flag"] = True
        return super(ConvertVisualizationModifySerializer, self).validate(attrs)

    def _parse_rules(self, attrs):
        result = []

        _id = attrs["id"]
        canvas = attrs["canvas"]

        self._fields = self._build_field(_id)
        self._build_field_mapper(self._fields)

        if not self._fields:
            return result

        self._build_node_mapper(canvas["nodes"], canvas["edges"])
        rules = self._handle_field(self._fields)

        result = self._clean_enter_rule(rules)
        return result

    def _handle_field(self, fields, for_ignore_title="", root_path="root", index=0):
        result = []
        for field in fields:
            self._parse_rule(field, result, for_ignore_title, root_path, index=index)

        return result

    def _parse_rule(self, field, result, for_ignore_title, root_path, index=0):
        field_path = field["path"]

        node_ids = self._field_node_path_mapper[field_path]

        if not node_ids:
            return

        if len(node_ids) <= index:
            return

        node_id = node_ids[index]

        if node_id not in self._node_mapper:
            return
        node = self._node_mapper[node_id]
        action_nodes = self._parse_action_nodes(node, set())
        self._extract_rule(field, action_nodes, result, for_ignore_title, root_path)

        sub_fields = field.get("sub_fields")
        if not sub_fields:
            return
        sub_rules = self._handle_field(sub_fields, for_ignore_title, root_path=root_path, index=index)
        result.extend(sub_rules)

    def _extract_rule(self, field, action_nodes, result, for_ignore_title, root_path):
        if not action_nodes:
            return
        field_path = field["path"]
        parent_field_path = self._field_mapper[field_path]["parent_field_path"]
        field_type = field["type"]
        title, parent_field_type, new_field_path = "", "", ""
        old_field_path = field_path

        if parent_field_path:
            parent_field = self._field_mapper[parent_field_path]
            parent_field_type = parent_field["type"]
            if parent_field_type == MetaFieldType.OBJECT and parent_field_path != root_path:
                # 加root的原因是因为root对象是模拟出来的
                title = parent_field_path

        for_action_length = len([1 for i in action_nodes if i["action_type"] == "for"])
        for_index = 0

        for action_index, action_node in enumerate(action_nodes):
            action_node_ready = self._check_ready(action_node)
            if action_node_ready:
                action_node_id = action_node["id"]
                if action_node_id in self._handle_id_set:
                    continue
                self._handle_id_set.add(action_node_id)

                action_prev_nodes = action_node["prev_nodes"]

                method_name = action_node["action_type"]
                method_input = action_node["input"]

                action_node_params = []

                for action_last_node in action_prev_nodes:
                    action_node_params.extend(action_last_node["node_params"])

                if parent_field_type == MetaFieldType.LIST and field_type == MetaFieldType.OBJECT:
                    action_node_params = [i for i in action_node_params if i != field_path]

                if method_name == "for":
                    # 先生成一份拷贝的规则
                    if for_action_length > 1:
                        new_field_path = compute_md5(action_node["id"])
                        old_field_path = left_replace(old_field_path, "root.", "", 1)
                        add_rule = {
                            "name": "add",
                            "setting": {
                                "field": new_field_path,
                                "values": [convert_engine_input_parser._build_dynamic_params(old_field_path)],
                                "multi_flag": True,
                            },
                        }
                        result.append(add_rule)
                        action_node_params = [new_field_path]

                    enter_info = self._get_for_enter_info(action_node)
                    if enter_info:
                        next_nodes = self._get_next_nodes(action_node)
                        for next_node in next_nodes:
                            self._for_enter_mapper[next_node] = enter_info

                rule_setting = convert_engine_input_parser.parse_input(
                    method_name, action_node_params, method_input, title, for_ignore_title
                )
                action_node_node_params = convert_engine_input_parser.parse_output(
                    method_name, method_input, title, for_ignore_title
                )
                if method_name == "for" and for_action_length > 1:
                    action_node_node_params = [new_field_path]
                action_node["node_params"] = action_node_node_params

                sub_rules = []
                if method_name == "for":
                    sub_rules = self._parse_sub_rule(field, root_path, index=for_index)
                    for_index += 1

                rule = {"id": action_node_id, "name": method_name, "setting": rule_setting, "sub_rules": sub_rules}

                result.append(rule)
                self._notify_node(field, action_node["prev_nodes"], result, for_ignore_title, root_path)
                self._notify_node(field, action_node["next_nodes"], result, for_ignore_title, root_path)

    def _notify_node(self, field, nodes, result, for_ignore_title, root_path):
        last_action_nodes = [i for i in nodes if i["asset_type"] == "action"]
        self._extract_rule(field, last_action_nodes, result, for_ignore_title, root_path)

    def _parse_sub_rule(self, field, root_path, index):
        field_type = field["type"]
        field_sub_fields = field.get("sub_fields")
        if not field_sub_fields:
            return []
        if field_type != MetaFieldType.LIST:
            return []
        if field_sub_fields[0]["type"] != MetaFieldType.OBJECT:
            return []
        for_sub_field = field_sub_fields[0]
        title = for_sub_field["path"] + "."
        return self._handle_field(field_sub_fields, for_ignore_title=title, root_path=root_path, index=index)

    @classmethod
    def _parse_action_nodes(cls, node, handled_ids):
        next_nodes = node["next_nodes"]
        action_nodes = []

        for prev_node in next_nodes:
            prev_node_type = prev_node["asset_type"]
            prev_node_id = prev_node["id"]

            if prev_node_type == "action":
                if prev_node_id in handled_ids:
                    continue
                action_nodes.append(prev_node)
                handled_ids.add(prev_node_id)
        return action_nodes

    @classmethod
    def _build_field(cls, relation_id):
        relation_fields = ["adapter_name", "fetch_type"]
        relation = convert_visualization_relation_service.get_convert_visualization_relation(
            relation_id=relation_id, fields=relation_fields
        )
        if not relation:
            return []

        fields = []

        data = convert_visualization_service.get_convert_visualization(
            adapter_name=relation.adapter_name, fetch_type=relation.fetch_type, fields=["demo"]
        )
        if not data:
            return []
        cls.parse_field(data.demo, fields)
        root_field = {"field": "root", "path": "root", "sub_fields": fields, "type": MetaFieldType.OBJECT}
        return [root_field]

    def _build_field_mapper(self, fields, parent_field_path=None):
        for field in fields:
            field_path = field["path"]
            field_type = field["type"]
            field_detail = {"type": field_type, "parent_field_path": parent_field_path, "path": field_path}
            self._field_mapper[field_path] = field_detail
            sub_fields = field.get("sub_fields")
            if not sub_fields:
                continue
            self._build_field_mapper(sub_fields, field_path)

    def _build_node_mapper(self, nodes, edges):
        nodes = [i for i in copy.deepcopy(nodes) if i["asset_type"] not in self._ignore_node_types]
        edges = copy.deepcopy(edges)

        for node in nodes:
            node_type = node["asset_type"]

            node_path = node["path"]
            node_id = node["id"]

            node["prev_nodes"] = []
            node["next_nodes"] = []

            node_params = []
            if node_type == "asset":
                if node_path != "root":
                    node_path = left_replace(node["path"], "root.", "", 1)
                    node_params = [node_path]
                    node["path"] = node_path
                self._field_node_path_mapper[node_path].append(node_id)
            node["node_params"] = node_params

        self._node_mapper = {node["id"]: node for node in nodes}

        for edge in edges:
            source = edge["source"]
            target = edge["target"]

            source_node = self._node_mapper.get(source)
            target_node = self._node_mapper.get(target)

            if source_node and target_node:
                source_node["next_nodes"].append(target_node)
                target_node["prev_nodes"].append(source_node)

        for node in nodes:
            node["ready"] = True if node["asset_type"] != "action" else False
        return

    def _clean_enter_rule(self, rules):
        for rule in rules:
            if not rule["name"] == "for":
                continue
            self.__clean_enter_rule_loop(rule["sub_rules"])

        return rules

    def __clean_enter_rule_loop(self, rules):
        for rule in rules:
            rule_id = rule["id"]
            rule_name = rule["name"]
            setting = rule["setting"]
            if rule_name == "enter" and self._for_enter_mapper.get(rule_id):
                parent_rule_enter_title = self._for_enter_mapper[rule_id] + "."
                setting["values"] = setting["values"].replace(parent_rule_enter_title, "")
            else:
                self.__clean_enter_rule_loop(rule["sub_rules"])

    @classmethod
    def _check_ready(cls, node):
        if node["ready"]:
            return True

        prev_nodes = node["prev_nodes"]
        for last_node in prev_nodes:
            if not last_node["ready"]:
                return False
            if not cls._check_ready(last_node):
                return False
        node["ready"] = True
        return True

    @classmethod
    def _get_next_nodes(cls, node, result=None):
        if result is None:
            result = []

        node_id = node["id"]
        result.append(node_id)

        next_nodes = node["next_nodes"]

        for next_node in next_nodes:
            cls._get_next_nodes(next_node, result)
        return result

    @classmethod
    def _get_for_enter_info(cls, action_node):
        if action_node["action_type"] == "enter":
            return action_node["input"]["values"]
        next_action_nodes = [i for i in action_node["next_nodes"] if i["asset_type"] == "action"]
        for next_action_node in next_action_nodes:
            enter_path = cls._get_for_enter_info(next_action_node)
            if not enter_path:
                continue
            return enter_path
        return None


class ConvertVisualizationResponseSerializer(SerializerMixin, ConvertVisualizationCommon):
    id = serializers.CharField(help_text="ID")
    adapter_name = serializers.CharField(help_text="适配器名称")
    model_id = serializers.CharField(help_text="模型ID")
    fetch_type = serializers.CharField(help_text="fetch类型")
    canvas = serializers.DictField(help_text="画布数据")
    demo = serializers.DictField(help_text="样例数据")
    demo_schema = serializers.SerializerMethodField(source="get_demo_schema", help_text="样例数据定义")
    can_modify = serializers.BooleanField(default=False)
    asset_type_id = serializers.CharField(allow_null=True)
    init = serializers.BooleanField(default=None)

    def get_demo_schema(self, data):
        result = []

        self.parse_field(data["demo"], result)
        return result


class ConvertVisualizationMetaModelSerializer(SerializerMixin):
    label = serializers.CharField(source="display_name")
    value = serializers.CharField(source="id")


class AdapterFetahTypeSerializer(SerializerMixin):
    label = serializers.CharField(source="display_name")
    value = serializers.CharField(source="name")


class ConvertVisualizationAdapterSerializer(SerializerMixin):
    name = serializers.CharField()
    displayName = serializers.CharField(source="display_name")
    fetchType = serializers.SerializerMethodField("get_fetch_type", source="fetch_setting")

    def get_fetch_type(self, data):
        result = []
        fetch_type_mapper = data.fetch_setting.fetch_type_mapper
        if not fetch_type_mapper:
            return result
        for key, val in fetch_type_mapper.items():
            result.extend(val)
        result = deduplicate(result)
        fetch_mapper = caasm_config.FETCH_TYPE_MAPPER
        return [{"label": fetch_mapper[i], "value": i} for i in result if fetch_mapper.get(i)]


class BaseDataSerializer(SerializerMixin):
    name = serializers.CharField()
    en_display_name = serializers.CharField()
    display_name = serializers.CharField()
    description = serializers.CharField()
    type = serializers.CharField()
    headers = serializers.ListField()
    src = serializers.ListField()
    must = serializers.BooleanField()
    default = DynamicField(help_text="默认值")
    show = serializers.BooleanField()


class ConvertFuncGetSerializer(SerializerMixin):
    name = serializers.CharField(required=False)


class InputSerializer(SerializerMixin):
    count = serializers.IntegerField()
    complex_type = serializers.DictField()
    data = serializers.ListSerializer(child=BaseDataSerializer())


class OutputSerializer(SerializerMixin):
    count = serializers.IntegerField()
    data = serializers.ListSerializer(child=BaseDataSerializer())


class ConvertFuncSerializer(SerializerMixin):
    name = serializers.CharField()
    en_display_name = serializers.CharField()
    display_name = serializers.CharField()
    classify = serializers.ListSerializer(child=serializers.CharField())
    description = serializers.CharField()
    input = InputSerializer()
    output = OutputSerializer()


class ConvertFuncTypeSerializer(SerializerMixin):
    name = serializers.CharField()
    func_list = serializers.ListSerializer(child=serializers.CharField())
