from caasm_tool.patch.router import DefaultRouter
from caasm_webapi.app.data_visualization.views.asset_type import (
    TotalAssetTypeView,
    AssetTypesAPIView,
    AssetTypeDetailAPIView,
    AssetTypeUpdateAPIView,
    AssetTypeDeleteAPIView,
    AssetTypeCreateAPIView,
)
from caasm_webapi.app.data_visualization.views.convert import (
    ConvertVisualizationShowView,
    ConvertVisualizationModifyView,
    ConvertMetaModelTotalView,
    ConvertVisualizationClearView,
    ConvertAdapterTotalView,
    ConvertFuncView,
    ConvertFuncTypeView,
    ConvertRuleExportView,
    ConvertRuleImportView,
)
from caasm_webapi.app.data_visualization.views.fabric import (
    FabricVisualizationShowView,
    FabricModelInfoVisualizationView,
    FabricModelModifyVisualizationView,
    FabricUniqueIdentificationPolicyView,
    FabricFieldPolicyView,
    FabricFieldPolicyListView,
    SaveFabricFieldPolicyView,
    FabricModelAssetTypeDeleteView,
    AddFabricFieldDefaultPolicyView,
    FabricFieldPolicyDeleteView,
    FabricPolicyDefaultConfigView,
    FabricIpPolicyDefaultConfigView,
)

data_visualization_router = DefaultRouter("dataVisualization")

data_visualization_router.join_url("convertClear/$", ConvertVisualizationClearView)
data_visualization_router.join_url("convertShow/$", ConvertVisualizationShowView)
data_visualization_router.join_url("convertModify/$", ConvertVisualizationModifyView)
data_visualization_router.join_url("convertMetaMode/$", ConvertMetaModelTotalView)
data_visualization_router.join_url("convertAdapter/$", ConvertAdapterTotalView)
data_visualization_router.join_url("convertFunc/$", ConvertFuncView)
data_visualization_router.join_url("convertFuncType/$", ConvertFuncTypeView)
data_visualization_router.join_url("convertRuleExport/$", ConvertRuleExportView)
data_visualization_router.join_url("convertRuleImport/$", ConvertRuleImportView)

data_visualization_router.join_url("totalAssetType/$", TotalAssetTypeView)
data_visualization_router.join_url("fabricShow/$", FabricVisualizationShowView)
data_visualization_router.join_url("fabricModelConfigShow/$", FabricModelInfoVisualizationView)
data_visualization_router.join_url("fabricModelModify/$", FabricModelModifyVisualizationView)
data_visualization_router.join_url("fabricModelConfigDelete/$", FabricModelAssetTypeDeleteView)
data_visualization_router.join_url("fabricUniqueFieldList/$", FabricUniqueIdentificationPolicyView)
data_visualization_router.join_url("fabricFieldPolicy/$", FabricFieldPolicyView)
data_visualization_router.join_url("fabricFieldPolicyList/$", FabricFieldPolicyListView)
data_visualization_router.join_url("fabricFieldPolicyModify/$", SaveFabricFieldPolicyView)
data_visualization_router.join_url("fabricFieldDefaultPolicy/$", AddFabricFieldDefaultPolicyView)
data_visualization_router.join_url("fabricFieldPolicyDelete/$", FabricFieldPolicyDeleteView)
data_visualization_router.join_url("fabricPolicyDefaultConfig/", FabricPolicyDefaultConfigView)
data_visualization_router.join_url("fabricPolicyIpConfig", FabricIpPolicyDefaultConfigView)
data_visualization_router.join_url("assetTypes/", AssetTypesAPIView)
data_visualization_router.join_url("assetType/", AssetTypeDetailAPIView)
data_visualization_router.join_url("assetTypeCreate/", AssetTypeCreateAPIView)
data_visualization_router.join_url("assetTypeUpdate/", AssetTypeUpdateAPIView)
data_visualization_router.join_url("assetTypeDelete/", AssetTypeDeleteAPIView)
