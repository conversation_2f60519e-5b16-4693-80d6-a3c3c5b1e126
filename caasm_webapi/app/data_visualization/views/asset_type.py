import uuid

from rest_framework.request import Request
from rest_framework.views import APIView

from caasm_persistence.handler.storage.model.response import SaveResponse
from caasm_service.entity.asset_type import AssetType
from caasm_service.entity.meta_model import MetaModel
from caasm_service.runtime import asset_type_service, meta_model_service, fabric_model_config_service
from caasm_webapi.app.data_visualization.requests.asset_type import (
    AssetTypeRequest,
    AssetTypeUpdateRequest,
    AssetTypeCreateRequest,
)
from caasm_webapi.app.data_visualization.responses.asset_type import AssetTypeResponse
from caasm_webapi.app.data_visualization.serializers.asset_type import (
    AssetTypeTotalRequestSerializer,
    AssetTypeTotalResponseSerializer,
    AssetTypesRequestSerializer,
    AssetTypeSerializer,
    AssetTypeRequestSerializer,
    AssetTypeUpdateRequestSerializer,
    AssetTypeCreateRequestSerializer,
)
from caasm_webapi.util.response import build_failed, build_success, ResponseCode


class TotalAssetTypeView(APIView):
    _default_sort_fields = [("create_time", -1)]

    def get(self, request):
        serializer = AssetTypeTotalRequestSerializer(data=request.query_params)
        if not serializer.is_valid():
            return build_failed(code=ResponseCode.REQUEST_ERROR, message=serializer)

        model_id = serializer.validated_data.get("modelId")

        asset_types = asset_type_service.find_asset_type(
            model_id=model_id, need_unknown_type=True, sort_fields=self._default_sort_fields
        )

        data = AssetTypeTotalResponseSerializer(instance=asset_types, many=True).data
        return build_success(data)


class AssetTypesAPIView(APIView):
    _DEFAULT_SORT_FIELDS = [("update_time", -1), ("_id", -1)]

    def post(self, request: Request):
        serializer = AssetTypesRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)
        validate_data = serializer.validated_data
        page_index = validate_data.get("page_index")
        page_size = validate_data.get("page_size")
        keyword = validate_data.get("keyword", "")

        count = asset_type_service.get_asset_type_count(keyword=keyword, need_unknown_type=False)
        data = []
        if count:
            cursor = asset_type_service.find_asset_type(
                limit=page_size,
                offset=page_index * page_size,
                keyword=keyword,
                need_unknown_type=False,
                sort_fields=self._DEFAULT_SORT_FIELDS,
            )
            asset_types = list(cursor)

            asset_type_responses = []
            meta_model_mapping = {}

            model_ids = [i.model_id for i in asset_types] if asset_types else []
            meta_models = meta_model_service.find_meta_model(model_ids=model_ids) if model_ids else []
            for meta_model in meta_models:
                meta_model_mapping[meta_model.id] = meta_model

            for asset_type in asset_types:
                meta_model = meta_model_mapping.get(asset_type.model_id)
                asset_type_res = AssetTypeResponse(asset_type, meta_model)
                asset_type_responses.append(asset_type_res)

            data = AssetTypeSerializer(instance=asset_type_responses, many=True).data
        result = {"count": count, "data": data}
        return build_success(result)


class AssetTypeDetailAPIView(APIView):
    def post(self, request: Request):
        serializer = AssetTypeRequestSerializer(data=request.data)
        if serializer.is_valid():
            req: AssetTypeRequest = serializer.save()
            asset_type: AssetType = asset_type_service.get_asset_type(asset_type_id=req.asset_type_id)
            if asset_type is None:
                return build_failed(ResponseCode.REQUEST_ERROR, "指定的资产类型不存在")
            meta_model: MetaModel = meta_model_service.get_meta_model(model_id=asset_type.model_id)
            if meta_model is None:
                return build_failed(ResponseCode.REQUEST_ERROR, "指定的实体模型不存在")
            asset_type_res = AssetTypeResponse(asset_type, meta_model)
            return build_success(data=AssetTypeSerializer(instance=asset_type_res).data)
        return build_failed(ResponseCode.SYSTEM_ERROR, str(serializer.errors))


class AssetTypeCreateAPIView(APIView):
    def post(self, request: Request):
        serializer: AssetTypeCreateRequestSerializer = AssetTypeCreateRequestSerializer(data=request.data)
        if serializer.is_valid():
            req: AssetTypeCreateRequest = serializer.save()
            model = meta_model_service.get_meta_model(req.model_id)
            if model is None:
                return build_failed(ResponseCode.REQUEST_ERROR, "指定的实体模型不存在")
            if asset_type_service.get_asset_type(display_name=req.display_name) is not None:
                return build_failed(ResponseCode.REQUEST_ERROR, "资产类型名称已存在")
            save_res: SaveResponse = asset_type_service.save_asset_type(
                str(uuid.uuid4()).replace("-", ""),
                req.display_name,
                req.model_id,
                internal=False,
                description=req.description,
            )
            if not save_res.flag:
                return build_failed(ResponseCode.REQUEST_ERROR, "新建模型失败")
            asset_type = asset_type_service.get_asset_type(asset_type_id=save_res.inserted_id)
            asset_type_res = AssetTypeResponse(asset_type, model)
            return build_success(AssetTypeSerializer(instance=asset_type_res).data)
        return build_failed(code=ResponseCode.REQUEST_ERROR, message=str(serializer.errors))


class AssetTypeUpdateAPIView(APIView):
    def post(self, request: Request):
        serializer = AssetTypeUpdateRequestSerializer(data=request.data)
        if serializer.is_valid():
            req: AssetTypeUpdateRequest = serializer.save()
            asset_type: AssetType = asset_type_service.get_asset_type(asset_type_id=req.asset_type_id)
            if asset_type is None:
                return build_failed(ResponseCode.REQUEST_ERROR, message="指定的资产类型不存在")
            if asset_type.internal:
                return build_failed(ResponseCode.REQUEST_ERROR, message="内建资产类型不支持修改")
            model: MetaModel = meta_model_service.get_meta_model(model_id=req.model_id)
            if model is None:
                return build_failed(ResponseCode.REQUEST_ERROR, message="指定的实体模型不存在")
            asset_type_service.update_asset_type_info(
                asset_type_id=req.asset_type_id,
                display_name=req.display_name,
                model_id=req.model_id,
                description=req.description,
            )

            # if model.id != asset_type.model_id:
            #     asset_type.model_id = model.id
            #     asset_type_service.update(asset_type)
            # asset_type_res = AssetTypeResponse(
            #     asset_type,
            #     model
            # )
            return build_success()
        return build_failed(code=ResponseCode.REQUEST_ERROR, message=str(serializer.errors))


class AssetTypeDeleteAPIView(APIView):
    def post(self, request: Request):
        serializer = AssetTypeRequestSerializer(data=request.data)
        if serializer.is_valid():
            req: AssetTypeRequest = serializer.save()
            asset_type = asset_type_service.get_asset_type(asset_type_id=req.asset_type_id)
            if asset_type is None:
                return build_failed(code=ResponseCode.REQUEST_ERROR, message="指定的资产类型不存在")
            if asset_type.internal:
                return build_failed(code=ResponseCode.REQUEST_ERROR, message="不能删除内建资产类型")
            if fabric_model_config_service.get_fabric_meta_model_config(asset_type_id=req.asset_type_id) is not None:
                return build_failed(code=ResponseCode.REQUEST_ERROR, message="请先删除资产类型自定义融合策略")
            asset_type_service.delete_asset_type(req.asset_type_id)
            return build_success(data={})
        return build_failed(code=ResponseCode.REQUEST_ERROR, message=str(serializer.errors))
