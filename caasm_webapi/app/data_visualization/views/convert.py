import hashlib
import json
from io import String<PERSON>

from django.http import StreamingHttpResponse
from rest_framework.views import APIView

from caasm_config.config import caasm_config
from caasm_service.constants.adapter import AdapterInnerType
from caasm_service.entity.meta_model import MetaModelType
from caasm_service.runtime import (
    convert_visualization_service,
    meta_model_service,
    adapter_service,
    convert_func_service,
    convert_func_type_service,
    convert_visualization_relation_service,
    asset_type_service,
)
from caasm_tool.http.content_type import JSON
from caasm_tool.util import deduplicate
from caasm_webapi.app.data_visualization.serializers.convert import (
    ConvertVisualizationResponseSerializer,
    ConvertVisualizationMetaModelSerializer,
    ConvertFuncSerializer,
    ConvertFuncGetSerializer,
    ConvertFuncTypeSerializer,
    ConvertVisualizationShowSerializer,
    ConvertVisualizationClearSerializer,
    ConvertVisualizationAdapterSerializer,
    ConvertVisualizationModifySerializer,
    ConvertVisualizationRuleImportSerializer,
)
from caasm_webapi.util.response import build_failed, ResponseCode, build_success, build_response
from caasm_webapi.util.tool import get_user


def _get_user_modify_flag(internal, request):
    if not internal:
        return True
    user = get_user(request)
    usernames = caasm_config.MODIFY_INTERNAL_CANVAS_USERS
    flag = caasm_config.MODIFY_INTERNAL_CANVAS_OPEN
    return bool(flag or (usernames and user.username in usernames))


class ConvertVisualizationShowView(APIView):
    _default_relation_fields = ["canvas", "asset_type_id", "init"]

    @classmethod
    def parse_data(cls, data):
        serializer = ConvertVisualizationShowSerializer(data=data)
        if not serializer.is_valid():
            return False, build_failed(ResponseCode.REQUEST_ERROR, message=serializer)

        validated_data = serializer.validated_data

        adapter_name = validated_data.get("adapter_name")
        fetch_type = validated_data.get("fetch_type")
        internal = validated_data.get("internal")
        model_id = validated_data.get("model_id")
        return True, (adapter_name, fetch_type, internal, model_id, validated_data)

    def get(self, request):
        flag, response = self.parse_data(request.query_params)
        if not flag:
            return response
        adapter_name, fetch_type, internal, model_id, validated_data = response

        if not convert_visualization_relation_service.get_convert_visualization_relation_count(
            adapter_name=adapter_name, fetch_type=fetch_type, internal=internal, model_id=model_id
        ):
            save_res = convert_visualization_relation_service.save_convert_visualization_relation(
                adapter_name, fetch_type, model_id, internal, init=True
            )
            if not save_res.flag:
                return build_failed(ResponseCode.SYSTEM_ERROR, message="获取画布信息失败")
            canvas = None
            convert_visualization_relation_id = save_res.inserted_id
            asset_type_id = None
            init = True
        else:
            convert_visualization_relation = convert_visualization_relation_service.get_convert_visualization_relation(
                adapter_name=adapter_name,
                fetch_type=fetch_type,
                model_id=model_id,
                internal=internal,
                fields=self._default_relation_fields,
            )
            canvas = convert_visualization_relation.canvas
            convert_visualization_relation_id = convert_visualization_relation.id
            asset_type_id = convert_visualization_relation.asset_type_id
            init = convert_visualization_relation.init

        convert_visualization = convert_visualization_service.get_convert_visualization(
            adapter_name=adapter_name, fetch_type=fetch_type, fields=["demo"]
        )
        demo = convert_visualization.demo if convert_visualization else None

        validated_data.update(
            {
                "demo": demo,
                "canvas": canvas,
                "id": convert_visualization_relation_id,
                "can_modify": _get_user_modify_flag(internal, request),
                "asset_type_id": asset_type_id,
                "init": init,
            }
        )
        result = ConvertVisualizationResponseSerializer(instance=validated_data).data
        return build_success(result)

    @classmethod
    def _build_key(cls, validated_data):
        return hashlib.md5(str(validated_data).encode()).hexdigest()


class ConvertVisualizationClearView(APIView):
    _default_relation_fields = ["internal"]

    @classmethod
    def is_can_be_modified(cls, request, relation_id):
        relation = convert_visualization_relation_service.get_convert_visualization_relation(
            relation_id=relation_id, fields=cls._default_relation_fields
        )
        return _get_user_modify_flag(relation.internal, request) if relation else False

    @classmethod
    def handle(cls, validated_data):
        relation_id = validated_data["id"]
        modify_values = {"canvas": None, "rules": [], "init": True}
        modify_res = convert_visualization_relation_service.update_convert_visualization_relation(
            modify_values, relation_id=relation_id
        )
        return build_success(modify_res.modified_count)

    @classmethod
    def check(cls, request):
        serializer = ConvertVisualizationClearSerializer(data=request.data)
        if not serializer.is_valid():
            return False, build_failed(ResponseCode.REQUEST_ERROR, message=serializer)
        return True, serializer

    def post(self, request):
        check_flag, check_data = self.check(request)
        if not check_flag:
            return check_data

        validated_data = check_data.validated_data
        relation_id = validated_data["id"]

        if not self.is_can_be_modified(request, relation_id):
            return build_failed(ResponseCode.REQUEST_ERROR, message="该画布暂不支持修改")

        return self.handle(validated_data)


class ConvertVisualizationModifyView(ConvertVisualizationClearView):
    @classmethod
    def check(cls, request):
        serializer = ConvertVisualizationModifySerializer(data=request.data)
        if not serializer.is_valid():
            return False, build_failed(ResponseCode.REQUEST_ERROR, message=serializer)
        return True, serializer

    @classmethod
    def handle(cls, validated_data):
        relation_id = validated_data.pop("id")
        validated_data["init"] = False
        convert_visualization_relation_service.update_convert_visualization_relation(
            relation_id=relation_id, values=validated_data
        )
        return build_success()


class ConvertMetaModelTotalView(APIView):
    _fields = ["display_name"]

    def get(self, request):
        res = meta_model_service.find_meta_model(init=False, model_type=MetaModelType.APPLICATION, fields=self._fields)
        return build_success(ConvertVisualizationMetaModelSerializer(instance=res, many=True).data)


class ConvertAdapterTotalView(APIView):
    _fields = ["name", "display_name", "fetch_setting"]

    def get(self, request):
        adapters = adapter_service.find_adapter(fields=self._fields, adapter_inner_type_ne=AdapterInnerType.VIRTUAL)
        result = ConvertVisualizationAdapterSerializer(instance=adapters, many=True).data
        return build_success(result)


class ConvertFuncView(APIView):
    def get(self, request):
        serializer = ConvertFuncGetSerializer(data=request.query_params)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)

        validated_data = serializer.validated_data
        display_name = validated_data.get("name")
        result = convert_func_service.find_convert_func(display_name=display_name)
        return build_success(ConvertFuncSerializer(instance=result, many=True).data)


class ConvertFuncTypeView(APIView):
    def get(self, request):
        serializer = ConvertFuncGetSerializer(data=request.query_params)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)

        validated_data = serializer.validated_data
        name = validated_data.get("name")
        result = convert_func_type_service.find_convert_func_type(name=name)
        return build_success(ConvertFuncTypeSerializer(instance=result, many=True).data)


class ConvertRuleExportView(APIView):
    def get(self, request):
        serializer = ConvertVisualizationClearSerializer(data=request.query_params)
        if not serializer.is_valid():
            return build_response(status_code=404, content_type=JSON)

        _id = serializer.validated_data.get("id")

        relation = convert_visualization_relation_service.get_convert_visualization_relation(relation_id=_id)
        if not relation:
            return build_response(status_code=404, content_type=JSON)

        if relation.init:
            return build_response(status_code=400, content_type=JSON)

        canvas = relation.canvas
        rules = relation.rules

        model_id = relation.model_id
        asset_type_id = relation.asset_type_id

        meta_model = meta_model_service.get_meta_model(model_id=model_id)
        asset_type = asset_type_service.get_asset_type(asset_type_id=asset_type_id)

        model_name = meta_model.name if meta_model else ""
        asset_type = asset_type.name if asset_type else ""
        fetch_type = relation.fetch_type
        adapter_name = relation.adapter_name

        result = {
            "canvas": canvas,
            "rules": [rule.as_dict() for rule in rules],
            "adapter_name": adapter_name,
            "fetch_type": fetch_type,
            "model_name": model_name,
            "asset_type": asset_type,
            "internal": relation.internal,
        }

        _file_name = "_".join(deduplicate([adapter_name, fetch_type, model_name, asset_type])) + ".json"
        _io = StringIO()
        json.dump(result, _io, indent=2, ensure_ascii=False)
        _io.seek(0)

        response = StreamingHttpResponse(_io)
        response["Content-Type"] = "application/octet-stream"
        response["Content-Disposition"] = f"attachment; filename={_file_name}"
        return response


class ConvertRuleImportView(APIView):
    def post(self, request):
        serializer = ConvertVisualizationRuleImportSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)
        validate_data = serializer.validated_data
        _id = validate_data.pop("id")

        internal = validate_data["internal"]

        if not _get_user_modify_flag(internal, request):
            return build_failed(ResponseCode.PERMISSION_ERROR)

        if _id:
            validate_data.pop("internal")
            convert_visualization_relation_service.update_convert_visualization_relation(validate_data, relation_id=_id)
            return build_success()

        relation = convert_visualization_relation_service.get_convert_visualization_relation(
            adapter_name=validate_data["adapter_name"],
            fetch_type=validate_data["fetch_type"],
            model_id=validate_data["model_id"],
            internal=validate_data["internal"],
            asset_type_id=validate_data["asset_type_id"],
        )
        if not relation:
            validate_data["init"] = False
            convert_visualization_relation_service.save_convert_visualization_relation(**validate_data)
        else:
            _id = relation.id
            convert_visualization_relation_service.update_convert_visualization_relation(validate_data, relation_id=_id)

        return build_success()
