from caasm_tool.patch.router import DefaultRouter
from caasm_webapi.app.vulnerability.views.vulnerability import (
    VulDetailAPI,
    VulTimeLineDetailAPI,
    AssetAPIView,
    BusinessAPIView,
)

vulnerability_router = DefaultRouter("vulnerability")

vulnerability_router.join_path("vulnerability/", VulDetailAPI, name="VulDetailAPI")
vulnerability_router.join_path("timeline/", VulTimeLineDetailAPI, name="VulTimeLineDetailAPI")
vulnerability_router.join_path("assets/", AssetAPIView, name="AssetAPIView")
vulnerability_router.join_path("businesses/", BusinessAPIView, name="BusinessAPIView")
