from rest_framework import serializers, fields
from rest_framework.serializers import Serializer

from caasm_webapi.util.serializer import SnapshotDateSerializer


class VulDetailSerializer(SnapshotDateSerializer):
    entity_id = serializers.CharField(required=True)


class KeywordRequestSerializer(Serializer):
    keyword = fields.CharField(required=False, default=None)

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        return {"keyword": validated_data["keyword"]}
