from rest_framework.views import APIView

from caasm_meta_data.constants import Category
from caasm_render.runtime import render_manager
from caasm_service.runtime import entity_service, vul_time_line_service
from caasm_tool.util import extract
from caasm_vul.enums import VUL_RESPONSE_STATUS_MAPPING, VulResponseStatus
from caasm_webapi.app.vulnerability.serializers.vulnerability import VulDetailSerializer, KeywordRequestSerializer
from caasm_webapi.util.response import build_success, build_failed, ResponseCode


class VulDetailAPI(APIView):
    _VUL_CVSS3_AV_FIELD = "vul.cvss3"
    _VUL_LAST_MODIFIED_FIELD = "vul.last_modified"
    _VUL_PUBLISHED_FIELD = "vul.published"
    _VUL_CATEGORY_FIELD = "vul.category"

    _BASE_ENTITY_ID_FIELD = "base.entity_id"

    _VUL_INSTANCE_UNIQUE_CVE_ID_FIELD = "vul_instance_unique.cve_id"
    _VUL_INSTANCE_UNIQUE_NAME_FIELD = "vul_instance_unique.name"
    _VUL_INSTANCE_UNIQUE_SOLUTION_FIELD = "vul_instance_unique.solution"
    _VUL_INSTANCE_UNIQUE_DESCRIPTION_FIELD = "vul_instance_unique.description"
    _VUL_INSTANCE_UNIQUE_SEVERITY_FIELD = "vul_instance_unique.severity"
    _VUL_INSTANCE_UNIQUE_TRAITS_FIELS = "vul_instance_unique.traits"
    _VUL_INSTANCE_UNIQUE_STATUS_FIELD = "vul_instance_unique.status"

    _VUL_INSTANCE_UNIQUE_QUERY_FIELDS = [
        _VUL_INSTANCE_UNIQUE_CVE_ID_FIELD,
        _VUL_INSTANCE_UNIQUE_NAME_FIELD,
        _VUL_INSTANCE_UNIQUE_SOLUTION_FIELD,
        _VUL_INSTANCE_UNIQUE_DESCRIPTION_FIELD,
        _VUL_INSTANCE_UNIQUE_SEVERITY_FIELD,
        _VUL_INSTANCE_UNIQUE_TRAITS_FIELS,
        _VUL_INSTANCE_UNIQUE_STATUS_FIELD,
    ]

    _VUL_INSTANCE_UNIQUE_FIELD_DEFINES = [
        ("name", _VUL_INSTANCE_UNIQUE_NAME_FIELD),
        ("cve_id", _VUL_INSTANCE_UNIQUE_CVE_ID_FIELD),
        ("severity", _VUL_INSTANCE_UNIQUE_SEVERITY_FIELD),
        ("status", _VUL_INSTANCE_UNIQUE_STATUS_FIELD),
        ("solution", _VUL_INSTANCE_UNIQUE_SOLUTION_FIELD),
        ("description", _VUL_INSTANCE_UNIQUE_DESCRIPTION_FIELD),
    ]
    _VUL_FIELD_DEFINES = [
        ("published", _VUL_PUBLISHED_FIELD),
        ("last_modified", _VUL_LAST_MODIFIED_FIELD),
        ("category", _VUL_CATEGORY_FIELD),
        ("attack_vector", "vul.cvss_3.av"),
        ("attack_complexity", "vul.cvss_3.ac"),
        ("privileged_required", "vul.cvss_3.pr"),
        ("ui", "vul.cvss_3.ui"),
        ("scope", "vul.cvss_3.s"),
        ("c_impact", "vul.cvss_3.c_impact"),
        ("i_impact", "vul.cvss_3.i_impact"),
        ("a_impact", "vul.cvss_3.a_impact"),
        ("cvss_2_vector", "vul.cvss_2.vector"),
        ("cvss_2_base_score", "vul.cvss_2.base_score"),
        ("cvss_3_vector", "vul.cvss_3.vector"),
        ("cvss_3_base_score", "vul.cvss_3.base_score"),
        ("cnvd_id", "vul.cnvd_id"),
        ("cnnvd_id", "vul.cnnvd_id"),
        ("refs", "vul.refs.url"),
        ("exps", "vul.exploits.url"),
    ]

    def get(self, request):
        serializer = VulDetailSerializer(data=request.query_params)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)

        validated_data = serializer.save()

        entity_id = validated_data.get("entity_id")
        date = validated_data.get("date")

        result = {}
        entity = entity_service.get_entity(
            Category.VUL_INSTANCE_UNIQUE,
            self._BASE_ENTITY_ID_FIELD,
            entity_id,
            date=date,
            fields=self._VUL_INSTANCE_UNIQUE_QUERY_FIELDS,
        )
        if not entity:
            return build_success(result)

        cve_id = extract(entity, self._VUL_INSTANCE_UNIQUE_CVE_ID_FIELD)
        vul = entity_service.get_entity(Category.VUL, "base.entity_id", cve_id) if cve_id else {}

        asset_vul_data = self.parse_asset_vul(entity)
        vul_data = self.parse_vul(vul)

        result = dict(**asset_vul_data, **vul_data)

        if cve_id:
            # TODO 情报没有sources的字段，需要确认下
            result["sources"] = [f"http://cve.mitre.org/cgi-bin/cvename.cgi?name={cve_id}"]
        return build_success(result)

    @classmethod
    def parse_asset_vul(cls, asset_vul_data):
        return cls._parse_common(asset_vul_data, Category.VUL_INSTANCE_UNIQUE, cls._VUL_INSTANCE_UNIQUE_FIELD_DEFINES)

    @classmethod
    def parse_vul(cls, vul_data):
        return cls._parse_common(vul_data, Category.VUL, cls._VUL_FIELD_DEFINES)

    @classmethod
    def _parse_common(cls, data, category, field_defines):
        result = {}
        field_mapper = render_manager.default_query.find_field_to_mapper(category)
        for resp_name, field_name in field_defines:
            field = field_mapper[field_name]
            value = extract(data, field_name)
            tmp_data = render_manager.render(value, field)
            result[resp_name] = tmp_data
        return result


class VulTimeLineDetailAPI(APIView):
    sort_fields = [("date", -1)]

    def get(self, request):
        serializer = VulDetailSerializer(data=request.query_params)
        if not serializer.is_valid():
            return build_failed(ResponseCode.REQUEST_ERROR, message=serializer)

        validated_data = serializer.save()

        vul_id = validated_data.get("entity_id")
        print(vul_id)
        offset = 0
        limit = 100
        temp_result = []
        while True:
            data = list(
                vul_time_line_service.find_vul_timeline(
                    vul_id=vul_id, offset=offset, limit=limit, sort_fields=self.sort_fields
                )
            )
            offset = offset + limit
            if not data:
                break
            temp_result.extend(data)
        result = []
        for item in temp_result:
            result.append(
                {
                    "label": VUL_RESPONSE_STATUS_MAPPING.get(item.status),
                    "date": item.date,
                    "status": VulResponseStatus(item.status).value,
                }
            )
        print(result)
        return build_success(data=result)


class AssetAPIView(APIView):
    def post(self, request):
        serializer = KeywordRequestSerializer(data=request.data)
        if serializer.is_valid():
            keyword = serializer.save()["keyword"]
            if keyword:
                entities = entity_service.find_entity(
                    Category.ASSET,
                    {
                        "bool": {
                            "must": [
                                {
                                    "bool": {
                                        "should": [
                                            {"term": {"base.asset_type": "host"}},
                                            {"term": {"base.asset_type": "terminal"}},
                                        ]
                                    }
                                },
                                {
                                    "script": {
                                        "script": {
                                            "source": f"""
                                            if (doc.containsKey('network.priority_addr')){{
                                                    def value = doc['network.priority_addr'].toString();
                                                    def matchPart = '{keyword}';
                                                    return value.contains(matchPart);
                                                }} else {{
                                                    return false;
                                                }}
                                            """
                                        }
                                    }
                                },
                            ]
                        }
                    },
                    limit=20,
                    fields=["network.priority_addr", "base.entity_id"],
                )
            else:
                entities = entity_service.find_entity(
                    Category.ASSET,
                    {
                        "bool": {
                            "should": [
                                {"term": {"base.asset_type": "host"}},
                                {"term": {"base.asset_type": "terminal"}},
                            ]
                        }
                    },
                    limit=20,
                    fields=["network.priority_addr", "base.entity_id"],
                )
            result = []
            for entity in entities:
                ip = extract(entity, "network.priority_addr")
                entity_id = extract(entity, "base.entity_id")
                if ip and entity_id:
                    result.append({"label": ip, "value": entity_id})
            return build_success(result)
        return build_failed(-1, str(serializer.errors))


class BusinessAPIView(APIView):
    def post(self, request):
        serializer = KeywordRequestSerializer(data=request.data)
        if serializer.is_valid():
            keyword = serializer.save()["keyword"]
            if keyword:
                entities = entity_service.find_entity(
                    Category.BUSINESS,
                    {"bool": {"must": [{"wildcard": {"business.full_name": f"{keyword}*"}}]}},
                    limit=20,
                    fields=["business.full_name", "base.entity_id"],
                )
            else:
                entities = entity_service.find_entity(
                    Category.BUSINESS, limit=20, fields=["business.full_name", "base.entity_id"]
                )
            result = []
            for entity in entities:
                full_name = extract(entity, "business.full_name")
                entity_id = extract(entity, "base.entity_id")
                if full_name and entity_id:
                    result.append({"label": full_name, "value": entity_id})
            return build_success(result)
        return build_failed(-1, str(serializer.errors))
