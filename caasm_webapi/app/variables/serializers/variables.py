import re

from caasm_tool.patch.serializer import SerializerMixin, D<PERSON><PERSON>ield
from rest_framework import serializers


class VariablesModifySerializer(SerializerMixin):
    id = serializers.CharField(required=False)
    name = serializers.CharField(required=True, help_text="名称")
    data_type = serializers.CharField(required=True, help_text="数据类型")
    data_value = DynamicField(help_text="value值")
    description = serializers.CharField(required=False, help_text="描述", default="")
    create_time = serializers.Char<PERSON>ield(required=False, help_text="创建时间")
    update_time = serializers.Char<PERSON>ield(required=False, help_text="更新时间")

    def validate_name(self, data):
        reg = "^[a-zA-Z_][a-zA-Z0-9_]*$"
        if not re.match(reg, data):
            raise serializers.ValidationError("名称请以字母或下划线开头,且只能包含字母、数字、下划线")
        return data


class VariablesSerializer(SerializerMixin):
    id = serializers.Char<PERSON><PERSON>(required=False)
    name = serializers.CharField(required=True, help_text="名称")
    data_type = serializers.CharField(required=True, help_text="数据类型")
    data_value = DynamicField(help_text="value值")
    description = serializers.CharField(required=False, help_text="描述", default="")
    create_time = serializers.CharField(required=False, help_text="创建时间")
    update_time = serializers.CharField(required=False, help_text="更新时间")


class VariablesListSerializer(SerializerMixin):
    keyword = serializers.CharField(required=False, help_text="name关键字搜索", default="")
    data_type = serializers.CharField(required=False, help_text="数据类型搜索", default="")
    page_index = serializers.IntegerField(help_text="页码", default=1, required=False)
    page_size = serializers.IntegerField(help_text="每页最大数", required=False, default=100, max_value=200)
    sort_fields = serializers.CharField(required=False, default=["-update_time"])


class VariablesDeleteSerializer(SerializerMixin):
    variables_ids = serializers.ListField(required=True, help_text="删除变量ID")
