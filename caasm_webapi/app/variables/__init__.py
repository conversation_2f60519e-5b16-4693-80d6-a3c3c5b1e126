from caasm_tool.patch.router import DefaultRouter
from caasm_webapi.app.variables.views.variables import (
    VariableAddAPIView,
    VariableListAPIView,
    VariableUpdateAPIView,
    VariableDeleteAPIView,
)

variable_router = DefaultRouter()


variable_router.join_url(url="variable/variableAdd/", view=VariableAddAPIView, name="variableAdd")

variable_router.join_url(url="variable/variableList/", view=VariableListAPIView, name="variableList")

variable_router.join_url(url="variable/variableUpdate/", view=VariableUpdateAPIView, name="variableUpdate")

variable_router.join_url(url="variable/variableDelete/", view=VariableDeleteAPIView, name="variableDelete")
