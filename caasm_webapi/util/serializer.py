import datetime

from rest_framework import fields, serializers

from caasm_service.runtime import snapshot_record_service
from caasm_tool.constants import DATE_FORMAT_1
from caasm_tool.patch.serializer import SerializerMixin


class SnapshotDateSerializer(SerializerMixin):
    date = fields.CharField(required=False, default=lambda: snapshot_record_service.get_latest_useful_date())

    def validae_date(self, date):
        try:
            datetime.datetime.strptime(date, DATE_FORMAT_1)
        except Exception as e:
            raise serializers.ValidationError("日期格式无效")
        return date
