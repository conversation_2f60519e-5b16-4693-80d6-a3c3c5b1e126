import time
import urllib.parse
import string
import random
import base64
from captcha.image import ImageCaptch<PERSON>


from django.http import HttpResponse

from caasm_config.config import caasm_config
from caasm_webapi.conf.settings import REDIS_CONN


GLOBAL_LETTERS: str = string.digits + string.ascii_letters
IMAGE_PROTOCAL_HEADER: str = "data:image/png;base64,"


class Lock(object):
    def __init__(self, ex=60):
        self._ex = ex
        self._redis_conn = REDIS_CONN
        self._timestamp = int(time.time())
        self._locks = []

    def __exit__(self, exc_type, exc_val, exc_tb):
        for lock in self._locks:
            self._redis_conn.delete(lock)

    def __enter__(self):
        return self

    def acquire(self, lock_name, suffix=None):
        lock_name = self._build_lock_name(lock_name, suffix)
        result = self._redis_conn.set(lock_name, self._timestamp, ex=self._ex, nx=True)
        if result:
            self._locks.append(lock_name)
        return result

    @classmethod
    def _build_lock_name(cls, lock_name, suffix):
        return f"Lock-{lock_name}-{suffix}"


def get_user(request):
    return request.user if hasattr(request, "user") else None


def get_user_id(request):
    user = get_user(request)
    if not user:
        return None
    return user.user_id


def build_file_response(file):
    response = HttpResponse(file)
    import magic

    mine_type = magic.from_buffer(file.read(), mime=True)
    if not mine_type or mine_type == "empty":
        mine_type = "application/octet-stream"
    response["Content-Type"] = mine_type  # 设置头信息，告诉浏览器这是个文件
    if file.filename:
        response["Content-Disposition"] = f'filename="{file.filename}"'
    return response


def get_ip(request):
    x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
    if x_forwarded_for:
        ip = x_forwarded_for.split(",")[0]
    else:
        ip = request.META.get("REMOTE_ADDR")
    return ip


def is_api_call(request):
    prefix_path = "/" + urllib.parse.urljoin(caasm_config.API_URL_PREFIX, "external")
    return request.path.startswith(prefix_path)


def generate_random_string(length: int = 4) -> str:
    """
    随机生成由小写、大写字母和数字组成的字符串
    """
    res: list = []
    for i in range(length):
        res.append(GLOBAL_LETTERS[random.randint(0, len(GLOBAL_LETTERS) - 1)])
    result: str = "".join(res)
    return result


def generate_captcha(letter: str) -> str:
    """
    使用letter字符串生成图片
    """
    image = ImageCaptcha()
    content = image.generate(letter)
    return IMAGE_PROTOCAL_HEADER + base64.b64encode(content.read()).decode("utf-8")
