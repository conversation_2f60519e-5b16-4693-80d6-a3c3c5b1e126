import hashlib
import logging

from django.core.cache import cache
from django.utils.deprecation import MiddlewareMixin

from caasm_config.config import caasm_config
from caasm_service.runtime import user_service, role_service, menu_service
from caasm_tool.util import get_class_path
from caasm_webapi.util.response import build_failed, ResponseCode
from caasm_webapi.util.tool import is_api_call

log = logging.getLogger()


class PermissionMiddleware(MiddlewareMixin):
    _cache_ttl = 30
    _cache_prefix = "CaasmWEBAPICached"

    def process_view(self, request, callback, callback_args, callback_kwargs):
        if not caasm_config.AUTH_PERMISSION_CHECK:
            return
        if self.need_check_permission(request):
            if not self.check_permission(request, callback):
                return build_failed(ResponseCode.PERMISSION_ERROR)

    @classmethod
    def need_check_permission(cls, request):
        if is_api_call(request):
            return False
        return True if hasattr(request, "_user") else False

    @classmethod
    def check_permission(cls, request, callback):

        if not hasattr(callback, "cls"):
            log.warning("Please use APIVIEW implement interface")
            return False
        if not hasattr(request, "_user"):
            return True

        permission_code = get_class_path(callback.cls)
        if permission_code in caasm_config.PERMISSION_CODE_WHITE_LIST:
            return True

        user = request._user
        user_id = user.user_id
        is_super = user.is_super
        if is_super:
            return True

        cache_key = cls._build_cache_key(user_id, permission_code)

        value = cache.get(cache_key)
        if value is None:
            value = cls._check_direct(user_id, permission_code)
            cache.set(cache_key, value, cls._cache_ttl)
        return value

    @classmethod
    def _build_cache_key(cls, user_id, permission_code):
        caches = [cls._cache_prefix, user_id, permission_code]

        return hashlib.md5(",".join(map(str, caches)).encode()).hexdigest()

    @classmethod
    def _check_direct(cls, user_id, permission_code):
        role_codes = set()
        for i in role_service.find_role(permission_code=permission_code, fields=["code"]):
            role_codes.add(i.code)
        if not role_codes:
            menu_code = list(menu_service.find_menu(permission_codes=[permission_code]))
            if menu_code:
                log.debug(f"User({user_id}) not bind any roles")
                return
            return True

        role_codes = list(role_codes)
        count = user_service.get_user_count(user_id=user_id, role_codes=role_codes)

        if not count:
            log.debug(f"User({user_id}) no permission({permission_code})")
            return
        return True
