import hashlib
import json
import logging
import time
import traceback

from django.core.cache import cache
from django.middleware.common import MiddlewareMixin
from django.urls import get_resolver, URLPattern, URLResolver, resolve, Resolver404
from rest_framework.request import Request
from rest_framework.response import Response

from caasm_config.config import caasm_config
from caasm_service.runtime import operation_log_service, user_service
from caasm_tool.util import get_class_path
from caasm_webapi.util.tool import get_ip, is_api_call

log = logging.getLogger()


def get_all_url(resolver=None, pre="/"):
    """
    获取所有的url
    :param resolver:
    :param pre:
    :return:
    """

    if resolver is None:
        resolver = get_resolver()
    for r in resolver.url_patterns:
        if isinstance(r, URLPattern):
            if "<pk>" in str(r.pattern):
                continue
            yield pre + str(r.pattern).replace("^", "").replace("$", ""), r.name
        if isinstance(r, URLResolver):
            yield from get_all_url(r, pre + str(r.pattern))


def get_all_url_action_map():
    """
    获取所有路径和动作之间的映射
    :return: 映射表单
    """
    result = {}
    for url, name in get_all_url():
        try:
            class_path = get_class_path(resolve(url).func.cls)
            result.update({class_path: name})
        except Resolver404:
            log.error("request path resole error ,path :{}".format(url))
    return result


class LogMiddleware(MiddlewareMixin):
    _cache_ttl = 60
    _cache_prefix = "CaasmLogCached"

    def __init__(self, get_response=None):
        super(LogMiddleware, self).__init__(get_response)
        self.start_time = 0
        self.end_time = 0
        self.data = {}
        self.automatic_data = ""
        self.get_response = get_response

    def process_request(self, request):
        if is_api_call(request):
            return

        if request.method == "GET":
            self.automatic_data = request.GET
        else:
            try:
                self.automatic_data = request.body.decode("utf-8")
            except Exception:
                self.automatic_data = request.POST

    def process_response(self, request: Request, response: Response):
        """
        请求记录 -- 操作审计日志
        :param response:
        :param request:
        :return:
        """
        if is_api_call(request):
            return response

        self.start_time = time.time()  # 开始时间
        re_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())  # 请求时间

        class_path = None
        try:
            try:
                class_path = get_class_path(resolve(request.path).func.cls)
                log.debug("requests url is :{}     code is : {}".format(request.path, class_path))
            except Resolver404:
                log.error("request path resole error ,path :{}".format(request.path))

            # 需要先判断操作请求  如果不在黑名单中则忽略
            if class_path not in caasm_config.OPERATION_LOG_BLACKLIST_URL:
                return response

            re_ip = get_ip(request)
            re_method = request.method
            re_content = self.automatic_data

            if re_content:
                re_content = json.loads(re_content)
            else:
                re_content = {}

            code = response.status_code
            response_data = str(response.content, "utf-8")

            if code == 200:
                response_json_data = json.loads(response_data)
            else:
                response_json_data = {}

            response_code = response_json_data.get("code", 1)

            ## username
            username = (
                re_content.get("settings")["username"] if not hasattr(request, "_user") else request._user.username
            )

            ## role
            role = self.get_cache_role(username)

            self.data.update(
                {
                    "time": re_time,
                    "timestamp": str(self.start_time * 1000),  # 请求时间
                    "url": request.path,  # 请求url
                    "method": re_method,  # 请求方法
                    "ip": re_ip,  # 请求IP
                    "role": role,
                    "response_code": response_code,
                    "response_code_text": "成功" if response_code == 0 else "失败",
                    "user_name": username,
                    "action": response_json_data.pop("audit_log", "未知操作"),
                    "code": code,
                }
            )
            operation_log_service.save_operation_log(log_record=self.data)
            self.end_time = time.time()
            log.info(
                "operation_log save :start_time: {} ,end_time:{}, use time :{}".format(
                    self.start_time, self.end_time, self.end_time - self.start_time
                )
            )
            response.content = json.dumps(response_json_data)
        except Exception as e:
            log.debug(traceback.format_exc())
            log.error(f"save operation log error({e})")
            return response

        return response

    @classmethod
    def get_user_role(cls, username):
        user_info = user_service.get_user(username=username, fields=["is_super", "role_codes"])
        if user_info:
            if user_info.is_super:
                role = ["超级管理员"]
            else:
                role = user_info.role_codes
        else:
            role = ["未知角色"]
        return role

    @classmethod
    def _build_cache_key(cls, username):
        caches = [cls._cache_prefix, username]

        return hashlib.md5(",".join(map(str, caches)).encode()).hexdigest()

    def get_cache_role(cls, username):
        cache_key = cls._build_cache_key(username)
        role = cache.get(cache_key)
        if role is None:
            role = cls.get_user_role(username)
            cache.set(cache_key, role, cls._cache_ttl)
        return role
