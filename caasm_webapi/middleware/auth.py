import hashlib
import json
import uuid

import requests
from django.utils.deprecation import MiddlewareMixin

from caasm_config.config import caasm_config
from caasm_service.runtime import user_service
from caasm_tool.util import get_class_path
from caasm_webapi.auth import get_auth
from caasm_webapi.conf.settings import REDIS_CONN
from caasm_webapi.util.response import build_failed, ResponseCode
from caasm_webapi.util.token import token_service
from caasm_webapi.util.tool import is_api_call


class AuthMiddleware(MiddlewareMixin):
    def process_view(self, request, callback, callback_args, callback_kwargs):
        if not self._need_check_session(request, callback):
            return
        cookie = request.META.get("HTTP_COOKIE")
        token = ""
        if cookie:
            for str_cookie in cookie.split("; "):
                if str_cookie.startswith("TOKEN="):
                    token = str_cookie[6:]
        if token and caasm_config.SINGLE_LOGIN:
            if self.single_sign_on(request=request, token=token):
                return
            else:
                return build_failed(ResponseCode.NOT_LOGIN)

        token = self.get_session_id(request)

        auth_type = token_service.get_token_auth_type(token)
        auth = get_auth(auth_type)

        if not auth:
            #  正常情况下，不会走到这里，除非改REDIS了
            return build_failed(ResponseCode.NOT_LOGIN)

        if not auth.check_token(token):

            return build_failed(ResponseCode.NOT_LOGIN)

        auth.refresh(token)
        user = auth.build_token_detail(token)

        setattr(request, "_user", user)
        setattr(request, "need_refresh", True)
        setattr(request, "token", token)

    def single_login_verification(self):
        pass

    @classmethod
    def generate_user_token(cls, user_id):
        return hashlib.md5(f"{user_id}-{uuid.uuid4()}".encode()).hexdigest()

    @classmethod
    def single_sign_on(cls, request, token):
        headers = {"Cookie": request.META.get("HTTP_COOKIE")}
        referer = caasm_config.SINGLE_LOGIN_SETTING.get("setting").get("referer_url")
        if referer:
            headers["Referer"] = referer
        r = requests.get(caasm_config.SINGLE_LOGIN_SETTING.get("setting").get("url"), headers=headers, verify=False)
        data = json.loads(r.content)
        if data.get(
            caasm_config.SINGLE_LOGIN_SETTING.get("setting").get("flag_key_name")
        ) == caasm_config.SINGLE_LOGIN_SETTING.get("setting").get("suc_flag"):
            cls.setting_token(request)
            return True
        else:
            return False

    @classmethod
    def _need_check_session(cls, request, callback):
        if is_api_call(request):
            return False

        if caasm_config.API_DEBUG:
            return False

        code = get_class_path(callback.cls)
        if code in caasm_config.AUTH_NOT_CHECK_CODES:
            return False

        return True

    @classmethod
    def get_session_id(cls, request):
        session_id = request.COOKIES.get(caasm_config.AUTH_COOKIE_NAME)
        return session_id

    def process_response(self, request, response):
        if hasattr(request, "need_refresh"):
            expire_time = caasm_config.AUTH_COOKIE_EXPIRE
            response.set_cookie(
                caasm_config.AUTH_COOKIE_NAME,
                request.token,
                httponly=True,
                expires=expire_time,
            )
        return response

    @classmethod
    def setting_token(cls, request):
        user = user_service.get_user(username="caasm_admin")
        user_id = user.id
        token = cls.generate_user_token(user)
        cls.append_user_token(user_id, token)
        cls.record_user_info(token, user, caasm_config.AUTH_COOKIE_EXPIRE)
        token_service.create_token(token, "password", 800000)
        auth = get_auth("password")
        auth.refresh(token)
        user = auth.build_token_detail(token)

        setattr(request, "_user", user)
        setattr(request, "need_refresh", True)
        setattr(request, "token", token)

    @classmethod
    def build_user_id_token(cls, token):
        return f"AuthUserId-{token}"

    @classmethod
    def build_user_token_set_name(cls, user_id):
        return f"AuthUserTokens-{user_id}"

    @classmethod
    def build_user_detail_token(cls, user_id):
        return f"AuthUserDetail-{user_id}"

    @classmethod
    def append_user_token(cls, user_id, token):
        user_token_set_name = cls.build_user_token_set_name(user_id)
        REDIS_CONN.sadd(user_token_set_name, token)

    @classmethod
    def refresh_user_info(cls, user, expire=None):
        user_str = json.dumps(user.as_dict(), default=str)
        user_detail_token_name = cls.build_user_detail_token(user.id)
        REDIS_CONN.set(user_detail_token_name, user_str, ex=expire)

    @classmethod
    def record_user_info(cls, token, user, expire):
        user_id_token_name = cls.build_user_id_token(token)
        REDIS_CONN.set(user_id_token_name, str(user.id), ex=expire)
        cls.refresh_user_info(user, expire)
