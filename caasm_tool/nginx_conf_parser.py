import re
import urllib.parse as urllib2
from collections import defaultdict
from dataclasses import dataclass, field
from functools import cached_property
from typing import List


@dataclass
class UpStream(object):
    addresses: List[str] = field(default_factory=list)
    name: str = ""


@dataclass
class Location(object):
    path: str
    upstream: UpStream
    protocol: str
    url: str = field(default_factory=lambda: "")


@dataclass
class ServerPort(object):
    port: int
    protocol: str


@dataclass
class Server(object):
    server_ports: List[ServerPort] = field(default_factory=list)
    content: str = field(repr=False, default_factory=lambda: "")
    server_names: List[str] = field(default_factory=list)
    locations: List[Location] = field(default_factory=list)
    protocol: str = field(default_factory=lambda: "")


class NginxConfParser(object):
    _SEP_FLAG = "\n"
    _NOTE_FLAG = "#"

    _HTTP = "http"
    _HTTPS = "https"

    _LISTEN_FLAG = "listen"
    _SSL_FLAG = "ssl"
    _SSL_OPEN_FLAG = "on"
    _DEFAULT_SERVER_NAME = "default_server"

    _SERVER_RE = re.compile(r"server\s*{")
    _PORT_RE = re.compile(r"\d+")
    _SERVER_NAME_RE = re.compile(r"server\s*{[^}]*\bserver_name\s+(.*?);")
    _UPSTREAM_RE = re.compile(r"upstream\s+(\w+(?:-\w+)?)\s*{([^}]*)}")
    _UPSTREAM_SERVER_RE = re.compile(r"server\s+([^;\s]+)")
    _LOCATION_RE = re.compile(r"location\s+[^{]*{(?:[^{}]*{[^{}]*})*[^{}]*}")
    _LOCATION_PATH_RE = re.compile(r"(?s)(?<=location\s).*?(?=\s*{)")
    _LOCATION_PROXY_PASS_RE = re.compile(r"proxy_pass\s+(.*?);")

    def __init__(self, content):
        self._content = content
        self._servers = []
        self._upstream_mapper = {}

    def parse(self):
        self._filter()
        self._parse_upstream()
        self._parse_server()
        return self._servers

    def _parse_upstream(self):
        matches = self._UPSTREAM_RE.findall(self._content)
        for match in matches:
            name = match[0]
            address = self._UPSTREAM_SERVER_RE.findall(match[1])
            self._upstream_mapper[name] = UpStream(name=name, addresses=address)

    def _parse_server(self):
        self._servers = self._extract_server()

        for server in self._servers:
            self._extract_location(server)

    def _extract_location(self, server):
        server_content = server.content
        matches = self._LOCATION_RE.findall(server_content)
        locations = []

        for location_content in matches:
            path = self._extract_location_path(location_content)
            proxy_pass_info = self._extract_location_proxy(location_content)
            if proxy_pass_info:
                upstream, protocol, url = proxy_pass_info
            else:
                upstream, protocol, url = "", server.protocol, ""
            location = Location(upstream=upstream, protocol=protocol, path=path, url=url)
            locations.append(location)
        server.locations = locations

    def _extract_location_path(self, location):
        matches = self._LOCATION_PATH_RE.findall(location)
        return matches[0] if matches else ""

    def _extract_location_proxy(self, location):
        matches = self._LOCATION_PROXY_PASS_RE.findall(location, re.DOTALL)
        proxy_pass = matches[0] if matches else ""

        if not proxy_pass:
            return

        parse_result = urllib2.urlparse(proxy_pass)
        hostname = parse_result.hostname
        port = parse_result.port
        url = parse_result.path

        upstream = self._upstream_mapper.get(hostname)
        if not upstream:
            address = f"{hostname}:{port}" if port is not None else hostname
            upstream = UpStream(addresses=[address])
        return upstream, parse_result.scheme, url

    def _filter(self):
        self._filter_note()

    def _filter_note(self):
        self._content = self._SEP_FLAG.join(
            [content for content in self._content.split(self._SEP_FLAG) if not content.startswith(self._NOTE_FLAG)]
        )

    def _extract_server(self):
        matches = self._SERVER_RE.finditer(self._content)

        stack, servers = [], []

        for match in matches:
            start_pos = match.start()
            stack.append(start_pos)

        while stack:
            start_pos = stack.pop()
            end_pos = start_pos + 1
            depth = 1
            while depth > 0 and end_pos < len(self._content):
                if self._content[end_pos] == "{":
                    depth += 1
                elif self._content[end_pos] == "}":
                    depth -= 1
                end_pos += 1
            content = self._content[start_pos:end_pos]

            ssl_flag = self._extract_server_global_ssl(content)
            server_ports = self._extract_server_port(content, ssl_flag)
            server_name = self._extract_server_name(content)

            protocol_mapper = defaultdict(list)
            for server_port in server_ports:
                protocol_mapper[server_port.protocol].append(server_port)

            for protocol, server_ports in protocol_mapper.items():
                server = Server(content=content, server_ports=server_ports, server_names=server_name, protocol=protocol)
                servers.append(server)

        return servers

    def _extract_server_port(self, server_content, ssl_flag):
        ports = []
        for detail in server_content.split(self._SEP_FLAG):
            detail = detail.strip()
            if not detail.startswith(self._LISTEN_FLAG):
                continue

            match_result = self._PORT_RE.findall(detail)
            if not match_result:
                continue

            tmp_ssl_flag = ssl_flag
            if self._SSL_FLAG in detail:
                tmp_ssl_flag = True

            protocol = self._extract_protocol(tmp_ssl_flag)
            server_port = ServerPort(port=int(match_result[0]), protocol=protocol)
            ports.append(server_port)
        return ports

    def _extract_server_name(self, server_content):
        server_names = set()
        matches = self._SERVER_NAME_RE.findall(server_content)
        for match in matches:
            server_names.update(match.split())
        return [server_name for server_name in list(server_names) if server_name != self._DEFAULT_SERVER_NAME]

    def _extract_server_global_ssl(self, server_content):
        for detail in server_content.split(self._SEP_FLAG):
            detail = detail.strip()
            if not detail.startswith(self._SSL_FLAG):
                continue
            if detail.split()[-1] == self._SSL_OPEN_FLAG:
                return True
        return False

    def _extract_protocol(self, ssl_flag):
        return self._HTTPS if ssl_flag else self._HTTP

    @property
    def servers(self):
        return self._servers

    @cached_property
    def upstreams(self):
        return list(self._upstream_mapper.values())
