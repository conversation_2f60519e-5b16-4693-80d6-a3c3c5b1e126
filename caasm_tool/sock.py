from urllib.parse import urlparse

import socks

from caasm_tool.constants import PROTOCOL_DEFAULT_MAPPER


def get_socks5(address, proxy=None, timeout=None):
    sock = socks.socksocket()
    sock.settimeout(timeout) if timeout else ...
    _, host, port = parse_address(address)

    if proxy:
        _, proxy_host, proxy_port = parse_address(proxy)
        sock.setproxy(socks.PROXY_TYPE_SOCKS5, proxy_host, proxy_port)
    sock.connect((host, port))
    return sock


def parse_address(address):
    _url = urlparse(address)
    scheme = _url.scheme
    hostname = _url.hostname
    port = _url.port
    if not port:
        port = PROTOCOL_DEFAULT_MAPPER.get(scheme)
    return scheme, hostname, port
