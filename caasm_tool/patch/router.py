import copy
import urllib.parse as urllib2

from django.urls import re_path, path
from rest_framework import routers


class Default<PERSON><PERSON>er(routers.DefaultRouter):
    """
    Extends `DefaultRouter` class to add a method for extending url routes from another router.
    """

    def __init__(self, prefix=None, *args, **kwargs):
        super(DefaultRouter, self).__init__(*args, **kwargs)
        self._default_urls = []
        self._default_url_mapper = {}
        if prefix:
            if prefix.startswith("/"):
                prefix = prefix[1:]

            if not prefix.endswith("/"):
                prefix = prefix + "/"
        self._prefix = prefix

    def extend(self, router):

        self.registry.extend(router.registry)
        if self._prefix:
            my_url_define = copy.deepcopy(router.my_url_define)

            router.my_urls = []
            router.my_url_define = {}

            for _url, (view, name, flag) in my_url_define.items():
                if flag == "url":
                    router.join_url(_url, view, name, self._prefix)
                else:
                    router.join_path(_url, view, name, self._prefix)

        self._default_urls.extend(router.my_urls)

    def join_url(self, url, view, name=None, prefix=None):
        url = self._build_url(url, prefix)
        self._default_urls.append(re_path(url, view.as_view(), name=name))
        self._default_url_mapper[url] = (view, name, "url")

    def join_path(self, url, view, name=None, prefix=None):
        url = self._build_url(url, prefix)
        self._default_urls.append(path(url, view.as_view(), name=name))
        self._default_url_mapper[url] = (view, name, "path")

    def get_urls(self):
        ori_urls = super(DefaultRouter, self).get_urls()
        return ori_urls + self._default_urls

    def _build_url(self, url, prefix=None):
        url = self._build_url_core(url, self._prefix)
        return self._build_url_core(url, prefix)

    @classmethod
    def _build_url_core(cls, url, prefix=None):
        if prefix:
            if url.startswith("/"):
                url = url[1:]
            url = urllib2.urljoin(prefix, url)
        return url

    @property
    def my_urls(self):
        return self._default_urls

    @property
    def my_url_define(self):
        return self._default_url_mapper

    @my_urls.setter
    def my_urls(self, urls):
        self._default_urls = urls

    @my_url_define.setter
    def my_url_define(self, url_define):
        self._default_url_mapper = url_define
