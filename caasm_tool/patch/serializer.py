from rest_framework import serializers


class SerializerMixin(serializers.Serializer):
    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        return validated_data

    def validate(self, attrs):
        if "page_index" in attrs:
            attrs["page_index"] = attrs["page_index"] - 1

        if "sort_fields" in attrs:
            if not attrs["sort_fields"]:
                # 走默认
                attrs["sort_fields"] = ["-create_time", "-_id"]

            sort_fields = attrs.get("sort_fields", [])
            new_sort_fields = []

            for sort_field in sort_fields:
                if sort_field.startswith("-"):
                    new_sort_fields.append((sort_field[1:], -1))
                else:
                    new_sort_fields.append((sort_field, 1))

            attrs["sort_fields"] = new_sort_fields

        return attrs


class DynamicField(serializers.Field):
    def to_internal_value(self, data):
        return data

    def to_representation(self, value):
        return value
