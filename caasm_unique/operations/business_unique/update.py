from caasm_businesses.enums import MAGNITUDE_MAPPING, GRADE_PROTECTION_LEVEL_MAPPING
from caasm_meta_data.constants import Category
from caasm_service.entity.business_poraiter import BusinessPortrait, Magnitude, GradeProtectionLevel
from caasm_service.runtime import business_portraiter_service, entity_service, business_portrait_update_service
from caasm_tool.util import restore, extract
from caasm_unique.operation import UniqueOperation, OperationType


class BusinessUniqueUpdateOperation(UniqueOperation):
    @property
    def category(self):
        return Category.BUSINESS_UNIQUE

    @property
    def type(self):
        return OperationType.UPDATE

    def execute(self, data):
        if "id" not in data:
            return False, ""
        id_ = data["id"]
        magnitude_enum = None
        magnitude = data.get("magnitude")
        if magnitude is not None:
            try:
                magnitude_enum = Magnitude(magnitude)
            except ValueError:
                return False, ""
        grade_protection_level_enum = None
        grade_protection_level = data.get("grade_protection_level")
        if grade_protection_level is not None:
            try:
                grade_protection_level_enum = GradeProtectionLevel(grade_protection_level)
            except ValueError:
                return False, ""
        if magnitude_enum is None and grade_protection_level_enum is None:
            return False, ""
        portrait: BusinessPortrait = business_portraiter_service.get_business_portraiter(id_)
        if not portrait:
            return False, ""
        updates = []
        portrait_update = {}
        if magnitude is not None:
            portrait_update["magnitude"] = magnitude
            updates.append({"field": "magnitude", "value": magnitude})
        if grade_protection_level is not None:
            portrait_update["grade_protection_level"] = grade_protection_level
            updates.append({"field": "grade_protection_level", "value": grade_protection_level})
        if portrait_update:
            business_portraiter_service.update_by_id(portrait.id, portrait_update)
        business_entity = entity_service.get_entity(Category.BUSINESS, "base.entity_id", portrait.business_id)
        if not business_entity:
            return True, ""
        update = {"_id": extract(business_entity, "_id")}
        if magnitude_enum is not None:
            restore("business.magnitude", {"text": MAGNITUDE_MAPPING.get(magnitude_enum), "value": magnitude}, update)
        if grade_protection_level_enum is not None:
            restore(
                "business.grade_protection_level",
                {
                    "text": GRADE_PROTECTION_LEVEL_MAPPING.get(grade_protection_level_enum),
                    "value": grade_protection_level,
                },
                update,
            )
        if update:
            entity_service.update_stream_direct([update], table=entity_service.get_table(Category.BUSINESS))
        business_portrait_update_service.save_update_direct({"entity_id": portrait.business_id, "updates": updates})
        entity_service.refresh_entity(Category.BUSINESS)
        return True, ""
