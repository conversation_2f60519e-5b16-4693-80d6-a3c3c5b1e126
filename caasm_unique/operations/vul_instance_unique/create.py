from datetime import datetime

from caasm_enforcement.handlers.basic.create_query_template import CreateQueryTemplateHandler
from caasm_meta_data.constants import Category
from caasm_service.runtime import entity_service
from caasm_tool.util import extract, restore
from caasm_unique.operation import UniqueOperation, OperationType
from caasm_vul.enums import VulSeverity, VUL_SEVERITY_MAPPING
from caasm_vul.id_maker import make_vul_id


class VulInstanceUniqueCreateOperation(UniqueOperation):
    SOURCE_MAPPING = {1: "渗透测试", 2: "监管下发", 3: "漏扫导入"}

    @property
    def category(self):
        return Category.VUL_INSTANCE_UNIQUE

    @property
    def type(self):
        return OperationType.CREATE

    def execute(self, data):
        if not isinstance(data, dict):
            return False, ""
        if "name" not in data:
            return False, ""
        if "url" not in data:
            return False
        if "source" not in data:
            return False, ""
        if "severity" not in data:
            return False, ""
        if "exposure" not in data:
            return False, ""
        name = data["name"]
        description = data.get("description")
        solution = data.get("solution")
        url = data.get("url")
        source = data["source"]
        severity = data["severity"]
        exposure = data["exposure"]
        asset = data.get("asset")
        business = data.get("business")
        if source not in self.SOURCE_MAPPING:
            return False, ""
        try:
            vul_severity = VulSeverity(severity)
        except ValueError:
            return False, ""
        if not isinstance(exposure, bool):
            return False, ""

        CreateQueryTemplateHandler(Category.VUL_INSTANCE_UNIQUE).execute()

        vul_id = make_vul_id({"name": name, "description": description, "solution": solution, "url": url})
        vul_unique = entity_service.get_entity(Category.VUL_INSTANCE_UNIQUE, "base.entity_id", vul_id)
        if vul_unique:
            return False, "漏洞已存在，不能重复创建"

        vul_unique = {
            "base": {
                "entity_id": vul_id,
                "last_seen": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "first_seen": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "adapter_name": "",
                "adapter_count": 0,
                "asset_type": "vul_instance_unique",
                "adapters": [],
            },
            "vul_instance_unique": {
                "name": name,
                "status": {"value": 0, "text": "活动"},
                "description": description,
                "solution": solution,
                "url": url,
                "source": {"value": source, "text": self.SOURCE_MAPPING.get(source)},
                "severity": {"value": severity, "text": VUL_SEVERITY_MAPPING.get(vul_severity)},
                "tags": [{"key": "互联网暴露", "value": "是" if exposure else "否"}],
                "create_date": datetime.now().strftime("%Y-%m-%d"),
            },
        }

        if asset:
            asset_entity = entity_service.get_entity(
                Category.ASSET,
                "base.entity_id",
                asset,
            )
            if asset_entity:
                ip = extract(asset_entity, "network.priority_addr")
                if ip:
                    restore("vul_instance_unique.asset", {"display_value": ip, "rel_id": asset}, vul_unique)
        if business:
            business_entity = entity_service.get_entity(
                Category.BUSINESS,
                "base.entity_id",
                business,
            )
            if business_entity:
                business_name = extract(business_entity, "business.full_name") or extract(
                    business_entity, "business.name"
                )
                if business_name:
                    restore("vul_instance_unique.business_name", business_name, vul_unique)

        r = entity_service.save_direct(
            vul_unique, table=entity_service.get_table(Category.VUL_INSTANCE_UNIQUE), refresh=True
        )
        print(r)
        entity_service.refresh_entity(Category.VUL_INSTANCE_UNIQUE, None)
        data = entity_service.get_entity(
            category=Category.VUL_INSTANCE_UNIQUE, field_name="base.entity_id", field_value=vul_id
        )
        print(data)
        return True, ""
