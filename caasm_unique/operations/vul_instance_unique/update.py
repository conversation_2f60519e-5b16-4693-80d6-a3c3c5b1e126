import datetime

from caasm_meta_data.constants import Category
from caasm_service.entity.business_poraiter import BusinessPortrait, Magnitude
from caasm_service.runtime import business_portraiter_service, vul_time_line_service, unique_service, entity_service
from caasm_tool.util import restore
from caasm_unique.operation import UniqueOperation, OperationType
from caasm_vul.enums import VulResponseStatus, VUL_RESPONSE_STATUS_MAPPING


class VulInstanceUniqueUpdateOperation(UniqueOperation):
    @property
    def category(self):
        return Category.VUL_INSTANCE_UNIQUE

    @property
    def type(self):
        return OperationType.UPDATE

    def execute(self, data):
        if "id" not in data:
            return False, ""
        id_ = data["id"]
        print(data)
        if "response_status" not in data and "repair_date" not in data:
            return False, ""
        result = {}
        response_status = data.get("response_status")
        repair_date = data.get("repair_date")

        if response_status:
            """
            需要同步修改时间轴
            """
            try:
                tmp = VulResponseStatus(response_status)
            except ValueError:
                return False, ""
            result["response_status"] = {"value": response_status, "text": VUL_RESPONSE_STATUS_MAPPING.get(tmp)}
            db_result = {
                "vul_id": id_,
                "status": response_status,
                "date": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "uploader": "",
            }
            vul_time_line_service.save_vul_timeline(vul_time_line_service.load_entity(**db_result))

        if repair_date:
            result["repair_date"] = repair_date

        condition = {"bool": {"must": [{"term": {"base.entity_id": id_}}]}}

        data = entity_service.get_entity(
            category=Category.VUL_INSTANCE_UNIQUE, field_name="base.entity_id", field_value=id_
        )
        data["vul_instance_unique"].update(result)
        print(entity_service.get_table(Category.VUL_INSTANCE_UNIQUE))
        r = entity_service.update_stream_direct(
            mappers=[data],
            table=entity_service.get_table(Category.VUL_INSTANCE_UNIQUE),
        )
        data = entity_service.get_entity(
            category=Category.VUL_INSTANCE_UNIQUE, field_name="base.entity_id", field_value=id_
        )
        print(data)
        entity_service.refresh_entity(Category.VUL_INSTANCE_UNIQUE, None)
        return True, ""

        if "magnitude" not in data:
            return False, ""
        magnitude = data["magnitude"]
        try:
            Magnitude(magnitude)
        except ValueError:
            return False, ""
        portrait: BusinessPortrait = business_portraiter_service.get_business_portraiter(id_)
        business_portraiter_service.update_by_id(portrait.id, {"magnitude": magnitude})
        return True, ""
