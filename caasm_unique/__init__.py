from caasm_unique.operation import UniqueOperationManager
from caasm_unique.operations.business_unique.update import BusinessUniqueUpdateOperation
from caasm_unique.operations.flaw.import_ import FlawImportOperation
from caasm_unique.operations.vul.update import VulUpdateOperation
from caasm_unique.operations.vul_instance_unique.create import VulInstanceUniqueCreateOperation
from caasm_unique.operations.vul_instance_unique.update import VulInstanceUniqueUpdateOperation

operations = [
    VulInstanceUniqueCreateOperation,
    VulUpdateOperation,
    BusinessUniqueUpdateOperation,
    FlawImportOperation,
    VulInstanceUniqueUpdateOperation,
]

unique_operation_manager = UniqueOperationManager()
for op in operations:
    unique_operation_manager.register(op())
