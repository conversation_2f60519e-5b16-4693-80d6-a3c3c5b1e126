import json
import logging

from caasm_config.config import caasm_config
from caasm_script import SCRIPT_DATA_PATH
from caasm_script.handlers._base import _InitializeHandler
from caasm_service.runtime import menu_service, role_service

log = logging.getLogger()


class MenuInitializer(_InitializeHandler):
    _PATHS = [
        SCRIPT_DATA_PATH / "menu.json",
        caasm_config.CUSTOMER_PATH / "data" / "menu.json",
    ]

    @classmethod
    def name(cls):
        return "menu"

    def init_step_1_create(self):
        self.init_batch(self._PATHS, self._init_step_2_create)

    def _init_step_2_create(self, path):
        if not path.exists():
            return
        menu_service.delete_multi({})
        with open(path) as f:
            menus = json.load(f)
        for _menu in menus:
            self.__create_menu(_menu)
        self.__rebuild_menu_role()

    def __create_menu(self, menu, code=None, parent_code=None, level=1):
        children = menu.get("children") or []
        actions = menu.get("actions")

        name = menu.get("name")
        icon = menu.get("icon")
        path = menu.get("path")
        priority = menu.get("priority")
        is_show = menu.get("is_show")
        redirect_path = menu.get("redirect_path")
        setting = menu.get("setting")

        if not code:
            code = name

        children_codes = []
        for child in children:
            child_name = child.get("name")
            child_code = self.__create_menu(child, code=code + child_name, parent_code=code, level=level + 1)
            children_codes.append(child_code)

        menu = menu_service.load_entity(
            **{
                "name": name,
                "icon": icon,
                "path": path,
                "priority": priority,
                "code": code,
                "parent_code": parent_code,
                "actions": actions,
                "children_codes": children_codes,
                "level": level,
                "is_show": is_show,
                "redirect_path": redirect_path,
                "setting": setting,
            }
        )
        menu_service.save_menu(menu)

        return code

    def __rebuild_menu_role(self):
        roles = [role for role in role_service.find_role() if role.menus]
        menu_mapper = {menu.code: menu for menu in menu_service.find_menu()}

        for role in roles:
            permission_codes = []
            self.__rebuild_menu_permission(role.menus, menu_mapper, permission_codes)

            role.permission_codes = list(set(permission_codes))
            role_service.update(role)

    def __rebuild_menu_permission(self, menu_relations, menu_mapper, permission_codes):
        for menu in menu_relations:
            action_codes = menu.action_codes
            menu_code = menu.code

            new_menu = menu_mapper.get(menu_code)
            if not new_menu:
                continue

            for action in new_menu.actions:
                if action.code in action_codes:
                    permission_codes.extend(action.permission_codes)

            self.__rebuild_menu_permission(menu.children, menu_mapper, permission_codes)


POINT = MenuInitializer
