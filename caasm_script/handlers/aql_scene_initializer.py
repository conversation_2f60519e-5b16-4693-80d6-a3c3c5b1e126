import logging
import traceback

import xlrd

from caasm_meta_data.constants import Category
from caasm_script import SCRIPT_DATA_PATH
from caasm_script.handlers._base import _InitializeHandler
from caasm_service.runtime import retrieve_statement_service, retrieve_scenes_service
from caasm_tool.constants import AqlParamsType

log = logging.getLogger()


class AqlSceneInitializer(_InitializeHandler):
    _CATEGORY_MAPPER = {
        "资产": Category.ASSET,
        "漏洞": Category.ASSET_VUL_INSTANCE,
    }
    _ENABLED_MAPPER = {
        "是": True,
        "否": False,
    }

    def __init__(self, *args, **kwargs):
        super(AqlSceneInitializer, self).__init__(*args, **kwargs)
        self._aql_scenes = []

    @classmethod
    def name(cls):
        return "aql_scene"

    @classmethod
    def init_step_1_empty(cls):
        retrieve_scenes_service.delete_multi({})

    def init_step_2_define(self):

        default_aql_scene_file_path = SCRIPT_DATA_PATH / "规则.xlsx"
        scene_groups = [(12, 13), (14, 15)]
        category_index = scene_groups[-1][1] + 1
        aql_index = scene_groups[-1][1] + 3
        aql_type_index = scene_groups[-1][1] + 4
        description_index = scene_groups[-1][1] + 5
        enabled_index = scene_groups[-1][1] + 7
        dynamic_params_start_index = scene_groups[-1][1] + 9
        priority = 0

        try:
            workbook = xlrd.open_workbook(default_aql_scene_file_path)
            sheet = workbook.sheets()[0]

            for row_index in range(sheet.nrows):
                if row_index == 0:
                    continue
                for scene_group in scene_groups:
                    main_scene = sheet.cell_value(row_index, scene_group[0])
                    sub_scene = sheet.cell_value(row_index, scene_group[1])

                    if not (main_scene and sub_scene):
                        continue

                    category = sheet.cell_value(row_index, category_index)
                    enabled = sheet.cell_value(row_index, enabled_index)

                    if category not in self._CATEGORY_MAPPER:
                        log.warning(f"Not support {category} scene import")
                        continue

                    if enabled not in self._ENABLED_MAPPER:
                        log.warning(f"Not implement ({enabled}) convert")
                        continue

                    params_type, params = self._parse_params(sheet, row_index, dynamic_params_start_index)
                    name = sheet.cell_value(row_index, description_index)
                    if not name:
                        log.warning(f"Not found name index is {row_index}. skip")
                        continue
                    scene = {
                        "main_scene": main_scene,
                        "sub_scene": sub_scene,
                        "category": self._CATEGORY_MAPPER[category],
                        "name": name,
                        "enabled": self._ENABLED_MAPPER[enabled],
                        "aql": sheet.cell_value(row_index, aql_index),
                        "priority": priority,
                        "params_type": params_type,
                        "params": params,
                        "aql_type": sheet.cell_value(row_index, aql_type_index) or "asql",
                    }
                    self._aql_scenes.append(scene)
                    priority += 1
        except Exception as e:
            log.warning(f"load aql scene({default_aql_scene_file_path}) error({e})({traceback.format_exc()})")

    def init_step_3_save(self):
        self.init_batch(self._aql_scenes, self._init_step_aql_scene_save)

    @classmethod
    def _init_step_aql_scene_save(cls, aql_scene):
        aql = aql_scene.pop("aql")

        digest = retrieve_statement_service.get_digest(aql)
        state = retrieve_statement_service.get_statement(digest=digest)
        statement_id = retrieve_statement_service.save_statement(aql, digest).inserted_id if not state else state.id

        if not statement_id:
            log.warning(f"Statement {aql} save error, skip")
            return
        aql_scene["retrieve_statement_id"] = statement_id
        scene_id = retrieve_scenes_service.save(retrieve_scenes_service.load_entity(**aql_scene)).inserted_id
        if not scene_id:
            log.warning(f"Scene({aql}) save error. skip......")
            return

    @classmethod
    def _parse_params(cls, sheet, row_index, col_start_index):
        params = []
        try:
            while True:
                name = sheet.cell_value(row_index, col_start_index)
                type_ = sheet.cell_value(row_index, col_start_index + 1)
                display_name = sheet.cell_value(row_index, col_start_index + 2)
                description = sheet.cell_value(row_index, col_start_index + 3)
                col_start_index += 4
                if not (name and type_):
                    continue

                param = {
                    "name": name,
                    "type": type_,
                    "display_name": display_name,
                    "description": description,
                    "validate_rules": [],
                    "default": None,
                    "required": True,
                }
                params.append(param)
        except IndexError:
            if params:
                return AqlParamsType.DYNAMIC, params
        return AqlParamsType.FIXED, []


POINT = AqlSceneInitializer
