import logging

from caasm_persistence.handler.runtime import mongo_handler
from caasm_script.handlers._base import _InitializeHandler
from caasm_service.constant import Table

log = logging.getLogger()


class DatabaseIndexInitializer(_InitializeHandler):
    @classmethod
    def name(cls):
        return "database_index"

    @classmethod
    def init_step_1_build(cls):
        build_indexes()


def build_indexes():
    index_info_list = [
        (Table.user_asset_aql_history, {"keys": [("aql", 1)]}),
        (Table.adapter, {"keys": [("name", 1)], "unique": True}),
        (Table.manufacturer, {"keys": [("name", 1)], "unique": True}),
        (Table.adapter_instance, {"keys": [("name", 1)], "unique": True}),
        (Table.sequence, {"keys": [("model_name", 1), ("key_name", 1)], "unique": True}),
        (Table.vulnerability, {"keys": [("base.entity_id", 1)], "unique": True}),
        (Table.fetch_record, {"keys": [("adapter_instance_id", 1), ("fetch_type", 1), ("index", 1)], "unique": True}),
        (
            Table.fetch_record,
            {
                "keys": [("adapter_instance_id", 1), ("fetch_type", 1), ("latest", 1)],
            },
        ),
        (
            Table.merge_record,
            {
                "keys": [("adapter_name", 1), ("fetch_type", 1), ("index", 1)],
                "unique": True,
            },
        ),
        (
            Table.merge_record,
            {
                "keys": [("adapter_name", 1), ("fetch_type", 1), ("latest", 1)],
            },
        ),
        (
            Table.convert_record,
            {
                "keys": [("adapter_name", 1), ("fetch_type", 1), ("index", 1)],
                "unique": True,
            },
        ),
        (
            Table.user,
            {
                "keys": [
                    ("username", 1),
                ],
                "unique": True,
            },
        ),
        (Table.job, {"keys": [("name", 1)], "unique": True}),
        (Table.role, {"keys": [("code", 1)], "unique": True}),
        (Table.menu, {"keys": [("code", 1)], "unique": True}),
        (Table.setting, {"keys": [("name", 1)], "unique": True}),
        (
            Table.retrieve_scenes,
            {
                "keys": [
                    ("main_scene", 1),
                    ("sub_scene", 1),
                    ("name", 1),
                    ("retrieve_statement_id", 1),
                ],
                "unique": True,
            },
        ),
        (Table.retrieve_statement, {"keys": [("digest", 1)], "unique": True}),
        (Table.meta_field, {"keys": [("model_id", 1), ("name", 1)], "unique": True}),
        (Table.meta_model, {"keys": [("name", 1)], "unique": True}),
        (Table.convert_visualization, {"keys": [("adapter_name", 1), ("fetch_type", 1)], "unique": True}),
        (
            Table.convert_visualization_relation,
            {"keys": [("adapter_name", 1), ("model_id", 1), ("fetch_type", 1), ("internal", 1)], "unique": True},
        ),
        (Table.asset_type, {"keys": [("name", 1)], "unique": True}),
        (Table.fabric_meta_model_config, {"keys": [("asset_type_id", 1)], "unique": True}),
        (
            Table.internet_relation,
            {
                "keys": [
                    ("internet_ip", 1),
                    ("internet_port", 1),
                    ("protocol", 1),
                    ("internal_ip", 1),
                    ("internal_port", 1),
                    ("route", 1),
                    ("domain", 1),
                ],
                "unique": True,
            },
        ),
        (Table.adapter_instance_fetch_poc_log, {"keys": [("adapter_instance_id", 1), ("fetch_type", 1)]}),
        (Table.whitelist, {"keys": [("address", 1)], "unique": True}),
        (Table.snapshot_record, {"keys": [("date", 1)]}),
        (Table.meta_field_snapshot_record, {"keys": [("date", 1), ("model_id", 1)], "unique": True}),
        (Table.meta_model_snapshot_record, {"keys": [("date", 1), ("category", 1)], "unique": True}),
        (Table.workflow_playbook, {"keys": [("name", 1)], "unique": True}),
        (Table.workflow_record, {"keys": [("name", 1)]}),
        (Table.workflow_record, {"keys": [("status", 1)]}),
        (Table.workflow_record, {"keys": [("supervisor_task_id", 1)]}),
        (Table.workflow_record, {"keys": [("top_workflow_id", 1)]}),
        (Table.workflow_task, {"keys": [("workflow_id", 1), ("name", 1)], "unique": True}),
        (Table.workflow_task, {"keys": [("status", 1)]}),
        (Table.workflow_task, {"keys": [("top_workflow_id", 1)]}),
        (Table.quick_search, {"keys": [("category", 1), ("display_name", 1)], "unique": True}),
        (Table.category, {"keys": [("name", 1)], "unique": True}),
    ]

    database = mongo_handler.database()

    for index_info in index_info_list:
        table_name = index_info[0]
        indexes = index_info[1]

        try:
            database[table_name].create_index(**indexes)
        except Exception as e:
            log.warning(f"Index create error({e}) index_info is {index_info}")


POINT = DatabaseIndexInitializer
