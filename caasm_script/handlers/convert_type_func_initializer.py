from pathlib import Path

import yaml

from caasm_config.config import caasm_config
from caasm_script.handlers._base import _InitializeHandler, StopException
from caasm_service.runtime import convert_func_type_service


class ConvertTypeFuncInitializer(_InitializeHandler):
    def __init__(self, *args, **kwargs):
        super(ConvertTypeFuncInitializer, self).__init__(*args, **kwargs)
        self._path = Path(caasm_config.ROOT_DIR) / "caasm_convert_engine" / "type_func.yml"

    @classmethod
    def name(cls):
        return "convert_type_func"

    def init_step_1_check(self):
        if not self._path.exists():
            raise StopException

    @classmethod
    def init_step_2_empty(cls):
        convert_func_type_service.delete_multi({})

    def init_step_3_save(self):
        with open(self._path, "r") as fd:
            convert_funcs = yaml.safe_load(fd)
            self._init_step_3_save(convert_funcs)

    def _init_step_3_save(self, convert_funcs):
        type_func = convert_funcs.get("type_func")
        for _type_func in type_func:
            mapper = {"name": _type_func.get("name"), "func_list": _type_func.get("func_list")}
            convert_func_type_service.save(convert_func_type_service.load_entity(**self.filter_dict_none(mapper)))


POINT = ConvertTypeFuncInitializer
