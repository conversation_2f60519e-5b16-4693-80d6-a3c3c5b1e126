import logging
from functools import cached_property

from caasm_config.config import caasm_config
from caasm_script.handlers._base import _InitializeHandler
from caasm_service.runtime import asset_type_service, meta_model_service, fabric_model_config_service

log = logging.getLogger()


class AssetTypeInitializer(_InitializeHandler):
    _DEFAULT_FIELDS = ["_id"]
    _DEFAULT_FIELD_GLOBAL_POLICY = [
        {
            "value": "elect",
            "label": "选举策略",
            "policy_description": "",
        },
        {
            "value": "adapter",
            "label": "适配器置信度策略",
            "policy_description": "",
        },
        {
            "value": "random",
            "label": "随机策略",
            "policy_description": "",
        },
    ]

    def init_step_1_asset_type(self):
        self.init_batch(self.asset_types, self._init_step_1_asset_type)

    def init_step_2_fabric_asset_type(self):
        self.init_batch(self.asset_types, self._init_step_2_fabric_asset_type)

    @classmethod
    def name(cls):
        return "asset_type"

    @cached_property
    def asset_types(self):
        return caasm_config.INTERNAL_ASSET_TYPES or []

    @classmethod
    def _init_step_1_asset_type(cls, asset_type_info):
        name = asset_type_info["name"]
        display_name = asset_type_info["display_name"]
        model_name = asset_type_info["model_name"]

        if model_name:
            meta_model = meta_model_service.get_meta_model(name=model_name, internal=True, fields=["_id`"])
            if not meta_model:
                log.error(f"Not found meta model info({model_name})")
                return
            model_id = meta_model.id
        else:
            model_id = None

        asset_type = asset_type_service.get_asset_type(name=name, internal=True)
        if not asset_type:
            asset_type_service.save_asset_type(name=name, display_name=display_name, internal=True, model_id=model_id)
        else:
            asset_type.display_name = display_name
            asset_type.model_id = model_id
            asset_type_service.update(asset_type)

    @classmethod
    def _init_step_2_fabric_asset_type(cls, asset_type_info):

        name = asset_type_info["name"]
        fabric = asset_type_info["fabric"] or {}
        asset_type = asset_type_service.get_asset_type(name=name, fields=cls._DEFAULT_FIELDS)
        if not asset_type:
            return

        asset_type_id = asset_type.id

        fabric_model_config = fabric_model_config_service.get_fabric_meta_model_config(asset_type_id=asset_type_id)
        if not fabric_model_config:
            fabric_model_config_service.save_fabric_model_config_service(
                asset_type_id,
                fabric_policy=fabric,
                field_global_policy=cls._DEFAULT_FIELD_GLOBAL_POLICY,
            )
            return
        if not fabric_model_config.is_modify:
            fabric_model_config_service.update_fabric_model_config_service(asset_type_id, fabric)
            return


POINT = AssetTypeInitializer
