from pathlib import Path

import yaml

from caasm_config.config import caasm_config
from caasm_script.handlers._base import _InitializeHandler, StopException
from caasm_service.runtime import convert_func_service


class ConvertFuncInitializer(_InitializeHandler):
    def __init__(self, *args, **kwargs):
        super(ConvertFuncInitializer, self).__init__(*args, **kwargs)
        self._path = Path(caasm_config.ROOT_DIR) / "caasm_convert_engine" / "func.yml"

    @classmethod
    def name(cls):
        return "convert_func"

    def init_step_0_check(self):
        if not self._path.exists():
            raise StopException

    @classmethod
    def init_step_1_empty(cls):
        convert_func_service.delete_multi({})

    def init_step_2_save(self):
        with open(self._path, "r") as fd:
            convert_funcs = yaml.safe_load(fd)
            self._save_convert_func(convert_funcs)

    def _save_convert_func(self, meta):
        converts = meta.get("func")
        for convert in converts:
            convert_mapper = {
                "name": convert.get("name"),
                "en_display_name": convert.get("en_display_name"),
                "description": convert.get("description"),
                "display_name": convert.get("display_name"),
                "input": convert.get("input"),
                "output": convert.get("output"),
                "classify": convert.get("classify"),
            }
            convert_func_service.save(convert_func_service.load_entity(**self.filter_dict_none(convert_mapper)))


POINT = ConvertFuncInitializer
