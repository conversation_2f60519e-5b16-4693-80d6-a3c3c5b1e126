import json
import logging
from functools import lru_cache
from functools import partial
from pathlib import Path

import yaml

from caasm_config.config import caasm_config
from caasm_script.handlers._base import _InitializeHandler
from caasm_service.runtime import (
    manufacturer_service,
    adapter_service,
    convert_visualization_service,
    meta_model_service,
    asset_type_service,
    convert_visualization_relation_service,
)

log = logging.getLogger()


class AdapterInitializer(_InitializeHandler):
    _PATHS = [
        Path(caasm_config.ROOT_DIR) / "caasm_adapter_app",
        Path(caasm_config.CUSTOMER_PATH) / "adapter_app",
    ]

    def __init__(self, *args, **kwargs):
        super(AdapterInitializer, self).__init__(*args, **kwargs)
        self._path = None
        self._adapter_dirs = []

    @classmethod
    def name(cls):
        return "adapter"

    def execute(self):
        for _path in self._PATHS:
            if not _path.exists():
                continue
            self._path = _path
            super(AdapterInitializer, self).execute()

    def init_step_1_adapter_dir(self):
        self._adapter_dirs = [son_path for son_path in self._path.iterdir() if son_path.is_dir()]

    def init_step_2_manufacturer(self):
        self._init_batch(self._init_step_manufacturer)

    def init_step_3_adapter(self):
        self._init_batch(self._init_step_adapter)

    def init_step_4_adapter_demo(self):
        self._init_batch(self._init_step_adapter_demo)

    def init_step_5_adapter_rule(self):
        self._init_batch(self._init_step_adapter_rule)

    def _init_step_manufacturer(self, adapter_dir):
        adapter_meta_content = self.__adapter_meta_content(adapter_dir)
        manufacturer_name = adapter_meta_content.get("company")
        manufacturer = manufacturer_service.get_manufacturer(name=manufacturer_name)
        if manufacturer:
            return
        manufacturer = manufacturer_service.load_entity(name=manufacturer_name)
        manufacturer_service.save(manufacturer)

    def _init_step_adapter(self, adapter_dir):
        adapter_meta_content = self.__adapter_meta_content(adapter_dir)
        fetch_setting = adapter_meta_content.get("fetch_setting")

        adapter_mapper = {
            "name": adapter_meta_content.get("name"),
            "display_name": adapter_meta_content.get("display_name"),
            "description": adapter_meta_content.get("description"),
            "manufacturer_id": self._get_manufacturer_id(adapter_dir),
            "version": adapter_meta_content.get("version"),
            "properties": adapter_meta_content.get("properties"),
            "connection": adapter_meta_content.get("connection"),
            "fetch_setting": fetch_setting,
            "fabric_setting": adapter_meta_content.get("fabric_setting"),
            "merge_setting": adapter_meta_content.get("merge_setting"),
            "convert_setting": adapter_meta_content.get("convert_setting"),
            "logo_id": self._get_logo_id(adapter_dir),
            "type": adapter_meta_content.get("type"),
            "priority": adapter_meta_content.get("priority"),
            "is_biz_useful": fetch_setting.pop("is_biz_useful", True),
        }
        adapter_service.save(adapter_service.load_entity(**{k: v for k, v in adapter_mapper.items() if v is not None}))

    def _init_step_adapter_demo(self, adapter_dir):
        adapter_name = self.__adapter_name(adapter_dir)
        records = self.filter_empty_path(adapter_dir / "demos")
        self.init_batch(records, partial(self.__init_step_adapter_demo, adapter_name))

    @classmethod
    def __init_step_adapter_demo(cls, adapter_name, demo):
        with open(demo, "r") as fd:
            demo_content = json.load(fd)
            fetch_type = demo.name.replace(".json", "")

        convert_visualization = convert_visualization_service.get_convert_visualization_count(
            adapter_name=adapter_name,
            fetch_type=fetch_type,
        )
        if not convert_visualization:
            convert_visualization_service.save_convert_visualization(adapter_name, fetch_type, demo_content)
        else:
            convert_visualization_service.update_convert_visualization(adapter_name, fetch_type, demo=demo_content)

    def _init_step_adapter_rule(self, adapter_dir):
        adapter_name = self.__adapter_name(adapter_dir)
        records = self.filter_empty_path(adapter_dir / "rules")
        self.init_batch(records, partial(self.__init_step_adapter_rule, adapter_name))

    def __init_step_adapter_rule(self, adapter_name, rule):
        with open(rule, "r") as f:
            rule_content = json.load(f)

        fetch_type = rule_content.get("fetch_type")
        model_name = rule_content.get("model_name")
        asset_type = rule_content.get("asset_type")
        canvas = rule_content.get("canvas")
        rules = rule_content.get("rules")

        meta_model = meta_model_service.get_meta_model(name=model_name)
        if not meta_model:
            log.warning(f"Not found model({model_name}) info. please check")
            return

        asset_type_model = asset_type_service.get_asset_type(name=asset_type)
        if not asset_type_model:
            log.warning(f"({adapter_name})Not found asset_type({asset_type}) info. please check")
            return

        relation = convert_visualization_relation_service.get_convert_visualization_relation(
            adapter_name=adapter_name,
            model_id=meta_model.id,
            fetch_type=fetch_type,
            internal=True,
        )
        save_flag = False

        if relation:
            delete_flag = True
            if not self.params.get("canvas_reset", True):
                if relation.modify_flag:
                    log.warning(f"Adapter({adapter_name}) fetchType({fetch_type}) canvas already modify. skip......")
                    delete_flag = False

            if delete_flag:
                convert_visualization_relation_service.delete_convert_visualization_relation(
                    adapter_name=adapter_name,
                    model_id=meta_model.id,
                    fetch_type=fetch_type,
                    internal=True,
                )
                save_flag = True
        else:
            save_flag = True

        if save_flag:
            convert_visualization_relation_service.save_convert_visualization_relation(
                adapter_name=adapter_name,
                model_id=meta_model.id,
                asset_type_id=asset_type_model.id,
                fetch_type=fetch_type,
                internal=True,
                rules=rules,
                canvas=canvas,
                init=False,
            )

    def _init_batch(self, callback):
        self.init_batch(self._adapter_dirs, callback)

    @classmethod
    def _get_manufacturer_id(cls, adapter_dir):
        company = cls.__adapter_meta_content(adapter_dir).get("company")
        manufacturer = manufacturer_service.get_manufacturer(name=company)
        return manufacturer.id if manufacturer else None

    @classmethod
    def _get_logo_id(cls, adapter_dir):
        adapter_name = cls.__adapter_name(adapter_dir)
        old_adapter = adapter_service.get_adapter(name=adapter_name, fields=["logo_id"])
        if old_adapter:
            if old_adapter.logo_id:
                adapter_service.delete_file(file_id=old_adapter.logo_id)
            adapter_service.delete_adapter(name=adapter_name)
        logo_path = cls.__adapter_meta_content(adapter_dir).get("logo")
        logo_content = cls.__load_logo(adapter_dir / logo_path)
        return adapter_service.save_file(logo_content) if logo_content else None

    @classmethod
    def __load_logo(cls, logo_address):
        if not logo_address.exists():
            log.warning(f"Not found logo({logo_address}) file")
            return
        with open(logo_address, "rb") as fd:
            return fd.read()

    @classmethod
    def __adapter_name(cls, adapter_dir):
        return cls.__adapter_meta_content(adapter_dir).get("name")

    @classmethod
    @lru_cache(maxsize=None)
    def __adapter_meta_content(cls, adapter_dir):
        meta_file = adapter_dir / "meta.yml"
        with open(meta_file, "r") as fd:
            return yaml.safe_load(fd)


POINT = AdapterInitializer


if __name__ == "__main__":
    AdapterInitializer().execute()
