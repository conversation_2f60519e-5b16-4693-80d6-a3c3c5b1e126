import logging
from collections import defaultdict

import yaml

from caasm_config.config import caasm_config
from caasm_script.handlers._base import _InitializeHandler
from caasm_service.runtime import quick_search_service
from caasm_service.schema.runtime import quick_search_schema

log = logging.getLogger()


class QuickSearchInitializer(_InitializeHandler):
    _PATHS = [
        caasm_config.ROOT_DIR / "caasm_script" / "data" / "quick_search.yml",
        caasm_config.CUSTOMER_PATH / "data" / "quick_search.yml",
    ]

    @classmethod
    def name(cls):
        return "quick_search"

    def init_step_1_save(self):
        self.init_batch(self._PATHS, self._init_step_1_save)

    @classmethod
    def _init_step_1_save(cls, path):
        if not path.exists():
            return
        quick_search_service.delete_multi({})
        with open(path, "rb") as fd:
            data = yaml.safe_load(fd)
            items = []
            remove_mapper = defaultdict(list)
            for key, value in data.items():
                exists_flag = set()
                value.reverse()
                for item_value in value:
                    item_value["category"] = key
                    quick_search = quick_search_schema.load(item_value)
                    if quick_search.display_name in exists_flag:
                        log.warning(f"Quick search display_name({quick_search.display_name}) repeated")
                        continue
                    exists_flag.add(quick_search.display_name)
                    remove_mapper[key].append(quick_search.display_name)
                    items.append(quick_search)

        for key, display_names in remove_mapper.items():
            if not display_names:
                continue
            quick_search_service.delete_quick_search(category=key, display_names=display_names)

        quick_search_service.save_quick_searches(items)

    @classmethod
    def save_quick_search(cls, category=None, name=None, aql=None, aql_type=None):
        quick_search_service.save_quick_search_aql(category=category, name=name, aql=aql, aql_type=aql_type, inner=True)


POINT = QuickSearchInitializer
