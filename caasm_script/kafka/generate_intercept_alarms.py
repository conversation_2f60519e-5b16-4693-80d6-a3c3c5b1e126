#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模拟数据生成脚本，用于生成青藤零域拦截告警数据并发送到Kafka

字段说明：
1. comId - String - ComId
2. srcId - String - 访问者AgentId
3. srcType - Integer - 访问者类型；1-主机 2-内网 3-外网
4. srcDisplayIP - String - 访问者Agent展示IP
5. srcIp - String - 访问者IP
6. srcOsType - Integer - 访问者系统
7. srcName - String - 访问者主机名
8. srcGroupId - Integer - 访问者业务组
9. srcGroupRelationshipName - String - 访问者业务组链
10. srcHostTags - Array[SimpleHostTag] - 访问者标签
11. dstId - String - 被访问者Id
12. dstType - Integer - 被访问者类型；1-主机 2-内网 3-外网
13. dstDisplayIP - Integer - 被访问者Agent展示IP
14. dstIp - String - 被访问者IP
15. dstOsType - Integer - 被访问者系统
16. dstName - String - 被访问者主机名
17. dstGroupId - Integer - 被访问者业务组
18. dstGroupRelationshipName - String - 被访问者业务组链
19. dstHostTags - Array[SimpleHostTag] - 被访问者标签
20. dstPort - Integer - 被访问者端口号
21. blockingType - String - 拦截方向；out-访问者 in-被访问者
22. pName - String - 进程名
23. protocol - String - 协议
24. count - Long - 拦截次数
25. reason - String - 拦截原因
26. firstTime - Date - 第一次访问时间
27. lastTime - Date - 最近访问时间
28. createTime - Date - 创建时间
29. hash - String - Hash值
"""

import argparse
import datetime
import hashlib
import ipaddress
import json
import logging
import random
import string
import time
import uuid
from typing import Dict, List, Any, Optional, Union

from confluent_kafka import Producer
from faker import Faker
from caasm_config.config import caasm_config


# 配置日志
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
log = logging.getLogger(__name__)

# 初始化Faker
fake = Faker(["zh_CN"])

# 定义常量
OS_TYPES = {1: "Windows", 2: "Linux", 3: "macOS", 4: "Unix", 5: "Android", 6: "iOS"}

PROTOCOLS = ["TCP", "UDP", "HTTP", "HTTPS", "FTP", "SSH", "SMTP", "DNS", "ICMP"]

PROCESS_NAMES = [
    "nginx",
    "apache2",
    "httpd",
    "mysqld",
    "postgres",
    "redis-server",
    "mongod",
    "java",
    "python",
    "node",
    "php-fpm",
    "sshd",
    "vsftpd",
    "smbd",
    "named",
    "docker",
    "containerd",
    "kubelet",
    "etcd",
    "prometheus",
    "grafana-server",
    "elasticsearch",
    "zookeeper",
    "kafka",
    "rabbitmq-server",
    "haproxy",
    "memcached",
    "rpcbind",
    "systemd",
    "cron",
    "rsyslogd",
    "ntpd",
    "chrony",
    "firewalld",
    "iptables",
    "ufw",
    "fail2ban",
    "snmpd",
    "collectd",
    "statsd",
    "telegraf",
    "influxd",
    "clickhouse",
    "cassandra",
    "couchdb",
    "rethinkdb",
]

BLOCKING_REASONS = [
    "未授权访问",
    "可疑连接",
    "端口扫描",
    "暴力破解尝试",
    "恶意软件通信",
    "数据泄露尝试",
    "异常流量模式",
    "已知恶意IP",
    "违反安全策略",
    "DDoS攻击",
    "SQL注入尝试",
    "XSS攻击尝试",
    "命令注入尝试",
    "文件包含漏洞利用",
    "远程代码执行尝试",
]

HOST_TAG_TYPES = [
    "业务系统",
    "环境",
    "部门",
    "重要程度",
    "安全等级",
    "操作系统",
    "地域",
    "机房",
]

# 网段定义
NETWORK_SEGMENTS = [
    ipaddress.IPv4Network("*********/16"),
    ipaddress.IPv4Network("*********/16"),
]


def generate_random_ip(network: ipaddress.IPv4Network) -> str:
    """生成指定网段内的随机IP地址"""
    # 获取网段的第一个地址和最后一个地址
    start = int(network.network_address)
    end = int(network.broadcast_address)

    # 生成随机IP
    random_ip_int = random.randint(start, end)
    random_ip = ipaddress.IPv4Address(random_ip_int)

    return str(random_ip)


def generate_host_tags(count: int = 3) -> List[Dict[str, str]]:
    """生成主机标签列表"""
    tags = []
    selected_types = random.sample(HOST_TAG_TYPES, min(count, len(HOST_TAG_TYPES)))

    for tag_type in selected_types:
        tag = {
            "type": tag_type,
            "value": fake.word() if tag_type != "业务系统" else fake.company(),
        }
        tags.append(tag)

    return tags


def generate_group_relationship_name() -> str:
    """生成业务组链"""
    depth = random.randint(1, 3)
    parts = []

    for _ in range(depth):
        parts.append(fake.word())

    return "/".join(parts)


def generate_hash(data: Dict[str, Any]) -> str:
    """生成数据的哈希值"""
    # 将数据转换为JSON字符串
    data_str = json.dumps(data, sort_keys=True)

    # 计算SHA-256哈希
    hash_obj = hashlib.sha256(data_str.encode())
    return hash_obj.hexdigest()


def generate_intercept_alarm() -> Dict[str, Any]:
    """生成一条拦截告警数据"""
    # 选择随机网段
    src_network = NETWORK_SEGMENTS[0]
    dst_network = NETWORK_SEGMENTS[1]

    # 生成时间
    now = datetime.datetime.now()
    first_time = now - datetime.timedelta(minutes=random.randint(5, 60))
    last_time = first_time + datetime.timedelta(minutes=random.randint(0, 5))

    # 生成基础数据
    src_type = random.randint(1, 3)
    dst_type = random.randint(1, 3)
    src_os_type = random.randint(1, 6)
    dst_os_type = random.randint(1, 6)

    # 生成IP
    src_ip = generate_random_ip(src_network)
    dst_ip = generate_random_ip(dst_network)

    # 构建告警数据
    alarm = {
        "comId": str(uuid.uuid4()),
        "srcId": f"agent_{uuid.uuid4().hex[:8]}",
        "srcType": src_type,
        "srcDisplayIP": src_ip,
        "srcIp": src_ip,
        "srcOsType": src_os_type,
        "srcName": fake.hostname(),
        "srcGroupId": random.randint(1, 100),
        "srcGroupRelationshipName": generate_group_relationship_name(),
        "srcHostTags": generate_host_tags(),
        "dstId": f"agent_{uuid.uuid4().hex[:8]}",
        "dstType": dst_type,
        "dstDisplayIP": dst_ip,
        "dstIp": dst_ip,
        "dstOsType": dst_os_type,
        "dstName": fake.hostname(),
        "dstGroupId": random.randint(1, 100),
        "dstGroupRelationshipName": generate_group_relationship_name(),
        "dstHostTags": generate_host_tags(),
        "dstPort": random.randint(1, 65535),
        "blockingType": random.choice(["in", "out"]),
        "pName": random.choice(PROCESS_NAMES),
        "protocol": random.choice(PROTOCOLS),
        "count": random.randint(1, 100),
        "reason": random.choice(BLOCKING_REASONS),
        "firstTime": first_time.isoformat(),
        "lastTime": last_time.isoformat(),
        "createTime": now.isoformat(),
    }

    # 添加哈希值
    alarm["hash"] = generate_hash(alarm)

    return alarm


def delivery_report(err, msg):
    """Kafka消息发送回调函数"""
    if err is not None:
        log.error(f"消息发送失败: {err}")
    else:
        log.info(f"消息发送成功: {msg.topic()} [{msg.partition()}] @ {msg.offset()}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="生成青藤零域拦截告警模拟数据并发送到Kafka")

    parser.add_argument(
        "-b",
        "--bootstrap-servers",
        type=str,
        default=f"{caasm_config.ALARM.get('kafka', {}).get('bootstrap_servers', 'localhost:9092')}",
        help="Kafka服务器地址，格式为host:port",
    )
    parser.add_argument("-t", "--topic", type=str, default="test_intercept_alarm", help="Kafka主题名称")
    parser.add_argument("-n", "--num-records", type=int, default=100, help="生成的记录数量")
    parser.add_argument("-i", "--interval", type=float, default=0.1, help="消息发送间隔(秒)")
    parser.add_argument("--dry-run", action="store_true", help="仅生成数据但不发送到Kafka")

    args = parser.parse_args()

    # 配置Kafka生产者
    if not args.dry_run:
        conf = {
            "bootstrap.servers": args.bootstrap_servers,
            "client.id": f"intercept-alarm-generator-{uuid.uuid4().hex[:8]}",
        }
        producer = Producer(conf)

    log.info(f"开始生成{args.num_records}条拦截告警数据...")

    for i in range(args.num_records):
        # 生成告警数据
        alarm = generate_intercept_alarm()

        # 打印进度
        if i % 10 == 0 or i == args.num_records - 1:
            log.info(f"已生成 {i + 1}/{args.num_records} 条数据")

        if args.dry_run:
            # 仅打印数据
            if i < 3 or i == args.num_records - 1:  # 只打印前3条和最后一条
                log.info(f"生成的数据: {json.dumps(alarm, indent=2, ensure_ascii=False)}")
        else:
            # 发送到Kafka
            producer.produce(
                args.topic,
                json.dumps(alarm, ensure_ascii=False).encode("utf-8"),
                callback=delivery_report,
            )
            producer.poll(0)  # 触发回调

            # 按指定间隔发送
            if args.interval > 0:
                time.sleep(args.interval)

    if not args.dry_run:
        # 等待所有消息发送完成
        log.info("等待剩余消息发送完成...")
        producer.flush()

    log.info(f"已成功生成{args.num_records}条拦截告警数据")


if __name__ == "__main__":
    main()
