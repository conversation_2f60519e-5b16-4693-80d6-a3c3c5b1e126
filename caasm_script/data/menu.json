[{"name": "监控中心", "icon": "dashboard", "path": "/dashboard", "is_show": false, "redirect_path": "/dashboard/details", "priority": 100, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}], "setting": {"affix": false, "cacheable": true, "file_path": "/dashboard"}, "children": [{"name": "仪表盘", "icon": "dashboard", "path": "/dashboard/details", "is_show": false, "redirect_path": "", "priority": 100, "setting": {"affix": true, "cacheable": true, "file_path": "/dashboard/details"}, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}]}, {"name": "仪表盘管理", "icon": "dashboard", "path": "/dashboard/dashboardist", "is_show": true, "redirect_path": "", "priority": 100, "setting": {"affix": false, "cacheable": true, "file_path": "/dashboard/list"}, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}]}, {"name": "图表管理", "icon": "dashboard", "path": "/dashboard/charts", "is_show": true, "redirect_path": "", "priority": 100, "setting": {"affix": false, "cacheable": true, "file_path": "/system/charts"}, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}]}, {"name": "态势感知", "icon": "", "path": "/dashboard/largescreenList", "is_show": false, "redirect_path": "", "priority": 99, "setting": {"affix": false, "cacheable": true, "file_path": "/largescreen/largescreen-list"}, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}]}, {"name": "大屏", "icon": "", "path": "/dashboard/largescreen-details/:id", "is_show": true, "redirect_path": "", "priority": 99, "setting": {"affix": false, "cacheable": true, "file_path": "/largescreen/largescreen-details"}, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}]}]}, {"name": "风险中心", "icon": "shujuronghe1", "path": "/risk", "is_show": false, "redirect_path": "/risk/alarm", "priority": 99, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}], "setting": {"affix": false, "cacheable": true, "file_path": "/association<PERSON>les"}, "children": [{"name": "关联规则", "icon": "", "path": "/risk/assrules", "is_show": false, "redirect_path": "", "priority": 95, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}], "setting": {"affix": false, "cacheable": true, "file_path": "/associationrules/assrules"}}, {"name": "规则告警", "icon": "", "path": "/risk/assrulesalarm", "is_show": false, "redirect_path": "", "priority": 95, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}], "setting": {"affix": false, "cacheable": true, "file_path": "/associationrules/rulealarm"}}, {"name": "规则运行历史", "icon": "", "path": "/risk/assruleshistory", "is_show": false, "redirect_path": "", "priority": 95, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}], "setting": {"affix": false, "cacheable": true, "file_path": "/associationrules/rulehistory"}}]}, {"name": "资产台账", "icon": "taizhang", "path": "/device", "is_show": false, "redirect_path": "/device/device-list", "priority": 99, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}], "setting": {"affix": false, "cacheable": true, "file_path": "/device"}, "children": [{"name": "资产台账", "icon": "taizhang", "path": "/device/device-list", "is_show": false, "redirect_path": "", "priority": 100, "setting": {"affix": false, "cacheable": true, "file_path": "/device/device-list"}, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}]}, {"name": "资产详情", "icon": "", "path": "/device/device-details/:id", "is_show": true, "redirect_path": "", "priority": 99, "setting": {"affix": false, "cacheable": true, "file_path": "/device/device-details"}, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}]}, {"name": "内网网段管理", "icon": "network", "path": "/device/networksegment", "is_show": false, "redirect_path": "", "priority": 98, "setting": {"affix": false, "cacheable": true, "file_path": "/networksegment/networksegment"}, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}]}, {"name": "内网网段管理详情", "icon": "", "path": "/device/networksegmentdetails/:id", "is_show": true, "redirect_path": "", "priority": 99, "setting": {"affix": false, "cacheable": true, "file_path": "/networksegment/networksegmentdetail"}, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}]}, {"name": "互联网网段管理", "icon": "network", "path": "/device/exnetworksegment", "is_show": false, "redirect_path": "", "priority": 97, "setting": {"affix": false, "cacheable": true, "file_path": "/exnetworksegment/exnetworksegment"}, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}]}, {"name": "互联网网段管理详情", "icon": "", "path": "/device/exnetworksegmentdetails/:id", "is_show": true, "redirect_path": "", "priority": 101, "setting": {"affix": false, "cacheable": true, "file_path": "/exnetworksegment/exnetworksegmentdetail"}, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}]}, {"name": "跨数据源字段分析", "icon": "taizhang", "path": "/device/analysisdata", "is_show": true, "redirect_path": "", "priority": 100, "setting": {"affix": false, "cacheable": true, "file_path": "/analysis/analysisdata"}, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}]}, {"name": "账户管理", "icon": "ums-role", "path": "/account/account-list", "is_show": false, "code": "账户管理", "redirect_path": "", "priority": 99, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}], "setting": {"affix": false, "cacheable": true, "file_path": "/account/account-list"}, "children": []}, {"name": "账户详情", "icon": "", "path": "/account/account-details/:id", "is_show": true, "code": "账户详情", "redirect_path": "", "priority": 99, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}], "setting": {"affix": false, "cacheable": true, "file_path": "/account/account-details"}, "children": []}]}, {"name": "互联网暴露面", "icon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-", "path": "/exposedsurface", "is_show": false, "redirect_path": "/exposedsurface/service", "priority": 99, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}], "setting": {"affix": false, "cacheable": true, "file_path": "/exposedsurface"}, "children": [{"name": "互联网服务映射", "icon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-", "path": "/exposedsurface/service", "is_show": false, "redirect_path": "", "priority": 100, "setting": {"affix": false, "cacheable": true, "file_path": "/exposedsurface/exposedsurfacedomain"}, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}]}, {"name": "互联网服务映射详情", "icon": "", "path": "/exposedsurface/servicedetail/:id", "is_show": true, "redirect_path": "", "priority": 99, "setting": {"affix": false, "cacheable": true, "file_path": "/exposedsurface/exposedsurfacedomaindetail"}, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}]}, {"name": "互联网暴露端口", "icon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-", "path": "/exposedsurface/views", "is_show": false, "redirect_path": "", "priority": 99, "setting": {"affix": false, "cacheable": true, "file_path": "/exposedsurface/exposedview"}, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}]}, {"name": "互联网暴露端口详情", "icon": "", "path": "/exposedsurface/viewsdetail/:id", "is_show": true, "redirect_path": "", "priority": 99, "setting": {"affix": false, "cacheable": true, "file_path": "/exposedsurface/exposedviewdetail"}, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}]}, {"name": "域名管理", "icon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-", "path": "/exposedsurface/domain", "is_show": false, "redirect_path": "", "priority": 98, "setting": {"affix": false, "cacheable": true, "file_path": "/exposedsurface/exposeddomain"}, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}]}, {"name": "域名管理详情", "icon": "", "path": "/exposedsurface/exposeddomaindetail/:id", "is_show": true, "redirect_path": "", "priority": 99, "setting": {"affix": false, "cacheable": true, "file_path": "/exposedsurface/exposeddomaindetail"}, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}]}, {"name": "互联网IP管理", "icon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-", "path": "/exposedsurface/exposedip", "is_show": false, "redirect_path": "", "priority": 97, "setting": {"affix": false, "cacheable": true, "file_path": "/exposedsurface/exposedip"}, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}]}, {"name": "互联网IP管理详情", "icon": "", "path": "/exposedsurface/exposedipdetail/:id", "is_show": true, "redirect_path": "", "priority": 99, "setting": {"affix": false, "cacheable": true, "file_path": "/exposedsurface/exposedipdetail"}, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}]}]}, {"name": "漏洞管理", "icon": "loudonggua<PERSON><PERSON>", "path": "/vulnerability", "is_show": false, "code": "漏洞管理", "priority": 98, "redirect_path": "/vulnerability/vulnerability-list", "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}], "setting": {"affix": false, "cacheable": true, "file_path": "/vulnerability"}, "children": [{"name": "漏洞实例管理", "icon": "loudongqingbao", "path": "/vulnerability/vulnerability-list", "is_show": false, "code": "漏洞实例", "redirect_path": "", "priority": 101, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}], "setting": {"affix": false, "cacheable": true, "file_path": "/vulnerability/vulnerability-list"}, "children": []}, {"name": "漏洞实例详情", "icon": "", "path": "/vulnerability/vulnerability-details/:id", "is_show": true, "code": "漏洞实例详情", "redirect_path": "", "priority": 101, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}], "setting": {"affix": false, "cacheable": true, "file_path": "/vulnerability/vulnerability-details"}, "children": []}, {"name": "漏洞情报管理", "icon": "renfangqingbao", "path": "/vulnerability/vulintelligence", "is_show": false, "redirect_path": "", "priority": 101, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}], "setting": {"affix": false, "cacheable": true, "file_path": "/vulnerability/vulintelligence"}}, {"name": "漏洞情报管理详情", "icon": "", "path": "/vulnerability/vulintelligencedetail/:id", "is_show": true, "redirect_path": "", "priority": 101, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}], "setting": {"affix": false, "cacheable": true, "file_path": "/vulnerability/vulintelligencedetail"}}, {"name": "漏洞优先级设置", "icon": "youxianji1", "path": "/vulnerability/priority", "is_show": false, "redirect_path": "", "priority": 101, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}], "setting": {"affix": false, "cacheable": true, "file_path": "/vulnerability/priority"}}, {"name": "通知报告管理", "icon": "jianguanfengkong", "path": "/vulnerability/regulatorynotification", "is_show": false, "redirect_path": "", "priority": 101, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}], "setting": {"affix": false, "cacheable": true, "file_path": "/vulnerability/regulatorynotification"}}, {"name": "基础软硬件产品缺陷", "icon": "ruanjiankaifabao", "path": "/vulnerability/flaw", "is_show": false, "redirect_path": "", "priority": 101, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}], "setting": {"affix": false, "cacheable": true, "file_path": "/vulnerability/flaw"}}, {"name": "基础软硬件产品缺陷详情", "icon": "ruanjiankaifabao", "path": "/vulnerability/flawdetail/:id", "is_show": true, "redirect_path": "", "priority": 101, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}], "setting": {"affix": false, "cacheable": true, "file_path": "/vulnerability/flawdetail"}}]}, {"name": "业务系统", "icon": "tubiaozhizuomoban-", "path": "/business", "is_show": false, "redirect_path": "/business/business-cad", "priority": 97, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}], "setting": {"affix": false, "cacheable": true, "file_path": "/business"}, "children": [{"name": "系统画像", "icon": "tubiaozhizuomoban-", "path": "/business/business-cad", "is_show": false, "redirect_path": "", "priority": 100, "setting": {"affix": false, "cacheable": true, "file_path": "/business/business-cad"}, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}]}, {"name": "业务画像", "icon": "", "path": "/business/business-details/:id", "is_show": true, "redirect_path": "", "priority": 99, "setting": {"affix": false, "cacheable": true, "file_path": "/business/business-details"}, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}]}, {"name": "系统清单", "icon": "ruanjiankaifabao", "path": "/business/bussinesslist", "is_show": false, "redirect_path": "", "priority": 99, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}], "setting": {"affix": false, "cacheable": true, "file_path": "/bussinesslist/bussinesslist"}}, {"name": "系统清单详情", "icon": "ruanjiankaifabao", "path": "/business/bussinesslistdetail/:id", "is_show": true, "redirect_path": "", "priority": 101, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}], "setting": {"affix": false, "cacheable": true, "file_path": "/bussinesslist/bussinessdetail"}}]}, {"name": "数据接入", "icon": "shujuronghe1", "path": "/governancedata", "is_show": false, "redirect_path": "/governancedata/model", "priority": 95, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}], "setting": {"affix": false, "cacheable": true, "file_path": "/governancedata"}, "children": [{"name": "适配器", "icon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "path": "/adapters", "is_show": false, "redirect_path": "/adapters/adapters", "priority": 101, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}], "setting": {"affix": false, "cacheable": true, "file_path": "/adapters/adaptercontroll"}, "children": [{"name": "适配器", "icon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "path": "/adapters/adapters", "is_show": false, "redirect_path": "", "priority": 100, "setting": {"affix": false, "cacheable": true, "file_path": "/adapters/adapters"}, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}]}, {"name": "适配器连接", "icon": "detail", "path": "/adapters/connections", "is_show": true, "redirect_path": "", "priority": 99, "setting": {"affix": false, "cacheable": true, "file_path": "/adapters/connections"}, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}]}, {"name": "采集数据", "icon": "detail", "path": "/adapters/fetchData", "is_show": true, "redirect_path": "", "priority": 99, "setting": {"affix": false, "cacheable": true, "file_path": "/adapters/fetchData"}, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}]}, {"name": "融合历史", "icon": "detail", "path": "/adapters/fabrichistory", "is_show": true, "redirect_path": "", "priority": 99, "setting": {"affix": false, "cacheable": false, "file_path": "/adapters/fabrichistory"}, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}]}, {"name": "融合详情", "icon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-", "path": "/adapters/fabricdetail/:id", "is_show": true, "redirect_path": "", "priority": 99, "setting": {"affix": false, "cacheable": false, "file_path": "/adapters/fabricdetail"}, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}]}, {"name": "适配器获取历史", "icon": "detail", "path": "/adapters/get-history", "is_show": true, "redirect_path": "", "priority": 98, "setting": {"affix": false, "cacheable": true, "file_path": "/adapters/get-history"}, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}]}]}, {"name": "资产类型管理", "icon": "zichan<PERSON><PERSON>ing", "path": "/governancedata/devicetype", "is_show": false, "redirect_path": "", "priority": 100, "setting": {"affix": false, "cacheable": true, "file_path": "/governancedata/devicetype"}, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}]}, {"name": "字段集管理", "icon": "Tab_zidingyiziduan", "path": "/governancedata/fieldSet", "is_show": false, "redirect_path": "", "priority": 99, "setting": {"affix": false, "cacheable": true, "file_path": "/governancedata/fieldset"}, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}]}, {"name": "模型管理", "icon": "moxing", "path": "/governancedata/model", "is_show": false, "redirect_path": "", "priority": 98, "setting": {"affix": false, "cacheable": true, "file_path": "/governancedata/model"}, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}]}, {"name": "资产映射管理", "icon": "y<PERSON>he", "path": "/governancedata/parse", "is_show": false, "redirect_path": "", "priority": 97, "setting": {"affix": false, "cacheable": true, "file_path": "/governance/parse"}, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}]}, {"name": "融合管理", "icon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "path": "/governancedata/fusion", "is_show": false, "redirect_path": "", "priority": 96, "setting": {"affix": false, "cacheable": true, "file_path": "/governance/fusion"}, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}]}, {"name": "流程测试", "icon": "liucheng1", "path": "/governancedata/poc", "is_show": false, "redirect_path": "", "priority": 96, "setting": {"affix": false, "cacheable": true, "file_path": "/pipeline/index"}, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}]}, {"name": "漏洞历史", "icon": "lishijilu_o", "path": "/basicdata/vul", "is_show": false, "redirect_path": "", "priority": 95, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}], "setting": {"affix": false, "cacheable": true, "file_path": "/basicdata/vulnerabilitydata"}}, {"name": "漏洞历史详情", "icon": "", "path": "/basicdata/vuldetail/:id", "is_show": true, "redirect_path": "", "priority": 95, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}], "setting": {"affix": false, "cacheable": true, "file_path": "/basicdata/vulnerabilitydatadetail"}}, {"name": "网络配置", "icon": "lishijilu_o", "path": "/basicdata/networksetting", "is_show": false, "redirect_path": "", "priority": 95, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}], "setting": {"affix": false, "cacheable": true, "file_path": "/basicdata/network"}}, {"name": "网络配置详情", "icon": "", "path": "/basicdata/networksetting/:id", "is_show": true, "redirect_path": "", "priority": 95, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}], "setting": {"affix": false, "cacheable": true, "file_path": "/basicdata/networkdetail"}}]}, {"name": "系统管理", "icon": "xitongguanli", "path": "/system", "is_show": false, "redirect_path": "/system/user", "priority": 94, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}], "setting": {"affix": false, "cacheable": false, "file_path": "/system"}, "children": [{"name": "用户管理", "icon": "zhanghuxinxi1", "path": "/system/user", "is_show": false, "redirect_path": "", "priority": 100, "setting": {"affix": false, "cacheable": false, "file_path": "/system/user"}, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": ["caasm_webapi.app.auth.views.user.UserListView", "caasm_webapi.app.auth.views.user.UserAddView", "caasm_webapi.app.auth.views.user.UserModifyView", "caasm_webapi.app.auth.views.user.UserDeleteView"]}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": ["caasm_webapi.app.auth.views.user.UserListView"]}]}, {"name": "角色管理", "icon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "path": "/system/role", "is_show": false, "redirect_path": "", "priority": 100, "setting": {"affix": false, "cacheable": false, "file_path": "/system/role"}, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": ["caasm_webapi.app.permission.views.role.RoleListAPI", "caasm_webapi.app.permission.views.role.RoleAddAPI", "caasm_webapi.app.permission.views.role.RoleModifyAPI", "caasm_webapi.app.permission.views.role.RoleDeleteAPI", "caasm_webapi.app.permission.views.menu.MenuListAPI"]}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": ["caasm_webapi.app.permission.views.role.RoleListAPI"]}]}, {"name": "角色分配", "icon": "jiaosefenpei", "path": "/system/department", "is_show": false, "redirect_path": "", "priority": 100, "setting": {"affix": false, "cacheable": false, "file_path": "/system/department"}, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": ["caasm_webapi.app.permission.views.role.TotalRoleAPI", "caasm_webapi.app.auth.views.user.TotalUserAPI", "caasm_webapi.app.auth.views.user.UserListView", "caasm_webapi.app.auth.views.user.UserModifyView"]}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": ["caasm_webapi.app.auth.views.user.UserListView"]}]}, {"name": "操作审计", "icon": "jilu", "path": "/system/audit", "is_show": false, "redirect_path": "", "priority": 100, "setting": {"affix": false, "cacheable": false, "file_path": "/system/audit"}, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}]}, {"name": "变量管理", "icon": "bianliangfuzhi", "path": "/system/variable", "is_show": false, "redirect_path": "", "priority": 100, "setting": {"affix": false, "cacheable": false, "file_path": "/system/variable"}, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}]}, {"name": "系统设置", "icon": "shezhi1", "path": "/system/setting", "is_show": false, "redirect_path": "", "priority": 100, "setting": {"affix": false, "cacheable": false, "file_path": "/system/setting"}, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}]}, {"name": "系统监控", "icon": "monitoring", "path": "/system/monitor", "is_show": false, "redirect_path": "", "priority": 100, "setting": {"affix": false, "cacheable": false, "file_path": "/system/systemmonitor"}, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}]}, {"name": "导出管理", "icon": "daochu", "path": "/system/export", "is_show": false, "redirect_path": "", "priority": 100, "setting": {"affix": false, "cacheable": false, "file_path": "/system/exporttask"}, "actions": [{"code": "invisible", "name": "不可见", "icon": ""}, {"code": "editable", "name": "可编辑", "icon": "", "permission_codes": []}, {"code": "read_only", "name": "只读", "icon": "", "permission_codes": []}]}]}]