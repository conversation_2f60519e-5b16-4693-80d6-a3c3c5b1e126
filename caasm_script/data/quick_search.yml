vul_instance_unique:
  - display_name: 今日新增漏洞
    asql: $.vul_instance_unique.status = '新增'
  - display_name: 今日复现漏洞
    asql: $.vul_instance_unique.status = '复现'
  - display_name: 活动漏洞
    asql: $.vul_instance_unique.status = '活动'
  - display_name: 已消亡漏洞
    asql: $.vul_instance_unique.status = '消亡'

asset:
  - display_name: IP资产检索
    asql: $.network.ips.addr = "{ip}"
    params_type: "dynamic"
    params:
      - name: ip
        description: 要查询的IP地址
        display_name: IP地址
        type: ip

network:
  - display_name: IP检索
    asql: ($.internet_entry.cmcc_ip = "{ip}" or $.internet_entry.cmcc_ip_v6 = "{ip}" or $.internet_entry.ctcc_ip = "{ip}" or $.internet_entry.ctcc_ip_v6 = "{ip}" or  $.internet_entry.cucc_ip = "{ip}" or  $.internet_entry.cucc_ip_v6 = "{ip}" or $.internet_entry.internet_ips = "{ip}" or $.internet_entry.intranet_ip = "{ip}" or $.internet_entry.intranet_ip_v6 = "{ip}") or ($.network_mapping.entry_ip = "{ip}" or $.network_mapping.entry_ip_v6 = "{ip}" or $.network_mapping.destinations.ip = "{ip}" or $.network_mapping.destinations.ip_v6 = "{ip}" or  $.network_mapping.device_ip = "{ip}")
    params_type: "dynamic"
    params:
      - name: ip
        description: 要查询的IP地址
        display_name: IP地址
        type: ip


account:
  - display_name: "账户关键字检索"
    asql: $.account.username.regex("{keyword}") or $.account.owner_name.regex("{keyword}") or $.account.nickname.regex("{keyword}") or $.account.email.regex("{keyword}") or  $.account.phone.regex("{keyword}")
    params_type: "dynamic"
    params:
      - name: keyword
        description: 关键字信息，可以检索邮箱、账户名称、责任人名称、手机号、邮箱
        display_name: 关键字
        type: keyword