name: "sangfor_hci"
display_name: "HCI6.3.0"
description: "超融合软件由计算虚拟化aSV、存储虚拟化aSAN、网络虚拟化aNET、安全虚拟化aSEC组成，以搭建在超融合平台之上的云管理平台承载多HCI集群的管理运维工作，提供可靠中心、安全中心、监控中心、纳管第三方资源等高级运维功能，具备稳定可靠、性能优异、安全有效、智能便捷的特点。同时，超融合软件原生适配的X86和ARM底层架构进一步提供向国产化持续演进的能力，可为数字化转型的各类系统、平台以及数据中心建设提供先进的、软件定义的基础设施方案。"
type: "虚拟化平台"
company: "深信服"
logo: "sangfor_hci.png"
version: "v0.1"
priority: 1
properties:
  - "虚拟化平台"

connection:
  - name: username
    type: string
    required: true
    display_name: "用户名"
    description: "用户名称"
    validate_rules:
      - name: length
        error_hint: "用户名长度必须大于等于2且小于等于100"
        setting:
          min: 2
          max: 100

  - name: password
    type: password
    required: true
    display_name: "密码"
    description: "密码信息"
    validate_rules:
      - name: length
        error_hint: "密码长度必须大于等于6且小于等于100"
        setting:
          min: 6
          max: 100

  - name: address
    type: url
    required: true
    display_name: "地址"
    description: "地址信息"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'


fetch_setting:
  type: disposable
  point: "sangfor_hci.fetch:find_asset"
  size: 1000
  fetch_type_mapper:
    asset:
      - asset

fabric_setting:
  choose_point_mapper:
    asset: "sangfor_hci.fabric:choose_new_record"

merge_setting:
  size: 1000
  setting: { }

convert_setting:
  size: 1000
  before_executor_mapper: { }
  executor_mapper: { }