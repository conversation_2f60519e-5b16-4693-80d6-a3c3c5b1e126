name: "sundray_iot"
display_name: "物联网开放平台"
description: "信锐物联网开放平台为企业和集成商提供传感硬件接入、物联通信组网、IOT平台技术基础框架和底层服务，让使用者高效、低成本、个性化部署物联网行业应用，助力企业物联网升级。目前信锐物联平台已经有海量传感器接入保障场景的应用，联接网关和数据回传设备（LoRaWan、Zigbee3.0、RS485、网口等）标准接入各种行业智能设备。"
type: "物联网安全"
company: "信锐"
logo: "sundray_iot.png"
version: "v0.1"
priority: 1
properties:
  - "物联网安全"

connection:
  - name: username
    type: string
    required: true
    display_name: "用户名"
    description: "用户名称"
    validate_rules:
      - name: length
        error_hint: "用户名长度必须大于等于2且小于等于100"
        setting:
          min: 2
          max: 100

  - name: password
    type: password
    required: true
    display_name: "密码"
    description: "密码信息"
    validate_rules:
      - name: length
        error_hint: "密码长度必须大于等于6且小于等于100"
        setting:
          min: 6
          max: 100

  - name: address
    type: url
    required: true
    display_name: "地址"
    description: "地址信息"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'


fetch_setting:
  type: disposable
  point: "sundray_iot.fetch:find_asset"
  size: 1000
  fetch_type_mapper:
    asset:
      - asset

fabric_setting:
  choose_point_mapper:
    asset: "sundray_iot.fabric:choose_new_record"

merge_setting:
  size: 1000
  setting: { }

convert_setting:
  size: 1000
  before_executor_mapper: { }
  executor_mapper: { }