from caasm_adapter.sdk.adapter_fetch import fetch_sdk
from sangfor_sip.manage import SangforSipManager
import logging

log = logging.getLogger()


def find_asset(connection, fetch_type, page_index, page_size, session=None, condition=None, **kwargs):
    records = SangforSipManager(connection, session=session, condition=condition).find_asset(
        fetch_type, page_index, page_size
    )
    # page_index 默认是0，但是优维是从1开始
    result = fetch_sdk.build_asset(records, fetch_type)

    return fetch_sdk.return_success(result)


def build_query_condition(connection, session, fetch_type):
    return {"host": {"start_time": 0, "status": False}, "terminal": {"start_time": 0, "status": False}}


def get_auth_connection(connection=None, session=None, condition=None):
    SangforSipManager(connection, session=session, condition=condition).auth()
