import random
import struct
from hashlib import sha1

from sangfor_sip.clients.base import SangforSIPBaseClient


class SangforSIPAuthClient(SangforSIPBaseClient):
    URL = "/sangforinter/v1/auth/party/login"
    METHOD = "POST"

    def build_request_json(self, *args, **kwargs):
        int32_data = self.generate_random_32bit_int()
        return {
            "rand": int32_data,
            "userName": self.username,
            "clientProduct": "",
            "clientVersion": "",
            "clientId": "",
            "desc": "",
            "auth": self.build_auth(int32_data, desc=""),
            "platformName": self.platform,
        }

    def generate_random_32bit_int(self):
        # 生成一个32位无符号整数
        random_uint32 = random.randint(0, 2**32 - 1)

        # 使用struct模块将无符号整数转换为32位整数
        random_32bit_int = struct.unpack("i", struct.pack("I", random_uint32))[0]

        return random_32bit_int

    def build_auth(self, int32_data=None, desc=None):
        str_data = str(str(int32_data) + self.password + "sangfor3party" + self.username + desc)
        return sha1(str_data.encode()).hexdigest()

    @property
    def data_key_name(self):
        return "data.token"

    @property
    def flag_key_name(self):
        return "code"

    @property
    def suc_flag(self):
        return 0

    @property
    def username(self):
        return self.connection.get("username", "")

    @property
    def password(self):
        return self.connection.get("password", "")

    @property
    def platform(self):
        return self.connection.get("platform")
