from caasm_adapter.util.client import FetchJsonResultClient


class SangforSIPBaseClient(FetchJsonResultClient):
    def __init__(self, connection, session=None, token=None):
        super(SangforSIPBaseClient, self).__init__(connection, session)
        self._token = token

    def build_request_params(self, start_time=None, end_time=None, max_count=None):
        return {"token": self._token, "fromActionTime": start_time, "toActionTime": end_time, "maxCount": max_count}

    @property
    def suc_flag(self):
        raise NotImplementedError

    @property
    def flag_key_name(self):
        raise NotImplementedError

    @property
    def data_key_name(self):
        raise NotImplementedError
