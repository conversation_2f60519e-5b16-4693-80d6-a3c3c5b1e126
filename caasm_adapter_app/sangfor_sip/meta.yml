name: sangfor_sip
display_name: "深信服态势感知平台"
description: "融合了“安全运营”和“高级威胁检测”两大场景能力，旨在为用户构建一套集检测、可视、响应于一体的大数据智能安全分析平台，让网络安全可感知、易运营，安全事件快速联动闭环。"
type: "流量检测"
company: "深信服态势感知平台"
logo: "sangfor.png"
version: "v0.1"
priority: 1
properties:
  - 流量检测


connection:
  - name: address
    type: url
    required: true
    display_name: "请求地址"
    description: "请求地址，端口一般为 7443"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'

  - name: platform
    type: string
    required: true
    display_name: "平台"
    description: "在SIP平台中填写的平台"
    validate_rules:
      - name: length
        error_hint: "密钥长度必须大于等于6且小于等于100"
        setting:
          min: 0
          max: 10

  - name: username
    type: string
    required: true
    display_name: "用户名"
    validate_rules:
      - name: length
        error_hint: "密钥长度必须大于等于6且小于等于100"
        setting:
          min: 6
          max: 100

  - name: password
    type: password
    required: true
    display_name: "密码"
    validate_rules:
      - name: length
        error_hint: "密钥长度必须大于等于6且小于等于100"
        setting:
          min: 6
          max: 100

  - name: cidr
    type: list
    required: false
    display_name: "所属网段"
    description: "用于网段过滤，过滤掉不属于该网段的数据，如不填写，则采集所有数据"
    validate_rules:
      - name: element_length
        error_hint: "项目ID长度不合法。长度最小不得小于1，最大不得大于100"
        setting:
          type: string
          min: 1
          max: 100

fetch_setting:
  point: "sangfor_sip.fetch:find_asset"
  size: 20
  test_auth_point: "sangfor_sip.fetch:get_auth_connection"
  condition_point: "sangfor_sip.fetch:build_query_condition"
  is_need_test_service: true
  fetch_type_mapper:
    asset:
      - host
      - terminal

merge_setting: {}



