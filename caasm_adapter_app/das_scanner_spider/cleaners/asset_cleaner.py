import logging
from collections import defaultdict

from caasm_adapter.fetcher.cleaners.base import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from caasm_service.runtime import fetch_service
from caasm_tool.constants import FetchType

log = logging.getLogger()


class AssetCleaner(FetchAssetCleaner):
    def __init__(self, *args, **kwargs):
        self._storage = set()
        super(AssetCleaner, self).__init__(*args, **kwargs)

    def clean(self):
        offset = 0
        self._storage.clear()
        tmp_table = self.build_fetch_tmp_table()

        while True:
            fetch_data = self.find_fetch_data(offset)
            if not fetch_data:
                break
            offset += self.size
            fetch_records = self._parse_fetch_data(fetch_data)
            fetch_service.save_multi_direct(fetch_records, tmp_table)

        fetch_table = fetch_service.build_fetch_data_table(
            self._adapter_name, self._adapter_instance_id, FetchType.ASSET, self._fetch_index
        )
        fetch_service.drop(fetch_table)
        fetch_service.rename(tmp_table, fetch_table)

    def _parse_fetch_data(self, fetch_records):
        result = []

        for fetch_record in fetch_records:
            hosts = fetch_record["hosts"]
            vuls = fetch_record["vuls"]

            new_hosts = self.__parse_host(hosts)

            if not new_hosts:
                continue

            self.__merge_vul_to_host(new_hosts, vuls)

            result.extend(new_hosts)

        return result

    def __parse_host(self, hosts):
        result = []
        for host in hosts:
            ip = host["ip"]
            if ip in self._storage:
                continue

            self._storage.add(ip)
            host["vuls"] = []
            result.append(host)

        return result

    @classmethod
    def __merge_vul_to_host(cls, hosts, vuls):
        host_mapper = {host["ip"]: host for host in hosts}
        host_vul_exists_storage = defaultdict(set)

        for vul in vuls:
            ip_addresses = vul["ipaddrs"]
            vul_id = vul["id"]
            for ip_address in ip_addresses:
                ip, port = ip_address.split(":")
                if ip in host_mapper:
                    if vul_id in host_vul_exists_storage[ip]:
                        log.warning(f"IP({ip}) have merged vul({vul_id})")
                        continue

                    host_vul_exists_storage[ip].add(vul_id)
                    host_mapper[ip]["vuls"].append(vul)

    @property
    def data_types(self):
        return ["asset"]

    @property
    def size(self):
        return 5
