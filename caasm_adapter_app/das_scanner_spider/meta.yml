name: "das_scanner_spider"
display_name: "安恒明鉴（页面数据抓取）"
description: "明鉴数据库漏洞扫描系统是安恒在深入分析研究数据库典型安全漏洞以及流行的攻击技术基础上，研发的一款数据库安全评估工具。"
type: "脆弱性评估"
company: "安恒"
logo: "logo.png"
version: "v0.1"
priority: 1
properties:
  - "脆弱性评估"

connection:
  - name: username
    type: string
    required: true
    display_name: "用户名"
    description: "用户名称"
    validate_rules:
      - name: length
        error_hint: "用户名长度必须大于等于2且小于等于100"
        setting:
          min: 2
          max: 100

  - name: password
    type: password
    required: true
    display_name: "密码"
    description: "密码信息"
    validate_rules:
      - name: length
        error_hint: "密码长度必须大于等于6且小于等于100"
        setting:
          min: 6
          max: 100

  - name: address
    type: url
    required: true
    display_name: "地址"
    description: "地址信息"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'

  - name: query_day
    type: integer
    required: false
    display_name: "查询间隔天数"
    description: "查询间隔天数。比如间隔为90，从当采集开始计算，往后推90天作为查询时间段"
    validate_rules:
      - name: number
        error_hint: "查询间隔天数无效，请在1-90的范围选择输入"
        setting:
          min: 1
          max: 90

fetch_setting:
  type: disposable
  point: "das_scanner_spider.fetch:find_asset"
  test_auth_point: "das_scanner_spider.fetch:get_auth_connection"
  condition_point: "das_scanner_spider.fetch:build_query_condition"
  is_need_test_service: true
  size: 30
  fetch_type_mapper:
    asset:
      - asset
  cleaner_mapper:
    asset:
      default:
        - "das_scanner_spider.cleaners.asset_cleaner:AssetCleaner"

fabric_setting:
  choose_point_mapper:
    asset: "das_scanner_spider.fabric:choose_new_record"

merge_setting:
  size: 200
  setting:
    asset:
      asset:
        fields:
          - ip

convert_setting:
  size: 200
  before_executor_mapper:
    asset:
      - add:
          - field: asset_type
            value: "主机"
  executor_mapper:
    asset:
      主机:
        - add:
            - field: device_sid
              value: ${host_detail.id}

            - field: os.full
              value: ${system}

            - field: name
              value: ${host_detail.name}

            - field: host_name
              value: ${host_detail.name}

        - convert:
            - field: device_sid
              type: string

            - field: device_sid
              type: list

        - add:
            - field: device.sid
              value: ${device_sid}

        - for_add:
            - size: 1
              field: ips
              setting:
                - field: addr
                  value: ${ip}

        - rename:
            - src_field: host_services
              dst_field: ports
            - src_field: vuls
              dst_field: vulners

        - for:
            - field: ports
              handler:
                - method: drop
                  setting:
                    - field: id
                - method: rename
                  setting:
                    - src_field: servicename
                      dst_field: service_name
                    - src_field: port
                      dst_field: number
                    - src_field: v
                      dst_field: service_version
                    - src_field: pd
                      dst_field: description
                    - src_field: protocal
                      dst_field: protocol
                - method: convert
                  setting:
                    - field: number
                      type: int
            - field: vulners
              handler:
                - method: add
                  setting:
                    - field: type
                      value: 1
                - method: rename
                  setting:
                    - src_field: vul_detail.description
                      dst_field: description
                    - src_field: vul_detail.suggest
                      dst_field: solution
                    - src_field: vul_detail.cve
                      dst_field: cve_id
                    - src_field: vul_detail.cvss
                      dst_field: cvss_score
                - method: convert
                  setting:
                    - field: cvss_score
                      type: float

                - method: script
                  setting:
                    - content: |
                        ip_addresses = event.get("ipaddrs") or []
                        if not ip_addresses:
                            return
                        
                        services = []
                        
                        for ip_address in ip_addresses:
                            ip, port = ip_address.split(":")
                            services.append(
                                {
                                    "port": port,
                                    "ip": ip
                                }
                            )
                        event["services"] = services
        - filter:
            - field: ports
              son_field: number
              condition: equal_check
              setting:
                value: 0


        - script:
            - content: |
                ports = event.get("ports") or []
                port_mapper = {port["number"]: {"name": port["service_name"], "protocol": port["protocol"]} for port in ports}
                ip = event.get("ip")
                vulners = event.get("vulners") or []
                
                for vulner in vulners:
                    services = vulner.get("services")
                    new_services = []
                    for service in services:
                        tmp_ip = service.get("ip")
                        tmp_port = service.get("port")
                
                        if tmp_ip != ip:
                            continue
                        tmp_port = int(tmp_port)
                        if not tmp_port:
                            continue
                
                        port_info = port_mapper.get(tmp_port) or {}
                        port_info["port"] = tmp_port
                        new_services.append(port_info)
                
                    vulner["service"] = new_services

        - unwind:
            - src_field: vulners
              son_field: service