import datetime

from caasm_adapter.sdk.adapter_fetch import fetch_sdk
from das_scanner_spider.manage import DasScannerSpiderManager


def find_asset(connection, fetch_type, condition, page_index=0, page_size=1, session=None, **kwargs):
    query_time = condition.get("now")
    assets = DasScannerSpiderManager(connection, session).find_asset(page_index, page_size, query_time)
    return fetch_sdk.return_success(fetch_sdk.build_asset(assets, fetch_type))


def build_query_condition(connection, session=None, fetch_type=None):
    return {"now": datetime.datetime.now()}


def get_auth_connection(connection, session=None):
    DasScannerSpiderManager(connection, session).auth()
