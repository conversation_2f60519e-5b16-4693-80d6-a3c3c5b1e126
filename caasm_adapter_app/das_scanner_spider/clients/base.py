import logging
import time

from caasm_adapter.util.client import FetchJsonResultClient

log = logging.getLogger()


class DasScannerSpiderClient(FetchJsonResultClient):
    METHOD = "get"

    def __init__(self, connection, session=None, token=None):
        super(DasScannerSpiderClient, self).__init__(connection, session)
        self.timestamp = int(time.time())
        self.token = token

    def build_request_url(self, *args, **kwargs):
        result = super(DasScannerSpiderClient, self).build_request_url(*args, **kwargs)
        return result + f"?_t={self.timestamp}"

    @property
    def flag_key_name(self):
        return "success"

    @property
    def suc_flag(self):
        return True

    @property
    def data_key_name(self):
        return "data"

    def build_request_header(self, *args, **kwargs):
        bearer_token = "Bearer"
        if self.token:
            bearer_token += " " + self.token

        return {"Authorization": bearer_token}


class DasScannerSpiderPageClient(DasScannerSpiderClient):
    def clean_result(self, result):
        return result or []
