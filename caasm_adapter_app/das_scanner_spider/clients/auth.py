import base64
import datetime
import hashlib

import ddddocr
from Crypto.Cipher import AES

from caasm_adapter.util.exception import AdapterFetchAuthFailedException
from das_scanner_spider.clients.base import DasScannerSpiderClient


class DasScannerSpiderCaptchaClient(DasScannerSpiderClient):
    OCR = ddddocr.DdddOcr(beta=True, show_ad=False)
    URL = "/auth/captcha"
    METHOD = "get"

    @property
    def data_key_name(self):
        return ""

    def clean_result(self, result):
        base = result.get("base64")
        return {"captcha": self.OCR.classification(base64.b64decode(base)), "hash_id": result.get("hash", "")}

    def error_handle(self, err, *args, **kwargs):
        raise AdapterFetchAuthFailedException

    def parse_error_handle(self, result, *args, **kwargs):
        raise AdapterFetchAuthFailedException


class DasScannerSpiderAuthClient(DasScannerSpiderClient):
    _FORMAT = "%a, %d %b %Y %H:%M:%S GMT"
    URL = "/auth/tokens"
    METHOD = "post"

    def build_request_header(self, captcha, hash_id):
        headers = super(DasScannerSpiderAuthClient, self).build_request_header(captcha, hash_id)
        time_info = datetime.datetime.utcfromtimestamp(self.timestamp).strftime(self._FORMAT)
        headers.update({"Times": time_info, "Encrypt": "true", "Hash": hash_id, "User-Type": "admin"})
        return headers

    def _encrypt_password(self):
        password = self.password
        key = datetime.datetime.utcfromtimestamp(self.timestamp).strftime("%Y%d%H-%m-%M")
        enc_key = hashlib.md5(key.encode()).hexdigest()
        return Encryptor(enc_key.encode()).encrypt(password)

    def build_request_params(self, captcha, hash_id):
        return {"username": self.username, "password": self._encrypt_password(), "captcha": captcha}

    @property
    def password(self):
        return self.connection.get("password", "")

    @property
    def username(self):
        return self.connection.get("username", "")

    @property
    def data_key_name(self):
        return "token"

    def parse_error_handle(self, result, *args, **kwargs):
        raise AdapterFetchAuthFailedException

    def error_handle(self, err, *args, **kwargs):
        raise AdapterFetchAuthFailedException


class Encryptor(object):
    def __init__(self, key):
        self.key = key
        self.length = AES.block_size
        self.aes = AES.new(self.key, AES.MODE_ECB)

    def pad(self, text):
        count = len(text.encode("utf-8"))
        add = self.length - (count % self.length)
        enc_text = text + (chr(add) * add)
        return enc_text

    def encrypt(self, data):
        res = self.aes.encrypt(self.pad(data).encode("utf8"))
        return str(base64.b64encode(res), encoding="utf8")
