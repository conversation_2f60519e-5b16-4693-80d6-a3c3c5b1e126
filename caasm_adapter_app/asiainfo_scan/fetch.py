import time

from selenium.webdriver.common.by import By

from asiainfo_scan.manage import AsiainfoScanManager, FetchType
from caasm_adapter.sdk.adapter_fetch import fetch_sdk
from caasm_tool.util import build_url

URL = "/officescan/console/html/cgi/cgiChkMasterPwd.exe"


def get_asiainfo_cookie(connection, condition):
    driver = condition.get("driver")
    driver.get(build_url(connection.get("address"), URL))
    size = len(driver.requests)
    for i in range(0, size - 1):
        if "<PERSON><PERSON>" not in driver.requests[size - 1 - i].headers:
            continue
        return driver.requests[size - 1 - i].headers["Cookie"]


def find_asset(connection, fetch_type, page_index=0, page_size=1, session=None, condition=None, **kwargs):
    cookie = get_asiainfo_cookie(connection, condition)
    records = AsiainfoScanManager(connection, session=session, cookie=cookie).find_asset(
        fetch_type, page_index, page_size
    )
    result = fetch_sdk.build_asset(records, fetch_type)
    return fetch_sdk.return_success(result)


def build_query_condition(connection, session=None, fetch_type=None):
    driver = fetch_sdk.init_driver()
    password = connection.get("password")
    username = connection.get("username")
    driver.get(build_url(connection.get("address"), URL))
    account_ele = driver.find_element(By.ID, "labelUsername")
    account_ele.send_keys(username)
    password_ele = driver.find_element(By.XPATH, '//input[@id="labelPassword"]')
    password_ele.send_keys(password)
    button_ele = driver.find_element(By.ID, "btn-signin")
    button_ele.click()
    time.sleep(5)
    return {"driver": driver}


def free_condition(connection=None, session=None, condition=None):
    driver = condition.get("driver")
    if driver:
        driver.quit()


def get_auth_connection(connection=None, session=None):
    condition = {}
    try:
        condition = build_query_condition(connection=connection, session=session)
        cookie = get_asiainfo_cookie(connection, condition)
        AsiainfoScanManager(connection, session=session, cookie=cookie).find_asset(FetchType.TERMINAL)
    except Exception as e:
        driver = condition.get("driver")
        if driver:
            driver.quit()
        raise e
