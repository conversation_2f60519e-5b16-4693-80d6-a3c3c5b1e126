name: "asiainfo_scan"
display_name: "终端安全防护OfficeScan"
description: "亚信安全病毒防护OfficeScan，基于新一代终端防病毒技术，利用机器学习、行为监控、爆发阻止、云查杀和传统特征库结合的方式，有效防范恶意威胁软件、勒索病毒、挖矿软件等已知和未知威胁。通过行为监控、同时以插件化的方式构建终端安全平台，全面 覆盖威胁防御和终端安全管理，支持大规模分级部署、支持多级管理架构，从而为客户提供完整的一体化终端安全防护方案。"
type: "终端防护"
company: "亚信"
logo: "asiainfo_scan.png"
version: "v0.1"
priority: 1
properties:
  - "终端防护"
  - "防病毒"

connection:
  - name: username
    type: string
    required: true
    display_name: "用户名"
    description: "用户名称"
    validate_rules:
      - name: length
        error_hint: "用户名长度必须大于等于2且小于等于100"
        setting:
          min: 2
          max: 100

  - name: password
    type: password
    required: true
    display_name: "密码"
    description: "密码信息"
    validate_rules:
      - name: length
        error_hint: "密码长度必须大于等于6且小于等于100"
        setting:
          min: 6
          max: 100

  - name: address
    type: url
    required: true
    display_name: "地址"
    description: "地址信息"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'


fetch_setting:
  type: disposable
  point: "asiainfo_scan.fetch:find_asset"
  condition_point: "asiainfo_scan.fetch:build_query_condition"
  test_auth_point: "asiainfo_scan.fetch:get_auth_connection"
  finish_point: "asiainfo_scan.fetch:free_condition"
  is_need_test_service: true
  size: 1000
  fetch_type_mapper:
    asset:
      - terminal

fabric_setting:
  choose_point_mapper:
    asset: "asiainfo_scan.fabric:choose_new_record"

merge_setting:
  size: 1000
  setting: { }

convert_setting:
  size: 1000
  before_executor_mapper: { }
  executor_mapper: { }