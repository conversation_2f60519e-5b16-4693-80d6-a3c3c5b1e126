from caasm_adapter.sdk.adapter_fetch import fetch_sdk
from ct_cdn.manage import ChainNetCenterManager


def find(connection, fetch_type, page_index=0, page_size=1, session=None, **kwargs):
    records = _manager(connection, session).find(page_index + 1, page_size=page_size)
    result = fetch_sdk.build_asset(records, fetch_type)
    return fetch_sdk.return_success(result)


def auth(connection, session):
    _manager(connection, session).find(page_index=1, page_size=1)


def _manager(connection, session):
    return ChainNetCenterManager(connection, session)
