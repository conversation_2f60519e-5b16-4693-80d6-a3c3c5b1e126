import base64
import datetime
import hmac

from requests.auth import HTTP<PERSON>asic<PERSON><PERSON>

from caasm_adapter.util.client import FetchRestfulClient


class ChainNetCenterBaseClient(FetchRestfulClient):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._date = datetime.datetime.utcnow().strftime("%a, %d %b %Y %H:%M:%S GMT")

    def build_request_auth(self, *args, **kwargs):
        data = base64.b64encode(hmac.new(self.key.encode(), self._date.encode(), "sha1").digest())
        return HTTPBasicAuth(self.username, data)

    def build_request_header(self, *args, **kwargs):
        return {"Content-Type": "application/json", "Date": self._date, "Accept": "application/json"}

    @property
    def username(self):
        return self.connection.get("username", "")

    @property
    def key(self):
        return self.connection.get("key", "")

    @property
    def flag_key_name(self):
        return ""

    @property
    def suc_flag(self):
        return ""
