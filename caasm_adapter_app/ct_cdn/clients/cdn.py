from ct_cdn.clients.base import ChainNetCenterBaseClient


class Chain<PERSON><PERSON>enterCNDListClient(ChainNetCenterBaseClient):
    URL = "/api/domain/list"
    METHOD = "post"

    def build_request_json(self, page_index, page_size):
        return {
            "pageNumber": page_index,
            "pageSize": page_size,
        }

    @property
    def data_key_name(self):
        return "resultList"


class ChainNetCenterCNDDetailClient(ChainNetCenterBaseClient):
    URL = "/api/domain/"
    METHOD = "get"

    def build_request_url(self, domain_name):
        return super().build_request_url() + domain_name

    @property
    def suc_flag(self):
        return ""

    @property
    def data_key_name(self):
        return ""

    @property
    def flag_key_name(self):
        return ""
