name: "ct_cdn"
display_name: "网宿科技CDN"
description: "CDN是一个边缘分布式Serverless Nginx平台，可以为用户提供可编程式CDN交互服务。通过CDN，用户可以快速构建个性化业务体系，将业务应用下沉至边缘侧，以降低业务开发交付周期，实现CDN与软件业务开发生命周期的高效无缝衔接。"
type: "网络配置"
company: "网宿科技"
logo: "ct_cdn.png"
version: "v0.1"
priority: 1
properties:
  - "CDN"

connection:
  - name: address
    type: url
    required: true
    display_name: "地址"
    description: "地址信息"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'

  - name: username
    type: string
    required: true
    display_name: "用户名"
    description: "用户名称"
    validate_rules:
      - name: length
        error_hint: "用户名长度必须大于等于2且小于等于100"
        setting:
          min: 2
          max: 100

  - name: key
    type: password
    required: true
    display_name: "密钥"
    description: "密钥信息"
    validate_rules:
      - name: length
        error_hint: "密钥长度必须大于等于6且小于等于100"
        setting:
          min: 6
          max: 100


fetch_setting:
  type: disposable
  point: "ct_cdn.fetch:find"
  test_auth_point: "ct_cdn.fetch:auth"
  is_need_test_service: true
  size: 200
  fetch_type_mapper:
    network:
      - network_mapping
  cleaner_mapper:
    network:
      network_mapping:
        - "ct_cdn.cleaners.cdn_cleaner:CDNCleaner"

fabric_setting:
  choose_point_mapper: { }

merge_setting:
  size: 200
  setting: { }

convert_setting:
  size: 200
  before_executor_mapper: { }
  executor_mapper: { }