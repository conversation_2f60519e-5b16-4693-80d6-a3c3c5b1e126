from IPy import IP

from caasm_adapter.fetcher.cleaners.base import FetchTotalBaseCleaner


class CDNCleaner(FetchTotalBaseCleaner):
    def build_common(self, biz_records):
        return [self._format_record(biz_record) for biz_record in biz_records if biz_record["status"] == "enabled"]

    def _format_record(self, biz_record):
        domain_name = biz_record["domainName"]
        detail = biz_record.get("detail", {})

        origin_config = detail.get("origin-config", {})

        origin_ips = origin_config.get("origin-ips", "").split(";")
        adv_origi_configs = origin_config.get("adv-origin-configs", {}).get("adv-origin-config", [])

        for adv_origi_config in adv_origi_configs:
            origin_ips.extend(adv_origi_config.get("master-ips", "").split(";"))
            origin_ips.extend(adv_origi_config.get("backup-ips", "").split(";"))

        origin_ips = list(set(origin_ips))
        return {
            "domainName": domain_name,
            "backendResources": [self._format_address(origin_ip) for origin_ip in origin_ips],
        }

    @classmethod
    def _format_address(cls, data):
        domain = ip = ""
        try:
            ip = IP(data).strFullsize()
        except Exception as e:
            domain = ip
        addr = domain or ip
        addr_type = 1 if ip else 2
        return {"addr": addr, "type": addr_type}
