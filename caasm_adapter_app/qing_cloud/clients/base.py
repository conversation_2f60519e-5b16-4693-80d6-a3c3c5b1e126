import base64
import datetime
import hmac
import logging
import urllib.parse as urllib2
import uuid
from hashlib import sha256

from caasm_adapter.util.client import FetchJsonResultClient
from caasm_tool.util import extract

log = logging.getLogger()


class QingCloudClient(FetchJsonResultClient):
    URL = "/iaas/"
    ACTION = ""
    METHOD = "get"
    SUCCESS_CODE = 0

    def __init__(self, *args, **kwargs):
        super(QingCloudClient, self).__init__(*args, **kwargs)
        self._time_stamp = datetime.datetime.utcnow().strftime("%Y-%m-%dT%H:%M:%SZ")
        self._req_id = uuid.uuid4().hex

    def build_request_common_params(self):
        params = {
            "action": self.ACTION,
            "time_stamp": self._time_stamp,
            "access_key_id": self.access_key,
            "signature_version": self.signature_version,
            "signature_method": self.signature_method,
            "zone": self.zone,
            "req_id": self._req_id,
            "verbose": self.verbose,
            "version": self.version,
        }
        return params

    def check_biz_result(self, result):
        if not result:
            return False
        if not self.flag_key_name:
            return True
        suc_flag = extract(result, self.flag_key_name)
        if self.compare_flag(suc_flag) or suc_flag == 5100:
            return True
        return False

    def parse_biz_result(self, result, *args, **kwargs):
        result = super(FetchJsonResultClient, self).parse_biz_result(result)
        if not self.check_biz_result(result):
            log.warning(f"{self.name}Response error. detail is {result}")
            return self.parse_error_handle(result, *args, **kwargs)

        data_key_name = self.data_key_name
        suc_flag = extract(result, self.flag_key_name)
        if suc_flag == 5100:
            return []

        if data_key_name:
            result = extract(result, data_key_name)
        try:
            result = self.clean_result(result)
        except Exception as e:
            log.warning(f"Clean result error({e})")
            return self.parse_error_handle(result, *args, **kwargs)
        else:
            return result

    def send_request(self, request_url, params, json_data, form_data, headers, cookies, auth):
        common_params = self.build_request_common_params()
        if params:
            params.update(common_params)
        else:
            params = common_params

        for i in [params, json_data, form_data]:
            if not i:
                continue
            i["signature"] = self.compute_sign(i)
        return super(QingCloudClient, self).send_request(
            request_url, params, json_data, form_data, headers, cookies, auth
        )

    @property
    def flag_key_name(self):
        return "ret_code"

    @property
    def suc_flag(self):
        return self.SUCCESS_CODE

    @property
    def access_key(self):
        return self.connection.get("access_key", "")

    @property
    def secret_key(self):
        return self.connection.get("secret_key", "")

    @property
    def signature_version(self):
        return self.connection.get("signature_version", 1)

    @property
    def signature_method(self):
        return self.connection.get("signature_method", "HmacSHA256")

    @property
    def version(self):
        return self.connection.get("version", 1)

    @property
    def verbose(self):
        return self.connection.get("verbose", 0)

    @property
    def zone(self):
        return self.connection.get("zone")

    def compute_sign(self, params):
        sign_params = []

        sorted_param_keys = sorted(params.keys())

        for param_key in sorted_param_keys:
            param_val = str(params[param_key])
            param_key = str(param_key)

            param_key = urllib2.quote(param_key.encode(), safe="")
            param_val = urllib2.quote(param_val, safe="-_~")
            sign_params.append(f"{param_key}={param_val}")
        sign_param_str = "&".join(sign_params)

        wait_sign_params = [self.METHOD.upper(), self.URL, sign_param_str]

        wait_sign_byte = "\n".join(wait_sign_params).encode()

        # 前面生成的被签名串
        log.debug(f"Qing Cloud SignStr is {wait_sign_byte}")
        encrypt_obj = hmac.new(self.secret_key.encode(), wait_sign_byte, digestmod=sha256)
        signature = base64.b64encode(encrypt_obj.digest()).strip()
        return signature
