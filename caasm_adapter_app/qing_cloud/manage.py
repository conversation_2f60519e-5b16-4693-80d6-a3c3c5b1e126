from caasm_adapter.util.exception import AdapterFetchApiNotSupportException
from qing_cloud.clients.ecs import QingCloudEcsListClient


class ApiType(object):
    VIRTUAL_MACHINE = "host"


class QingCloudManger(object):
    client_mapper = {ApiType.VIRTUAL_MACHINE: QingCloudEcsListClient}

    def __init__(self, connection, session=None):
        self._connection = connection
        self._session = session

    def find(self, fetch_type, page_index, page_size):
        limit = page_size
        offset = 1 if page_index == 1 else page_index * page_size
        return self._instance(fetch_type).handle(offset=offset, limit=limit)

    def _instance(self, api_type):
        clazz = self.client_mapper.get(api_type)
        if not clazz:
            raise AdapterFetchApiNotSupportException()
        return clazz(self._connection, self._session)
