name: qing_cloud
display_name: "青云"
description: "青云QingCloud完全国产化，核心技术100%自研；无缝打通公有云和私有云，交付完全一致的功能和体验；经大规模实践验证，行业落地经验丰富；“云、网、边、端”全面布局。"
type: "云平台"
company: "青云"
logo: "qing_cloud.png"
version: "v0.1"
priority: 10
properties:
  - 云管

connection:
  - name: access_key
    type: string
    required: true
    display_name: "access_key"
    description: "access_key"
    validate_rules:
      - name: length
        error_hint: "access_key格式无效。长度最小不得小于2，最大不得大于100"
        setting:
          min: 2
          max: 100

  - name: secret_key
    type: password
    required: true
    display_name: "secret_key"
    description: "secret_key"
    validate_rules:
      - name: length
        error_hint: "secret_key格式无效。长度最小不得小于6，最大不得大于200"
        setting:
          min: 6
          max: 200

  - name: address
    type: url
    required: true
    display_name: "地址"
    description: "地址信息"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'

  - name: zone
    type: string
    required: true
    display_name: "区域"
    description: "区域信息"
    validate_rules: [ ]

fetch_setting:
  size: 100
  point: "qing_cloud.fetch:find_asset"
  is_need_test_service: true
  test_auth_point: "qing_cloud.fetch:get_auth_connection"
  fetch_type_mapper:
    asset:
      - host