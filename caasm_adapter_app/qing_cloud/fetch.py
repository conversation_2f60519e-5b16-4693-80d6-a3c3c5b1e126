from caasm_adapter.sdk.adapter_fetch import fetch_sdk
from qing_cloud.manage import QingCloudManger


def find_asset(connection, fetch_type, page_index=0, page_size=1, session=None, **kwargs):
    page_index = page_index + 1
    result = _build_manager(connection, session).find(fetch_type, page_index, page_size)
    result = fetch_sdk.build_asset(result, fetch_type)
    return fetch_sdk.return_success(result)


def get_auth_connection(connection, session):
    _build_manager(connection, session).find("host", 1, 1)


def _build_manager(connection, session):
    return QingCloudManger(connection, session)
