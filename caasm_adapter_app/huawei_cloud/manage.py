import copy
import logging

from caasm_tool.util import extract, restore
from huawei_cloud.clients.sdk.compute.cloud_server_group import HuaweiCloudServerGroupListClient
from huawei_cloud.clients.sdk.compute.ecs import HuaweiCloudECSListClient
from huawei_cloud.clients.region import HuaweiCloudRegionClient
from huawei_cloud.clients.sdk.databases.db_service import HuaweiCloudDBServiceListClient
from huawei_cloud.clients.sdk.network.cloud_load_balancer import (
    HuaweiCloudELBListClient,
    HuaweiCloudELBMemberListClient,
)
from huawei_cloud.clients.sdk.network.cloud_security_group import HuaweiCloudSecurityGroupsListClient
from huawei_cloud.clients.sdk.network.cloud_slb_listener import HuaweiCloudSLBListenersListClient
from huawei_cloud.clients.sdk.network.cloud_slb_security_policy import HuaweiCloudSLBSecurityPoliciesListClient
from huawei_cloud.clients.sdk.network.internet_ip import HuaweiCloudPublicIpListClient
from huawei_cloud.clients.sdk.network.vpc import HuaweiCloudVpcListClient
from huawei_cloud.clients.sdk.security.cdn import HuaweiCloudCDNListClient, HuaweiCloudCDNDetailClient
from huawei_cloud.clients.sdk.security.web_waf import (
    HuaweiCloudWebWafDomainNameListClient,
    HuaweiCloudWebWafDomainNameDetailClient,
)
from huawei_cloud.clients.sdk.storage.cloud_access_group import HuaweiCloudFileSystemRuleListClient
from huawei_cloud.clients.sdk.storage.cloud_file_system import HuaweiCloudFileSystemListClient
from huawei_cloud.clients.sdk.storage.disk import HuaweiCloudVolumesListClient
from huawei_cloud.clients.sdk.storage.object_storage_bucket import HuaweiCloudObsListClient, HuaweiCloudObsACLClient
from huawei_cloud.clients.sdk.storage.snapshot import HuaweiCloudSnapshotsListClient

log = logging.getLogger()


class ClientType(object):
    REGION = "region"
    ECS = "host"
    VPC = "vpc"
    CLOUD_DISK = "cloud_disk"
    EIP = "internet_ip"
    DB_SERVICE = "db_service"
    WEB_WAF_DOMAIN_LIST = "web_waf_domain_list"
    WEB_WAF_DOMAIN_DETAIL = "web_waf_domain_detail"
    CDN = "cdn"
    CDN_DETAIL = "cdn_detail"
    CLOUD_SNAPSHOT = "cloud_snapshot"
    CLOUD_FILE_SYSTEM = "cloud_file_system"
    CLOUD_SECURITY_GROUP = "cloud_security_group"
    OBJECT_STORAGE_BUCKET = "object_storage_bucket"
    OBJECT_STORAGE_BUCKET_ACL = "object_storage_bucket_acl"
    CLOUD_LOAD_BALANCER = "cloud_load_balancer"
    CLOUD_LOAD_BALANCER_MEMBERS = "cloud_load_balancer_members"
    CLOUD_SERVER_GROUP = "cloud_server_group"
    CLOUD_ACCESS_GROUP = "cloud_access_group"
    CLOUD_SLB_LISTENER = "cloud_slb_listener"
    CLOUD_SLB_SECURITY_POLICY = "cloud_slb_security_policy"


class FetchType(object):
    VPC = "vpc"
    EIP = "internet_ip"
    HOST = "host"
    CLOUD_DISK = "cloud_disk"
    DB_SERVICE = "db_service"
    CLOUD_SNAPSHOT = "cloud_snapshot"
    NETWORK_MAPPING = "network_mapping"
    OBJECT_STORAGE_BUCKET = "object_storage_bucket"
    CLOUD_FILE_SYSTEM = "cloud_file_system"
    CLOUD_SERVER_GROUP = "cloud_server_group"
    CLOUD_SECURITY_GROUP = "cloud_security_group"
    CLOUD_LOAD_BALANCER = "cloud_load_balancer"
    CLOUD_ACCESS_GROUP = "cloud_access_group"
    CLOUD_SLB_LISTENER = "cloud_slb_listener"
    CLOUD_SLB_SECURITY_POLICY = "cloud_slb_security_policy"


class HuaweiCloudManager(object):
    client_mapping = {
        ClientType.REGION: HuaweiCloudRegionClient,
        ClientType.ECS: HuaweiCloudECSListClient,
        ClientType.VPC: HuaweiCloudVpcListClient,
        ClientType.EIP: HuaweiCloudPublicIpListClient,
        ClientType.DB_SERVICE: HuaweiCloudDBServiceListClient,
        ClientType.CLOUD_SNAPSHOT: HuaweiCloudSnapshotsListClient,
        ClientType.CLOUD_FILE_SYSTEM: HuaweiCloudFileSystemListClient,
        ClientType.OBJECT_STORAGE_BUCKET: HuaweiCloudObsListClient,
        ClientType.OBJECT_STORAGE_BUCKET_ACL: HuaweiCloudObsACLClient,
        ClientType.CLOUD_DISK: HuaweiCloudVolumesListClient,
        ClientType.CLOUD_SECURITY_GROUP: HuaweiCloudSecurityGroupsListClient,
        ClientType.CLOUD_LOAD_BALANCER: HuaweiCloudELBListClient,
        ClientType.CLOUD_SLB_LISTENER: HuaweiCloudSLBListenersListClient,
        ClientType.CLOUD_LOAD_BALANCER_MEMBERS: HuaweiCloudELBMemberListClient,
        ClientType.CLOUD_SERVER_GROUP: HuaweiCloudServerGroupListClient,
        ClientType.CLOUD_ACCESS_GROUP: HuaweiCloudFileSystemRuleListClient,
        ClientType.CLOUD_SLB_SECURITY_POLICY: HuaweiCloudSLBSecurityPoliciesListClient,
        ClientType.WEB_WAF_DOMAIN_LIST: HuaweiCloudWebWafDomainNameListClient,
        ClientType.WEB_WAF_DOMAIN_DETAIL: HuaweiCloudWebWafDomainNameDetailClient,
        ClientType.CDN: HuaweiCloudCDNListClient,
        ClientType.CDN_DETAIL: HuaweiCloudCDNDetailClient,
    }

    TOTAL_FETCH_TYPE = [FetchType.CLOUD_SNAPSHOT]

    NETWORK_MAPPING_CLIENT_ARRAY = [ClientType.WEB_WAF_DOMAIN_LIST, ClientType.CDN]

    def __init__(self, connection, condition=None):
        self._connection = connection
        self._condition = condition or {}
        self._find_method = {
            FetchType.CLOUD_LOAD_BALANCER: self._find_elb,
            FetchType.OBJECT_STORAGE_BUCKET: self._find_osb,
            FetchType.CLOUD_SNAPSHOT: self._find_snapshot,
            FetchType.CLOUD_FILE_SYSTEM: self._find_file_system,
            FetchType.CLOUD_ACCESS_GROUP: self._find_access_group,
            FetchType.NETWORK_MAPPING: self._find_network_mapping,
        }

        self.network_mapper_method = {
            ClientType.WEB_WAF_DOMAIN_LIST: self._find_web_wef_domain_list,
            ClientType.CDN: self._find_cdn_domain_list,
        }

    def _find_network_mapping(self, fetch_type, page_size):
        result = []
        condition = copy.deepcopy(self._condition)
        for item in self.NETWORK_MAPPING_CLIENT_ARRAY:
            try:
                data = self.network_mapper_method.get(item)(item, page_size)
            except Exception as e:
                continue
            self._condition = condition
            result.extend(data)
        return result

    def _find_cdn_domain_list(self, client_type, page_size):
        new_result = []
        current_region, current_page, record_id = self._init_find()
        if not current_region:
            return []
        result = (
            self._call(client_type, current_region, page_index=current_page, page_size=page_size, record_id=record_id)
            or []
        )
        if result:
            for item in result:
                try:
                    domain_name = item.get("domain_name")
                    route_data = self._call(ClientType.CDN_DETAIL, region=current_region, domain_name=domain_name)
                    if route_data:
                        route_data["properties"] = "CDN"
                        new_result.append({"cdn_data": route_data})
                except Exception as e:
                    continue
            self._record_find(client_type, result)
            return new_result

        self._reset_find(client_type)
        return self._find_cdn_domain_list(client_type, page_size)

    def _find_web_wef_domain_list(self, client_type, page_size):
        current_region, current_page, record_id = self._init_find()
        if not current_region:
            return []
        result = (
            self._call(client_type, current_region, page_index=current_page, page_size=page_size, record_id=record_id)
            or []
        )
        if result:
            for item in result:
                route_data = {}
                try:
                    item["properties"] = "WAF"
                    instance_id = item.get("id")
                    route_data = self._call(
                        ClientType.WEB_WAF_DOMAIN_DETAIL, region=current_region, instance_id=instance_id
                    )
                    host_server = extract(route_data, "server")
                    for _server in host_server:
                        ip_type = extract(_server, "type")
                        address = extract(_server, "address")
                        _server["ipv4"] = address if ip_type == "ipv4" else ""
                        _server["ipv6"] = address if ip_type == "ipv6" else ""
                    restore("server", host_server, route_data)
                except Exception as e:
                    import traceback

                    continue
                item["detail_data"] = route_data
            self._record_find(client_type, result)
            return result

        self._reset_find(client_type)
        return self._find_web_wef_domain_list(client_type, page_size)

    def _find_access_group(self, fetch_type, page_size):
        """
        查看文件系统权限组
        首先要查询 文件系统
        """
        data_key, total_key = "shares", "count"
        current_region, current_page, record_id = self._init_find()
        if not current_region:
            return []
        total = self.get_condition_total(current_region, ClientType.CLOUD_FILE_SYSTEM)
        if total is not None and total < page_size * current_page:
            return []
        result = (
            self._call(
                ClientType.CLOUD_FILE_SYSTEM,
                current_region,
                page_index=current_page,
                page_size=page_size,
                record_id=record_id,
            )
            or {}
        )
        _total = extract(result, total_key)
        _data = extract(result, data_key) or []
        if _data:
            result = []
            restore(f"total.{ClientType.CLOUD_FILE_SYSTEM}.{current_region}", _total, self._condition)
            self._record_find(ClientType.CLOUD_FILE_SYSTEM, _data)
            for _item in _data:
                share_id = extract(_item, "id")
                access_group = self._call(fetch_type, region=current_region, share_id=share_id)
                if access_group:
                    result.extend(access_group)

            return result
        self._reset_find(ClientType.CLOUD_FILE_SYSTEM)
        return self._find_access_group(fetch_type=ClientType.CLOUD_ACCESS_GROUP, page_size=page_size)

    def _find_total_data(self, client_type=None, page_size=None, total_key="", data_key=""):
        current_region, current_page, record_id = self._init_find()
        if not current_region:
            return []
        total = self.get_condition_total(current_region, client_type)
        if total is not None and total < page_size * current_page:
            return []
        result = (
            self._call(client_type, current_region, page_index=current_page, page_size=page_size, record_id=record_id)
            or {}
        )
        _total = extract(result, total_key)
        _data = extract(result, data_key) or []
        if _data:
            restore(f"total.{client_type}.{current_region}", _total, self._condition)
            self._record_find(client_type, _data)
            return _data

        self._reset_find(client_type)
        return self._find_total_data(
            client_type=client_type, page_size=page_size, data_key=data_key, total_key=total_key
        )

    def _find_snapshot(self, fetch_type, page_size):
        return self._find_total_data(fetch_type, page_size, data_key="snapshots", total_key="count")

    def _find_file_system(self, fetch_type, page_size):
        return self._find_total_data(fetch_type, page_size, data_key="shares", total_key="count")

    def get_condition_total(self, region, fetch_type):
        fetch_total = self._condition.get("total", {}).get(fetch_type, {}).get(region)
        return fetch_total

    def _find_default(self, fetch_type, page_size):
        return self._find_core(fetch_type, page_size)

    def _find_osb(self, fetch_type, page_size):
        """
        查询 osb
        """
        if self._condition.get("osb") < 1:
            return []
        current_region, current_page, record_id = self._init_find()
        if not current_region:
            return []

        result = self._call(ClientType.OBJECT_STORAGE_BUCKET, current_region) or []
        if result:
            for osb in result:
                osb["_region"] = current_region
                bucket_name = osb.get("name")
                if not bucket_name:
                    continue
                osb_acl = self._find_osb_acl_by_bucket_name(current_region, bucket_name)
                osb["osb_acl"] = osb_acl
            regions = self._condition.get("regions")
            self._condition["current_region"] = regions.pop() if regions else None
            self._condition["osb"] = 0
            return result

        self._condition["current_page_index"] = 0
        self._condition["current_record_id"] = None
        regions = self._condition.get("regions")
        self._condition["current_region"] = regions.pop() if regions else None
        if self._condition["current_region"] == None:
            self._condition["osb"] = 0

        return self._find_osb(fetch_type, page_size)

    def _find_osb_acl_by_bucket_name(self, region, bucket_name):
        try:
            records = self._call(ClientType.OBJECT_STORAGE_BUCKET_ACL, region, bucketName=bucket_name)
            return records
        except Exception as e:
            log.error(f"get osb acl error ! {e}")
            return []

    def auth(self):
        HuaweiCloudRegionClient(self._connection)

    def find_region(self):
        return self._call(ClientType.REGION)

    def find_asset(self, fetch_type, page_size):
        if fetch_type == FetchType.NETWORK_MAPPING:
            result = self._find_network_mapping(fetch_type, page_size)
        else:
            result = self._find_method.get(fetch_type, self._find_default)(fetch_type, page_size)
            return self.clean_result(result)

    def clean_result(self, result):
        result = self.enrich_cloud_account(data=result)
        return result

    def enrich_cloud_account(self, data=None):
        if not self.cloud_account:
            return data
        for record in data:
            restore(field="cloud.account", value=self.cloud_account, result=record)
        return data

    def _find_elb(self, fetch_type, page_size):
        current_region, current_page, record_id = self._init_find()
        if not current_region:
            return []
        result = (
            self._call(fetch_type, current_region, page_index=current_page, page_size=page_size, record_id=record_id)
            or []
        )
        if result:
            for elb in result:
                pools = elb.get("pools", [])
                if not pools:
                    continue
                members = self._find_elb_member_by_pool(current_region, pools)
                elb["_members"] = members
            self._record_find(fetch_type, result)
            return result

        self._reset_find(fetch_type)
        return self._find_elb(fetch_type, page_size)

    def _find_core(self, client_type, page_size):
        current_region, current_page, record_id = self._init_find()
        if not current_region:
            return []
        result = (
            self._call(client_type, current_region, page_index=current_page, page_size=page_size, record_id=record_id)
            or []
        )
        if result:
            self._record_find(client_type, result)
            return result

        self._reset_find(client_type)
        return self._find_core(client_type, page_size)

    def _init_find(self):
        regions = self._condition.get("regions")
        current_region = self._condition.get("current_region")
        current_page = self._condition.get("current_page_index")

        if not current_region:
            current_region = regions.pop() if regions else None
        return current_region, current_page, self._condition.get("current_record_id")

    def _record_find(self, client_type, result):
        if "current_page_index" not in self._condition:
            self._condition["current_page_index"] = 0
        self._condition["current_page_index"] += 1
        for record in result:
            record["_region"] = self._condition["current_region"]
        self._condition["current_record_id"] = result[-1]["id"] if result else None

    def _reset_find(self, client_type):
        regions = self._condition.get("regions")
        self._condition["current_region"] = regions.pop() if regions else None
        self._condition["current_page_index"] = 0
        self._condition["current_record_id"] = None

    def _get_current_region(self, client_type):
        return self._condition.get("current_region")

    def _find_elb_member_by_pool(self, region, pool_ids):
        result = []
        for pool_id in pool_ids:
            _pool_id = pool_id.get("id")
            result.extend(self.__find_elb_members(_pool_id, region))
        return result

    def __find_elb_members(self, pool_id, region, page_size=200):
        result = []
        record_id = None
        while True:
            try:
                records = self._call(
                    ClientType.CLOUD_LOAD_BALANCER_MEMBERS,
                    region,
                    pool_id=pool_id,
                    page_size=page_size,
                    record_id=record_id,
                )
                if not records:
                    break
                result.extend(records)
                record_id = records[-1]["id"]
            except Exception as e:
                import traceback

                log.warning(f"Find elb members error({e})")
                break
        return result

    def _call(self, client_type, region=None, *args, **kwargs):
        return self.client_mapping[client_type](self._connection, region).handle(*args, **kwargs)

    @property
    def cloud_account(self):
        return self._connection.get("cloud_account")
