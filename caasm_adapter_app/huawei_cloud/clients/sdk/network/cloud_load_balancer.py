from huaweicloudsdkelb.v3.region.elb_region import ElbRegion
from huaweicloudsdkelb.v3 import ElbClient, ListLoadBalancersRequest, ListMembersRequest

from huawei_cloud.clients.sdk.base import HuaweiCloudBaseClient


class HuaweiCloudELBListClient(HuaweiCloudBaseClient):
    def build_request(self, page_size, record_id=None, *args, **kwargs):
        return ListLoadBalancersRequest(limit=page_size, marker=record_id)

    @property
    def method_name(self):
        return "list_load_balancers"

    @property
    def data_key(self):
        return "loadbalancers"

    @property
    def client_class(self):
        return ElbClient

    @property
    def client_region(self):
        return ElbRegion


class HuaweiCloudELBMemberListClient(HuaweiCloudBaseClient):
    def build_request(self, pool_id, page_size, record_id=None, *args, **kwargs):
        return ListMembersRequest(pool_id=pool_id, marker=record_id, limit=page_size)

    @property
    def method_name(self):
        return "list_members"

    @property
    def data_key(self):
        return "members"

    @property
    def client_class(self):
        return ElbClient

    @property
    def client_region(self):
        return ElbRegion
