import json

from huaweicloudsdkwaf.v1 import WafClient, ListHostRequest, ListHostRouteRequest, ShowHostRequest
from huaweicloudsdkwaf.v1.region.waf_region import WafRegion

from huawei_cloud.clients.sdk.base import HuaweiCloudBaseClient


class HuaweiCloudWebWafDomainNameListClient(HuaweiCloudBaseClient):
    def build_request(self, page_index, page_size, *args, **kwargs):
        page_index = page_index + 1
        page_size = 100
        return ListHostRequest(page=page_index, pagesize=page_size)

    @property
    def method_name(self):
        return "list_host"

    @property
    def data_key(self):
        return "items"

    @property
    def client_class(self):
        return WafClient

    @property
    def client_region(self):
        return WafRegion


class HuaweiCloudWebWafDomainNameDetailClient(HuaweiCloudBaseClient):
    def build_request(self, instance_id=None, *args, **kwargs):
        return ShowHostRequest(instance_id=instance_id)

    @property
    def method_name(self):
        return "show_host"

    @property
    def data_key(self):
        return ""

    @property
    def client_class(self):
        return WafClient

    @property
    def client_region(self):
        return WafRegion
