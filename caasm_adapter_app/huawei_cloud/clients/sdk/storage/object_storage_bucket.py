from huaweicloudsdkims.v2 import ImsClient, ListImagesRequest
from obs import ObsClient
from huawei_cloud.clients.sdk.base import HuaweiCloudBaseClient


class HuaweiCloudObsListClient(HuaweiCloudBaseClient):
    def _build_client(self):
        try:
            server_name = f"https://obs.{self._region}.myhuaweicloud.com"
            self._client = self.client_class(
                access_key_id=self.access_key, secret_access_key=self.secret_key, server=server_name
            )
        except Exception as e:
            e_message = str(e)
            for _error_message in self.IGNORE_ERROR_MESSAGES:
                if _error_message in e_message:
                    return
            raise e

    def build_request(self, *args, **kwargs):
        return True

    def parse_response(self, response):
        biz_value = self.extract_biz_value(response)
        return self.clean_biz_value(biz_value)

    @property
    def method_name(self):
        return "listBuckets"

    @property
    def data_key(self):
        return "body.buckets"

    @property
    def client_class(self):
        return ObsClient

    @property
    def client_region(self):
        return None


class HuaweiCloudObsACLClient(HuaweiCloudBaseClient):
    def _build_client(self):
        try:
            server_name = f"https://obs.{self._region}.myhuaweicloud.com"
            self._client = self.client_class(
                access_key_id=self.access_key, secret_access_key=self.secret_key, server=server_name
            )
        except Exception as e:
            e_message = str(e)
            for _error_message in self.IGNORE_ERROR_MESSAGES:
                if _error_message in e_message:
                    return
            raise e

    def build_request(self, *args, **kwargs):
        return kwargs

    @property
    def method_name(self):
        return "getBucketAcl"

    @property
    def data_key(self):
        return "body.grants"

    @property
    def client_class(self):
        return ObsClient

    @property
    def client_region(self):
        return None

    def handle(self, bucketName=None, *args, **kwargs):
        if not self._method:
            return None
        try:
            response = self.client.getBucketAcl(bucketName=bucketName)
        except Exception as e:
            import traceback

            if str(e) in self.IGNORE_ERROR_MESSAGES:
                return None
        else:
            return self.parse_response(response)

    def parse_response(self, response):
        biz_value = self.extract_biz_value(response)
        return self.clean_biz_value(biz_value)
