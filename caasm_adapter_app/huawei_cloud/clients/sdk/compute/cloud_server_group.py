from huaweicloudsdkecs.v2 import EcsClient, ListServerGroupsRequest
from huaweicloudsdkecs.v2.region.ecs_region import EcsRegion
from huaweicloudsdkims.v2 import ImsClient, ListImagesRequest
from huaweicloudsdkims.v2.region.ims_region import ImsRegion

from huawei_cloud.clients.sdk.base import HuaweiCloudBaseClient


class HuaweiCloudServerGroupListClient(HuaweiCloudBaseClient):
    def build_request(self, page_size, record_id=None, *args, **kwargs):
        return ListServerGroupsRequest(limit=page_size, marker=record_id)

    @property
    def method_name(self):
        return "list_server_groups"

    @property
    def data_key(self):
        return "servers"

    @property
    def client_class(self):
        return EcsClient

    @property
    def client_region(self):
        return EcsRegion
