{"canvas": {"nodes": [{"id": "root", "field": "根节点", "path": "root", "datatype": "object", "type": "asset", "level": 0, "sub_fields": [], "x": 0, "y": 0, "asset_type": "asset", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 100, "id": "root.alias", "level": 1, "path": "alias", "sub_fields": [], "type": "asset", "datatype": "any", "asset_type": "asset", "field": "alias", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 200, "id": "root.associate_instance_id", "level": 1, "path": "associate_instance_id", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "associate_instance_id", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 300, "id": "root.associate_instance_type", "level": 1, "path": "associate_instance_type", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "associate_instance_type", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 400, "id": "root.bandwidth", "level": 1, "path": "bandwidth", "sub_fields": [{"field": "billing_info", "path": "bandwidth.billing_info", "type": "string"}, {"field": "charge_mode", "path": "bandwidth.charge_mode", "type": "string"}, {"field": "id", "path": "bandwidth.id", "type": "string"}, {"field": "name", "path": "bandwidth.name", "type": "string"}, {"field": "share_type", "path": "bandwidth.share_type", "type": "string"}, {"field": "size", "path": "bandwidth.size", "type": "integer"}], "type": "asset", "datatype": "object", "asset_type": "asset", "field": "bandwidth", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 500, "id": "root.bandwidth.billing_info", "level": 2, "path": "bandwidth.billing_info", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "billing_info", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 600, "id": "root.bandwidth.charge_mode", "level": 2, "path": "bandwidth.charge_mode", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "charge_mode", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 700, "id": "root.bandwidth.id", "level": 2, "path": "bandwidth.id", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "id", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 800, "id": "root.bandwidth.name", "level": 2, "path": "bandwidth.name", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "name", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 900, "id": "root.bandwidth.share_type", "level": 2, "path": "bandwidth.share_type", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "share_type", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 1000, "id": "root.bandwidth.size", "level": 2, "path": "bandwidth.size", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "size", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 1100, "id": "root.billing_info", "level": 1, "path": "billing_info", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "billing_info", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 1200, "id": "root.created_at", "level": 1, "path": "created_at", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "created_at", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 1300, "id": "root.description", "level": 1, "path": "description", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "description", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 1400, "id": "root.enterprise_project_id", "level": 1, "path": "enterprise_project_id", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "enterprise_project_id", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 1500, "id": "root.public_border_group", "level": 1, "path": "public_border_group", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "public_border_group", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 1600, "id": "root.allow_share_bandwidth_types", "level": 1, "path": "allow_share_bandwidth_types", "sub_fields": [{"type": "string", "field": "element", "path": "allow_share_bandwidth_types.element"}], "type": "asset", "datatype": "list", "asset_type": "asset", "field": "allow_share_bandwidth_types", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 1700, "id": "root.id", "level": 1, "path": "id", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "id", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 1800, "id": "root.ip_version", "level": 1, "path": "ip_version", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "ip_version", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 1900, "id": "root.lock_status", "level": 1, "path": "lock_status", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "lock_status", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 2000, "id": "root.project_id", "level": 1, "path": "project_id", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "project_id", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 2100, "id": "root.public_ip_address", "level": 1, "path": "public_ip_address", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "public_ip_address", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 2200, "id": "root.public_ipv6_address", "level": 1, "path": "public_ipv6_address", "sub_fields": [], "type": "asset", "datatype": "any", "asset_type": "asset", "field": "public_ipv6_address", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 2300, "id": "root.publicip_pool_id", "level": 1, "path": "publicip_pool_id", "sub_fields": [], "type": "asset", "datatype": "any", "asset_type": "asset", "field": "publicip_pool_id", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 2400, "id": "root.publicip_pool_name", "level": 1, "path": "publicip_pool_name", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "publicip_pool_name", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 2500, "id": "root.status", "level": 1, "path": "status", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "status", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 2600, "id": "root.type", "level": 1, "path": "type", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "type", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 2700, "id": "root.updated_at", "level": 1, "path": "updated_at", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "updated_at", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 2800, "id": "root.vnic", "level": 1, "path": "vnic", "sub_fields": [{"field": "device_id", "path": "vnic.device_id", "type": "string"}, {"field": "device_owner", "path": "vnic.device_owner", "type": "string"}, {"field": "instance_id", "path": "vnic.instance_id", "type": "string"}, {"field": "instance_type", "path": "vnic.instance_type", "type": "string"}, {"field": "mac", "path": "vnic.mac", "type": "string"}, {"field": "port_id", "path": "vnic.port_id", "type": "string"}, {"field": "port_profile", "path": "vnic.port_profile", "type": "any", "sub_fields": []}, {"field": "private_ip_address", "path": "vnic.private_ip_address", "type": "string"}, {"field": "vni", "path": "vnic.vni", "type": "any", "sub_fields": []}, {"field": "vpc_id", "path": "vnic.vpc_id", "type": "string"}, {"field": "vtep", "path": "vnic.vtep", "type": "any", "sub_fields": []}], "type": "asset", "datatype": "object", "asset_type": "asset", "field": "vnic", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 2900, "id": "root.vnic.device_id", "level": 2, "path": "vnic.device_id", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "device_id", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 3000, "id": "root.vnic.device_owner", "level": 2, "path": "vnic.device_owner", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "device_owner", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 3100, "id": "root.vnic.instance_id", "level": 2, "path": "vnic.instance_id", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "instance_id", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 3200, "id": "root.vnic.instance_type", "level": 2, "path": "vnic.instance_type", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "instance_type", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 3300, "id": "root.vnic.mac", "level": 2, "path": "vnic.mac", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "mac", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 3400, "id": "root.vnic.port_id", "level": 2, "path": "vnic.port_id", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "port_id", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 3500, "id": "root.vnic.port_profile", "level": 2, "path": "vnic.port_profile", "sub_fields": [], "type": "asset", "datatype": "any", "asset_type": "asset", "field": "port_profile", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 3600, "id": "root.vnic.private_ip_address", "level": 2, "path": "vnic.private_ip_address", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "private_ip_address", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 3700, "id": "root.vnic.vni", "level": 2, "path": "vnic.vni", "sub_fields": [], "type": "asset", "datatype": "any", "asset_type": "asset", "field": "vni", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 3800, "id": "root.vnic.vpc_id", "level": 2, "path": "vnic.vpc_id", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "vpc_id", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 3900, "id": "root.vnic.vtep", "level": 2, "path": "vnic.vtep", "sub_fields": [], "type": "asset", "datatype": "any", "asset_type": "asset", "field": "vtep", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.7-zt8NQ9I", "x": 459.517578125, "y": -167.12109375, "order": 1, "level": 1, "source_type": "object", "path": "root.7-zt8NQ9I", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "新增字段", "description": "新增字段，字段值必须是字符串类型", "field": "新增字段", "input": {"asset_web_type": "add", "description": "新增字段，字段值必须是字符串类型", "field": "hua<PERSON>", "values": ["华为云"]}, "action_type": "add", "attrs": {"text": "新增字段"}}, {"type": "asset", "asset_type": "action", "id": "root.7-zt8NQ9I.I5vYn67gB", "x": 789.517578125, "y": -165.12109375, "order": 2, "level": 2, "path": "root.7-zt8NQ9I.I5vYn67gB", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "cloud.vendor"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "5pbPebr-0", "display_name": "云", "description": "cloud.vendor", "x": 989.517578125, "y": -165.12109375, "label": "云-云供应商", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.uQm0I0jZH", "x": 463.517578125, "y": -101.12109375, "order": 3, "level": 1, "source_type": "object", "path": "root.uQm0I0jZH", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "新增字段", "description": "新增字段，字段值必须是字符串类型", "field": "新增字段", "input": {"asset_web_type": "add", "description": "新增字段，字段值必须是字符串类型", "field": "eip", "values": ["公网IP"]}, "action_type": "add", "attrs": {"text": "新增字段"}}, {"type": "asset", "asset_type": "action", "id": "root.uQm0I0jZH.5LhRQNLBw", "x": 786.517578125, "y": -100.12109375, "order": 4, "level": 2, "path": "root.uQm0I0jZH.5LhRQNLBw", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "cloud.service_name"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "LS0dsoCtg", "display_name": "云", "description": "cloud.service_name", "x": 986.517578125, "y": -100.12109375, "label": "云-云服务名称", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.id.Cgy3dhBIq", "x": 893.517578125, "y": 1696.8789062500002, "order": 5, "level": 2, "source_type": "string", "path": "root.id.Cgy3dhBIq", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "cloud.id"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "Gy_RMhcvf", "display_name": "云", "description": "cloud.id", "x": 1093.517578125, "y": 1696.8789062500002, "label": "云-资源ID", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.public_ip_address.S0sUn7KvD", "x": 717.517578125, "y": 2101.8789062500027, "order": 6, "level": 2, "source_type": "string", "path": "root.public_ip_address.S0sUn7KvD", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "internet_ip.ipv4"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "zQoWDjsSL", "display_name": "公网IP", "description": "internet_ip.ipv4", "x": 917.517578125, "y": 2101.8789062500027, "label": "公网IP-IPv4地址", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.vnic.instance_id.Dc23bOcIq", "x": 881.517578125, "y": 3097.878906250001, "order": 7, "level": 3, "source_type": "string", "path": "root.vnic.instance_id.Dc23bOcIq", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "internet_ip.instance_id"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "LjMQUS54J", "display_name": "公网IP", "description": "internet_ip.instance_id", "x": 1081.517578125, "y": 3097.878906250001, "label": "公网IP-绑定的实例ID", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.vnic.vpc_id.e9pr9qrOE", "x": 1152.517578125, "y": 3798.878906249999, "order": 8, "level": 3, "source_type": "string", "path": "root.vnic.vpc_id.e9pr9qrOE", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "cloud.vpc_id"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "offN7Zz_C", "display_name": "云", "description": "cloud.vpc_id", "x": 1352.517578125, "y": 3798.878906249999, "label": "云-VPC_ID", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.created_at.fBU62-Vw9", "x": 974.5175781249999, "y": 1198.8789062499998, "order": 9, "level": 2, "source_type": "string", "path": "root.created_at.fBU62-Vw9", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "将数据转化为datetime类型", "description": "用于时间类型的转换，将其他格式转换为datatime类型，并可设定转换格式，并在此基础上新增或删除多少秒", "field": "将数据转化为datetime类型", "input": {"asset_web_type": "datetime", "description": "用于时间类型的转换，将其他格式转换为datatime类型，并可设定转换格式，并在此基础上新增或删除多少秒", "format": "%Y-%m-%dT%H:%M:%SZ"}, "action_type": "datetime", "attrs": {"text": "将数据转化为datetime类型"}}, {"type": "asset", "asset_type": "action", "id": "root.created_at.fBU62-Vw9.DweHKTnKE", "x": 1502.517578125, "y": 1198.8789062499998, "order": 10, "level": 3, "path": "root.created_at.fBU62-Vw9.DweHKTnKE", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "base.first_seen"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "x8G5ySHwK", "display_name": "基础", "description": "base.first_seen", "x": 1702.517578125, "y": 1198.8789062499998, "label": "基础-首次出现时间", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.updated_at.zK_v9KogZ", "x": 914.517578125, "y": 2694.87890625, "order": 11, "level": 2, "source_type": "string", "path": "root.updated_at.zK_v9KogZ", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "将数据转化为datetime类型", "description": "用于时间类型的转换，将其他格式转换为datatime类型，并可设定转换格式，并在此基础上新增或删除多少秒", "field": "将数据转化为datetime类型", "input": {"asset_web_type": "datetime", "description": "用于时间类型的转换，将其他格式转换为datatime类型，并可设定转换格式，并在此基础上新增或删除多少秒", "format": "%Y-%m-%dT%H:%M:%SZ"}, "action_type": "datetime", "attrs": {"text": "将数据转化为datetime类型"}}, {"type": "asset", "asset_type": "action", "id": "root.updated_at.zK_v9KogZ.6Opsgh0no", "x": 1472.517578125, "y": 2696.87890625, "order": 12, "level": 3, "path": "root.updated_at.zK_v9KogZ.6Opsgh0no", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "base.last_seen"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "pcnM5EQ6w", "display_name": "基础", "description": "base.last_seen", "x": 1672.517578125, "y": 2696.87890625, "label": "基础-最近出现时间", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.status.4nJ1L777e", "x": 899.5175781249999, "y": 2493.8789062500014, "order": 13, "level": 2, "source_type": "string", "path": "root.status.4nJ1L777e", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "翻译", "description": "用于字段值翻译，常见用于枚举字段", "field": "翻译", "input": {"asset_web_type": "translate", "description": "用于字段值翻译，常见用于枚举字段", "values": [{"name": "ERROR", "value": "1"}, {"name": "ACTIVE", "value": "2"}, {"name": "DOWN", "value": "3"}, {"name": "FREEZED", "value": "1"}, {"name": "BIND_ERROR", "value": "999"}, {"name": "BINDING", "value": "1"}, {"name": "PENDING_DELETE", "value": "1"}, {"name": "PENDING_CREATE", "value": "1"}, {"name": "NOTIFYING", "value": "1"}, {"name": "NOTIFY_DELETE", "value": "1"}, {"name": "PENDING_UPDATE", "value": "1"}], "default": "999"}, "action_type": "translate", "attrs": {"text": "翻译"}}, {"type": "asset", "asset_type": "action", "id": "root.status.4nJ1L777e.rTV-GLfLT", "x": 1463.517578125, "y": 2493.8789062500014, "order": 14, "level": 3, "path": "root.status.4nJ1L777e.rTV-GLfLT", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "internet_ip.status"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "MAtnE1511", "display_name": "公网IP", "description": "internet_ip.status", "x": 1663.517578125, "y": 2493.8789062500014, "label": "公网IP-状态", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.description.r5G8aOK6a", "x": 960.517578125, "y": 1293.8789062499998, "order": 15, "level": 2, "source_type": "string", "path": "root.description.r5G8aOK6a", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "internet_ip.remark"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "ViLTpfADm", "display_name": "公网IP", "description": "internet_ip.remark", "x": 1160.517578125, "y": 1293.8789062499998, "label": "公网IP-描述信息", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.public_ipv6_address.oLpYCsJ1N", "x": 720.517578125, "y": 2200.878906250001, "order": 16, "level": 2, "source_type": "any", "path": "root.public_ipv6_address.oLpYCsJ1N", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "internet_ip.ipv6"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "ubcL_kKAr", "display_name": "公网IP", "description": "internet_ip.ipv6", "x": 920.517578125, "y": 2200.878906250001, "label": "公网IP-IPv6地址", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.nr2ocRshX", "x": 467.51757812500006, "y": -16.12109375, "order": 17, "level": 1, "source_type": "object", "path": "root.nr2ocRshX", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "生成对象数组", "description": "生成对象数组", "field": "生成对象数组", "input": {"asset_web_type": "add_list_object", "description": "生成对象数组", "size": "1", "field": "pos", "mapping": [{"name": "intranet_ip", "value": "${vnic.private_ip_address}"}]}, "action_type": "add_list_object", "attrs": {"text": "生成对象数组"}}, {"type": "asset", "asset_type": "action", "id": "root.nr2ocRshX.teL9NpUmo", "x": 802.517578125, "y": -21.12109375, "order": 18, "level": 2, "path": "root.nr2ocRshX.teL9NpUmo", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "空值过滤器", "description": "过滤空值", "field": "空值过滤器", "input": {"asset_web_type": "filter_empty", "description": "过滤空值", "child_field": "intranet_ip", "child_is_remove": true}, "action_type": "filter_empty", "attrs": {"text": "空值过滤器"}}, {"type": "asset", "asset_type": "action", "id": "root.nr2ocRshX.teL9NpUmo.U2_C7nULP", "x": 1252.517578125, "y": -27.12109375, "order": 19, "level": 3, "path": "root.nr2ocRshX.teL9NpUmo.U2_C7nULP", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "internet_ip.ports"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "kiZBqAtpj", "display_name": "公网IP", "description": "internet_ip.ports", "x": 1452.517578125, "y": -27.12109375, "label": "公网IP-端口", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.public_ip_address.XISOJiy5p", "x": 711.751953125, "y": 2000.87890625, "order": 1, "level": 2, "source_type": "string", "path": "root.public_ip_address.XISOJiy5p", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "internet_ip.ip"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "ll3BY9Q_g", "display_name": "公网IP", "description": "internet_ip.ip", "x": 911.751953125, "y": 2000.87890625, "label": "公网IP-IP地址", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}], "edges": [{"source": "root", "target": "root.alias", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.056036148646685161700725223450", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 100, "anchor_index": 0}}, {"source": "root", "target": "root.associate_instance_id", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.349366983394221231700725223450", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 200, "anchor_index": 0}}, {"source": "root", "target": "root.associate_instance_type", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.159039676012118881700725223450", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 300, "anchor_index": 0}}, {"source": "root", "target": "root.bandwidth", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.70669219139234071700725223450", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 400, "anchor_index": 0}}, {"source": "root.bandwidth", "target": "root.bandwidth.billing_info", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.60060549043933141700725223450", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 425.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 500, "anchor_index": 0}}, {"source": "root.bandwidth", "target": "root.bandwidth.charge_mode", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.51718326346331181700725223450", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 425.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 600, "anchor_index": 0}}, {"source": "root.bandwidth", "target": "root.bandwidth.id", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.81662211411728111700725223450", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 425.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 700, "anchor_index": 0}}, {"source": "root.bandwidth", "target": "root.bandwidth.name", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.43931176006785221700725223450", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 425.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 800, "anchor_index": 0}}, {"source": "root.bandwidth", "target": "root.bandwidth.share_type", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.65419495385432551700725223450", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 425.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 900, "anchor_index": 0}}, {"source": "root.bandwidth", "target": "root.bandwidth.size", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.72194498026311171700725223450", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 425.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 1000, "anchor_index": 0}}, {"source": "root", "target": "root.billing_info", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.68937142930117861700725223450", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 1100, "anchor_index": 0}}, {"source": "root", "target": "root.created_at", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.240235591852691941700725223450", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 1200, "anchor_index": 0}}, {"source": "root", "target": "root.description", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.153389533396865561700725223451", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 1300, "anchor_index": 0}}, {"source": "root", "target": "root.enterprise_project_id", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.69952376645128571700725223451", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 1400, "anchor_index": 0}}, {"source": "root", "target": "root.public_border_group", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.7278834795609751700725223451", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 1500, "anchor_index": 0}}, {"source": "root", "target": "root.allow_share_bandwidth_types", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.19415810375576781700725223451", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 1600, "anchor_index": 0}}, {"source": "root", "target": "root.id", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.164366678308173861700725223451", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 1700, "anchor_index": 0}}, {"source": "root", "target": "root.ip_version", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.256644171700517231700725223451", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 1800, "anchor_index": 0}}, {"source": "root", "target": "root.lock_status", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.0098438752567409081700725223451", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 1900, "anchor_index": 0}}, {"source": "root", "target": "root.project_id", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.32442022125789441700725223451", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 2000, "anchor_index": 0}}, {"source": "root", "target": "root.public_ip_address", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.76167852592585211700725223451", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 2100, "anchor_index": 0}}, {"source": "root", "target": "root.public_ipv6_address", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.055595591569065571700725223451", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 2200, "anchor_index": 0}}, {"source": "root", "target": "root.publicip_pool_id", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.0501149595128109751700725223451", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 2300, "anchor_index": 0}}, {"source": "root", "target": "root.publicip_pool_name", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.338888207535485541700725223451", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 2400, "anchor_index": 0}}, {"source": "root", "target": "root.status", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.93337460835160861700725223451", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 2500, "anchor_index": 0}}, {"source": "root", "target": "root.type", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.1784915428161241700725223451", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 2600, "anchor_index": 0}}, {"source": "root", "target": "root.updated_at", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.249941824346834541700725223451", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 2700, "anchor_index": 0}}, {"source": "root", "target": "root.vnic", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.092265131801539281700725223451", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 2800, "anchor_index": 0}}, {"source": "root.vnic", "target": "root.vnic.device_id", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.95493458028815661700725223451", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2825.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 2900, "anchor_index": 0}}, {"source": "root.vnic", "target": "root.vnic.device_owner", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.208817462723074331700725223451", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2825.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 3000, "anchor_index": 0}}, {"source": "root.vnic", "target": "root.vnic.instance_id", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.58426634435001421700725223451", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2825.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 3100, "anchor_index": 0}}, {"source": "root.vnic", "target": "root.vnic.instance_type", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.052667937060505431700725223451", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2825.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 3200, "anchor_index": 0}}, {"source": "root.vnic", "target": "root.vnic.mac", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.63516643385054251700725223451", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2825.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 3300, "anchor_index": 0}}, {"source": "root.vnic", "target": "root.vnic.port_id", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.60658004351968911700725223451", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2825.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 3400, "anchor_index": 0}}, {"source": "root.vnic", "target": "root.vnic.port_profile", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.89740513099033171700725223451", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2825.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 3500, "anchor_index": 0}}, {"source": "root.vnic", "target": "root.vnic.private_ip_address", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.88240309016253821700725223451", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2825.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 3600, "anchor_index": 0}}, {"source": "root.vnic", "target": "root.vnic.vni", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.85358737356328021700725223451", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2825.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 3700, "anchor_index": 0}}, {"source": "root.vnic", "target": "root.vnic.vpc_id", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.486996207288580731700725223451", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2825.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 3800, "anchor_index": 0}}, {"source": "root.vnic", "target": "root.vnic.vtep", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.200517604815495521700725223451", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2825.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 3900, "anchor_index": 0}}, {"source": "root", "target": "root.7-zt8NQ9I", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.0097883108529457361700725263444", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 50.5, "y": 0, "anchor_index": 1}, "end_point": {"x": 409.017578125, "y": -167.12109375, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.7-zt8NQ9I", "target": "root.7-zt8NQ9I.I5vYn67gB", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.59023337718601711700725289713", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 510.017578125, "y": -167.12109375, "anchor_index": 1}, "end_point": {"x": 739.017578125, "y": -165.12109375, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.7-zt8NQ9I.I5vYn67gB", "target": "5pbPebr-0", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.52215960528530351700725300963", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 840.017578125, "y": -165.12109375, "anchor_index": 1}, "end_point": {"x": 939.017578125, "y": -165.12109375, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root", "target": "root.uQm0I0jZH", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.277165087366578971700725304718", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 50.5, "y": 0, "anchor_index": 1}, "end_point": {"x": 413.017578125, "y": -101.12109375, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.uQm0I0jZH", "target": "root.uQm0I0jZH.5LhRQNLBw", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.12858582711649281700725329573", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 514.017578125, "y": -101.12109375, "anchor_index": 1}, "end_point": {"x": 736.017578125, "y": -100.12109375, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.uQm0I0jZH.5LhRQNLBw", "target": "LS0dsoCtg", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.51723991440595611700725342240", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 837.017578125, "y": -100.12109375, "anchor_index": 1}, "end_point": {"x": 936.017578125, "y": -100.12109375, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.id", "target": "root.id.Cgy3dhBIq", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.67688254920038761700725389983", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 1700, "anchor_index": 1}, "end_point": {"x": 843.017578125, "y": 1696.8789062500002, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.id.Cgy3dhBIq", "target": "Gy_RMhcvf", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.438818569560752941700725398455", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 944.017578125, "y": 1696.8789062500002, "anchor_index": 1}, "end_point": {"x": 1043.017578125, "y": 1696.8789062500002, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.public_ip_address", "target": "root.public_ip_address.S0sUn7KvD", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.04518144560265981700725427089", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 2100, "anchor_index": 1}, "end_point": {"x": 667.017578125, "y": 2101.8789062500027, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.public_ip_address.S0sUn7KvD", "target": "zQoWDjsSL", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.470544946499454661700725438357", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 768.017578125, "y": 2101.8789062500027, "anchor_index": 1}, "end_point": {"x": 867.017578125, "y": 2101.8789062500027, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.vnic.instance_id", "target": "root.vnic.instance_id.Dc23bOcIq", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.42303952399450951700725461020", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 450.5, "y": 3100, "anchor_index": 1}, "end_point": {"x": 831.017578125, "y": 3097.878906250001, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.vnic.instance_id.Dc23bOcIq", "target": "LjMQUS54J", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.97274408350064251700725476299", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 932.017578125, "y": 3097.878906250001, "anchor_index": 1}, "end_point": {"x": 1031.017578125, "y": 3097.878906250001, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.vnic.vpc_id", "target": "root.vnic.vpc_id.e9pr9qrOE", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.72344996044042991700725498097", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 450.5, "y": 3800, "anchor_index": 1}, "end_point": {"x": 1102.017578125, "y": 3798.878906249999, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.vnic.vpc_id.e9pr9qrOE", "target": "offN7Zz_C", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.81417232674751471700725508338", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1203.017578125, "y": 3798.878906249999, "anchor_index": 1}, "end_point": {"x": 1302.017578125, "y": 3798.878906249999, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.created_at", "target": "root.created_at.fBU62-Vw9", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.8303656579195241700725746674", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 1200, "anchor_index": 1}, "end_point": {"x": 924.0175781249999, "y": 1198.8789062499998, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.created_at.fBU62-Vw9", "target": "root.created_at.fBU62-Vw9.DweHKTnKE", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.79896010641084581700725764611", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1025.017578125, "y": 1198.8789062499998, "anchor_index": 1}, "end_point": {"x": 1452.017578125, "y": 1198.8789062499998, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.created_at.fBU62-Vw9.DweHKTnKE", "target": "x8G5ySHwK", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.68165698912106891700725775438", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1553.017578125, "y": 1198.8789062499998, "anchor_index": 1}, "end_point": {"x": 1652.017578125, "y": 1198.8789062499998, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.updated_at", "target": "root.updated_at.zK_v9KogZ", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.58836340837534481700725786270", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 2700, "anchor_index": 1}, "end_point": {"x": 864.017578125, "y": 2694.87890625, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.updated_at.zK_v9KogZ", "target": "root.updated_at.zK_v9KogZ.6Opsgh0no", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.226586459528616221700725804751", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 965.017578125, "y": 2694.87890625, "anchor_index": 1}, "end_point": {"x": 1422.017578125, "y": 2696.87890625, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.updated_at.zK_v9KogZ.6Opsgh0no", "target": "pcnM5EQ6w", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.068024311540389131700725820386", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1523.017578125, "y": 2696.87890625, "anchor_index": 1}, "end_point": {"x": 1622.017578125, "y": 2696.87890625, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.status", "target": "root.status.4nJ1L777e", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.17457913412768391700725877134", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 2500, "anchor_index": 1}, "end_point": {"x": 849.0175781249999, "y": 2493.8789062500014, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.status.4nJ1L777e", "target": "root.status.4nJ1L777e.rTV-GLfLT", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.5986744998333121700726625718", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 950.0175781249999, "y": 2493.8789062500014, "anchor_index": 1}, "end_point": {"x": 1413.017578125, "y": 2493.8789062500014, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.status.4nJ1L777e.rTV-GLfLT", "target": "MAtnE1511", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.84645661467988951700726633549", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1514.017578125, "y": 2493.8789062500014, "anchor_index": 1}, "end_point": {"x": 1613.017578125, "y": 2493.8789062500014, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.description", "target": "root.description.r5G8aOK6a", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.0305769244448486081700727241700", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 1300, "anchor_index": 1}, "end_point": {"x": 910.017578125, "y": 1293.8789062499998, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.description.r5G8aOK6a", "target": "ViLTpfADm", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.76225449618583021700727266213", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1011.017578125, "y": 1293.8789062499998, "anchor_index": 1}, "end_point": {"x": 1110.017578125, "y": 1293.8789062499998, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.public_ipv6_address", "target": "root.public_ipv6_address.oLpYCsJ1N", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.69741867442866231700727322646", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 2200, "anchor_index": 1}, "end_point": {"x": 670.017578125, "y": 2200.878906250001, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.public_ipv6_address.oLpYCsJ1N", "target": "ubcL_kKAr", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.52528925122573921700727337468", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 771.017578125, "y": 2200.878906250001, "anchor_index": 1}, "end_point": {"x": 870.017578125, "y": 2200.878906250001, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root", "target": "root.nr2ocRshX", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.232221622831053631700727878281", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 50.5, "y": 0, "anchor_index": 1}, "end_point": {"x": 417.01757812500006, "y": -16.12109375, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.nr2ocRshX", "target": "root.nr2ocRshX.teL9NpUmo", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.92146664213970931700727950354", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 518.017578125, "y": -16.12109375, "anchor_index": 1}, "end_point": {"x": 752.017578125, "y": -21.12109375, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.nr2ocRshX.teL9NpUmo", "target": "root.nr2ocRshX.teL9NpUmo.U2_C7nULP", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.81193662974197281700727970924", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 853.017578125, "y": -21.12109375, "anchor_index": 1}, "end_point": {"x": 1202.017578125, "y": -27.12109375, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.nr2ocRshX.teL9NpUmo.U2_C7nULP", "target": "kiZBqAtpj", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.57836712908611391700727987907", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1303.017578125, "y": -27.12109375, "anchor_index": 1}, "end_point": {"x": 1402.017578125, "y": -27.12109375, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.public_ip_address", "target": "root.public_ip_address.XISOJiy5p", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.98756816784551081706580816088", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 2100, "anchor_index": 1}, "end_point": {"x": 661.251953125, "y": 2000.87890625, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.public_ip_address.XISOJiy5p", "target": "ll3BY9Q_g", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.047328605554614271706580828544", "style": {}, "start_point": {"x": 762.251953125, "y": 2000.87890625, "anchor_index": 1}, "end_point": {"x": 861.251953125, "y": 2000.87890625, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}]}, "rules": [{"name": "add", "setting": {"field": "hua<PERSON>", "values": ["华为云"], "multi_flag": null, "is_output_root": null}, "sub_rules": [], "_id": "root.7-zt8NQ9I"}, {"name": "enter", "setting": {"values": "cloud.vendor", "field": "${huawei}"}, "sub_rules": [], "_id": "root.7-zt8NQ9I.I5vYn67gB"}, {"name": "add", "setting": {"field": "eip", "values": ["公网IP"], "multi_flag": null, "is_output_root": null}, "sub_rules": [], "_id": "root.uQm0I0jZH"}, {"name": "enter", "setting": {"values": "cloud.service_name", "field": "${eip}"}, "sub_rules": [], "_id": "root.uQm0I0jZH.5LhRQNLBw"}, {"name": "add_list_object", "setting": {"size": 1, "field": "pos", "mapping": [{"name": "intranet_ip", "value": "${vnic.private_ip_address}"}]}, "sub_rules": [], "_id": "root.nr2ocRshX"}, {"name": "filter_empty", "setting": {"child_field": "intranet_ip", "child_is_remove": true, "field": "pos"}, "sub_rules": [], "_id": "root.nr2ocRshX.teL9NpUmo"}, {"name": "enter", "setting": {"values": "internet_ip.ports", "field": "${pos}"}, "sub_rules": [], "_id": "root.nr2ocRshX.teL9NpUmo.U2_C7nULP"}, {"name": "datetime", "setting": {"format": "%Y-%m-%dT%H:%M:%SZ", "field": "created_at"}, "sub_rules": [], "_id": "root.created_at.fBU62-Vw9"}, {"name": "enter", "setting": {"values": "base.first_seen", "field": "${created_at}"}, "sub_rules": [], "_id": "root.created_at.fBU62-Vw9.DweHKTnKE"}, {"name": "enter", "setting": {"values": "internet_ip.remark", "field": "${description}"}, "sub_rules": [], "_id": "root.description.r5G8aOK6a"}, {"name": "enter", "setting": {"values": "cloud.id", "field": "${id}"}, "sub_rules": [], "_id": "root.id.Cgy3dhBIq"}, {"name": "enter", "setting": {"values": "internet_ip.ipv4", "field": "${public_ip_address}"}, "sub_rules": [], "_id": "root.public_ip_address.S0sUn7KvD"}, {"name": "enter", "setting": {"values": "internet_ip.ip", "field": "${public_ip_address}"}, "sub_rules": [], "_id": "root.public_ip_address.XISOJiy5p"}, {"name": "enter", "setting": {"values": "internet_ip.ipv6", "field": "${public_ipv6_address}"}, "sub_rules": [], "_id": "root.public_ipv6_address.oLpYCsJ1N"}, {"name": "translate", "setting": {"values": [{"name": "ERROR", "value": 1}, {"name": "ACTIVE", "value": 2}, {"name": "DOWN", "value": 3}, {"name": "FREEZED", "value": 1}, {"name": "BIND_ERROR", "value": 999}, {"name": "BINDING", "value": 1}, {"name": "PENDING_DELETE", "value": 1}, {"name": "PENDING_CREATE", "value": 1}, {"name": "NOTIFYING", "value": 1}, {"name": "NOTIFY_DELETE", "value": 1}, {"name": "PENDING_UPDATE", "value": 1}], "default": 999, "field": "status"}, "sub_rules": [], "_id": "root.status.4nJ1L777e"}, {"name": "enter", "setting": {"values": "internet_ip.status", "field": "${status}"}, "sub_rules": [], "_id": "root.status.4nJ1L777e.rTV-GLfLT"}, {"name": "datetime", "setting": {"format": "%Y-%m-%dT%H:%M:%SZ", "field": "updated_at"}, "sub_rules": [], "_id": "root.updated_at.zK_v9KogZ"}, {"name": "enter", "setting": {"values": "base.last_seen", "field": "${updated_at}"}, "sub_rules": [], "_id": "root.updated_at.zK_v9KogZ.6Opsgh0no"}, {"name": "enter", "setting": {"values": "internet_ip.instance_id", "field": "${vnic.instance_id}"}, "sub_rules": [], "_id": "root.vnic.instance_id.Dc23bOcIq"}, {"name": "enter", "setting": {"values": "cloud.vpc_id", "field": "${vnic.vpc_id}"}, "sub_rules": [], "_id": "root.vnic.vpc_id.e9pr9qrOE"}], "adapter_name": "huawei_cloud", "fetch_type": "internet_ip", "model_name": "internet_ip", "asset_type": "internet_ip", "internal": true}