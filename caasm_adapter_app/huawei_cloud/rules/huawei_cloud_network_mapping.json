{"canvas": {"nodes": [{"id": "root", "field": "根节点", "path": "root", "datatype": "object", "type": "asset", "level": 0, "sub_fields": [], "x": 0, "y": 0, "asset_type": "asset", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 100, "id": "root.id", "level": 1, "path": "id", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "id", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 200, "id": "root.hostid", "level": 1, "path": "hostid", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "hostid", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 300, "id": "root.description", "level": 1, "path": "description", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "description", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 400, "id": "root.properties", "level": 1, "path": "properties", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "properties", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 500, "id": "root.type", "level": 1, "path": "type", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "type", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 600, "id": "root.proxy", "level": 1, "path": "proxy", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "proxy", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 700, "id": "root.flag", "level": 1, "path": "flag", "sub_fields": [{"field": "pci_3ds", "path": "flag.pci_3ds", "type": "string"}, {"field": "pci_dss", "path": "flag.pci_dss", "type": "string"}, {"field": "ipv6", "path": "flag.ipv6", "type": "string"}, {"field": "cname", "path": "flag.cname", "type": "string"}, {"field": "is_dual_az", "path": "flag.is_dual_az", "type": "string"}], "type": "asset", "datatype": "object", "asset_type": "asset", "field": "flag", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 800, "id": "root.flag.pci_3ds", "level": 2, "path": "flag.pci_3ds", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "pci_3ds", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 900, "id": "root.flag.pci_dss", "level": 2, "path": "flag.pci_dss", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "pci_dss", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 1000, "id": "root.flag.ipv6", "level": 2, "path": "flag.ipv6", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "ipv6", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 1100, "id": "root.flag.cname", "level": 2, "path": "flag.cname", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "cname", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 1200, "id": "root.flag.is_dual_az", "level": 2, "path": "flag.is_dual_az", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "is_dual_az", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 1300, "id": "root.region", "level": 1, "path": "region", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "region", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 1400, "id": "root.hostname", "level": 1, "path": "hostname", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "hostname", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 1500, "id": "root.access_code", "level": 1, "path": "access_code", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "access_code", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 1600, "id": "root.policyid", "level": 1, "path": "policyid", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "policyid", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 1700, "id": "root.timestamp", "level": 1, "path": "timestamp", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "timestamp", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 1800, "id": "root.protect_status", "level": 1, "path": "protect_status", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "protect_status", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 1900, "id": "root.access_status", "level": 1, "path": "access_status", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "access_status", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 2000, "id": "root.exclusive_ip", "level": 1, "path": "exclusive_ip", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "exclusive_ip", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 2100, "id": "root.web_tag", "level": 1, "path": "web_tag", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "web_tag", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 2200, "id": "root.paid_type", "level": 1, "path": "paid_type", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "paid_type", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 2300, "id": "root.detail_data", "level": 1, "path": "detail_data", "sub_fields": [{"field": "id", "path": "detail_data.id", "type": "string"}, {"field": "hostname", "path": "detail_data.hostname", "type": "string"}, {"field": "protocol", "path": "detail_data.protocol", "type": "string"}, {"field": "server", "path": "detail_data.server", "type": "list", "sub_fields": [{"type": "object", "sub_fields": [{"field": "address", "path": "detail_data.server.element.address", "type": "string"}, {"field": "ipv4", "path": "detail_data.server.element.ipv4", "type": "string"}, {"field": "ipv6", "path": "detail_data.server.element.ipv6", "type": "string"}, {"field": "port", "path": "detail_data.server.element.port", "type": "integer"}, {"field": "type", "path": "detail_data.server.element.type", "type": "string"}, {"field": "weight", "path": "detail_data.server.element.weight", "type": "integer"}, {"field": "front_protocol", "path": "detail_data.server.element.front_protocol", "type": "string"}, {"field": "back_protocol", "path": "detail_data.server.element.back_protocol", "type": "string"}], "field": "element", "path": "detail_data.server.element"}]}, {"field": "proxy", "path": "detail_data.proxy", "type": "integer"}, {"field": "locked", "path": "detail_data.locked", "type": "integer"}, {"field": "timestamp", "path": "detail_data.timestamp", "type": "integer"}, {"field": "flag", "path": "detail_data.flag", "type": "object", "sub_fields": [{"field": "pci_3ds", "path": "detail_data.flag.pci_3ds", "type": "string"}, {"field": "pci_dss", "path": "detail_data.flag.pci_dss", "type": "string"}, {"field": "ipv6", "path": "detail_data.flag.ipv6", "type": "string"}, {"field": "cname", "path": "detail_data.flag.cname", "type": "string"}, {"field": "is_dual_az", "path": "detail_data.flag.is_dual_az", "type": "string"}]}, {"field": "description", "path": "detail_data.description", "type": "string"}, {"field": "policyid", "path": "detail_data.policyid", "type": "string"}, {"field": "domainid", "path": "detail_data.domainid", "type": "string"}, {"field": "projectid", "path": "detail_data.projectid", "type": "string"}, {"field": "enterprise_project_id", "path": "detail_data.enterprise_project_id", "type": "string"}, {"field": "protect_status", "path": "detail_data.protect_status", "type": "integer"}, {"field": "access_status", "path": "detail_data.access_status", "type": "integer"}, {"field": "access_code", "path": "detail_data.access_code", "type": "string"}, {"field": "block_page", "path": "detail_data.block_page", "type": "object", "sub_fields": [{"field": "template", "path": "detail_data.block_page.template", "type": "string"}]}, {"field": "web_tag", "path": "detail_data.web_tag", "type": "string"}, {"field": "exclusive_ip", "path": "detail_data.exclusive_ip", "type": "integer"}, {"field": "http2_enable", "path": "detail_data.http2_enable", "type": "integer"}], "type": "asset", "datatype": "object", "asset_type": "asset", "field": "detail_data", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 2400, "id": "root.detail_data.id", "level": 2, "path": "detail_data.id", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "id", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 2500, "id": "root.detail_data.hostname", "level": 2, "path": "detail_data.hostname", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "hostname", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 2600, "id": "root.detail_data.protocol", "level": 2, "path": "detail_data.protocol", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "protocol", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 2700, "id": "root.detail_data.server", "level": 2, "path": "detail_data.server", "sub_fields": [{"type": "object", "sub_fields": [{"field": "address", "path": "detail_data.server.element.address", "type": "string", "x": 1477.517578125, "y": 2898.87890625}, {"field": "ipv4", "path": "detail_data.server.element.ipv4", "type": "string", "x": 1477.517578125, "y": 2998.87890625}, {"field": "ipv6", "path": "detail_data.server.element.ipv6", "type": "string", "x": 1477.517578125, "y": 3098.87890625}, {"field": "port", "path": "detail_data.server.element.port", "type": "integer", "x": 1477.517578125, "y": 3198.87890625}, {"field": "type", "path": "detail_data.server.element.type", "type": "string", "x": 1477.517578125, "y": 3298.87890625}, {"field": "weight", "path": "detail_data.server.element.weight", "type": "integer", "x": 1477.517578125, "y": 3398.87890625}, {"field": "front_protocol", "path": "detail_data.server.element.front_protocol", "type": "string", "x": 1477.517578125, "y": 3498.87890625}, {"field": "back_protocol", "path": "detail_data.server.element.back_protocol", "type": "string", "x": 1477.517578125, "y": 3598.87890625}], "field": "element", "path": "detail_data.server.element", "x": 1277.517578125, "y": 2798.87890625}], "type": "asset", "datatype": "list", "asset_type": "asset", "field": "server", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 2800, "id": "root.detail_data.proxy", "level": 2, "path": "detail_data.proxy", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "proxy", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 2900, "id": "root.detail_data.locked", "level": 2, "path": "detail_data.locked", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "locked", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 3000, "id": "root.detail_data.timestamp", "level": 2, "path": "detail_data.timestamp", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "timestamp", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 3100, "id": "root.detail_data.flag", "level": 2, "path": "detail_data.flag", "sub_fields": [{"field": "pci_3ds", "path": "detail_data.flag.pci_3ds", "type": "string", "x": 600, "y": 3200}, {"field": "pci_dss", "path": "detail_data.flag.pci_dss", "type": "string", "x": 600, "y": 3300}, {"field": "ipv6", "path": "detail_data.flag.ipv6", "type": "string", "x": 600, "y": 3400}, {"field": "cname", "path": "detail_data.flag.cname", "type": "string", "x": 600, "y": 3500}, {"field": "is_dual_az", "path": "detail_data.flag.is_dual_az", "type": "string", "x": 600, "y": 3600}], "type": "asset", "datatype": "object", "asset_type": "asset", "field": "flag", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 600, "y": 3200, "id": "root.detail_data.flag.pci_3ds", "level": 3, "path": "detail_data.flag.pci_3ds", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "pci_3ds", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 600, "y": 3300, "id": "root.detail_data.flag.pci_dss", "level": 3, "path": "detail_data.flag.pci_dss", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "pci_dss", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 600, "y": 3400, "id": "root.detail_data.flag.ipv6", "level": 3, "path": "detail_data.flag.ipv6", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "ipv6", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 600, "y": 3500, "id": "root.detail_data.flag.cname", "level": 3, "path": "detail_data.flag.cname", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "cname", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 600, "y": 3600, "id": "root.detail_data.flag.is_dual_az", "level": 3, "path": "detail_data.flag.is_dual_az", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "is_dual_az", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 3700, "id": "root.detail_data.description", "level": 2, "path": "detail_data.description", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "description", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 3800, "id": "root.detail_data.policyid", "level": 2, "path": "detail_data.policyid", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "policyid", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 3900, "id": "root.detail_data.domainid", "level": 2, "path": "detail_data.domainid", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "domainid", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 4000, "id": "root.detail_data.projectid", "level": 2, "path": "detail_data.projectid", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "projectid", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 4100, "id": "root.detail_data.enterprise_project_id", "level": 2, "path": "detail_data.enterprise_project_id", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "enterprise_project_id", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 4200, "id": "root.detail_data.protect_status", "level": 2, "path": "detail_data.protect_status", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "protect_status", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 4300, "id": "root.detail_data.access_status", "level": 2, "path": "detail_data.access_status", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "access_status", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 4400, "id": "root.detail_data.access_code", "level": 2, "path": "detail_data.access_code", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "access_code", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 4500, "id": "root.detail_data.block_page", "level": 2, "path": "detail_data.block_page", "sub_fields": [{"field": "template", "path": "detail_data.block_page.template", "type": "string", "x": 600, "y": 4600}], "type": "asset", "datatype": "object", "asset_type": "asset", "field": "block_page", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 600, "y": 4600, "id": "root.detail_data.block_page.template", "level": 3, "path": "detail_data.block_page.template", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "template", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 4700, "id": "root.detail_data.web_tag", "level": 2, "path": "detail_data.web_tag", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "web_tag", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 4800, "id": "root.detail_data.exclusive_ip", "level": 2, "path": "detail_data.exclusive_ip", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "exclusive_ip", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 4900, "id": "root.detail_data.http2_enable", "level": 2, "path": "detail_data.http2_enable", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "http2_enable", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 5000, "id": "root.cdn_data", "level": 1, "path": "cdn_data", "sub_fields": [{"field": "sources", "path": "cdn_data.sources", "type": "list", "sub_fields": [{"type": "object", "sub_fields": [{"field": "priority", "path": "cdn_data.sources.element.priority", "type": "integer"}, {"field": "origin_addr", "path": "cdn_data.sources.element.origin_addr", "type": "string"}, {"field": "origin_type", "path": "cdn_data.sources.element.origin_type", "type": "string"}, {"field": "http_port", "path": "cdn_data.sources.element.http_port", "type": "integer"}, {"field": "https_port", "path": "cdn_data.sources.element.https_port", "type": "integer"}, {"field": "host_name", "path": "cdn_data.sources.element.host_name", "type": "string"}], "field": "element", "path": "cdn_data.sources.element"}]}, {"field": "id", "path": "cdn_data.id", "type": "string"}, {"field": "domain_name", "path": "cdn_data.domain_name", "type": "string"}, {"field": "business_type", "path": "cdn_data.business_type", "type": "string"}, {"field": "service_area", "path": "cdn_data.service_area", "type": "string"}, {"field": "cname", "path": "cdn_data.cname", "type": "string"}, {"field": "domain_status", "path": "cdn_data.domain_status", "type": "string"}, {"field": "https_status", "path": "cdn_data.https_status", "type": "integer"}, {"field": "create_time", "path": "cdn_data.create_time", "type": "integer"}, {"field": "update_time", "path": "cdn_data.update_time", "type": "integer"}, {"field": "disabled", "path": "cdn_data.disabled", "type": "integer"}, {"field": "locked", "path": "cdn_data.locked", "type": "integer"}, {"field": "auto_refresh_preheat", "path": "cdn_data.auto_refresh_preheat", "type": "integer"}, {"field": "properties", "path": "cdn_data.properties", "type": "string"}], "type": "asset", "datatype": "object", "asset_type": "asset", "field": "cdn_data", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 5100, "id": "root.cdn_data.sources", "level": 2, "path": "cdn_data.sources", "sub_fields": [{"type": "object", "sub_fields": [{"field": "priority", "path": "cdn_data.sources.element.priority", "type": "integer", "x": 1559.517578125, "y": 5298.878906250004}, {"field": "origin_addr", "path": "cdn_data.sources.element.origin_addr", "type": "string", "x": 1559.517578125, "y": 5398.878906250004}, {"field": "origin_type", "path": "cdn_data.sources.element.origin_type", "type": "string", "x": 1559.517578125, "y": 5498.878906250004}, {"field": "http_port", "path": "cdn_data.sources.element.http_port", "type": "integer", "x": 1559.517578125, "y": 5598.878906250004}, {"field": "https_port", "path": "cdn_data.sources.element.https_port", "type": "integer", "x": 1559.517578125, "y": 5698.878906250004}, {"field": "host_name", "path": "cdn_data.sources.element.host_name", "type": "string", "x": 1559.517578125, "y": 5798.878906250004}], "field": "element", "path": "cdn_data.sources.element", "x": 1359.517578125, "y": 5198.878906250004}], "type": "asset", "datatype": "list", "asset_type": "asset", "field": "sources", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 5200, "id": "root.cdn_data.id", "level": 2, "path": "cdn_data.id", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "id", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 5300, "id": "root.cdn_data.domain_name", "level": 2, "path": "cdn_data.domain_name", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "domain_name", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 5400, "id": "root.cdn_data.business_type", "level": 2, "path": "cdn_data.business_type", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "business_type", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 5500, "id": "root.cdn_data.service_area", "level": 2, "path": "cdn_data.service_area", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "service_area", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 5600, "id": "root.cdn_data.cname", "level": 2, "path": "cdn_data.cname", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "cname", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 5700, "id": "root.cdn_data.domain_status", "level": 2, "path": "cdn_data.domain_status", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "domain_status", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 5800, "id": "root.cdn_data.https_status", "level": 2, "path": "cdn_data.https_status", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "https_status", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 5900, "id": "root.cdn_data.create_time", "level": 2, "path": "cdn_data.create_time", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "create_time", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 6000, "id": "root.cdn_data.update_time", "level": 2, "path": "cdn_data.update_time", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "update_time", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 6100, "id": "root.cdn_data.disabled", "level": 2, "path": "cdn_data.disabled", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "disabled", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 6200, "id": "root.cdn_data.locked", "level": 2, "path": "cdn_data.locked", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "locked", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 6300, "id": "root.cdn_data.auto_refresh_preheat", "level": 2, "path": "cdn_data.auto_refresh_preheat", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "auto_refresh_preheat", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 6400, "id": "root.cdn_data.properties", "level": 2, "path": "cdn_data.properties", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "properties", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.IZAV59ghf", "x": 462.517578125, "y": -156.12109375, "order": 1, "level": 1, "source_type": "object", "path": "root.IZAV59ghf", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "新增字段", "description": "新增字段，字段值必须是字符串类型", "field": "新增字段", "input": {"asset_web_type": "add", "description": "新增字段，字段值必须是字符串类型", "field": "sdsdsdsd", "values": ["${properties}", "${cdn_data.properties}"], "multi_flag": true}, "action_type": "add", "attrs": {"text": "新增字段"}}, {"type": "asset", "asset_type": "action", "id": "root.IZAV59ghf.4AM9Uoq0B", "x": 996.517578125, "y": -158.12109375, "order": 2, "level": 2, "path": "root.IZAV59ghf.4AM9Uoq0B", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "asset_base.properties"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "ejoH1aDyD", "display_name": "管理属性", "description": "asset_base.properties", "x": 1196.517578125, "y": -158.12109375, "label": "管理属性-资产属性列表", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.jE9kibry6", "x": 469.517578125, "y": -279.12109375, "order": 3, "level": 1, "source_type": "object", "path": "root.jE9kibry6", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "新增字段", "description": "新增字段，字段值必须是字符串类型", "field": "新增字段", "input": {"asset_web_type": "add", "description": "新增字段，字段值必须是字符串类型", "field": "sdsd", "values": ["华为云"]}, "action_type": "add", "attrs": {"text": "新增字段"}}, {"type": "asset", "asset_type": "action", "id": "root.jE9kibry6.XsBmBAAot", "x": 895.517578125, "y": -280.12109375, "order": 4, "level": 2, "path": "root.jE9kibry6.XsBmBAAot", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "cloud.vendor"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "_DA3YYyM0", "display_name": "云", "description": "cloud.vendor", "x": 1095.517578125, "y": -280.12109375, "label": "云-云供应商", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.cdn_data.properties.0kRdQbT-6", "x": 819.517578125, "y": 6403.87890625, "order": 9, "level": 3, "source_type": "string", "path": "root.cdn_data.properties.0kRdQbT-6", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "翻译", "description": "用于字段值翻译，常见用于枚举字段", "field": "翻译", "input": {"asset_web_type": "translate", "description": "用于字段值翻译，常见用于枚举字段", "values": [{"name": "CDN", "value": "华为云CDN"}]}, "action_type": "translate", "attrs": {"text": "翻译"}}, {"type": "asset", "asset_type": "action", "id": "root.cdn_data.properties.0kRdQbT-6.OTTEirNRU", "x": 1123.517578125, "y": 6400.87890625, "order": 10, "level": 4, "path": "root.cdn_data.properties.0kRdQbT-6.OTTEirNRU", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "network_mapping.class"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "wc5XMANpW", "display_name": "网络映射", "description": "network_mapping.class", "x": 1323.517578125, "y": 6400.87890625, "label": "网络映射-映射类别", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.region.TGgUfNNOp", "x": 850.517578125, "y": 1298.878906249999, "order": 11, "level": 2, "source_type": "string", "path": "root.region.TGgUfNNOp", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "cloud.region.display_value"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "W6wF6tgoX", "display_name": "云", "description": "cloud.region.display_value", "x": 1050.517578125, "y": 1298.878906249999, "label": "云-所属区域-展示名称", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.hostname.mD-ZUnga9", "x": 843.517578125, "y": 1389.878906249999, "order": 12, "level": 2, "source_type": "string", "path": "root.hostname.mD-ZUnga9", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "network_mapping.domain"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "xsZ0tsGPt", "display_name": "网络映射", "description": "network_mapping.domain", "x": 1043.517578125, "y": 1389.878906249999, "label": "网络映射-域名", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.access_code.fVmFoSTCH", "x": 847.517578125, "y": 1488.878906249999, "order": 13, "level": 2, "source_type": "string", "path": "root.access_code.fVmFoSTCH", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "network_mapping.cname"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "awsikTnA5", "display_name": "网络映射", "description": "network_mapping.cname", "x": 1047.517578125, "y": 1488.878906249999, "label": "网络映射-CNAME地址", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.web_tag.xVrjxdExO", "x": 871.517578125, "y": 2094.87890625, "order": 14, "level": 2, "source_type": "string", "path": "root.web_tag.xVrjxdExO", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "network_mapping.sys_name"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "cc24oVcsF", "display_name": "网络映射", "description": "network_mapping.sys_name", "x": 1071.517578125, "y": 2094.87890625, "label": "网络映射-关联系统名称", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.detail_data.server.lvOAeUMOZ", "x": 1077.517578125, "y": 2698.87890625, "order": 15, "level": 3, "source_type": "list", "path": "root.detail_data.server.lvOAeUMOZ", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "for循环", "description": "用于将数组进行循环操作", "field": "for循环", "input": {"asset_web_type": "for", "description": "用于将数组进行循环操作"}, "action_type": "for", "attrs": {"text": "for循环"}}, {"x": 1277.517578125, "y": 2798.87890625, "id": "root.detail_data.server.lvOAeUMOZ.element", "level": 4, "path": "detail_data.server.element", "sub_fields": [{"field": "address", "path": "detail_data.server.element.address", "type": "string", "x": 1477.517578125, "y": 2898.87890625}, {"field": "ipv4", "path": "detail_data.server.element.ipv4", "type": "string", "x": 1477.517578125, "y": 2998.87890625}, {"field": "ipv6", "path": "detail_data.server.element.ipv6", "type": "string", "x": 1477.517578125, "y": 3098.87890625}, {"field": "port", "path": "detail_data.server.element.port", "type": "integer", "x": 1477.517578125, "y": 3198.87890625}, {"field": "type", "path": "detail_data.server.element.type", "type": "string", "x": 1477.517578125, "y": 3298.87890625}, {"field": "weight", "path": "detail_data.server.element.weight", "type": "integer", "x": 1477.517578125, "y": 3398.87890625}, {"field": "front_protocol", "path": "detail_data.server.element.front_protocol", "type": "string", "x": 1477.517578125, "y": 3498.87890625}, {"field": "back_protocol", "path": "detail_data.server.element.back_protocol", "type": "string", "x": 1477.517578125, "y": 3598.87890625}], "type": "asset", "datatype": "object", "asset_type": "asset", "field": "element", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 1477.517578125, "y": 2898.87890625, "id": "root.detail_data.server.lvOAeUMOZ.element.address", "level": 5, "path": "detail_data.server.element.address", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "address", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 1477.517578125, "y": 2998.87890625, "id": "root.detail_data.server.lvOAeUMOZ.element.ipv4", "level": 5, "path": "detail_data.server.element.ipv4", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "ipv4", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 1477.517578125, "y": 3098.87890625, "id": "root.detail_data.server.lvOAeUMOZ.element.ipv6", "level": 5, "path": "detail_data.server.element.ipv6", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "ipv6", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 1477.517578125, "y": 3198.87890625, "id": "root.detail_data.server.lvOAeUMOZ.element.port", "level": 5, "path": "detail_data.server.element.port", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "port", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 1477.517578125, "y": 3298.87890625, "id": "root.detail_data.server.lvOAeUMOZ.element.type", "level": 5, "path": "detail_data.server.element.type", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "type", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 1477.517578125, "y": 3398.87890625, "id": "root.detail_data.server.lvOAeUMOZ.element.weight", "level": 5, "path": "detail_data.server.element.weight", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "weight", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 1477.517578125, "y": 3498.87890625, "id": "root.detail_data.server.lvOAeUMOZ.element.front_protocol", "level": 5, "path": "detail_data.server.element.front_protocol", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "front_protocol", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 1477.517578125, "y": 3598.87890625, "id": "root.detail_data.server.lvOAeUMOZ.element.back_protocol", "level": 5, "path": "detail_data.server.element.back_protocol", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "back_protocol", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.detail_data.server.lvOAeUMOZ._LJuvuf56", "x": 1548.517578125, "y": 2696.87890625, "order": 16, "level": 4, "path": "root.detail_data.server.lvOAeUMOZ._LJuvuf56", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "network_mapping.destinations"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "HttqhBwkL", "display_name": "网络映射", "description": "network_mapping.destinations", "x": 1748.517578125, "y": 2696.87890625, "label": "网络映射-映射目标列表", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.detail_data.server.lvOAeUMOZ.element.ipv4.YIimgNuKt", "x": 1902.517578125, "y": 2995.87890625, "order": 17, "level": 6, "source_type": "string", "path": "root.detail_data.server.lvOAeUMOZ.element.ipv4.YIimgNuKt", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "network_mapping.destinations.ip"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "Fjh1R8qO_", "display_name": "网络映射", "description": "network_mapping.destinations.ip", "x": 2102.517578125, "y": 2995.87890625, "label": "网络映射-映射目标列表-IPv4地址", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.detail_data.server.lvOAeUMOZ.element.ipv6.z5QFDE5BJ", "x": 1900.517578125, "y": 3092.87890625, "order": 18, "level": 6, "source_type": "string", "path": "root.detail_data.server.lvOAeUMOZ.element.ipv6.z5QFDE5BJ", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "network_mapping.destinations.ip_v6"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "F5_NusHnN", "display_name": "网络映射", "description": "network_mapping.destinations.ip_v6", "x": 2100.517578125, "y": 3092.87890625, "label": "网络映射-映射目标列表-IPv6地址", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.detail_data.server.lvOAeUMOZ.element.port.rnReNcnDT", "x": 1905.517578125, "y": 3198.87890625, "order": 19, "level": 6, "source_type": "integer", "path": "root.detail_data.server.lvOAeUMOZ.element.port.rnReNcnDT", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "network_mapping.destinations.port"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "6fBBBeeeE", "display_name": "网络映射", "description": "network_mapping.destinations.port", "x": 2105.517578125, "y": 3198.87890625, "label": "网络映射-映射目标列表-端口", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.detail_data.timestamp.uBTANS_eL", "x": 2210.5175781249995, "y": 2945.8789062500005, "order": 23, "level": 3, "source_type": "integer", "path": "root.detail_data.timestamp.uBTANS_eL", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "数据相除", "description": "用于数据相乘，可指定目标字段，并将结果赋值目标字段。目前只支持任意字段与数字相乘", "field": "数据相除", "input": {"asset_web_type": "division", "description": "用于数据相乘，可指定目标字段，并将结果赋值目标字段。目前只支持任意字段与数字相乘", "values": ["1000"]}, "action_type": "division", "attrs": {"text": "数据相除"}}, {"type": "asset", "asset_type": "action", "id": "root.detail_data.timestamp.uBTANS_eL.wVTXqtXOy", "x": 2525.5175781249995, "y": 2944.8789062500005, "order": 24, "level": 4, "path": "root.detail_data.timestamp.uBTANS_eL.wVTXqtXOy", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "时间戳转为datetime", "description": "用于将时间戳转为datetime", "field": "时间戳转为datetime", "input": {"asset_web_type": "timestamp_to_datetime", "description": "用于将时间戳转为datetime"}, "action_type": "timestamp_to_datetime", "attrs": {"text": "时间戳转为datetime"}}, {"type": "asset", "asset_type": "action", "id": "root.detail_data.timestamp.uBTANS_eL.wVTXqtXOy.PwhZNTKCj", "x": 2867.5175781249995, "y": 2944.8789062500005, "order": 25, "level": 5, "path": "root.detail_data.timestamp.uBTANS_eL.wVTXqtXOy.PwhZNTKCj", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "base.first_seen"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "IJuIphSWt", "display_name": "基础", "description": "base.first_seen", "x": 3067.5175781249995, "y": 2944.8789062500005, "label": "基础-首次出现时间", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.description.sqIFjjz7s", "x": 891.517578125, "y": 299.87890624999955, "order": 26, "level": 2, "source_type": "string", "path": "root.description.sqIFjjz7s", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "base.description"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "D0SrB-ymm", "display_name": "基础", "description": "base.description", "x": 1091.517578125, "y": 299.87890624999955, "label": "基础-描述信息", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.cdn_data.cname.dKL5fKCq2", "x": 838.517578125, "y": 5600.87890625, "order": 27, "level": 3, "source_type": "string", "path": "root.cdn_data.cname.dKL5fKCq2", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "network_mapping.cname"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "LWmJ0Afui", "display_name": "网络映射", "description": "network_mapping.cname", "x": 1038.517578125, "y": 5600.87890625, "label": "网络映射-CNAME地址", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.cdn_data.domain_name.YtP6PzETC", "x": 800.517578125, "y": 5294.878906250001, "order": 28, "level": 3, "source_type": "string", "path": "root.cdn_data.domain_name.YtP6PzETC", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "network_mapping.domain"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "Jg5n5g0t_", "display_name": "网络映射", "description": "network_mapping.domain", "x": 1000.517578125, "y": 5294.878906250001, "label": "网络映射-域名", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.cdn_data.sources.SsDCo4EXa", "x": 1159.517578125, "y": 5098.878906250004, "order": 29, "level": 3, "source_type": "list", "path": "root.cdn_data.sources.SsDCo4EXa", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "for循环", "description": "用于将数组进行循环操作", "field": "for循环", "input": {"asset_web_type": "for", "description": "用于将数组进行循环操作"}, "action_type": "for", "attrs": {"text": "for循环"}}, {"x": 1359.517578125, "y": 5198.878906250004, "id": "root.cdn_data.sources.SsDCo4EXa.element", "level": 4, "path": "cdn_data.sources.element", "sub_fields": [{"field": "priority", "path": "cdn_data.sources.element.priority", "type": "integer", "x": 1559.517578125, "y": 5298.878906250004}, {"field": "origin_addr", "path": "cdn_data.sources.element.origin_addr", "type": "string", "x": 1559.517578125, "y": 5398.878906250004}, {"field": "origin_type", "path": "cdn_data.sources.element.origin_type", "type": "string", "x": 1559.517578125, "y": 5498.878906250004}, {"field": "http_port", "path": "cdn_data.sources.element.http_port", "type": "integer", "x": 1559.517578125, "y": 5598.878906250004}, {"field": "https_port", "path": "cdn_data.sources.element.https_port", "type": "integer", "x": 1559.517578125, "y": 5698.878906250004}, {"field": "host_name", "path": "cdn_data.sources.element.host_name", "type": "string", "x": 1559.517578125, "y": 5798.878906250004}], "type": "asset", "datatype": "object", "asset_type": "asset", "field": "element", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 1559.517578125, "y": 5298.878906250004, "id": "root.cdn_data.sources.SsDCo4EXa.element.priority", "level": 5, "path": "cdn_data.sources.element.priority", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "priority", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 1559.517578125, "y": 5398.878906250004, "id": "root.cdn_data.sources.SsDCo4EXa.element.origin_addr", "level": 5, "path": "cdn_data.sources.element.origin_addr", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "origin_addr", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 1559.517578125, "y": 5498.878906250004, "id": "root.cdn_data.sources.SsDCo4EXa.element.origin_type", "level": 5, "path": "cdn_data.sources.element.origin_type", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "origin_type", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 1559.517578125, "y": 5598.878906250004, "id": "root.cdn_data.sources.SsDCo4EXa.element.http_port", "level": 5, "path": "cdn_data.sources.element.http_port", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "http_port", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 1559.517578125, "y": 5698.878906250004, "id": "root.cdn_data.sources.SsDCo4EXa.element.https_port", "level": 5, "path": "cdn_data.sources.element.https_port", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "https_port", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 1559.517578125, "y": 5798.878906250004, "id": "root.cdn_data.sources.SsDCo4EXa.element.host_name", "level": 5, "path": "cdn_data.sources.element.host_name", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "host_name", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.cdn_data.sources.SsDCo4EXa.SSQd9kHvm", "x": 1787.517578125, "y": 5078.87890625, "order": 1, "level": 4, "path": "root.cdn_data.sources.SsDCo4EXa.SSQd9kHvm", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "network_mapping.backend_sources"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "2gbP1IxYR", "display_name": "网络映射", "description": "network_mapping.backend_sources", "x": 1987.517578125, "y": 5078.87890625, "label": "网络映射-回源信息列表", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.cdn_data.sources.SsDCo4EXa.element.origin_type.TODogHyi3", "x": 2021.03515625, "y": 5494.7578125000055, "order": 2, "level": 6, "source_type": "string", "path": "root.cdn_data.sources.SsDCo4EXa.element.origin_type.TODogHyi3", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "翻译", "description": "用于字段值翻译，常见用于枚举字段", "field": "翻译", "input": {"asset_web_type": "translate", "description": "用于字段值翻译，常见用于枚举字段", "values": [{"name": "ipaddr", "value": "1"}, {"name": "domain", "value": "2"}, {"name": "obs_bucket", "value": "3"}, {"name": "third_bucket", "value": "4"}]}, "action_type": "translate", "attrs": {"text": "翻译"}}, {"type": "asset", "asset_type": "action", "id": "root.cdn_data.sources.SsDCo4EXa.element.origin_type.TODogHyi3.7wUaxqkEk", "x": 2470.03515625, "y": 5488.7578125000055, "order": 3, "level": 7, "path": "root.cdn_data.sources.SsDCo4EXa.element.origin_type.TODogHyi3.7wUaxqkEk", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "network_mapping.backend_sources.type"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "4u5Dy0Buk", "display_name": "网络映射", "description": "network_mapping.backend_sources.type", "x": 2670.03515625, "y": 5488.7578125000055, "label": "网络映射-回源信息列表-回源类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.cdn_data.sources.SsDCo4EXa.element.origin_addr.D9k9nJEJe", "x": 2160.03515625, "y": 5389.7578125000055, "order": 4, "level": 6, "source_type": "string", "path": "root.cdn_data.sources.SsDCo4EXa.element.origin_addr.D9k9nJEJe", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "network_mapping.backend_sources.addr"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "GvEz5t5Mk", "display_name": "网络映射", "description": "network_mapping.backend_sources.addr", "x": 2360.03515625, "y": 5389.7578125000055, "label": "网络映射-回源信息列表-回源地址", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.cdn_data.sources.SsDCo4EXa.element.https_port.1zA0XhOnG", "x": 2027.03515625, "y": 5690.7578125000055, "order": 5, "level": 6, "source_type": "integer", "path": "root.cdn_data.sources.SsDCo4EXa.element.https_port.1zA0XhOnG", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "network_mapping.backend_sources.port"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "HDvHJzFLB", "display_name": "网络映射", "description": "network_mapping.backend_sources.port", "x": 2227.03515625, "y": 5690.7578125000055, "label": "网络映射-回源信息列表-回源端口", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.properties.KVX_ksg-1", "x": 867.5175781250002, "y": 398.87890625000364, "order": 6, "level": 2, "source_type": "string", "path": "root.properties.KVX_ksg-1", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "翻译", "description": "用于字段值翻译，常见用于枚举字段", "field": "翻译", "input": {"asset_web_type": "translate", "description": "用于字段值翻译，常见用于枚举字段", "values": [{"name": "WAF", "value": "华为云WAF"}]}, "action_type": "translate", "attrs": {"text": "翻译"}}, {"type": "asset", "asset_type": "action", "id": "root.properties.KVX_ksg-1.Cn5UDCy9f", "x": 1239.5175781250002, "y": 398.87890625000364, "order": 7, "level": 3, "path": "root.properties.KVX_ksg-1.Cn5UDCy9f", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "network_mapping.class"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "Ti1M38YGW", "display_name": "网络映射", "description": "network_mapping.class", "x": 1439.5175781250002, "y": 398.87890625000364, "label": "网络映射-映射类别", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.detail_data.server.lvOAeUMOZ.element.front_protocol.wW5KIYS8m", "x": 2037.30859375, "y": 3491.37890625, "order": 2, "level": 6, "source_type": "string", "path": "root.detail_data.server.lvOAeUMOZ.element.front_protocol.wW5KIYS8m", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "network_mapping.destinations.protocol"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "03-EQuq4C", "display_name": "网络映射", "description": "network_mapping.destinations.protocol", "x": 2237.30859375, "y": 3491.37890625, "label": "网络映射-映射目标列表-协议", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}], "edges": [{"source": "root", "target": "root.id", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.321338255069865351700047081951", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 100, "anchor_index": 0}}, {"source": "root", "target": "root.hostid", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.104344071105042291700047081951", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 200, "anchor_index": 0}}, {"source": "root", "target": "root.description", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.021177328014734711700047081951", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 300, "anchor_index": 0}}, {"source": "root", "target": "root.properties", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.79461763272715191700047081951", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 400, "anchor_index": 0}}, {"source": "root", "target": "root.type", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.90768172142699851700047081951", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 500, "anchor_index": 0}}, {"source": "root", "target": "root.proxy", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.420781526964879141700047081951", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 600, "anchor_index": 0}}, {"source": "root", "target": "root.flag", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.53809381003753121700047081951", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 700, "anchor_index": 0}}, {"source": "root.flag", "target": "root.flag.pci_3ds", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.97804726292438931700047081952", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 725.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 800, "anchor_index": 0}}, {"source": "root.flag", "target": "root.flag.pci_dss", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.452732205521341641700047081952", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 725.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 900, "anchor_index": 0}}, {"source": "root.flag", "target": "root.flag.ipv6", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.073164416123630671700047081952", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 725.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 1000, "anchor_index": 0}}, {"source": "root.flag", "target": "root.flag.cname", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.342722918314663171700047081952", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 725.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 1100, "anchor_index": 0}}, {"source": "root.flag", "target": "root.flag.is_dual_az", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.111631077520879311700047081952", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 725.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 1200, "anchor_index": 0}}, {"source": "root", "target": "root.region", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.80437738018533091700047081952", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 1300, "anchor_index": 0}}, {"source": "root", "target": "root.hostname", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.239813556946422371700047081952", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 1400, "anchor_index": 0}}, {"source": "root", "target": "root.access_code", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.93944942994946671700047081952", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 1500, "anchor_index": 0}}, {"source": "root", "target": "root.policyid", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.404344876574968871700047081952", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 1600, "anchor_index": 0}}, {"source": "root", "target": "root.timestamp", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.65248026743278451700047081952", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 1700, "anchor_index": 0}}, {"source": "root", "target": "root.protect_status", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.145096528196039021700047081952", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 1800, "anchor_index": 0}}, {"source": "root", "target": "root.access_status", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.87736265487807291700047081952", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 1900, "anchor_index": 0}}, {"source": "root", "target": "root.exclusive_ip", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.084858752799748641700047081952", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 2000, "anchor_index": 0}}, {"source": "root", "target": "root.web_tag", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.95573663782995721700047081952", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 2100, "anchor_index": 0}}, {"source": "root", "target": "root.paid_type", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.81515970161919031700047081952", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 2200, "anchor_index": 0}}, {"source": "root", "target": "root.detail_data", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.45717588860872341700047081952", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 2300, "anchor_index": 0}}, {"source": "root.detail_data", "target": "root.detail_data.id", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.22708697594721051700047081952", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2325.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 2400, "anchor_index": 0}}, {"source": "root.detail_data", "target": "root.detail_data.hostname", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.61945722333574071700047081952", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2325.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 2500, "anchor_index": 0}}, {"source": "root.detail_data", "target": "root.detail_data.protocol", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.457284548156474461700047081952", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2325.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 2600, "anchor_index": 0}}, {"source": "root.detail_data", "target": "root.detail_data.server", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.0256901891406329241700047081952", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2325.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 2700, "anchor_index": 0}}, {"source": "root.detail_data", "target": "root.detail_data.proxy", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.223278199634955541700047081952", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2325.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 2800, "anchor_index": 0}}, {"source": "root.detail_data", "target": "root.detail_data.locked", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.365649388230496751700047081952", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2325.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 2900, "anchor_index": 0}}, {"source": "root.detail_data", "target": "root.detail_data.timestamp", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.0597371365750858361700047081952", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2325.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 3000, "anchor_index": 0}}, {"source": "root.detail_data", "target": "root.detail_data.flag", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.3473637151712511700047081952", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2325.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 3100, "anchor_index": 0}}, {"source": "root.detail_data.flag", "target": "root.detail_data.flag.pci_3ds", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.23478893183252871700047081952", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 400, "y": 3125.5, "anchor_index": 1}, "end_point": {"x": 549.5, "y": 3200, "anchor_index": 0}}, {"source": "root.detail_data.flag", "target": "root.detail_data.flag.pci_dss", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.334455022595982141700047081952", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 400, "y": 3125.5, "anchor_index": 1}, "end_point": {"x": 549.5, "y": 3300, "anchor_index": 0}}, {"source": "root.detail_data.flag", "target": "root.detail_data.flag.ipv6", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.415128471213509261700047081952", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 400, "y": 3125.5, "anchor_index": 1}, "end_point": {"x": 549.5, "y": 3400, "anchor_index": 0}}, {"source": "root.detail_data.flag", "target": "root.detail_data.flag.cname", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.68934113515515171700047081952", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 400, "y": 3125.5, "anchor_index": 1}, "end_point": {"x": 549.5, "y": 3500, "anchor_index": 0}}, {"source": "root.detail_data.flag", "target": "root.detail_data.flag.is_dual_az", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.26743920068108951700047081953", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 400, "y": 3125.5, "anchor_index": 1}, "end_point": {"x": 549.5, "y": 3600, "anchor_index": 0}}, {"source": "root.detail_data", "target": "root.detail_data.description", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.93511058177203671700047081953", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2325.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 3700, "anchor_index": 0}}, {"source": "root.detail_data", "target": "root.detail_data.policyid", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.56292298205949561700047081953", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2325.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 3800, "anchor_index": 0}}, {"source": "root.detail_data", "target": "root.detail_data.domainid", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.396874440312785071700047081953", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2325.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 3900, "anchor_index": 0}}, {"source": "root.detail_data", "target": "root.detail_data.projectid", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.34857710299192311700047081953", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2325.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 4000, "anchor_index": 0}}, {"source": "root.detail_data", "target": "root.detail_data.enterprise_project_id", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.163057456309537941700047081953", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2325.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 4100, "anchor_index": 0}}, {"source": "root.detail_data", "target": "root.detail_data.protect_status", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.68357418407132191700047081953", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2325.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 4200, "anchor_index": 0}}, {"source": "root.detail_data", "target": "root.detail_data.access_status", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.57127035664973521700047081953", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2325.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 4300, "anchor_index": 0}}, {"source": "root.detail_data", "target": "root.detail_data.access_code", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.72287220033155711700047081953", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2325.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 4400, "anchor_index": 0}}, {"source": "root.detail_data", "target": "root.detail_data.block_page", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.54846270724551861700047081953", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2325.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 4500, "anchor_index": 0}}, {"source": "root.detail_data.block_page", "target": "root.detail_data.block_page.template", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.68211681953293551700047081953", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 400, "y": 4525.5, "anchor_index": 1}, "end_point": {"x": 549.5, "y": 4600, "anchor_index": 0}}, {"source": "root.detail_data", "target": "root.detail_data.web_tag", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.83884775093168251700047081953", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2325.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 4700, "anchor_index": 0}}, {"source": "root.detail_data", "target": "root.detail_data.exclusive_ip", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.284722112472512331700047081953", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2325.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 4800, "anchor_index": 0}}, {"source": "root.detail_data", "target": "root.detail_data.http2_enable", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.6168333309282211700047081953", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2325.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 4900, "anchor_index": 0}}, {"source": "root", "target": "root.cdn_data", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.51416830284077551700047081953", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 5000, "anchor_index": 0}}, {"source": "root.cdn_data", "target": "root.cdn_data.sources", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.93910431029768261700047081953", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 5025.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 5100, "anchor_index": 0}}, {"source": "root.cdn_data", "target": "root.cdn_data.id", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.186734600000392441700047081953", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 5025.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 5200, "anchor_index": 0}}, {"source": "root.cdn_data", "target": "root.cdn_data.domain_name", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.39817651840439641700047081953", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 5025.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 5300, "anchor_index": 0}}, {"source": "root.cdn_data", "target": "root.cdn_data.business_type", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.119609872224532451700047081953", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 5025.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 5400, "anchor_index": 0}}, {"source": "root.cdn_data", "target": "root.cdn_data.service_area", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.85309512963505881700047081953", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 5025.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 5500, "anchor_index": 0}}, {"source": "root.cdn_data", "target": "root.cdn_data.cname", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.69632404610565351700047081953", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 5025.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 5600, "anchor_index": 0}}, {"source": "root.cdn_data", "target": "root.cdn_data.domain_status", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.13132206058137541700047081953", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 5025.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 5700, "anchor_index": 0}}, {"source": "root.cdn_data", "target": "root.cdn_data.https_status", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.344245528795284141700047081953", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 5025.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 5800, "anchor_index": 0}}, {"source": "root.cdn_data", "target": "root.cdn_data.create_time", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.1929034333953251700047081953", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 5025.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 5900, "anchor_index": 0}}, {"source": "root.cdn_data", "target": "root.cdn_data.update_time", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.60207533249155381700047081953", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 5025.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 6000, "anchor_index": 0}}, {"source": "root.cdn_data", "target": "root.cdn_data.disabled", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.89918700486815631700047081953", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 5025.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 6100, "anchor_index": 0}}, {"source": "root.cdn_data", "target": "root.cdn_data.locked", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.62006841724444351700047081953", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 5025.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 6200, "anchor_index": 0}}, {"source": "root.cdn_data", "target": "root.cdn_data.auto_refresh_preheat", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.5868149915266861700047081953", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 5025.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 6300, "anchor_index": 0}}, {"source": "root.cdn_data", "target": "root.cdn_data.properties", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.98437880935210531700047081954", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 5025.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 6400, "anchor_index": 0}}, {"source": "root", "target": "root.IZAV59ghf", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.31742575554263921700047089952", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 50.5, "y": 0, "anchor_index": 1}, "end_point": {"x": 412.017578125, "y": -156.12109375, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.IZAV59ghf", "target": "root.IZAV59ghf.4AM9Uoq0B", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.45917159322077231700047123262", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 513.017578125, "y": -156.12109375, "anchor_index": 1}, "end_point": {"x": 946.017578125, "y": -158.12109375, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.IZAV59ghf.4AM9Uoq0B", "target": "ejoH1aDyD", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.83083713179674851700047141428", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1047.017578125, "y": -158.12109375, "anchor_index": 1}, "end_point": {"x": 1146.017578125, "y": -158.12109375, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root", "target": "root.jE9kibry6", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.51503248628874231700047144713", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 50.5, "y": 0, "anchor_index": 1}, "end_point": {"x": 419.017578125, "y": -279.12109375, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.jE9kibry6", "target": "root.jE9kibry6.XsBmBAAot", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.98383264899924571700047163176", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 520.017578125, "y": -279.12109375, "anchor_index": 1}, "end_point": {"x": 845.017578125, "y": -280.12109375, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.jE9kibry6.XsBmBAAot", "target": "_DA3YYyM0", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.18324828891603231700047172613", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 946.017578125, "y": -280.12109375, "anchor_index": 1}, "end_point": {"x": 1045.017578125, "y": -280.12109375, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.cdn_data.properties", "target": "root.cdn_data.properties.0kRdQbT-6", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.57180680987600961700047279909", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 450.5, "y": 6400, "anchor_index": 1}, "end_point": {"x": 769.017578125, "y": 6403.87890625, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.cdn_data.properties.0kRdQbT-6", "target": "root.cdn_data.properties.0kRdQbT-6.OTTEirNRU", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.85635476236877331700047309406", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 870.017578125, "y": 6403.87890625, "anchor_index": 1}, "end_point": {"x": 1073.017578125, "y": 6400.87890625, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.cdn_data.properties.0kRdQbT-6.OTTEirNRU", "target": "wc5XMANpW", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.19355460394791081700047334086", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1174.017578125, "y": 6400.87890625, "anchor_index": 1}, "end_point": {"x": 1273.017578125, "y": 6400.87890625, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.region", "target": "root.region.TGgUfNNOp", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.93028451982487591700047357078", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 1300, "anchor_index": 1}, "end_point": {"x": 800.017578125, "y": 1298.878906249999, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.region.TGgUfNNOp", "target": "W6wF6tgoX", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.97578239672898871700047365795", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 901.017578125, "y": 1298.878906249999, "anchor_index": 1}, "end_point": {"x": 1000.017578125, "y": 1298.878906249999, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.hostname", "target": "root.hostname.mD-ZUnga9", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.49008575041993211700047374405", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 1400, "anchor_index": 1}, "end_point": {"x": 793.017578125, "y": 1389.878906249999, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.hostname.mD-ZUnga9", "target": "xsZ0tsGPt", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.107920045283815821700047383270", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 894.017578125, "y": 1389.878906249999, "anchor_index": 1}, "end_point": {"x": 993.017578125, "y": 1389.878906249999, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.access_code", "target": "root.access_code.fVmFoSTCH", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.44899110931761731700047389029", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 1500, "anchor_index": 1}, "end_point": {"x": 797.017578125, "y": 1488.878906249999, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.access_code.fVmFoSTCH", "target": "awsikTnA5", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.90764334268648691700047396988", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 898.017578125, "y": 1488.878906249999, "anchor_index": 1}, "end_point": {"x": 997.017578125, "y": 1488.878906249999, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.web_tag", "target": "root.web_tag.xVrjxdExO", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.22125416365794861700047410102", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 2100, "anchor_index": 1}, "end_point": {"x": 821.017578125, "y": 2094.87890625, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.web_tag.xVrjxdExO", "target": "cc24oVcsF", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.152120534146651831700047420650", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 922.017578125, "y": 2094.87890625, "anchor_index": 1}, "end_point": {"x": 1021.017578125, "y": 2094.87890625, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.detail_data.server", "target": "root.detail_data.server.lvOAeUMOZ", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.9165688747668631700047444208", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 450.5, "y": 2700, "anchor_index": 1}, "end_point": {"x": 1027.017578125, "y": 2698.87890625, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.detail_data.server.lvOAeUMOZ", "target": "root.detail_data.server.lvOAeUMOZ.element", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.208653863755867471700047452304", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1128.017578125, "y": 2698.87890625, "anchor_index": 1}, "end_point": {"x": 1227.017578125, "y": 2798.87890625, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.detail_data.server.lvOAeUMOZ.element", "target": "root.detail_data.server.lvOAeUMOZ.element.address", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.394110731792530761700047452304", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1277.517578125, "y": 2824.37890625, "anchor_index": 1}, "end_point": {"x": 1427.017578125, "y": 2898.87890625, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.detail_data.server.lvOAeUMOZ.element", "target": "root.detail_data.server.lvOAeUMOZ.element.ipv4", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.61721906789920891700047452304", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1277.517578125, "y": 2824.37890625, "anchor_index": 1}, "end_point": {"x": 1427.017578125, "y": 2998.87890625, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.detail_data.server.lvOAeUMOZ.element", "target": "root.detail_data.server.lvOAeUMOZ.element.ipv6", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.62807957557344161700047452304", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1277.517578125, "y": 2824.37890625, "anchor_index": 1}, "end_point": {"x": 1427.017578125, "y": 3098.87890625, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.detail_data.server.lvOAeUMOZ.element", "target": "root.detail_data.server.lvOAeUMOZ.element.port", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.07142915414250851700047452304", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1277.517578125, "y": 2824.37890625, "anchor_index": 1}, "end_point": {"x": 1427.017578125, "y": 3198.87890625, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.detail_data.server.lvOAeUMOZ.element", "target": "root.detail_data.server.lvOAeUMOZ.element.type", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.87762967598418861700047452305", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1277.517578125, "y": 2824.37890625, "anchor_index": 1}, "end_point": {"x": 1427.017578125, "y": 3298.87890625, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.detail_data.server.lvOAeUMOZ.element", "target": "root.detail_data.server.lvOAeUMOZ.element.weight", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.142767409985751881700047452305", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1277.517578125, "y": 2824.37890625, "anchor_index": 1}, "end_point": {"x": 1427.017578125, "y": 3398.87890625, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.detail_data.server.lvOAeUMOZ.element", "target": "root.detail_data.server.lvOAeUMOZ.element.front_protocol", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.55239573553556551700047452305", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1277.517578125, "y": 2824.37890625, "anchor_index": 1}, "end_point": {"x": 1427.017578125, "y": 3498.87890625, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.detail_data.server.lvOAeUMOZ.element", "target": "root.detail_data.server.lvOAeUMOZ.element.back_protocol", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.52947642581098141700047452305", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1277.517578125, "y": 2824.37890625, "anchor_index": 1}, "end_point": {"x": 1427.017578125, "y": 3598.87890625, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.detail_data.server.lvOAeUMOZ", "target": "root.detail_data.server.lvOAeUMOZ._LJuvuf56", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.301299366990825931700047456272", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1128.017578125, "y": 2698.87890625, "anchor_index": 1}, "end_point": {"x": 1498.017578125, "y": 2696.87890625, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.detail_data.server.lvOAeUMOZ._LJuvuf56", "target": "HttqhBwkL", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.174557370137518041700047466321", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1599.017578125, "y": 2696.87890625, "anchor_index": 1}, "end_point": {"x": 1698.017578125, "y": 2696.87890625, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.detail_data.server.lvOAeUMOZ.element.ipv4", "target": "root.detail_data.server.lvOAeUMOZ.element.ipv4.YIimgNuKt", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.75782430825378851700047470962", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1528.017578125, "y": 2998.87890625, "anchor_index": 1}, "end_point": {"x": 1852.017578125, "y": 2995.87890625, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.detail_data.server.lvOAeUMOZ.element.ipv4.YIimgNuKt", "target": "Fjh1R8qO_", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.231907266532888961700047477489", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1953.017578125, "y": 2995.87890625, "anchor_index": 1}, "end_point": {"x": 2052.017578125, "y": 2995.87890625, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.detail_data.server.lvOAeUMOZ.element.ipv6", "target": "root.detail_data.server.lvOAeUMOZ.element.ipv6.z5QFDE5BJ", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.395456259291437241700047481007", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1528.017578125, "y": 3098.87890625, "anchor_index": 1}, "end_point": {"x": 1850.017578125, "y": 3092.87890625, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.detail_data.server.lvOAeUMOZ.element.ipv6.z5QFDE5BJ", "target": "F5_NusHnN", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.369918648529780741700047488250", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1951.017578125, "y": 3092.87890625, "anchor_index": 1}, "end_point": {"x": 2050.017578125, "y": 3092.87890625, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.detail_data.server.lvOAeUMOZ.element.port", "target": "root.detail_data.server.lvOAeUMOZ.element.port.rnReNcnDT", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.465055068595517931700047491997", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1528.017578125, "y": 3198.87890625, "anchor_index": 1}, "end_point": {"x": 1855.017578125, "y": 3198.87890625, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.detail_data.server.lvOAeUMOZ.element.port.rnReNcnDT", "target": "6fBBBeeeE", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.86158379650651451700047503844", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1956.017578125, "y": 3198.87890625, "anchor_index": 1}, "end_point": {"x": 2055.017578125, "y": 3198.87890625, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.detail_data.timestamp", "target": "root.detail_data.timestamp.uBTANS_eL", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.072437333655118061700047570339", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 450.5, "y": 3000, "anchor_index": 1}, "end_point": {"x": 2160.0175781249995, "y": 2945.8789062500005, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.detail_data.timestamp.uBTANS_eL", "target": "root.detail_data.timestamp.uBTANS_eL.wVTXqtXOy", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.76845838163858551700047600391", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 2261.0175781249995, "y": 2945.8789062500005, "anchor_index": 1}, "end_point": {"x": 2475.0175781249995, "y": 2944.8789062500005, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.detail_data.timestamp.uBTANS_eL.wVTXqtXOy", "target": "root.detail_data.timestamp.uBTANS_eL.wVTXqtXOy.PwhZNTKCj", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.90786135531379861700047610448", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 2576.0175781249995, "y": 2944.8789062500005, "anchor_index": 1}, "end_point": {"x": 2817.0175781249995, "y": 2944.8789062500005, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.detail_data.timestamp.uBTANS_eL.wVTXqtXOy.PwhZNTKCj", "target": "IJuIphSWt", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.79239770310549071700047617284", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 2918.0175781249995, "y": 2944.8789062500005, "anchor_index": 1}, "end_point": {"x": 3017.0175781249995, "y": 2944.8789062500005, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.description", "target": "root.description.sqIFjjz7s", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.96405566208870041700047659555", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 300, "anchor_index": 1}, "end_point": {"x": 841.017578125, "y": 299.87890624999955, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.description.sqIFjjz7s", "target": "D0SrB-ymm", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.53382778174508831700047667815", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 942.017578125, "y": 299.87890624999955, "anchor_index": 1}, "end_point": {"x": 1041.017578125, "y": 299.87890624999955, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.cdn_data.cname", "target": "root.cdn_data.cname.dKL5fKCq2", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.137701477220847581700047685309", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 450.5, "y": 5600, "anchor_index": 1}, "end_point": {"x": 788.017578125, "y": 5600.87890625, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.cdn_data.cname.dKL5fKCq2", "target": "LWmJ0Afui", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.0044625483111022661700047693056", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 889.017578125, "y": 5600.87890625, "anchor_index": 1}, "end_point": {"x": 988.017578125, "y": 5600.87890625, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.cdn_data.domain_name", "target": "root.cdn_data.domain_name.YtP6PzETC", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.55475281187961391700047698625", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 450.5, "y": 5300, "anchor_index": 1}, "end_point": {"x": 750.017578125, "y": 5294.878906250001, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.cdn_data.domain_name.YtP6PzETC", "target": "Jg5n5g0t_", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.726119686057051700047710036", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 851.017578125, "y": 5294.878906250001, "anchor_index": 1}, "end_point": {"x": 950.017578125, "y": 5294.878906250001, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.cdn_data.sources", "target": "root.cdn_data.sources.SsDCo4EXa", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.93276406112903491700047729429", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 450.5, "y": 5100, "anchor_index": 1}, "end_point": {"x": 1109.017578125, "y": 5098.878906250004, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.cdn_data.sources.SsDCo4EXa", "target": "root.cdn_data.sources.SsDCo4EXa.element", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.52080724317073561700047734681", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1210.017578125, "y": 5098.878906250004, "anchor_index": 1}, "end_point": {"x": 1309.017578125, "y": 5198.878906250004, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.cdn_data.sources.SsDCo4EXa.element", "target": "root.cdn_data.sources.SsDCo4EXa.element.priority", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.228558140213962481700047734681", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1359.517578125, "y": 5224.378906250004, "anchor_index": 1}, "end_point": {"x": 1509.017578125, "y": 5298.878906250004, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.cdn_data.sources.SsDCo4EXa.element", "target": "root.cdn_data.sources.SsDCo4EXa.element.origin_addr", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.78177684170725461700047734681", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1359.517578125, "y": 5224.378906250004, "anchor_index": 1}, "end_point": {"x": 1509.017578125, "y": 5398.878906250004, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.cdn_data.sources.SsDCo4EXa.element", "target": "root.cdn_data.sources.SsDCo4EXa.element.origin_type", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.48742420152100751700047734681", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1359.517578125, "y": 5224.378906250004, "anchor_index": 1}, "end_point": {"x": 1509.017578125, "y": 5498.878906250004, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.cdn_data.sources.SsDCo4EXa.element", "target": "root.cdn_data.sources.SsDCo4EXa.element.http_port", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.98387864941185651700047734681", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1359.517578125, "y": 5224.378906250004, "anchor_index": 1}, "end_point": {"x": 1509.017578125, "y": 5598.878906250004, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.cdn_data.sources.SsDCo4EXa.element", "target": "root.cdn_data.sources.SsDCo4EXa.element.https_port", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.491411206701640071700047734681", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1359.517578125, "y": 5224.378906250004, "anchor_index": 1}, "end_point": {"x": 1509.017578125, "y": 5698.878906250004, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.cdn_data.sources.SsDCo4EXa.element", "target": "root.cdn_data.sources.SsDCo4EXa.element.host_name", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.105460767777274091700047734681", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1359.517578125, "y": 5224.378906250004, "anchor_index": 1}, "end_point": {"x": 1509.017578125, "y": 5798.878906250004, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.cdn_data.sources.SsDCo4EXa", "target": "root.cdn_data.sources.SsDCo4EXa.SSQd9kHvm", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.90714356108555491700047856305", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1210.017578125, "y": 5098.878906250004, "anchor_index": 1}, "end_point": {"x": 1737.017578125, "y": 5078.87890625, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.cdn_data.sources.SsDCo4EXa.SSQd9kHvm", "target": "2gbP1IxYR", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.93997608770317181700047863875", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1838.017578125, "y": 5078.87890625, "anchor_index": 1}, "end_point": {"x": 1937.017578125, "y": 5078.87890625, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.cdn_data.sources.SsDCo4EXa.element.origin_type", "target": "root.cdn_data.sources.SsDCo4EXa.element.origin_type.TODogHyi3", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.57633630499451561700047955520", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1610.017578125, "y": 5498.878906250004, "anchor_index": 1}, "end_point": {"x": 1970.53515625, "y": 5494.7578125000055, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.cdn_data.sources.SsDCo4EXa.element.origin_type.TODogHyi3", "target": "root.cdn_data.sources.SsDCo4EXa.element.origin_type.TODogHyi3.7wUaxqkEk", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.068364485282550461700048036347", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 2071.53515625, "y": 5494.7578125000055, "anchor_index": 1}, "end_point": {"x": 2419.53515625, "y": 5488.7578125000055, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.cdn_data.sources.SsDCo4EXa.element.origin_type.TODogHyi3.7wUaxqkEk", "target": "4u5Dy0Buk", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.58715308099051661700048044337", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 2520.53515625, "y": 5488.7578125000055, "anchor_index": 1}, "end_point": {"x": 2619.53515625, "y": 5488.7578125000055, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.cdn_data.sources.SsDCo4EXa.element.origin_addr", "target": "root.cdn_data.sources.SsDCo4EXa.element.origin_addr.D9k9nJEJe", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.291900127332028971700048058682", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1610.017578125, "y": 5398.878906250004, "anchor_index": 1}, "end_point": {"x": 2109.53515625, "y": 5389.7578125000055, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.cdn_data.sources.SsDCo4EXa.element.origin_addr.D9k9nJEJe", "target": "GvEz5t5Mk", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.89367992429947951700048064223", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 2210.53515625, "y": 5389.7578125000055, "anchor_index": 1}, "end_point": {"x": 2309.53515625, "y": 5389.7578125000055, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.cdn_data.sources.SsDCo4EXa.element.https_port", "target": "root.cdn_data.sources.SsDCo4EXa.element.https_port.1zA0XhOnG", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.70133053024132691700048066794", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1610.017578125, "y": 5698.878906250004, "anchor_index": 1}, "end_point": {"x": 1976.53515625, "y": 5690.7578125000055, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.cdn_data.sources.SsDCo4EXa.element.https_port.1zA0XhOnG", "target": "HDvHJzFLB", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.0614726379753511851700048075136", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 2077.53515625, "y": 5690.7578125000055, "anchor_index": 1}, "end_point": {"x": 2176.53515625, "y": 5690.7578125000055, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.properties", "target": "root.properties.KVX_ksg-1", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.37847220424180431700048140688", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 400, "anchor_index": 1}, "end_point": {"x": 817.0175781250002, "y": 398.87890625000364, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.properties.KVX_ksg-1", "target": "root.properties.KVX_ksg-1.Cn5UDCy9f", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.68984293575265961700048157635", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 918.0175781250002, "y": 398.87890625000364, "anchor_index": 1}, "end_point": {"x": 1189.0175781250002, "y": 398.87890625000364, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.properties.KVX_ksg-1.Cn5UDCy9f", "target": "Ti1M38YGW", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.54629872165079531700048166184", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1290.0175781250002, "y": 398.87890625000364, "anchor_index": 1}, "end_point": {"x": 1389.0175781250002, "y": 398.87890625000364, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.detail_data.server.lvOAeUMOZ.element.front_protocol", "target": "root.detail_data.server.lvOAeUMOZ.element.front_protocol.wW5KIYS8m", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.407938963862806151703586220597", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1528.017578125, "y": 3498.87890625, "anchor_index": 1}, "end_point": {"x": 1986.80859375, "y": 3491.37890625, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.detail_data.server.lvOAeUMOZ.element.front_protocol.wW5KIYS8m", "target": "03-EQuq4C", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.33933533933054741703586227946", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 2087.80859375, "y": 3491.37890625, "anchor_index": 1}, "end_point": {"x": 2186.80859375, "y": 3491.37890625, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}]}, "rules": [{"name": "add", "setting": {"field": "sdsdsdsd", "values": ["${properties}", "${cdn_data.properties}"], "multi_flag": true, "is_output_root": null}, "sub_rules": [], "_id": "root.IZAV59ghf"}, {"name": "enter", "setting": {"values": "asset_base.properties", "field": "${sdsdsdsd}"}, "sub_rules": [], "_id": "root.IZAV59ghf.4AM9Uoq0B"}, {"name": "add", "setting": {"field": "sdsd", "values": ["华为云"], "multi_flag": null, "is_output_root": null}, "sub_rules": [], "_id": "root.jE9kibry6"}, {"name": "enter", "setting": {"values": "cloud.vendor", "field": "${sdsd}"}, "sub_rules": [], "_id": "root.jE9kibry6.XsBmBAAot"}, {"name": "enter", "setting": {"values": "base.description", "field": "${description}"}, "sub_rules": [], "_id": "root.description.sqIFjjz7s"}, {"name": "translate", "setting": {"values": [{"name": "WAF", "value": "华为云WAF"}], "default": null, "field": "properties"}, "sub_rules": [], "_id": "root.properties.KVX_ksg-1"}, {"name": "enter", "setting": {"values": "network_mapping.class", "field": "${properties}"}, "sub_rules": [], "_id": "root.properties.KVX_ksg-1.Cn5UDCy9f"}, {"name": "enter", "setting": {"values": "cloud.region.display_value", "field": "${region}"}, "sub_rules": [], "_id": "root.region.TGgUfNNOp"}, {"name": "enter", "setting": {"values": "network_mapping.domain", "field": "${hostname}"}, "sub_rules": [], "_id": "root.hostname.mD-ZUnga9"}, {"name": "enter", "setting": {"values": "network_mapping.cname", "field": "${access_code}"}, "sub_rules": [], "_id": "root.access_code.fVmFoSTCH"}, {"name": "enter", "setting": {"values": "network_mapping.sys_name", "field": "${web_tag}"}, "sub_rules": [], "_id": "root.web_tag.xVrjxdExO"}, {"name": "for", "setting": {"field": "detail_data.server"}, "sub_rules": [{"id": "root.detail_data.server.lvOAeUMOZ.element.ipv4.YIimgNuKt", "name": "enter", "setting": {"values": "ip", "field": "${ipv4}"}, "sub_rules": []}, {"id": "root.detail_data.server.lvOAeUMOZ.element.ipv6.z5QFDE5BJ", "name": "enter", "setting": {"values": "ip_v6", "field": "${ipv6}"}, "sub_rules": []}, {"id": "root.detail_data.server.lvOAeUMOZ.element.port.rnReNcnDT", "name": "enter", "setting": {"values": "port", "field": "${port}"}, "sub_rules": []}, {"id": "root.detail_data.server.lvOAeUMOZ.element.front_protocol.wW5KIYS8m", "name": "enter", "setting": {"values": "protocol", "field": "${front_protocol}"}, "sub_rules": []}], "_id": "root.detail_data.server.lvOAeUMOZ"}, {"name": "enter", "setting": {"values": "network_mapping.destinations", "field": "${detail_data.server}"}, "sub_rules": [], "_id": "root.detail_data.server.lvOAeUMOZ._LJuvuf56"}, {"name": "division", "setting": {"values": [1000], "field": "${detail_data.timestamp}"}, "sub_rules": [], "_id": "root.detail_data.timestamp.uBTANS_eL"}, {"name": "timestamp_to_datetime", "setting": {"field": "${detail_data.timestamp}"}, "sub_rules": [], "_id": "root.detail_data.timestamp.uBTANS_eL.wVTXqtXOy"}, {"name": "enter", "setting": {"values": "base.first_seen", "field": "${detail_data.timestamp}"}, "sub_rules": [], "_id": "root.detail_data.timestamp.uBTANS_eL.wVTXqtXOy.PwhZNTKCj"}, {"name": "for", "setting": {"field": "cdn_data.sources"}, "sub_rules": [{"id": "root.cdn_data.sources.SsDCo4EXa.element.origin_addr.D9k9nJEJe", "name": "enter", "setting": {"values": "addr", "field": "${origin_addr}"}, "sub_rules": []}, {"id": "root.cdn_data.sources.SsDCo4EXa.element.origin_type.TODogHyi3", "name": "translate", "setting": {"values": [{"name": "ipaddr", "value": 1}, {"name": "domain", "value": 2}, {"name": "obs_bucket", "value": 3}, {"name": "third_bucket", "value": 4}], "default": null, "field": "origin_type"}, "sub_rules": []}, {"id": "root.cdn_data.sources.SsDCo4EXa.element.origin_type.TODogHyi3.7wUaxqkEk", "name": "enter", "setting": {"values": "type", "field": "${origin_type}"}, "sub_rules": []}, {"id": "root.cdn_data.sources.SsDCo4EXa.element.https_port.1zA0XhOnG", "name": "enter", "setting": {"values": "port", "field": "${https_port}"}, "sub_rules": []}], "_id": "root.cdn_data.sources.SsDCo4EXa"}, {"name": "enter", "setting": {"values": "network_mapping.backend_sources", "field": "${cdn_data.sources}"}, "sub_rules": [], "_id": "root.cdn_data.sources.SsDCo4EXa.SSQd9kHvm"}, {"name": "enter", "setting": {"values": "network_mapping.domain", "field": "${cdn_data.domain_name}"}, "sub_rules": [], "_id": "root.cdn_data.domain_name.YtP6PzETC"}, {"name": "enter", "setting": {"values": "network_mapping.cname", "field": "${cdn_data.cname}"}, "sub_rules": [], "_id": "root.cdn_data.cname.dKL5fKCq2"}, {"name": "translate", "setting": {"values": [{"name": "CDN", "value": "华为云CDN"}], "default": null, "field": "cdn_data.properties"}, "sub_rules": [], "_id": "root.cdn_data.properties.0kRdQbT-6"}, {"name": "enter", "setting": {"values": "network_mapping.class", "field": "${cdn_data.properties}"}, "sub_rules": [], "_id": "root.cdn_data.properties.0kRdQbT-6.OTTEirNRU"}], "adapter_name": "huawei_cloud", "fetch_type": "network_mapping", "model_name": "network_mapping", "asset_type": "network_mapping", "internal": true}