from caasm_adapter.sdk.adapter_fetch import fetch_sdk
from huawei_cloud.manage import HuaweiCloudManager


def find_asset(connection, fetch_type, condition, page_size=20, **kwargs):
    manager = _build_manager(connection, condition=condition)
    records = manager.find_asset(fetch_type, page_size)
    result = fetch_sdk.build_asset(records, fetch_type)
    return fetch_sdk.return_success(result)


def get_auth_connection(connection, session):
    _build_manager(connection).auth()


def build_query_condition(connection, session, fetch_type):
    new_regions = connection.get("regions")
    huawei_cloud_manager = _build_manager(connection)
    regions = huawei_cloud_manager.find_region()
    if new_regions:
        regions.extend(new_regions)
    return {"regions": regions, "current_region": None, "total": {}, "osb": 1}


def _build_manager(connection, condition=None):
    return HuaweiCloudManager(connection, condition=condition)
