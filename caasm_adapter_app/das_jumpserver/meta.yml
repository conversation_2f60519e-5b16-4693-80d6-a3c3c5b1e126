name: das_jumpserver
display_name: "明御运维审计与风险控制系统"
description: "明御运维审计与风险控制系统（简称：堡垒机或跳板机）是安恒信息结合多年运维安全管理理论和实际运维经验的基础上，结合各类法令法规(如等级保护、分级保护、银监、证监、PCI、企业内控管理、SOX塞班斯、ISO27001等) 对运维管理的要求，并提供4A(认证Authentication、账号Account、授权Authorization、审计Audit)的统一安全管理方案。"
type: "堡垒机"
logo: "das_jumpserver.png"
company: "杭州安恒信息技术股份有限公"
version: "v0.1"
priority: 10
properties:
  - 堡垒机

connection:
  - name: token
    type: password
    required: true
    display_name: "token"
    description: "认证token信息，请访问安恒堡垒机WEB页面，在个人信息中API访问键中获取"
    validate_rules:
      - name: length
        error_hint: "密码长度最小不得小于6，最大不得大于36"
        setting:
          min: 6
          max: 32

  - name: address
    type: url
    required: true
    display_name: "请求地址"
    description: "请求地址"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'


fetch_setting:
  size: 500
  point: "das_jumpserver.fetch:find_asset"
  test_auth_point: "das_jumpserver.fetch:get_auth_connection"
  is_need_test_service: true
  fetch_type_mapper:
    asset:
      - host
      - department
    department:
      - department
    owner:
      - owner
      - department

  cleaner_mapper:
    owner:
      owner:
        - "das_jumpserver.cleaners.owner_cleaner:OwnerCleaner"
    department:
      department:
        - "das_jumpserver.cleaners.department_cleaner:DepartmentCleaner"
    asset:
      default:
        - "das_jumpserver.cleaners.asset_cleaner:AssetCleaner"

merge_setting:
  size: 1000
  setting:
    asset:
      asset:
        fields:
          - hostId
    owner:
      owner:
        fields:
          - userId
    department:
      department:
        fields:
          - departmentId

fabric_setting:
  choose_point_mapper:
    asset: "das_jumpserver.fabric:choose_new_record"

convert_setting:
  before_executor_mapper:
    asset:
      - add:
          - field: asset_type
            value: "主机"
    owner:
      - add:
          - field: asset_type
            value: "责任人"

    department:
      - add:
          - field: asset_type
            value: "部门"
  executor_mapper:
    asset:
      主机:
        - rename:
            - src_field: hostname
              dst_field: host_name
            - src_field: os
              dst_field: os.full
        - add:
            - field: last_seen
              value: "${modifyTime}"
            - field: first_seen
              value: "${createTime}"

        - for_add:
            - size: 1
              field: ips
              setting:
                - field: addr
                  value: ${ip}
            - size: 1
              field: departments
              setting:
                - field: full_name
                  value: ${departmentName}

    owner:
      default:
        - rename:
            - src_field: email
              dst_field: emails
            - src_field: mobile
              dst_field: phones
            - src_field: departmentName
              dst_field: departments
        - convert:
            - field: emails
              type: list
            - field: phones
              type: list
            - field: departments
              type: list

        - filter:
            - field: emails
              condition: empty_check
            - field: phones
              condition: empty_check
            - field: departments
              condition: empty_check

    department:
      default:
        - rename:
            - src_field: "childrenFullNames"
              dst_field: "children_full_names"
            - src_field: "fullName"
              dst_field: "full_name"
            - src_field: "departmentName"
              dst_field: "name"
            - src_field: "departmentId"
              dst_field: "source_id"
            - src_field: "parentFullName"
              dst_field: "parent_full_name"
        - convert:
            - field: "source_id"
              type: "string"
