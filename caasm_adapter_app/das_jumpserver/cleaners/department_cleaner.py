from caasm_adapter.fetcher.cleaners.base import FetchDepartmentCleaner


class DepartmentCleaner(FetchDepartmentCleaner):
    def clean(self):
        total_records = self.find_total_fetch_data()
        self._parse_relation(total_records)
        self.delete_fetch_data(total_records)
        self.save_fetch_data(total_records)

    @classmethod
    def _parse_relation(cls, departments):
        department_mapper = {department["departmentId"]: department for department in departments}

        for department in departments:
            department["childrenFullNames"] = []
            department["fullName"] = department.get("departmentName")

        for department in departments:
            department_id = department["departmentId"]
            cls._parse_parent_relation(department_id, departments, department_mapper, department["childrenFullNames"])

        for department in departments:
            parent_id = department["parentId"]
            parent_department = department_mapper.get(parent_id)
            if not parent_department:
                department["parentFullName"] = ""
            else:
                department["parentFullName"] = parent_department.get("fullName", "")

    @classmethod
    def _parse_parent_relation(cls, department_id, departments, department_mapper, result):
        department = department_mapper.get(department_id)
        if not department:
            return

        department_full_name = department["fullName"]

        son_ids = []
        for tmp_department in departments:
            parent_id = tmp_department["parentId"]
            tmp_department_id = tmp_department["departmentId"]

            if parent_id != department_id:
                continue

            tmp_department_full_name = department_full_name + tmp_department["departmentName"]
            tmp_department["fullName"] = tmp_department_full_name
            result.append(tmp_department_full_name)
            son_ids.append(tmp_department_id)

        for son_id in son_ids:
            cls._parse_parent_relation(son_id, departments, department_mapper, result)
