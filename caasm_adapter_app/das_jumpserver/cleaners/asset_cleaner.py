from caasm_adapter.fetcher.cleaners.base import FetchAssetCleaner
from das_jumpserver.cleaners.department_cleaner import DepartmentCleaner


class AssetCleaner(FetchAssetCleaner, DepartmentCleaner):
    @property
    def data_types(self):
        return ["host", "department"]

    def clean(self):
        total_records = self.find_total_fetch_data()
        departments, hosts = [], []

        for info in total_records:
            fetch_type = info.get("fetch_type")
            if fetch_type == "host":
                hosts.append(info)
            elif fetch_type == "department":
                departments.append(info)

        self._parse_relation(departments)
        department_mapper = {i["departmentId"]: i.get("fullName", "") for i in departments}

        for host in hosts:
            department_id = host["departmentId"]
            department_full_name = department_mapper.get(department_id, "")
            host["departmentName"] = department_full_name

        self.delete_fetch_data(total_records)
        self.save_fetch_data(hosts)
