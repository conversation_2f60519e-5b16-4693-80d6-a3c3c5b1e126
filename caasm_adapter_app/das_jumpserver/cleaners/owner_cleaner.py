from caasm_adapter.fetcher.cleaners.base import FetchOwnerCleaner
from das_jumpserver.cleaners.department_cleaner import DepartmentCleaner


class OwnerCleaner(FetchOwnerCleaner, DepartmentCleaner):
    @property
    def data_types(self):
        return ["owner", "department"]

    def clean(self):
        total_records = self.find_total_fetch_data()
        departments, owners = [], []

        for info in total_records:
            fetch_type = info.get("fetch_type")
            if fetch_type == "owner":
                owners.append(info)
            elif fetch_type == "department":
                departments.append(info)

        self._parse_relation(departments)
        department_mapper = {i["departmentId"]: i.get("fullName", "") for i in departments}

        for owner in owners:
            department_id = owner["departmentId"]
            department_full_name = department_mapper.get(department_id, "")
            owner["departmentName"] = department_full_name

        self.delete_fetch_data(total_records)
        self.save_fetch_data(owners)
