from caasm_adapter.sdk.adapter_fetch import fetch_sdk
from das_jumpserver.manage import DasJumpServerManager


def find_asset(connection, fetch_type, page_index=0, session=None, **kwargs):
    result = _build_manager(connection, session).find(fetch_type, page_index)
    fetch_sdk.build_asset(result, fetch_type)
    return fetch_sdk.return_success(result)


def get_auth_connection(connection, session=None):
    _build_manager(connection, session).test_auth_connection()


def _build_manager(connection, session):
    return DasJumpServerManager(connection, session)
