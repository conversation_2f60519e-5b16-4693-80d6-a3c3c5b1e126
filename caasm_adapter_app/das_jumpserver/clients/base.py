import datetime
import logging

from caasm_adapter.util.client import FetchJsonResultClient
from caasm_tool.constants import GMT_FORMAT

log = logging.getLogger()


class DasJumpServerBaseClient(FetchJsonResultClient):
    SUCCESS_CODE = 200
    METHOD = "get"

    @property
    def access_token(self):
        return self.connection.get("token")

    @property
    def gmt_date(self):
        return datetime.datetime.utcnow().strftime(GMT_FORMAT)

    def build_request_header(self, *args, **kwargs):
        return {"Content-Type": "application/json", "AccessToken": self.access_token, "Date": self.gmt_date}

    def parse_response(self, response, *args, **kwargs):
        if response.status_code != self.SUCCESS_CODE:
            return self.error_handle(None, *args, **kwargs)
        return super(DasJumpServerBaseClient, self).parse_response(response, *args, **kwargs)

    @property
    def suc_flag(self):
        return ""

    @property
    def data_key_name(self):
        return ""

    @property
    def flag_key_name(self):
        return ""

    def check_biz_result(self, result):
        return True


class DasJumpServerBasePageClient(DasJumpServerBaseClient):
    def build_request_url(self, id):
        url = super(DasJumpServerBasePageClient, self).build_request_url()
        return url.format(id)

    def clean_result(self, result):
        return result or []


class DasJumpServerBaseDetailClient(DasJumpServerBaseClient):
    METHOD = "get"

    def build_request_url(self, id):
        url = super(DasJumpServerBaseDetailClient, self).build_request_url()
        return url.format(id)

    def clean_result(self, result):
        return result or {}
