name: "ns_focus"
display_name: "绿盟互联网资产核查"
description: "互联网暴露面资产是指，暴露在互联网上、具备公网地址的，面向互联网提供WEB、FTP、EMAIL、数据库等互联网服务的服务器和设备，其基本属性包括：IP地址、端口、服务名称及版本、操作系统类型及版本、应用框架类型及版本、业务系统归属、责任人等，其中“IP+端口+服务”的三元组为资产的唯一标识符。互联网资产核查服务主要是指绿盟科技依靠系统平台和人工服务的方式，为企业提供互联网资产的发现、识别、监测、稽核等服务，帮助企业发现和梳理互联网资产，同时监控互联网资产的变化情况，并以直观的报表方式进行呈现，帮助企业有序、高效的管理互联网资产，为企业的正常发展和稳定运行提供有效的安全保障。"
type: "互联网测绘"
company: "绿盟"
logo: "ns_focus.png"
version: "v0.1"
priority: 1
properties:
  - "互联网测绘"

connection:
  - name: username
    type: string
    required: true
    display_name: "用户名"
    description: "用户名称"
    validate_rules:
      - name: length
        error_hint: "用户名长度必须大于等于2且小于等于100"
        setting:
          min: 2
          max: 100

  - name: password
    type: password
    required: true
    display_name: "密码"
    description: "密码信息"
    validate_rules:
      - name: length
        error_hint: "密码长度必须大于等于6且小于等于100"
        setting:
          min: 6
          max: 100

  - name: address
    type: url
    required: true
    display_name: "地址"
    description: "地址信息"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'


fetch_setting:
  type: disposable
  point: "ns_focus.fetch:find_asset"
  size: 1000
  fetch_type_mapper:
    asset:
      - asset

fabric_setting:
  choose_point_mapper:
    asset: "ns_focus.fabric:choose_new_record"

merge_setting:
  size: 1000
  setting: { }

convert_setting:
  size: 1000
  before_executor_mapper: { }
  executor_mapper: { }