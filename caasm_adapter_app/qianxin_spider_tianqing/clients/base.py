from caasm_adapter.util.client import FetchJsonResultClient


class QAXSpiderBaseClient(FetchJsonResultClient):
    METHOD = "get"

    def __init__(self, connection, session=None, cookies=None):
        super(QAXSpiderBaseClient, self).__init__(connection, session)
        self._cookies = cookies

    def build_request_cookies(self, *args, **kwargs):
        return self._cookies

    @property
    def flag_key_name(self):
        return "result"

    @property
    def suc_flag(self):
        return 0

    @property
    def data_key_name(self):
        return "data"


class QAXSpiderPageBaseClient(QAXSpiderBaseClient):
    @property
    def data_key_name(self):
        return "data.list"
