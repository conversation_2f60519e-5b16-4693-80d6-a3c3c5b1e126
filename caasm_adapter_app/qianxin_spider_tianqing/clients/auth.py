import hashlib

from requests.utils import dict_from_cookiejar

from caasm_adapter.util.exception import AdapterFetchAuthFailedException
from qianxin_spider_tianqing.clients.base import QAXSpiderBaseClient


class QAXSpiderAuthClient(QAXSpiderBaseClient):
    URL = "/login/login"
    METHOD = "post"

    def build_request_header(self, *args, **kwargs):
        return {"Content-Type": "application/x-www-form-urlencoded; charset=UTF-8"}

    def build_request_data(self, *args, **kwargs):
        enc_password = hashlib.md5(self.password.encode()).hexdigest()
        res = f"username={self.username}&userpass={enc_password}"
        return res

    def parse_response(self, response, *args, **kwargs):
        parse_result = super(QAXSpiderAuthClient, self).parse_response(response)
        parse_result["data"] = dict_from_cookiejar(response.cookies)
        return parse_result

    def parse_error_handle(self, result, *args, **kwargs):
        raise AdapterFetchAuthFailedException

    @property
    def username(self):
        return self.connection.get("username")

    @property
    def password(self):
        return self.connection.get("password")
