import logging

from qianxin_spider_tianqing.clients.account import QAXSpiderAccountClient
from qianxin_spider_tianqing.clients.asset import QAXSpiderAssetClient, QAXSpiderAssetDetailClient
from qianxin_spider_tianqing.clients.auth import QAXSpiderAuthClient
from qianxin_spider_tianqing.clients.hardware import QAX<PERSON>piderHardwareClient
from qianxin_spider_tianqing.clients.network import QAXSpiderNetworkClient
from qianxin_spider_tianqing.clients.os import QAXSpiderOSClient
from qianxin_spider_tianqing.clients.port import QAXSpiderPortClient
from qianxin_spider_tianqing.clients.process import QAXSpiderProcessClient
from qianxin_spider_tianqing.clients.service import QAXSpiderServiceClient
from qianxin_spider_tianqing.clients.software import QAXSoftwareClient
from qianxin_spider_tianqing.clients.vul import QAXSpiderVulClient

log = logging.getLogger()


class ClientType(object):
    ASSET = "terminal"
    ASSET_DETAIL = "asset"
    PORT = "port"
    ACCOUNT = "account"
    SERVICE = "service"
    OS = "os"
    NETWORK = "_network"
    PROCESS = "process"
    SOFTWARE = "software"
    VUL = "vul"
    HARDWARE = "hardware"
    AUTH = "auth"


class QAXSpiderManager(object):
    _CLASS_DEFINE_MAPPER = {
        ClientType.ASSET: QAXSpiderAssetClient,
        ClientType.ASSET_DETAIL: QAXSpiderAssetDetailClient,
        ClientType.PORT: QAXSpiderPortClient,
        ClientType.ACCOUNT: QAXSpiderAccountClient,
        ClientType.SERVICE: QAXSpiderServiceClient,
        ClientType.VUL: QAXSpiderVulClient,
        ClientType.SOFTWARE: QAXSoftwareClient,
        ClientType.NETWORK: QAXSpiderNetworkClient,
        ClientType.OS: QAXSpiderOSClient,
        ClientType.PROCESS: QAXSpiderProcessClient,
        ClientType.HARDWARE: QAXSpiderHardwareClient,
        ClientType.AUTH: QAXSpiderAuthClient,
    }
    _API_MAPPER = {
        ClientType.ASSET: (
            "mid",
            [
                (ClientType.ASSET_DETAIL, False),
                (ClientType.HARDWARE, False),
                (ClientType.OS, False),
                (ClientType.ACCOUNT, False),
                (ClientType.PORT, False),
                (ClientType.NETWORK, False),
                (ClientType.PROCESS, False),  # 进程信息
                (ClientType.SERVICE, False),  # 服务信息
                (ClientType.SOFTWARE, True),  # 软件信息,
                (ClientType.VUL, True),  # 补丁信息
            ],
        )
    }

    def __init__(self, connection, condition=None, session=None):
        self._connection = connection
        self._condition = condition or {}
        self._session = session
        self._instance_storage = {}

    def auth(self):
        return self._call(ClientType.AUTH)

    def find(self, fetch_type, page_index, page_size):
        result = []

        _api_define = self._API_MAPPER.get(fetch_type)
        if not _api_define:
            log.warning(f"Not support api({fetch_type}) handle")
            return result

        self.auth()

        key_name, api_clients = _api_define

        records = self._call(fetch_type, offset=page_index * page_size, limit=page_size)

        for record in records:
            key_value = record[key_name]
            record[key_name] = key_value
            self._padding_record(key_name, key_value, api_clients, record)
        return records

    def _padding_record(self, key_name, key_value, api_clients, result):
        for api_name, is_batch in api_clients:
            try:
                if is_batch:
                    data = self._find_common(api_name, key_name, key_value)
                else:
                    data = self._get_common(api_name, key_name, key_value)
            except Exception as e:
                log.warning(f"Api {api_name} handle  error {e}")
            else:
                result[api_name] = data
        return result

    def _get_common(self, api_type, key_name, key_value):
        return self._call(api_type, **{key_name: key_value}) or {}

    def _find_common(self, api_type, key_name, key_value=None, offset=0, limit=100):
        result = []
        limit = limit or 100

        while True:
            params = {"offset": offset, "limit": limit}
            if key_value:
                params[key_name] = key_value
            data = self._call(api_type, **params)

            if not data:
                break

            result.extend(data)
            offset += limit
        return result

    def _call(self, client_type, *args, **kwargs):
        instance = self._instance_storage.get(client_type)
        if not instance:
            instance = self._CLASS_DEFINE_MAPPER[client_type](self._connection, self._session, self._cookies)
            self._instance_storage[client_type] = instance

        return instance.handle(*args, **kwargs)

    @property
    def _cookies(self):
        return self._condition.get("cookies")
