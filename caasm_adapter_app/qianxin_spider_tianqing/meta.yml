name: qianxin_spider_tianqing
display_name: "奇安信天擎(管理后台数据抓取)"
description: "终端安全管理系统数据开放平台(简称 ODP)作为终端安全管理系统的对外提供应 用编程接口(简称 API)数据服务，第三方软件或者平台可通过调用 ODP 接口获取终端 安全管理系统里的终端信息"
type: "终端防护"
company: "奇安信"
logo: "logo.png"
version: "v0.1"
priority: 10
properties:
  - Agent
  - 终端防护
  - 防病毒
  - 补丁管理
  - EDR

connection:
  - name: username
    type: string
    required: true
    display_name: "用户名称"
    description: "用户信息"
    validate_rules:
      - name: length
        error_hint: "用户信息格式无效。长度最小不得小于2，最大不得大于200"
        setting:
          min: 2
          max: 100

  - name: password
    type: password
    required: true
    display_name: "用户密码"
    description: "用户密码"
    validate_rules:
      - name: length
        error_hint: "密码格式无效。长度最小不得小于6，最大不得大于200"
        setting:
          min: 6
          max: 200

  - name: address
    type: url
    required: true
    display_name: "请求地址"
    description: "请求地址"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'

fetch_setting:
  point: "qianxin_spider_tianqing.fetch:find_asset"
  is_need_test_service: true
  test_auth_point: "qianxin_spider_tianqing.fetch:get_auth_connection"
  condition_point: "qianxin_spider_tianqing.fetch:build_query_condition"
  size: 100
  worker: 5
  fetch_type_mapper:
    asset:
      - terminal
  cleaner_mapper:
    asset:
      terminal:
        - "qianxin_spider_tianqing.cleaners.default_cleaner:DefaultCleaner"

merge_setting:
  size: 100
  setting: { }

convert_setting:
  size: 100
  before_executor_mapper: { }
  executor_mapper: { }
fabric_setting:
  choose_point_mapper:
    asset: "qianxin_spider_tianqing.fabric:choose_new_record"