from caasm_adapter.sdk.adapter_fetch import fetch_sdk
from qianxin_spider_tianqing.manage import QAX<PERSON>piderManager


def find_asset(connection, fetch_type, page_index=0, page_size=1, condition=None, **kwargs):
    session = kwargs.get("session")
    records = QAXSpiderManager(connection, condition, session).find(fetch_type, page_index, page_size)
    result = fetch_sdk.build_asset(records, fetch_type)
    return fetch_sdk.return_success(result)


def get_auth_connection(connection, session):
    QAXSpiderManager(connection, session=session).auth()


def build_query_condition(connection, session, fetch_type):
    cookies = QAXSpiderManager(connection, session=session).auth()
    return {"cookies": cookies}
