from caasm_adapter.fetcher.cleaners.base import FetchBaseCleaner


class DefaultCleaner(FetchBaseCleaner):
    def clean_single(self, detail):
        realm_define = detail.get("asset", {}).get("baseinfo", {}).get("group_path")
        if not realm_define:
            return {}

        realms = realm_define.split("/")

        result = [{"name": realm_define.replace("/", "-"), "direct": True}]
        _last_name = ""
        for _realm in realms[:-1]:
            _name = _realm if not _last_name else _last_name + "-" + _realm
            _tmp_realm = {"name": _name, "direct": False}
            result.append(_tmp_realm)
            _last_name = _name

        return {"realms": result}
