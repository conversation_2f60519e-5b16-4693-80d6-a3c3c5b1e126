from caasm_adapter.sdk.adapter_fetch import fetch_sdk
from uwintech_cmdb.manage import UWTechManager


def find_asset(connection, fetch_type, page_index, page_size, session=None, condition=None, **kwargs):
    manager = UWTechManager(connection, session=session, condition=condition)
    # page_index 默认是0，但是优维是从1开始
    records = manager.find(fetch_type, page_index + 1, page_size)
    result = fetch_sdk.build_asset(records, fetch_type)

    return fetch_sdk.return_success(result)


def get_auth_connection(connection, session=None):
    UWTechManager(connection, session).find("asset", 1, 1)


def build_query_condition():
    return {}
