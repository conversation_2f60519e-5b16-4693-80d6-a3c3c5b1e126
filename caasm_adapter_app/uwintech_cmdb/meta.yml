name: uwintech_cmdb
display_name: "优维科技"
description: "优维科技核心产品EasyOps是基于DevOps理念开发的一站式DevOps及运维管理平台，是从应用的资源、变更、状态三个维度来构建全面的IT管理能力，特别是运维自动化、监控、CMDB资源管理能力"
type: "CMDB"
company: "优维科技"
version: "v0.1"
logo: "uwintech_cmdb.png"
priority: 50
properties:
  - Agent
  - CMDB


connection:
  - name: access_key
    type: string
    required: true
    display_name: "access_key"
    description: "access_key"
    validate_rules:
      - name: length
        error_hint: "access_key格式无效。长度最小不得小于2，最大不得大于100"
        setting:
          min: 2
          max: 100

  - name: secret_key
    type: password
    required: true
    display_name: "secret_key"
    description: "secret_key"
    validate_rules:
      - name: length
        error_hint: "secret_key格式无效。长度最小不得小于6，最大不得大于200"
        setting:
          min: 6
          max: 200

  - name: address
    type: url
    required: true
    display_name: "请求地址"
    description: "请求地址"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'


fetch_setting:
  size: 200
  point: "uwintech_cmdb.fetch:find_asset"
  is_need_test_service: true
  test_auth_point: "uwintech_cmdb.fetch:get_auth_connection"
  fetch_type_mapper:
    asset:
      - computer
      - internet_ip
    business:
      - business
    owner:
      - owner
    department:
      - department
    network:
      - network_mapping

  cleaner_mapper:
    asset:
      internet_ip:
        - "uwintech_cmdb.cleaners.public_ip:PublicIPCleaner"
      computer:
        - "uwintech_cmdb.cleaners.asset:AssetCleaner"
    department:
      department:
        - "uwintech_cmdb.cleaners.department:DepartmentCleaner"
    business:
      business:
        - "uwintech_cmdb.cleaners.business:BusinessCleaner"

merge_setting:
  size: 200
  setting: { }

convert_setting:
  size: 200
  before_executor_mapper: { }
  executor_mapper: { }
fabric_setting:
  choose_point_mapper:
    asset: "uwintech_cmdb.fabric:choose_new_record"