import hashlib
import hmac
import json
import logging
import time
from urllib.parse import urlencode

from caasm_adapter.util.client import FetchJsonResultClient

log = logging.getLogger()


class UWTechBaseClient(FetchJsonResultClient):
    _success_flag = 0
    METHOD = "post"

    def __init__(self, connection, session=None):
        super(UWTechBaseClient, self).__init__(connection, session=session)
        self._timestamp = int(time.time())

    def build_request_header(self, *args, **kwargs):
        return {"host": "openapi.easyops-only.com", "Content-Type": "application/json"}

    def build_request_url(self, *args, **kwargs):
        data = self.build_data(*args, **kwargs)
        signature = self._compute_signature(data)
        params = {"accesskey": self.access_key, "signature": signature, "expires": self._timestamp}
        url_suffix = urlencode(params)
        url = super(UWTechBaseClient, self).build_request_url(*args, **kwargs)
        return url + f"?{url_suffix}"

    @property
    def flag_key_name(self):
        return "code"

    @property
    def suc_flag(self):
        return self._success_flag

    @property
    def data_key_name(self):
        return "data.list"

    def build_data(self, *args, **kwargs):
        raise NotImplementedError

    def _compute_signature(self, data):
        # 签名准备
        method = self.METHOD.upper()
        params = ""
        m = hashlib.md5()
        m.update(json.dumps(data).encode("utf-8"))
        content = m.hexdigest()

        str_sign = "\n".join(
            [
                method,
                self.URL,
                params,
                "application/json",
                content,
                str(self._timestamp),
                self.access_key,
            ]
        )
        # 生成签名
        return hmac.new(self.secret_key.encode(), str_sign.encode(), hashlib.sha1).hexdigest()

    @property
    def access_key(self):
        return self._connection.get("access_key")

    @property
    def secret_key(self):
        return self._connection.get("secret_key")

    def build_request_json(self, *args, **kwargs):
        return self.build_data(*args, **kwargs)
