from uwintech_cmdb.clients.base import UWTechBaseClient


class UWTechPublicIpRelationClient(UWTechBaseClient):
    URL = "/cmdb_resource/object/INTERNET_SERVER_IP/instance/_search"
    FILTER_IP_FLAGS = ["%20", "%80", "%50", "%65030", "%65031", "%51"]

    def build_data(self, page_index, page_size):
        return {
            "page": page_index,
            "page_size": page_size,
            "fields": {
                "nodeName": 1,
                "nodeIp": 1,
                "internetIp": 1,
                "internetPort": 1,
                "intraPort": 1,
                "intraIp": 1,
            },
            "query": {"$and": [{"$or": [{"vsStatus": {"$eq": "normal"}}]}]},
        }

    def clean_result(self, result):
        for detail in result:
            self.clean_detail(detail)
        return result

    @classmethod
    def clean_detail(cls, detail):
        internal_ip = detail.get("internetIp")
        intra_ip = detail.get("intraIp")
        detail["intraIp"] = cls._clean_ip(intra_ip)
        detail["internetIp"] = cls._clean_ip(internal_ip)

    @classmethod
    def _clean_ip(cls, ip):
        ip = ip or ""
        for flag in cls.FILTER_IP_FLAGS:
            ip = ip.replace(flag, "")
        return ip.strip()
