from caasm_adapter.fetcher.cleaners.base import FetchTotalBaseCleaner


class DepartmentCleaner(FetchTotalBaseCleaner):
    _enabled_status = "valid"

    def build_common(self, biz_records):
        # 1、过滤无效的部门
        valid_records = self._filter_record(biz_records)

        # 2、格式化
        departs, depart_mapper = self._format_record(valid_records)

        # 3、分等级
        result = self._grade_record(departs, depart_mapper)
        return result

    @classmethod
    def _filter_record(cls, records):
        new_records = []

        for record in records:
            if record.get("depStatus") != cls._enabled_status:
                continue
            if not record.get("treeCode"):
                continue
            if not record.get("depName"):
                continue

            new_records.append(record)
        return new_records

    @classmethod
    def _format_record(cls, records):
        depart_mapper = {}
        departs = []
        for valid_record in records:
            valid_record["childrenFullNames"] = []
            valid_record["depFullName"] = valid_record["depName"]
            source_id = valid_record["treeCode"]
            depart_mapper[source_id] = valid_record
            departs.append(valid_record)
        return departs, depart_mapper

    @classmethod
    def _grade_record(cls, records, record_mapper):
        for record in records:
            tree_code = record["treeCode"]
            parent_tree_code = tree_code[:-3]
            while parent_tree_code:
                if parent_tree_code in record_mapper:
                    parent = record_mapper[parent_tree_code]

                    if record["depName"] == record["depFullName"]:
                        record["depFullName"] = parent["depFullName"] + record["depName"]

                    record["parentFullName"] = parent["depFullName"]
                    parent["childrenFullNames"].append(record["depFullName"])
                    break
                parent_tree_code = parent_tree_code[:-3]
        return records
