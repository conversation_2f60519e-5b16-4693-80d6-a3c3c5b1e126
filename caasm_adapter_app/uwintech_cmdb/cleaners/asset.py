from caasm_adapter.fetcher.cleaners.base import FetchBase<PERSON>leaner
from caasm_adapter.sdk.adapter_fetch import fetch_sdk
from caasm_meta_data.constants import Category
from caasm_tool.util import extract


class AssetCleaner(FetchBaseCleaner):
    _department_full_name_field = "adapter.depFullName"
    _department_tree_code_field = "adapter.treeCode"
    _department_query_fields = [_department_tree_code_field, _department_full_name_field]

    def __init__(self, *args, **kwargs):
        super(AssetCleaner, self).__init__(*args, **kwargs)
        self._steps = [self._padding_ip, self._extract_department]

    def clean_single(self, detail):
        result = {}
        for step in self._steps:
            step_result = step(detail)
            if not step_result:
                continue
            result.update(step_result)
        return result

    @classmethod
    def _padding_ip(cls, record):
        eth_list = record.get("eth", [])
        ip = record.get("ip")
        if not ip:
            return

        found_flag = False
        for eth in eth_list:
            if eth.get("ip") == ip:
                found_flag = True
                break
        if not found_flag:
            eth_list.append({"ip": ip})
        return {"eth": eth_list}

    def _extract_department(self, record):
        owners = record.get("owner")
        if not owners:
            return

        departments = []

        for owner in owners:
            department = owner.pop("DEPARTMENT", None)
            if not department:
                continue
            departments.extend(department)

        department_source_ids = []
        for department in departments:
            tree_code = department.get("treeCode")
            if tree_code in department_source_ids:
                continue
            department_source_ids.append(tree_code)

        if not department_source_ids:
            return

        local_departments = fetch_sdk.find_fetch_data(
            adapter_instance_id=self._adapter_instance_id,
            fetch_type=Category.DEPARTMENT,
            condition={self._department_tree_code_field: {"$in": department_source_ids}},
            fields=self._department_query_fields,
        )

        department_mapper = {}

        for _local_department in local_departments:
            tree_code = extract(_local_department, self._department_tree_code_field)
            full_name = extract(_local_department, self._department_full_name_field)
            department_mapper[tree_code] = full_name

        new_departments = []

        for department in departments:
            tree_code = department.get("treeCode")
            full_name = department_mapper.get(tree_code)
            if full_name:
                department["depFullName"] = full_name
            new_departments.append(department)
        return {"department": new_departments, "owner": owners}
