import logging
from collections import defaultdict

from caasm_adapter.fetcher.cleaners.base import FetchTotalBaseCleaner

log = logging.getLogger()


class BusinessCleaner(FetchTotalBaseCleaner):
    _TYPE_FIELD = "__type__"
    DEFAULT_FIELDS = []

    def build_common(self, biz_records):
        clusters, apps, businesses = self._group_record(biz_records)

        asset_clusters = self.clean_cluster(clusters)
        asset_apps = self.clean_app(apps, asset_clusters)
        asset_businesses = self.clean_business(businesses, asset_apps)

        return asset_businesses

    @classmethod
    def clean_business(cls, businesses, asset_apps):
        result = []
        for business in businesses:
            business.pop("_id", None)
            apps = business.get("_businesses_APP") or []

            asset_devices = set()
            for app in apps:
                app_id = app["appId"]
                asset_devices = asset_devices.union(asset_apps[app_id])
            asset_devices = list(asset_devices)
            business["devices"] = asset_devices

            result.append(business)
        return result

    @classmethod
    def clean_app(cls, apps, asset_clusters):
        asset_apps = defaultdict(list)
        for app in apps:
            app_id = app["instanceId"]
            clusters = app.get("clusters") or []

            devices = set()
            for cluster in clusters:
                cluster_id = cluster["clusterId"]
                asset_devices = asset_clusters[cluster_id]

                if not asset_devices:
                    continue
                devices.update(asset_devices)

            asset_apps[app_id].extend(devices)
        return asset_apps

    @classmethod
    def clean_cluster(cls, clusters):
        asset_clusters = defaultdict(list)
        for cluster in clusters:
            cluster_id = cluster.get("instanceId")
            device_list = cluster.get("deviceList") or []

            if not cluster_id:
                continue

            asset_devices = [device["ip"] for device in device_list if "ip" in device]
            asset_clusters[cluster_id].extend(asset_devices)
        return asset_clusters

    @classmethod
    def _group_record(cls, records):
        clusters, apps, businesses = [], [], []
        for record in records:
            fetch_type = record[cls._TYPE_FIELD]

            if fetch_type == "cluster":
                clusters.append(record)
            elif fetch_type == "app":
                apps.append(record)
            elif fetch_type == "business":
                businesses.append(record)
        return clusters, apps, businesses
