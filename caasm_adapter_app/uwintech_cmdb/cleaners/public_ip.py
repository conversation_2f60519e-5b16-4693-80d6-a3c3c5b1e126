from caasm_adapter.fetcher.cleaners.base import FetchTotalBaseCleaner


class PublicIPCleaner(FetchTotalBaseCleaner):
    def build_common(self, biz_records):
        _tmp_storage = {}

        for relation in biz_records:
            internal_ip = relation.pop("internetIp", None)
            if not internal_ip:
                continue
            port = {
                "internetPort": relation.pop("internetPort", None),
                "intraIp": relation.pop("intraIp", ""),
                "intraPort": relation.pop("intraPort", None),
            }
            if internal_ip not in _tmp_storage:
                basic = {"internetIp": internal_ip, "ports": [port]}
                basic.update(relation)
                _tmp_storage[internal_ip] = basic
            else:
                _tmp_storage[internal_ip]["ports"].append(port)
        return list(_tmp_storage.values())
