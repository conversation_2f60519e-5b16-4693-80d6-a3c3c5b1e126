# 需要修改的是肯定要改的，具体怎么改参考https://e.gitee.com/weilansec/projects/433637/docs?directory=1188256&page=1&program_id=433637&scope=root&sub_id=7656957
# 需要确认的是可能不改，根据实际业务情况决定

name: chaitin_cloud_atlas
display_name: "长亭云图ASM平台" # 需要修改
description: "云图（Cloud Atlas）攻击面管理运营平台由长亭科技发布，将攻击面管理平台强大的可见性与来自国内最好的攻击者洞察力相结合，持续识别、分析和消除客户资产所面临的实际风险。" # 需要修改
type: "互联网测绘"    # 需要修改
company: "北京长亭未来科技有限公司"  # 需要修改
logo: "chaitin_cloud_atlas.png" # 需要修改

version: "v0.1"
priority: 100  # 需要确认
properties:
  - EASM
  - 互联网测绘

connection: # 需要修改
  - name: address
    type: url
    required: true
    default: "https://public.asm.chaitin.cn/openapi/v1/"
    display_name: "地址"
    description: "地址信息"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'

  - name: token
    type: password
    required: true
    display_name: "token"
    description: "用户的认证token"
    validate_rules:
      - name: length
        error_hint: "token格式无效。长度最小不得小于2，最大不得大于100"
        setting:
          min: 2
          max: 100

  - name: space_id
    type: integer
    required: true
    display_name: "空间ID"
    description: "用户的工作空间ID"
    validate_rules:
      - name: number
        error_hint: "空间ID格式无效。最小值不得小于1"
        setting:
          min: 1

  # 需要按照address的样例补全业务所需的剩余参数（比如username、password）

fetch_setting:
  type: disposable
  point: "chaitin_cloud_atlas.fetch:find_asset"
  is_need_test_service: true
  test_connection_point: "chaitin_cloud_atlas.fetch:connection"
  test_auth_point: "chaitin_cloud_atlas.fetch:auth"
  count_point: "chaitin_cloud_atlas.fetch:count" # 需要确认
  mode: "default" # 需要确认
  size: 100 # 需要确认
  fetch_type_mapper:
    asset:
      - domain_name
      - root_domain
      - port_server
      - internet_ip
      - cert
      - webapp
      - asset_vul
      - web_app_fingerprint
  cleaner_mapper:
    asset:
      webapp:
#        - "chaitin_cloud_atlas.cleaners.asset_vul_webapp:AssetVulCleaner"
        - "chaitin_cloud_atlas.cleaners.asset_domain_root_domain:DomainRootDomainCleaner"

fabric_setting:
  choose_point_mapper:
    asset: "chaitin_cloud_atlas.fabric:choose_new_record" # 需要确认

merge_setting:
  size: 100  # 需要确认
  setting: { } # 需要修改

convert_setting:
  size: 100 # 需要确认
  before_executor_mapper: { }
  executor_mapper: { }