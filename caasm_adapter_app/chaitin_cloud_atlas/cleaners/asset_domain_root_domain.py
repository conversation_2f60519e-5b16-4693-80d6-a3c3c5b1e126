from caasm_adapter.fetcher.cleaners.base import FetchLinkBaseCleaner


class DomainRootDomainCleaner(FetchLinkBaseCleaner):
    @property
    def indexes(self):
        return ("root_domain",)

    @property
    def main_data_type(self):
        return "root_domain"

    @property
    def link_data_type(self):
        return "domain_name"

    @property
    def main_field(self):
        return "root_domain"

    @property
    def link_field(self):
        return "domain"
