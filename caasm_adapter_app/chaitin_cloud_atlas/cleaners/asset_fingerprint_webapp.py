from caasm_adapter.fetcher.cleaners.base import FetchLinkBaseCleaner


class FingerprintWebappCleaner(FetchLinkBaseCleaner):
    @property
    def indexes(self):
        return ("url",)

    @property
    def main_data_type(self):
        return "web_app_fingerprint"

    @property
    def link_data_type(self):
        return "webapp"

    @property
    def main_field(self):
        return "netloc"

    @property
    def link_field(self):
        return "netloc"

    @property
    def dst_field(self):
        return "fingerprints"
