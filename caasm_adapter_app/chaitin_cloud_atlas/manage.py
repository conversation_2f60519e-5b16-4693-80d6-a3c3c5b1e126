# 从clients包中引用对应的实现类
from chaitin_cloud_atlas.clients.client import (
    ChaitinCloudAtlasEnterpriseFetchClient,
    ChaitinCloudAtlasInternetIpFetchClient,
    ChaitinCloudAtlasRootDomainFetchClient,
    ChaitinCloudAtlasSubDomainFetchClient,
    ChaitinCloudAtlasCertFetchClient,
    ChaitinCloudAtlasPortServerFetchClient,
    ChaitinCloudAtlasWebAppFetchClient,
    ChaitinCloudAtlasWebAppFingerPrintFetchClient,
    ChaitinCloudAtlasHighRiskVulFetchClient,
)


class ClientType(object):
    """
    所有对接的API定义，比如接入了auth、device、vul, 那么就需要定义下面三个API
    AUTH = "auth"
    DEVICE = "device"
    VUL = "vul"
    """

    AUTH = "auth"

    INTERNET_IP = "internet_ip"
    ROOT_DOMAIN = "root_domain"
    DOMAIN = "domain_name"
    CERT = "cert"
    PORT_SERVER = "port_server"
    WEBAPP = "webapp"
    WEB_APP_FINGERPRINT = "web_app_fingerprint"
    ASSET_VUL = "asset_vul"


class Manager(object):
    # key 是ClientType， val是具体的实现类
    _CLIENT_MAPPER = {
        ClientType.AUTH: ChaitinCloudAtlasRootDomainFetchClient,
        ClientType.INTERNET_IP: ChaitinCloudAtlasInternetIpFetchClient,
        ClientType.ROOT_DOMAIN: ChaitinCloudAtlasRootDomainFetchClient,
        ClientType.DOMAIN: ChaitinCloudAtlasSubDomainFetchClient,
        ClientType.CERT: ChaitinCloudAtlasCertFetchClient,
        ClientType.PORT_SERVER: ChaitinCloudAtlasPortServerFetchClient,
        ClientType.WEBAPP: ChaitinCloudAtlasWebAppFetchClient,
        ClientType.WEB_APP_FINGERPRINT: ChaitinCloudAtlasWebAppFingerPrintFetchClient,
        ClientType.ASSET_VUL: ChaitinCloudAtlasHighRiskVulFetchClient,
    }

    def __init__(self, connection, session=None):
        self._connection = connection
        self._session = session
        self._client_instance_mapper = {}

    def find_asset(self, fetch_type, page_index, page_size):
        """
        查找资产
        """
        return self._call(fetch_type, size=page_size, page=page_index)

    def auth(self):
        """
        认证方法
        """
        return self._call(ClientType.AUTH)

    def count(self, client_type, *args, **kwargs):
        if client_type == "root_domain":
            dd = 1
        if client_type not in self._client_instance_mapper:
            instance = self._CLIENT_MAPPER[client_type](self._connection, self._session)
            self._client_instance_mapper[client_type] = instance
        return self._client_instance_mapper[client_type].get_count(*args, **kwargs)

    def _call(self, client_type, *args, **kwargs):
        if client_type == "root_domain":
            dd = 1
        if client_type not in self._client_instance_mapper:
            instance = self._CLIENT_MAPPER[client_type](self._connection, self._session)
            self._client_instance_mapper[client_type] = instance
        return self._client_instance_mapper[client_type].handle(*args, **kwargs)

    # def _fetch_domains(self, page_index, page_size):
    #     domains = self._call(ClientType.ROOT_DOMAIN, size=page_size, page=page_index) or []
    #     sub_domains = self._call(ClientType.SUB_DOMAIN, size=page_size, page=page_index) or []
    #     domain_set = set()
    #     for sub_domain in sub_domains:
    #         domain = sub_domain.get("domain")
    #         domain_set.add(domain)
    #     for domain in domains:
    #         root_domain = domain.get("root_domain")
    #         if root_domain not in domain_set:
    #             new_domain = {
    #                 "id": domain.get("id"),
    #                 "status": domain.get("status"),
    #                 "domain": domain.get("root_domain"),
    #                 "subdomain": domain.get("root_domain"),
    #                 "icp_date": domain.get("icp_date"),
    #                 "icp_num": domain.get("icp_num"),
    #                 "icp_official_name": domain.get("icp_official_name"),
    #                 "created_at": domain.get("created_at"),
    #                 "updated_at": domain.get("updated_at"),
    #                 "lastseen_at": domain.get("lastseen_at"),
    #                 "sources": domain.get("sources"),
    #             }
    #             sub_domains.append(new_domain)
    #     return sub_domains
