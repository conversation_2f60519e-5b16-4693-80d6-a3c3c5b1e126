from abc import ABC

from caasm_adapter.util.client import FetchJsonResultClient
from caasm_tool.util import extract


class ChaitinCloudAtlasFetchClient(FetchJsonResultClient, ABC):
    METHOD = "GET"

    def build_request_header(self, *args, **kwargs):
        token = self.connection.get("token")
        space_id = str(self.connection.get("space_id"))
        return {
            "Content-Type": "application/json",
            "Token": token,
            "Space": space_id,
        }

    def build_request_params(self, *args, **kwargs):
        return {
            "page": kwargs.get("page"),
            "size": kwargs.get("size"),
            "status": "valid",
        }

    @property
    def suc_flag(self):
        return 200

    @property
    def flag_key_name(self):
        return "code"

    @property
    def error_key_name(self):
        return "message"

    @property
    def data_key_name(self):
        return "data.items"

    @classmethod
    def extract_count(cls, data):
        return extract(data, "data.total")


class ChaitinCloudAtlasEnterpriseFetchClient(ChaitinCloudAtlasFetchClient):
    URL = "seed/enterprise"


class ChaitinCloudAtlasRootDomainFetchClient(ChaitinCloudAtlasFetchClient):
    URL = "asset/root-domain"


class ChaitinCloudAtlasSubDomainFetchClient(ChaitinCloudAtlasFetchClient):
    URL = "asset/dns"

    def build_request_params(self, *args, **kwargs):
        params = super().build_request_params(*args, **kwargs)
        params.update(
            {
                "flat": 0,
            }
        )
        return params

    def clean_result(self, result):
        for item in result:
            records = []
            a_records = item.get("a_record", [])
            aaaa_records = item.get("aaaa_record", [])
            cname_records = item.get("cname_record", [])
            for a_record in a_records:
                records.append(
                    {
                        "type": "a",
                        "value": a_record,
                    }
                )
            for aaaa_record in aaaa_records:
                records.append({"type": "aaaa", "value": aaaa_record})
            for cname_record in cname_records:
                records.append({"type": "cname", "value": cname_record})
            item["records"] = records
        return result


class ChaitinCloudAtlasInternetIpFetchClient(ChaitinCloudAtlasFetchClient):
    URL = "asset/ip"


class ChaitinCloudAtlasCertFetchClient(ChaitinCloudAtlasFetchClient):
    URL = "asset/cert"


class ChaitinCloudAtlasPortServerFetchClient(ChaitinCloudAtlasFetchClient):
    URL = "attack/port"


class ChaitinCloudAtlasWebAppFetchClient(ChaitinCloudAtlasFetchClient):
    URL = "attack/dir"


class ChaitinCloudAtlasWebAppFingerPrintFetchClient(ChaitinCloudAtlasFetchClient):
    URL = "attack/appfinger"

    # def build_request_params(self, url, *args, **kwargs):
    #     params = super().build_request_params(*args, **kwargs) or {}
    #     params.update(
    #         {
    #             "url": url
    #         }
    #     )
    #     return params


class ChaitinCloudAtlasHighRiskVulFetchClient(ChaitinCloudAtlasFetchClient):
    URL = "risk/high-risk"

    # def build_request_params(self, query, *args, **kwargs):
    #     params = super().build_request_params(*args, **kwargs) or {}
    #     params.update(
    #         {
    #             "query": query
    #         }
    #     )
    #     return params
