import abc
import json
import logging
from alibabacloud_tea_util import models as util_models
from caasm_adapter.util.client import FetchClient
from caasm_adapter.util.exception import AdapterFetchApiNotSupportException
from alibabacloud_tea_openapi import models as open_api_models

log = logging.getLogger()


class AliClientBase(FetchClient, metaclass=abc.ABCMeta):
    TIMEOUT = 10

    def __init__(self, connection):
        super(AliClientBase, self).__init__(connection)
        self._client = self.create_client()

    def create_client(self):
        raise NotImplementedError

    def handle_common(self, *args, **kwargs):
        try:
            result = self.send(*args, **kwargs)
        except Exception as e:
            return self.error_handle(e, *args, **kwargs)
        else:
            return result

    def send(self, *args, **kwargs):
        raise NotImplementedError

    def build_request_model(self, *args, **kwargs):
        raise NotImplementedError

    @property
    def ak(self):
        return self._connection.get("access_key")

    @property
    def sk(self):
        return self._connection.get("secret_key")

    @property
    def client(self):
        return self._client

    @property
    def region(self):
        return "ram.aliyuncs.com"

    def parse_biz_result(self, result, *args, **kwargs):
        if not result:
            return result
        try:
            result = self.parse_biz_core(result)
        except Exception as e:
            log.warning(f"Clean result error({e})")
            return self.parse_error_handle(result, *args, **kwargs)
        else:
            return self.extract_data(result)

    def parse_biz_core(self, result):
        return json.loads(result)

    def extract_data(self, result):
        return result


class AliOpenAPIDetailClient(AliClientBase, metaclass=abc.ABCMeta):
    DEFAULT_ENDPOINT = ""

    def __init__(self, connection, region=""):
        super(AliOpenAPIDetailClient, self).__init__(connection)

    def handle_common(self, *args, **kwargs):
        if not self.client:
            message = f"暂未找到区域({self.region})的({self.__class__})采集入口"
            return self.error_handle(AdapterFetchApiNotSupportException(message), *args, **kwargs)
        return super(AliOpenAPIDetailClient, self).handle_common(*args, **kwargs)

    def create_client(self):
        endpoint = self.get_endpoint()
        if not endpoint:
            return None
        config = open_api_models.Config(access_key_id=self.ak, access_key_secret=self.sk, endpoint=endpoint)
        return self.create_openapi_client(config)

    def send(self, *args, **kwargs):
        runtime = self.build_request_runtime(*args, **kwargs)
        model = self.build_request_model(*args, **kwargs)
        return self.send_openapi_request(model, runtime)

    def create_openapi_client(self, config):
        raise NotImplementedError

    def build_request_runtime(self, *args, **kwargs):
        return util_models.RuntimeOptions()

    def send_openapi_request(self, model, runtime):
        raise NotImplementedError

    def extract_data(self, result):
        return self.build_biz_single(result)

    def get_endpoint(self):
        return "ram.aliyuncs.com"

    def build_biz_single(self, info):
        return info.to_map() if info else {}
