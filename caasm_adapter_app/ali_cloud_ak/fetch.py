from ali_cloud_ak.manage import AliCloudAKManager
from caasm_adapter.sdk.adapter_fetch import fetch_sdk


def build_query_condition(connection, fetch_type, **kwargs):
    return {"user": {"is_truncated": True, "marker": None}}


def find_asset(connection, fetch_type, condition, page_index=0, page_size=20, **kwargs):
    manager = _manager(connection, condition)
    records = manager.find(fetch_type, page_size=page_size, page_index=page_index + 1)
    result = fetch_sdk.build_asset(records, fetch_type)
    return fetch_sdk.return_success(result)


def _manager(connection, condition=None):
    return AliCloudAKManager(connection, condition=condition)
