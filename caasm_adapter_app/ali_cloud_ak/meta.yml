name: "ali_cloud_ak"
display_name: "阿里云AK"
description: "阿里云创立于2009年，是全球领先的云计算及人工智能科技公司，为200多个国家和地区的企业、开发者和政府机构提供服务。 阿里云致力于以在线公共服务的方式，提供安全、可靠的计算和数据处理能力，让计算和人工智能成为普惠科技。"
type: "云平台"
company: "阿里巴巴"
logo: "ali_cloud.png"
version: "v0.1"
priority: 1
properties:
  - "云平台"

connection:
  - name: access_key
    type: string
    required: true
    display_name: "access_key"
    description: "阿里云访问Access key"
    validate_rules:
      - name: length
        error_hint: "access_key格式无效。长度最小不得小于2，最大不得大于100"
        setting:
          min: 2
          max: 100

  - name: secret_key
    type: password
    required: true
    display_name: "Secret Key"
    description: "阿里云访问Secret Key"
    validate_rules:
      - name: length
        error_hint: "secret_key格式无效。长度最小不得小于6，最大不得大于200"
        setting:
          min: 6
          max: 200


  - name: cloud_account
    type: string
    required: false
    display_name: "云账户"
    description: "阿里云访问Access key"
    validate_rules:
      - name: length
        error_hint: "cloud_account格式无效。长度最小不得小于1，最大不得大于200"
        setting:
          min: 1
          max: 200


fetch_setting:
  type: disposable
  point: "ali_cloud_ak.fetch:find_asset"
  condition_point: "ali_cloud_ak.fetch:build_query_condition"
  is_need_test_service: false
#  test_auth_point: "ali_cloud_ak.fetch:get_auth_connection"
#  test_connection_point: "ali_cloud_ak.fetch:check_connection"
  size: 50
  fetch_type_mapper:
    asset:
      - access_key

merge_setting:
  size: 50
  setting: { }

convert_setting:
  size: 50
  before_executor_mapper: { }
  executor_mapper: { }