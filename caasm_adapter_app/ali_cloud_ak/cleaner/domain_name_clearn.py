from caasm_adapter.fetcher.cleaners.base import FetchBase<PERSON>leaner
from caasm_tool.util import extract


class DomainNameCleaner(FetchBaseCleaner):
    A = "A"
    NS = "NS"
    AAAA = "AAAA"
    CNAME = "CNAME"
    MX = "MX"
    SRV = "SRV"
    TXT = "TXT"
    CAA = "CAA"

    HANDLE_LIST = [A, AAAA, NS, TXT]
    HANDLE_STR = [CNAME]

    def handle_type_record(self, data=None, record_type=None, record_value=None, domain_name=None):
        if record_type in self.HANDLE_LIST:
            if not data[domain_name][record_type]:
                data[domain_name][record_type] = []
            else:
                data[domain_name][record_type].append(record_value)

        if record_type in self.HANDLE_STR:
            if not data[domain_name][record_type]:
                data[domain_name][record_type] = record_value

    def handle_domain_records(self, records):
        domain_unique = {}
        for _single_record in records:
            domain_name = extract(_single_record, "DomainName")
            if domain_name in domain_unique:
                domain_unique[domain_name].append(_single_record)
            else:
                domain_unique[domain_name] = [_single_record]

        data = {}
        for key, value in domain_unique.items():
            data[key] = {"DomainName": key}
            for _single_value in value:
                record_type = extract(_single_value, "Type")
                record_value = extract(_single_value, "Value")
                self.handle_type_record(data, record_type, record_value, key)
            data[key]["DomainRecords"] = value
        return list(data.values())

    def clean_multi(self, fetch_records):
        result = []

        for record in fetch_records:
            main_domain_name = extract(record, "DomainName")
            domain_records = extract(record, "DomainRecords")
            if domain_records:
                """
                如果解析记录存在，则需要将解析记录值提出来
                """
                result.extend(self.handle_domain_records(domain_records))
            result.append(record)
        return result
