from caasm_adapter.util.client import FetchJsonResultClient


class TopRSASBaseClient(FetchJsonResultClient):
    def __init__(self, connection, session=None, user_mark=None):
        super(TopRSASBaseClient, self).__init__(connection, session=session)
        self._user_mark = user_mark

    def build_request_params(self, *args, **kwargs):
        if self._user_mark:
            return {"userMark": self._user_mark}
        return {}

    @property
    def suc_flag(self):
        return True

    @property
    def flag_key_name(self):
        return "result"

    @property
    def data_key_name(self):
        return "data"


class TopRSASQueryBaseClient(TopRSASBaseClient):
    METHOD = "get"

    def build_request_params(self, page_index, page_size, **kwargs):
        result = super(TopRSASQueryBaseClient, self).build_request_params()
        result.update({"page": page_index, "rows": page_size})

        if kwargs:
            result.update(kwargs)
        return result

    @property
    def data_key_name(self):
        return "rows"

    def clean_result(self, result):
        return result or []
