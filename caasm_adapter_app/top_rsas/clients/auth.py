import base64

from Crypto.Cipher import AES

from caasm_adapter.util.exception import AdapterFetchAuthFailedException
from top_rsas.clients.base import TopRSASBaseClient


class AuthClient(TopRSASBaseClient):
    URL = "/home/<USER>/Register/restLogin/"
    METHOD = "get"

    def build_request_params(self, *args, **kwargs):
        return {"name": self.username, "password": self.encrypt(self.password)}

    @property
    def data_key_name(self):
        return "data.authid"

    def clean_result(self, result):
        if not result:
            raise AdapterFetchAuthFailedException
        return result

    def parse_error_handle(self, result, *args, **kwargs):
        raise AdapterFetchAuthFailedException

    def error_handle(self, err, *args, **kwargs):
        raise AdapterFetchAuthFailedException

    def encrypt(self, content):
        length = 16
        count = len(content.encode("utf-8"))

        if count % length != 0:
            add = length - (count % length)
        else:
            add = 0
        new_content = content + ("\0" * add)
        encrypt_data = AES.new(self.secret_key, AES.MODE_CBC, self.iv).encrypt(new_content.encode())
        result = str(base64.b64encode(encrypt_data), encoding="utf-8")
        return result

    @property
    def iv(self):
        return self.connection.get("iv", "1111111111111111").encode()

    @property
    def secret_key(self):
        return self.connection.get("secret_key", "1111111111111111").encode()

    @property
    def username(self):
        return self.connection.get("username", "")

    @property
    def password(self):
        return self.connection.get("password", "")
