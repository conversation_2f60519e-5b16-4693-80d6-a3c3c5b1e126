from caasm_adapter.sdk.adapter_fetch import fetch_sdk
from top_rsas.manage import TopRSASManager


def find_asset(connection, fetch_type, **kwargs):
    session = kwargs.get("session")
    condition = kwargs.get("condition")
    page_size = kwargs.get("page_size")
    condition["page_size"] = page_size

    result = TopRSASManager(connection, session=session).find(condition)
    fetch_sdk.build_asset(result, fetch_type)
    return fetch_sdk.return_success(result)


def build_query_condition(connection, session=None, fetch_type=None):
    task_list = TopRSASManager(connection, session).find_task_list()
    task_ids = [task["uuid"] for task in task_list]
    task_mapper = {task["uuid"]: task for task in task_list}

    return {
        "task_mapper": task_mapper,
        "task_ids": task_ids,
        "current_task_id": None,
        "page_index": 1,
        "handle_ids": set(),
    }


def get_auth_connection(connection, session=None):
    TopRSASManager(connection, session).auth()
