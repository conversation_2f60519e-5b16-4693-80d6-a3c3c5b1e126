name: top_rsas
display_name: "天融信脆弱性扫描与管理系统"
description: "天融信脆弱性扫描与管理系统（以下简称“漏扫系统”）以资产暴露面梳理、漏洞扫描、弱口令检测、配置核查、镜像扫描五大能力为基本抓手，并通过与防火墙、态势感知实现强强联合，帮助用户认清风险，实现事前预防，建设立体主动防护体系。"
type: "脆弱性评估"
logo: "top_rsas.png"
company: "天融信"
version: "v0.1"
priority: 10
properties:
  - 漏洞扫描

connection:
  - name: username
    type: string
    required: true
    display_name: "用户名"
    description: "用户名"
    validate_rules:
      - name: length
        error_hint: "用户名格式无效。长度最小不得小于2，最大不得大于100"
        setting:
          min: 2
          max: 100

  - name: password
    type: password
    required: true
    display_name: "密码"
    description: "密码"
    validate_rules:
      - name: length
        error_hint: "密码格式无效。长度最小不得小于6，最大不得大于200"
        setting:
          min: 6
          max: 200

  - name: address
    type: url
    required: true
    display_name: "请求地址"
    description: "请求地址"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'


fetch_setting:
  point: "top_rsas.fetch:find_asset"
  is_need_test_service: true
  condition_point: "top_rsas.fetch:build_query_condition"
  test_auth_point: "top_rsas.fetch:get_auth_connection"
  size: 500
  cleaner_mapper:
    asset:
      default:
        - "top_rsas.cleaners.default:DefaultCleaner"
  fetch_type_mapper:
    asset:
      - computer

merge_setting:
  size: 200
  setting:
    asset:
      host:
        fields:
          - ip_address

fabric_setting:
  choose_point_mapper:
    asset: "top_rsas.fabric:choose_new_record"

convert_setting:
  before_executor_mapper: { }
  executor_mapper: { }