from caasm_adapter.fetcher.cleaners.base import FetchTotalBaseCleaner


class DefaultCleaner(FetchTotalBaseCleaner):
    _SORT_FIELDS = [("adapter.task.end_time", -1)]
    _DEFAULT_FIELDS = []

    def __init__(self, *args, **kwargs):
        super(DefaultCleaner, self).__init__(*args, **kwargs)
        self._handled_ip_set = set()

    def build(self, records):
        result = []
        for record in records:
            ip = record.get("ip_address")
            if not ip:
                continue
            if ip in self._handled_ip_set:
                continue
            self._handled_ip_set.add(ip)
            result.append(record)
        return result
