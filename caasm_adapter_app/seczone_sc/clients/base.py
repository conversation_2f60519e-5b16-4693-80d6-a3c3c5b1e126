from caasm_adapter.util.client import FetchJsonResultClient


class SecZoneSCClient(FetchJsonResultClient):
    def __init__(self, token="", *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._token = token

    @property
    def suc_flag(self):
        return 1

    @property
    def flag_key_name(self):
        return "status"

    def build_request_header(self, *args, **kwargs):
        return {"Authorization": f"Bearer {self._token}"}


class SecZoneSCPageClient(SecZoneSCClient):
    METHOD = "get"

    def build_request_params(self, page_index, page_size):
        return {"pageIndex": page_index, "pageSize": page_size}

    @property
    def data_key_name(self):
        return "data.dataList"


class SecZoneSCAppOrProjectPageClient(SecZoneSCPageClient):
    def build_request_params(self, page_index, page_size, app_id="", project_id=""):
        params = super().build_request_params(page_index, page_size)
        if app_id:
            params["appUuid"] = app_id
        if project_id:
            params["projectUuid"] = project_id

        return params
