name: "seczone_sc"
display_name: "SourceCheck"
description: "开源组件安全及合规管理平台(SourceCheck)是开源网安自主研发的软件成分分析(SCA)产品，用于第三方组件的安全分析与管控，包括企业组件使用管理、组件使用合规审计、新漏洞感知预警、开源代码知识产权审计等，可实现对源码与制品的精准分析，能很好地融入企业内部的研发流程，是帮助企业实现开源风险治理的理想工具。"
type: "网络配置"
company: "开源网安"
logo: "seczone_sc.png"
version: "v0.1"
priority: 1
properties:
  - "应用"

connection:
  - name: address
    type: url
    required: true
    display_name: "地址"
    description: "地址信息"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'


  - name: username
    type: string
    required: false
    display_name: "邮箱"
    description: "邮箱"
    validate_rules:
      - name: length
        error_hint: "邮箱长度必须大于等于2且小于等于100"
        setting:
          min: 2
          max: 100

  - name: password
    type: password
    required: false
    display_name: "密钥"
    description: "密钥"
    validate_rules:
      - name: length
        error_hint: "密钥长度必须大于等于6且小于等于100"
        setting:
          min: 6
          max: 100

  - name: token
    type: string
    required: false
    display_name: "token"
    description: "token"
    validate_rules:
      - name: length
        error_hint: "token长度必须大于等于2且小于等于100"
        setting:
          min: 2
          max: 100

fetch_setting:
  type: disposable
  point: "seczone_sc.fetch:find_asset"
  condition_point: "seczone_sc.fetch:build_query_condition"
  size: 100
  is_need_test_service: true
  fetch_type_mapper:
    business:
      - business

fabric_setting:
  choose_point_mapper: { }

merge_setting:
  size: 100
  setting: { }

convert_setting:
  size: 100
  before_executor_mapper: { }
  executor_mapper: { }
