from caasm_adapter.sdk.adapter_fetch import fetch_sdk
from seczone_sc.manage import SecZoneSCManager


def find_asset(connection, fetch_type, page_index=0, page_size=30, session=None, condition=None, **kwargs):
    manager = SecZoneSCManager(connection, session, condition.get("token"))
    records = fetch_sdk.build_asset(manager.find_asset(fetch_type, page_index + 1, page_size), fetch_type)
    return fetch_sdk.return_success(records)


def build_query_condition(connection, session, fetch_type):
    token = connection.get("token")
    if not token:
        token = SecZoneSCManager(connection, session).auth()
    return {"token": token}
