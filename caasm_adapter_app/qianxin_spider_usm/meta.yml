name: "qianxin_spider_usm"
display_name: "奇安信网神统一服务器安全管理系统(页面数据抓取)"
description: "统一服务器安全管理系统是面向政企用户的虚拟化、云、数据中心环境，保护其服务器、虚拟机、云主机、容器等IT基础设施安全的产品。通过网络微隔离、多引擎协同文件防护、主机网络入侵防御等防护方式，消除工作负载中的恶意扫描、漏洞攻击、病毒感染及横向传播等安全威胁，结合安全态势感知及可视化能力，对安全风险进行统一分析和展现。"
type: "主机防护"
company: ""
logo: "logo.png"
version: "v0.1"
priority: 1
properties:
  - 主机防护
  - 防病毒
  - 终端防护

connection:
  - name: address
    type: url
    required: true
    display_name: "地址"
    description: "地址信息"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'

  - name: username
    type: string
    required: true
    display_name: "用户名"
    description: "用户名称"
    validate_rules:
      - name: length
        error_hint: "用户名长度必须大于等于2且小于等于100"
        setting:
          min: 2
          max: 100

  - name: password
    type: password
    required: true
    display_name: "密码"
    description: "密码信息"
    validate_rules:
      - name: length
        error_hint: "密码长度必须大于等于6且小于等于100"
        setting:
          min: 6
          max: 100


fetch_setting:
  type: disposable
  point: "qianxin_spider_usm.fetch:find_asset"
  is_need_test_service: true
  test_auth_point: "qianxin_spider_usm.fetch:get_auth_connection"
  size: 100
  fetch_type_mapper:
    asset:
      - computer

merge_setting:
  size: 300
  setting: { }


convert_setting:
  size: 300
fabric_setting:
  choose_point_mapper:
    asset: "qianxin_spider_usm.fabric:choose_new_record"