from caasm_adapter.sdk.adapter_fetch import fetch_sdk
from qianxin_spider_usm.manage import QAXSpiderUSMManager


def find_asset(connection, fetch_type, page_index=0, page_size=100, session=None, condition=None, **kwargs):
    manager = _build_manager(connection, session)
    records = manager.find(page_index, page_size)
    result = fetch_sdk.build_asset(records, fetch_type)
    return fetch_sdk.return_success(result)


def get_auth_connection(connection, session):
    manager = _build_manager(connection, session)
    manager.auth()
    manager.logoff()


def _build_manager(connection, session):
    return QAXSpiderUSMManager(connection, session)
