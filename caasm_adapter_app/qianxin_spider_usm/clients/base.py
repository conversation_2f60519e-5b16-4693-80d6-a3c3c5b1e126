import abc
import time

from caasm_adapter.util.client import FetchJsonResultClient


class QAXSpiderUSMBaseClient(FetchJsonResultClient, abc.ABC):
    def __init__(self, connection, session=None, token=None, cookies=None):
        super(QAXSpiderUSMBaseClient, self).__init__(connection, session)
        self._token = token
        self.timestamp = f"{int(time.time() * 1000)}000"
        self._cookies = cookies

    def build_request_header(self, *args, **kwargs):
        headers = {"timestamp": self.timestamp}
        if self._token:
            headers["X-CSRFTOKEN"] = self._token
        return headers

    def build_request_cookies(self, *args, **kwargs):
        return self._cookies

    def parse_response(self, response, *args, **kwargs):
        data = self.my_parse_response(response)
        return {"code": response.status_code, "data": data}

    def my_parse_response(self, response, *args, **kwargs):
        return super(QAXSpiderUSMBaseClient, self).parse_response(response, *args, **kwargs)

    @property
    def username(self):
        return self.connection.get("username")

    @property
    def password(self):
        return self.connection.get("password")

    @property
    def suc_flag(self):
        return 200

    @property
    def flag_key_name(self):
        return "code"

    @property
    def data_key_name(self):
        return "data"
