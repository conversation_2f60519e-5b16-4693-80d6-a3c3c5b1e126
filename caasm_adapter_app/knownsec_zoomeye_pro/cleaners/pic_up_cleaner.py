import logging
from IPy import IP
from caasm_adapter.fetcher.cleaners.base import FetchTotalBaseCleaner

log = logging.getLogger()


class PicupFetchTypeCleaner(FetchTotalBaseCleaner):
    def clean_internal(self, record, new_record):
        cidr_records = new_record.get("customized_cidrs") or []
        ip = new_record.get("ip")

        flag = False
        for cidr_record in cidr_records:
            try:
                if ip in IP(cidr_record):
                    flag = True
            except Exception as e:
                log.error(f"judge ip :{ip} in cidr :{cidr_record} fail!{e}")
        return {"fetch_type": "internet_ip", "fetch_time": record["fetch_type"]} if not flag else record
