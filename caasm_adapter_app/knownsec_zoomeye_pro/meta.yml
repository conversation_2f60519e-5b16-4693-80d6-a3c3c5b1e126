name: "knownsec_zoomeye_pro"
display_name: "zoomeye网络空间雷达系统(全网探测)"
description: "网络空间雷达系统是非常高效的私有化网络空间测绘系统，可以快速提供全面的网络空间资产探测和精准漏洞测绘，并将数据进行视觉化展现和集中输出，为政府、企事业及军工单位客户进行网络空间资产安全监管、管理及建立主动防御攻击系统提供决策依据和数据支撑。"
type: "内网资产测绘"
company: "知道创宇"
logo: "knownsec_zoomeye.png"
version: "v0.1"
priority: 1
properties:
  - "内网资产测绘"
  - "互联网测绘"

connection:
  - name: address
    type: url
    required: true
    display_name: "地址"
    description: "地址信息,请注意 这里有不同版本直接的区分，如V3版本 则 填入 http://ip/api;如V4版本，则填入 http://ip/api/v4"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'

  - name: username
    type: string
    required: true
    display_name: "用户名"
    description: "用户名称"
    validate_rules:
      - name: length
        error_hint: "用户名长度必须大于等于2且小于等于100"
        setting:
          min: 2
          max: 100

  - name: password
    type: password
    required: true
    display_name: "密码"
    description: "密码信息"
    validate_rules:
      - name: length
        error_hint: "密码长度必须大于等于6且小于等于100"
        setting:
          min: 6
          max: 100

  - name: cidr
    type: string
    required: true
    display_name: "cidr"
    description: "内网网段，以-做间隔"
    validate_rules:
      - name: reg
        error_hint: "内网网段输入有误，请保证每一个cidr的正确性 并且以;做为分割"
        setting:
          reg: '^(?:(?:25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)\.){3}(?:25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)\/(?:3[0-2]|[1-2]?\d)(?:;(?:(?:25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)\.){3}(?:25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)\/(?:3[0-2]|[1-2]?\d))*$'


fetch_setting:
  type: disposable
  point: "knownsec_zoomeye_pro.fetch:find_asset"
  is_need_test_service: true
  test_auth_point: "knownsec_zoomeye_pro.fetch:auth"
  size: 200
  fetch_type_mapper:
    asset:
      - computer
      - internet_ip
  cleaner_mapper:
    asset:
      computer:
        - "knownsec_zoomeye_pro.cleaners.pic_up_cleaner:PicupFetchTypeCleaner"

merge_setting:
  size: 1000
  setting: { }

convert_setting:
  size: 1000
  before_executor_mapper: { }
  executor_mapper: { }