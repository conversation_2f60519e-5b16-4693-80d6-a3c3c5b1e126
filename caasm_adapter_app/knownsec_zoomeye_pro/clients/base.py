import abc

from caasm_adapter.util.client import FetchJsonResultClient


class KnownSecZoomEyeProBaseClient(FetchJsonResultClient, metaclass=abc.ABCMeta):
    def __init__(self, connection, session=None, token=None):
        super(KnownSecZoomEyeProBaseClient, self).__init__(connection, session)
        self._token = token

    def build_request_header(self, *args, **kwargs):
        headers = {}
        if self._token:
            headers["b-json-web-token"] = self._token
        return headers

    @property
    def data_key_name(self):
        return "data"

    @property
    def suc_flag(self):
        return True

    @property
    def flag_key_name(self):
        return "result"
