import datetime

from caasm_adapter.sdk.adapter_fetch import fetch_sdk
from caasm_tool.constants import DATETIME_FORMAT
from caasm_tool.util import deduplicate
from ns_rsas.clients.task import NSRSASTaskListClient
from ns_rsas.manage import N<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>


def find_asset(connection, fetch_type, **kwargs):
    session = kwargs.get("session")
    condition = kwargs.get("condition", {})
    page_size = kwargs.get("page_size", 1)

    condition["page_size"] = page_size
    records = NSRSASHandler(connection, session).find(condition)
    result = fetch_sdk.build_asset(records, fetch_type)
    return fetch_sdk.return_success(result)


def build_query_condition(connection, session=None, fetch_type=None, only_query=False):
    query_day = connection.get("query_day")

    end_time = datetime.datetime.now()
    start_time = (end_time - datetime.timedelta(days=query_day)).replace(hour=0, minute=0, second=0, microsecond=0)
    start = start_time.strftime(DATETIME_FORMAT)
    end = end_time.strftime(DATETIME_FORMAT)

    task_ids = []
    task_list_instance = NSRSASTaskListClient(connection, session)

    page_size = 100
    page_index = 1

    # 获取区间范围的任务ID列表
    while True:
        tmp_task_ids = task_list_instance.handle(start_time=start, end_time=end, page=page_index, page_size=page_size)
        if not tmp_task_ids:
            break
        task_ids.extend(tmp_task_ids)

        if only_query:
            break

        page_index += 1

    task_ids = deduplicate(task_ids)

    return {"task_ids": task_ids, "current_task_id": None, "page_size": None, "page_index": 1}


def get_auth_connection(connection, session=None):
    return build_query_condition(connection, session, only_query=True)
