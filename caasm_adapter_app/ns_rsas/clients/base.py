import logging

from caasm_adapter.util.client import FetchJsonResultClient

log = logging.getLogger()


class NSRSASBaseClient(FetchJsonResultClient):
    TIMEOUT = 600

    def build_request_params(self, *args, **kwargs):
        return {"username": self.username, "password": self.password, "format": "json"}

    @property
    def suc_flag(self):
        return 0

    @property
    def flag_key_name(self):
        return "ret_code"

    @property
    def data_key_name(self):
        return "data"

    @property
    def username(self):
        return self.connection.get("username", "")

    @property
    def password(self):
        return self.connection.get("password", "")
