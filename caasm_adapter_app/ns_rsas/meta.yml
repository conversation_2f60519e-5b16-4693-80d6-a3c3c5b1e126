name: ns_rsas
display_name: "绿盟远程安全评估系统"
description: "绿盟远程安全评估系统RSAS（Remote Security Assessment System）是绿盟科技结合多年的漏洞挖掘和安全服务实践经验，自主研发的新一代漏洞管理产品，它高效、全方位的检测网络中的各类脆弱性风险，提供专业、有效的安全分析和修补建议，并贴合安全管理流程对修补效果进行审计，最大程度减小受攻击面，是您身边专业的“漏洞管理专家”。"
type: "脆弱性评估"
company: "绿盟"
logo: "ns_rsas.png"
version: "v0.1"
priority: 10
properties:
  - 漏洞扫描

connection:
  - name: username
    type: string
    required: true
    display_name: "用户名"
    description: "用户名称，该用户必须具有管理员权限，才能获取全部任务内容"
    validate_rules:
      - name: length
        error_hint: "用户名称格式无效。长度最小不得小于2，最大不得大于200"
        setting:
          min: 2
          max: 100

  - name: password
    type: password
    required: true
    display_name: "密码"
    description: "密码信息"
    validate_rules:
      - name: length
        error_hint: "密码格式无效。长度最小不得小于6，最大不得大于200"
        setting:
          min: 6
          max: 200

  - name: address
    type: url
    required: true
    display_name: "地址"
    description: "地址信息"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'

  - name: query_day
    type: integer
    required: true
    default: 90
    display_name: "最大查询天数"
    description: "最大查询天数"
    validate_rules:
      - name: number
        error_hint: "最大查询天数格式无效。最小值不得小于1，最大值不得大于180"
        setting:
          min: 1
          max: 180

fetch_setting:
  point: "ns_rsas.fetch:find_asset"
  test_auth_point: "ns_rsas.fetch:get_auth_connection"
  is_need_test_service: true
  condition_point: "ns_rsas.fetch:build_query_condition"
  size: 200
  cleaner_mapper:
    asset:
      default:
        - "ns_rsas.cleaners.default:DefaultCleaner"
  fetch_type_mapper:
    asset:
      - computer

merge_setting:
  size: 200
  setting:
    asset:
      computer:
        fields:
          - ip

convert_setting:
  size: 200
  before_executor_mapper: { }
  executor_mapper: { }

fabric_setting:
  choose_point_mapper:
    asset: "ns_rsas.fabric:choose_new_record"