import logging

log = logging.getLogger()


class AppendIxBase<PERSON><PERSON>ler(object):
    _name = ""
    _single = False
    _key = ""
    _direct_resp = False

    meta_mapping = {}

    def __init__(self):
        self._clean_ele_funcs = self._load_clean_ele_funcs()

    def convert(self, records):
        result = []
        for record in records:
            if self._direct_resp:
                value = self.clean(record)
            else:
                value = {}
                for index, element in enumerate(record):
                    if index not in self.meta_mapping:
                        log.debug(f"Not define index({index}) convert map ({self.__class__})")
                        continue
                    clean_func = self.get_clean_ele_func(index)
                    value[self.meta_mapping[index]] = clean_func(element)

            result.append(value)
        if self._single:
            result = result[0] if result else {}
        return result

    def get_clean_ele_func(self, index):
        return self._clean_ele_funcs.get(index, self.clean)

    def _load_clean_ele_funcs(self):
        result = {}
        for prop_name in dir(self):
            if not prop_name.startswith("clean_index_"):
                continue
            prop = getattr(self, prop_name, None)
            if not callable(prop):
                continue

            index = int(prop_name.replace("clean_index_", ""))
            result[index] = prop
        return result

    @classmethod
    def clean(cls, element):
        return element

    @property
    def name(self):
        return self._name

    @property
    def key(self):
        return self._key
