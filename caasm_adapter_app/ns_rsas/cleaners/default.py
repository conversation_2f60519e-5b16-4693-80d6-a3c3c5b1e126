from caasm_adapter.fetcher.cleaners.base import FetchTotalBaseCleaner


class DefaultCleaner(FetchTotalBaseCleaner):
    _DEFAULT_FIELDS = []

    def __init__(self, *args, **kwargs):
        super(DefaultCleaner, self).__init__(*args, **kwargs)
        self._handled_ip_set = set()

    def build_common(self, biz_records):
        result = []
        for record in biz_records:
            ip = record.get("ip")
            if not ip:
                continue
            if ip in self._handled_ip_set:
                continue
            self._handled_ip_set.add(ip)
            result.append(record)
        return result
