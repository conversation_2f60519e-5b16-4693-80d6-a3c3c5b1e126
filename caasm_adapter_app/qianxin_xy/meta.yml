name: "qianxin_xy"
display_name: "奇安信小鹰"
description: "奇安信全球鹰网络空间测绘系统可对暴露在整个互联网上的服务器和设备的端口、协议、应用、漏洞进行纵深探测，对资产安全状况进行建模分析和画像刻画。通过网络空间测绘技术，将地理空间、社会空间、网络空间相互映射，将虚拟的网络空间绘制成一幅动态、实时、有效的网络空间地图，实现互联网资产的可查、可定位，帮助客户解决互联资产暴露面梳理的难题。"
type: "外网资产测绘"
company: "奇安信"
logo: "qianxin_xy.png"
version: "v0.1"
priority: 1
properties:
  - "外网资产测绘"

connection:
  - name: username
    type: string
    required: true
    display_name: "用户名"
    description: "用户名称"
    validate_rules:
      - name: length
        error_hint: "用户名长度必须大于等于2且小于等于100"
        setting:
          min: 2
          max: 100

  - name: password
    type: password
    required: true
    display_name: "密码"
    description: "密码信息"
    validate_rules:
      - name: length
        error_hint: "密码长度必须大于等于6且小于等于100"
        setting:
          min: 6
          max: 100

  - name: address
    type: url
    required: true
    display_name: "地址"
    description: "地址信息"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'


fetch_setting:
  type: disposable
  point: "qianxin_xy.fetch:find_asset"
  size: 1000
  fetch_type_mapper:
    asset:
      - asset

fabric_setting:
  choose_point_mapper:
    asset: "qianxin_xy.fabric:choose_new_record"

merge_setting:
  size: 1000
  setting: { }

convert_setting:
  size: 1000
  before_executor_mapper: { }
  executor_mapper: { }