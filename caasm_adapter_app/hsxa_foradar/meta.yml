name: "hsxa_foradar"
display_name: "Foradar"
description: "产品定位为“基于攻防实战的互联网资产安全一站式自动化运维管理系统”。该产品是针对企事业单位互联网资产的暴露面梳理及攻击面管理的自动化运营系统。"
type: "互联网测绘"
company: "华顺信安"
logo: "hsxa_foradar.png"
version: "v0.1"
priority: 1
properties:
  - "互联网测绘"

connection:
  - name: address
    type: url
    required: true
    display_name: "地址"
    description: "地址信息"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'


  - name: email
    type: string
    required: true
    display_name: "邮箱"
    description: "邮箱"
    validate_rules:
      - name: length
        error_hint: "邮箱长度必须大于等于2且小于等于100"
        setting:
          min: 2
          max: 100

  - name: secret_key
    type: password
    required: true
    display_name: "密钥"
    description: "密钥"
    validate_rules:
      - name: length
        error_hint: "密钥长度必须大于等于6且小于等于100"
        setting:
          min: 6
          max: 100

fetch_setting:
  type: disposable
  point: "hsxa_foradar.fetch:find_asset"
  condition_point: "hsxa_foradar.fetch:build_query_condition"
  size: 100
  is_need_test_service: true
  fetch_type_mapper:
    asset:
      - port_server
      - domain_name

fabric_setting:
  choose_point_mapper: { }

merge_setting:
  size: 100
  setting: { }

convert_setting:
  size: 100
  before_executor_mapper: { }
  executor_mapper: { }
