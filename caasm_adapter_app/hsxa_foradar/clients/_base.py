import abc

from caasm_adapter.util.client import FetchJsonResultClient


class HSXAForadarBaseClient(FetchJsonResultClient):
    def __init__(self, token="", *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._token = token

    def build_request_header(self, *args, **kwargs):
        return {"Authorization": f"Bearer {self._token}"} if self._token else {}

    @property
    def flag_key_name(self):
        return "code"

    @property
    def suc_flag(self):
        return 0


class HSXAForadarPageClient(HSXAForadarBaseClient, metaclass=abc.ABCMeta):
    METHOD = "get"

    @property
    def data_key_name(self):
        return "data.items"

    def build_request_params(self, page_index, page_size):
        return {"page": page_index, "per_page": page_size}
