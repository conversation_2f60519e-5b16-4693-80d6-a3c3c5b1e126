name: "ucloud_osms"
display_name: "UCloud堡垒机"
description: "UCloud堡垒机为用户提供集中运维管理解决方案；运维人员可通过堡垒机远程访问云主机（UHost），实现对访问账号集中管理，并做精细的权限规划和运维审计；提升金融行业及企业的内部运维风险控制水平。堡垒机能够满足物理机房、私有云、公有云、混合云等多种场景下，企业各类IT资产的统一运维管理与操作审计，实现对运维过程的事前规划、事中控制和事后审计，此外，还扩展了自动化运维、资产拓扑发现、工作流引擎等功能及模块，帮助企业建立完善的运维管理与内控体系，为企业降本增效。"
type: "堡垒机"
company: "Ucloud优刻得"
logo: "ucloud_osms.png"
version: "v0.1"
priority: 1
properties:
  - "堡垒机"

connection:
  - name: username
    type: string
    required: true
    display_name: "用户名"
    description: "用户名称"
    validate_rules:
      - name: length
        error_hint: "用户名长度必须大于等于2且小于等于100"
        setting:
          min: 2
          max: 100

  - name: password
    type: password
    required: true
    display_name: "密码"
    description: "密码信息"
    validate_rules:
      - name: length
        error_hint: "密码长度必须大于等于6且小于等于100"
        setting:
          min: 6
          max: 100

  - name: address
    type: url
    required: true
    display_name: "地址"
    description: "地址信息"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'


fetch_setting:
  type: disposable
  point: "ucloud_osms.fetch:find_asset"
  size: 1000
  fetch_type_mapper:
    asset:
      - asset

fabric_setting:
  choose_point_mapper:
    asset: "ucloud_osms.fabric:choose_new_record"

merge_setting:
  size: 1000
  setting: { }

convert_setting:
  size: 1000
  before_executor_mapper: { }
  executor_mapper: { }