from abc import ABC

from caasm_adapter.util.client import FetchJsonResultClient


class SangforEdrFetchClient(FetchJsonResultClient, ABC):
    URL = "launch.php"
    METHOD = "post"

    def build_request_header(self, *args, **kwargs):
        return {"Content-Type": "application/x-www-form-urlencoded"}

    def check_biz_result(self, result):
        return result.get("success", False)

    @property
    def data_key_name(self):
        return "data"

    @property
    def suc_flag(self):
        raise "code"

    @property
    def flag_key_name(self):
        raise ""
