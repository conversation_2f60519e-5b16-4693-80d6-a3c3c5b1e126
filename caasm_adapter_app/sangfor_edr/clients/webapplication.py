import json


from sangfor_edr.clients.base import SangforEdrBaseClient


class SangforEdrWebapplicationClient(SangforEdrBaseClient):
    APP_NAME = "app.web.agent_management.route.agent_management_route"
    OPR = "list_agent_detail"
    KEY = "list"
    URL = "api/edrgoweb/v1/asset/agent/webapp/detail"

    def build_request_data(self, agent_id, query_id, *args, **kwargs):
        return json.dumps(
            {
                "search_name": "",
                "agent_id": agent_id,
                "page_no": 0,
                "page_limit": 50,
                "uuid": "sf-id-850",
                "tid": "0",
                "uid": "gongjimian",
                "token": self._connection.get("token"),
            }
        )

    def build_request_url(self, *args, **kwargs):
        url = super(SangforEdrWebapplicationClient, self).build_request_url(*args, **kwargs)
        return f"{url}?_method={self.METHOD.lower()}&s={self._connection.get('token')}"

    def build_request_header(self, *args, **kwargs):
        session_id = self.connection.get("session_id")
        return {"Cookie": f"sessionid={session_id}", "Content-Type": "application/json"}

    @property
    def data_key_name(self):
        return "data.list"

    @property
    def suc_flag(self):
        return "code"

    @property
    def flag_key_name(self):
        return ""

    def check_biz_result(self, result):
        code = result.get("code", False)
        return True if code == 0 else False
