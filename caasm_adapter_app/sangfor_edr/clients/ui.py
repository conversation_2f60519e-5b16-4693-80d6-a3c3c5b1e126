import logging

from requests import Response

from sangfor_edr.clients.client import SangforEdrFetchClient

log = logging.getLogger()


class SangforEdrUIClient(SangforEdrFetchClient):
    URL = "/ui/"
    METHOD = "get"

    def build_request_header(self, *args, **kwargs):
        session_id = self.connection.get("session_id")
        return {"Cookie": f"sessionid={session_id}"}

    def parse_response(self, response, *args, **kwargs):
        response: Response = response
        return dict(response.cookies)

    def check_biz_result(self, result):
        return result.get("sessionid", False)

    @property
    def data_key_name(self):
        return "sessionid"

    @property
    def suc_flag(self):
        raise "code"

    @property
    def flag_key_name(self):
        raise ""
