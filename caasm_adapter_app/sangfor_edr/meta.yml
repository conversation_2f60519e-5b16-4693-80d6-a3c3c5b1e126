name: "sangfor_edr"
display_name: "深信服EDR"
description: "深信服EDR是一款应用在终端防护场景的终端/主机防护品类产品，帮助用户构建轻量易用、威胁攻击实时保护、终端流量东西向可视可控的全方位终端安全防护体系。"
type: "主机防护"
company: "深信服"
logo: "sangfor_edr.png"
version: "v0.1"
priority: 10
properties:
  - "主机防护"
  - "HIDS"
  - "漏洞扫描"

connection:
  - name: address
    type: url
    required: true
    display_name: "地址"
    description: "地址信息"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'

  - name: username
    type: string
    required: true
    display_name: "用户名"
    description: "用户名称"
    validate_rules:
      - name: length
        error_hint: "用户名长度必须大于等于2且小于等于100"
        setting:
          min: 2
          max: 100

  - name: password
    type: password
    required: true
    display_name: "密码"
    description: "密码信息"
    validate_rules:
      - name: length
        error_hint: "密码长度必须大于等于6且小于等于100"
        setting:
          min: 6
          max: 100


fetch_setting:
  type: disposable
  point: "sangfor_edr.fetch:find_asset"
  size: 50
  test_auth_point: "sangfor_edr.fetch:get_auth_connection"
  is_need_test_service: true
  test_connection_point: "sangfor_edr.fetch:check_connection"
  fetch_type_mapper:
    asset:
      - host

fabric_setting:
  choose_point_mapper:
    asset: "sangfor_edr.fabric:choose_new_record"

merge_setting:
  size: 50
  setting: { }

convert_setting:
  size: 50
  before_executor_mapper: { }
  executor_mapper: { }