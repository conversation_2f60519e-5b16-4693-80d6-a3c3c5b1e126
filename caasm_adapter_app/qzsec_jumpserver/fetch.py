from caasm_adapter.sdk.adapter_fetch import fetch_sdk
from qzsec_jumpserver.manage import QzSecManager


def find_asset(connection, fetch_type, page_index, page_size, **kwargs):
    session = kwargs.get("session")

    records = QzSecManager(connection, session=session).find(fetch_type, page_index, page_size)
    result = fetch_sdk.build_asset(records, fetch_type)
    return fetch_sdk.return_success(result)


def get_auth_connection(connection, session):
    QzSecManager(connection, session=session).auth()
