from abc import ABCMeta

from caasm_adapter.util.client import FetchJsonResultClient


class QzSecApiClient(FetchJsonResultClient, metaclass=ABCMeta):
    def __init__(self, connection, session=None, token=None):
        super(QzSecApiClient, self).__init__(connection, session)
        self._token = token

    def build_request_header(self, *args, **kwargs):
        if not self._token:
            return {}
        return {"st-auth-token": self._token}

    def check_biz_result(self, result):
        return True

    @property
    def suc_flag(self):
        return ""

    @property
    def flag_key_name(self):
        return ""
