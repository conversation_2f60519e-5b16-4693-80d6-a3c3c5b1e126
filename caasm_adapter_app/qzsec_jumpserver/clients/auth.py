from caasm_adapter.util.exception import AdapterFetchAuthFailedException
from caasm_tool.util import extract
from qzsec_jumpserver.clients.base import QzSecApiClient


class QzSecAuthApiClient(QzSecApiClient):
    URL = "api/authenticate"
    METHOD = "post"

    def build_request_json(self, *args, **kwargs):
        return {"username": self.username, "password": self.password}

    @property
    def username(self):
        return self.connection.get("username")

    @property
    def password(self):
        return self.connection.get("password")

    @property
    def data_key_name(self):
        return "ST_AUTH_TOKEN"

    def check_biz_result(self, result):
        data = extract(result, self.data_key_name)
        return bool(data)

    def parse_error_handle(self, result, *args, **kwargs):
        raise AdapterFetchAuthFailedException

    def error_handle(self, err, *args, **kwargs):
        raise AdapterFetchAuthFailedException
