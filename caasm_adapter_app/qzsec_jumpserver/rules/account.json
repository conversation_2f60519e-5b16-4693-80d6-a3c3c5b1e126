{"canvas": {"nodes": [{"id": "root", "field": "根节点", "path": "root", "datatype": "object", "type": "asset", "level": 0, "sub_fields": [], "x": 0, "y": 0, "asset_type": "asset", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 100, "id": "root.id", "level": 1, "path": "id", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "id", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 200, "id": "root.loginName", "level": 1, "path": "loginName", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "loginName", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 300, "id": "root.userName", "level": 1, "path": "userName", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "userName", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 400, "id": "root.email", "level": 1, "path": "email", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "email", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 500, "id": "root.authType", "level": 1, "path": "authType", "sub_fields": [{"field": "id", "path": "authType.id", "type": "integer"}, {"field": "name", "path": "authType.name", "type": "string"}, {"field": "type", "path": "authType.type", "type": "integer"}, {"field": "configurations", "path": "authType.configurations", "type": "object", "sub_fields": [{"field": "rela", "path": "authType.configurations.rela", "type": "string"}, {"field": "authtype1", "path": "authType.configurations.authtype1", "type": "string"}, {"field": "authtype2", "path": "authType.configurations.authtype2", "type": "string"}]}, {"field": "enabled", "path": "authType.enabled", "type": "integer"}], "type": "asset", "datatype": "object", "asset_type": "asset", "field": "authType", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 600, "id": "root.authType.id", "level": 2, "path": "authType.id", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "id", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 700, "id": "root.authType.name", "level": 2, "path": "authType.name", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "name", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 800, "id": "root.authType.type", "level": 2, "path": "authType.type", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "type", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 900, "id": "root.authType.configurations", "level": 2, "path": "authType.configurations", "sub_fields": [{"field": "rela", "path": "authType.configurations.rela", "type": "string", "x": 600, "y": 1000}, {"field": "authtype1", "path": "authType.configurations.authtype1", "type": "string", "x": 600, "y": 1100}, {"field": "authtype2", "path": "authType.configurations.authtype2", "type": "string", "x": 600, "y": 1200}], "type": "asset", "datatype": "object", "asset_type": "asset", "field": "configurations", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 600, "y": 1000, "id": "root.authType.configurations.rela", "level": 3, "path": "authType.configurations.rela", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "rela", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 600, "y": 1100, "id": "root.authType.configurations.authtype1", "level": 3, "path": "authType.configurations.authtype1", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "authtype1", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 600, "y": 1200, "id": "root.authType.configurations.authtype2", "level": 3, "path": "authType.configurations.authtype2", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "authtype2", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 1300, "id": "root.authType.enabled", "level": 2, "path": "authType.enabled", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "enabled", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 1400, "id": "root.role", "level": 1, "path": "role", "sub_fields": [{"field": "id", "path": "role.id", "type": "integer"}, {"field": "name", "path": "role.name", "type": "string"}, {"field": "description", "path": "role.description", "type": "string"}, {"field": "icon", "path": "role.icon", "type": "string"}, {"field": "enabled", "path": "role.enabled", "type": "integer"}, {"field": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "path": "role.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "object", "sub_fields": [{"field": "enable", "path": "role.apiWhitelist.enable", "type": "integer"}]}], "type": "asset", "datatype": "object", "asset_type": "asset", "field": "role", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 1500, "id": "root.role.id", "level": 2, "path": "role.id", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "id", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 1600, "id": "root.role.name", "level": 2, "path": "role.name", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "name", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 1700, "id": "root.role.description", "level": 2, "path": "role.description", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "description", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 1800, "id": "root.role.icon", "level": 2, "path": "role.icon", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "icon", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 1900, "id": "root.role.enabled", "level": 2, "path": "role.enabled", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "enabled", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 2000, "id": "root.role.a<PERSON><PERSON><PERSON><PERSON>", "level": 2, "path": "role.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sub_fields": [{"field": "enable", "path": "role.apiWhitelist.enable", "type": "integer", "x": 600, "y": 2100}], "type": "asset", "datatype": "object", "asset_type": "asset", "field": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 600, "y": 2100, "id": "root.role.apiWhitelist.enable", "level": 3, "path": "role.apiWhitelist.enable", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "enable", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 2200, "id": "root.department", "level": 1, "path": "department", "sub_fields": [{"field": "id", "path": "department.id", "type": "integer"}, {"field": "name", "path": "department.name", "type": "string"}, {"field": "description", "path": "department.description", "type": "string"}], "type": "asset", "datatype": "object", "asset_type": "asset", "field": "department", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 2300, "id": "root.department.id", "level": 2, "path": "department.id", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "id", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 2400, "id": "root.department.name", "level": 2, "path": "department.name", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "name", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 2500, "id": "root.department.description", "level": 2, "path": "department.description", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "description", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 2600, "id": "root.authInfo", "level": 1, "path": "authInfo", "sub_fields": [{"field": "secret", "path": "authInfo.secret", "type": "string"}, {"field": "passwordType", "path": "authInfo.passwordType", "type": "string"}, {"field": "phoneNumber", "path": "authInfo.phoneNumber", "type": "string"}, {"field": "ldap_username", "path": "authInfo.ldap_username", "type": "string"}], "type": "asset", "datatype": "object", "asset_type": "asset", "field": "authInfo", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 2700, "id": "root.authInfo.secret", "level": 2, "path": "authInfo.secret", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "secret", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 2800, "id": "root.authInfo.passwordType", "level": 2, "path": "authInfo.passwordType", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "passwordType", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 2900, "id": "root.authInfo.phoneNumber", "level": 2, "path": "authInfo.phoneNumber", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "phoneNumber", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 3000, "id": "root.authInfo.ldap_username", "level": 2, "path": "authInfo.ldap_username", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "ldap_username", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 3100, "id": "root.state", "level": 1, "path": "state", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "state", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 3200, "id": "root.extra", "level": 1, "path": "extra", "sub_fields": [{"field": "hasAttribute", "path": "extra.hasAttribute", "type": "object", "sub_fields": [{"field": "pin1", "path": "extra.hasAttribute.pin1", "type": "integer"}, {"field": "pin2", "path": "extra.hasAttribute.pin2", "type": "integer"}, {"field": "authToken", "path": "extra.hasAttribute.authToken", "type": "integer"}, {"field": "password", "path": "extra.hasAttribute.password", "type": "integer"}]}], "type": "asset", "datatype": "object", "asset_type": "asset", "field": "extra", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 3300, "id": "root.extra.hasAttribute", "level": 2, "path": "extra.hasAttribute", "sub_fields": [{"field": "pin1", "path": "extra.hasAttribute.pin1", "type": "integer", "x": 600, "y": 3400}, {"field": "pin2", "path": "extra.hasAttribute.pin2", "type": "integer", "x": 600, "y": 3500}, {"field": "authToken", "path": "extra.hasAttribute.authToken", "type": "integer", "x": 600, "y": 3600}, {"field": "password", "path": "extra.hasAttribute.password", "type": "integer", "x": 600, "y": 3700}], "type": "asset", "datatype": "object", "asset_type": "asset", "field": "hasAttribute", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 600, "y": 3400, "id": "root.extra.hasAttribute.pin1", "level": 3, "path": "extra.hasAttribute.pin1", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "pin1", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 600, "y": 3500, "id": "root.extra.hasAttribute.pin2", "level": 3, "path": "extra.hasAttribute.pin2", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "pin2", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 600, "y": 3600, "id": "root.extra.hasAttribute.authToken", "level": 3, "path": "extra.hasAttribute.authToken", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "authToken", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 600, "y": 3700, "id": "root.extra.hasAttribute.password", "level": 3, "path": "extra.hasAttribute.password", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "password", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 3800, "id": "root.usergroups", "level": 1, "path": "usergroups", "sub_fields": [{"type": "object", "sub_fields": [{"field": "id", "path": "usergroups.element.id", "type": "integer"}, {"field": "name", "path": "usergroups.element.name", "type": "string"}, {"field": "type", "path": "usergroups.element.type", "type": "integer"}, {"field": "order", "path": "usergroups.element.order", "type": "integer"}, {"field": "department", "path": "usergroups.element.department", "type": "object", "sub_fields": [{"field": "id", "path": "usergroups.element.department.id", "type": "integer"}, {"field": "name", "path": "usergroups.element.department.name", "type": "string"}, {"field": "parent", "path": "usergroups.element.department.parent", "type": "object", "sub_fields": [{"field": "id", "path": "usergroups.element.department.parent.id", "type": "integer"}, {"field": "name", "path": "usergroups.element.department.parent.name", "type": "string"}, {"field": "description", "path": "usergroups.element.department.parent.description", "type": "string"}]}]}], "field": "element", "path": "usergroups.element"}], "type": "asset", "datatype": "list", "asset_type": "asset", "field": "usergroups", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 3900, "id": "root.lastLoginTime", "level": 1, "path": "lastLoginTime", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "lastLoginTime", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 4000, "id": "root.pwdChangeTime", "level": 1, "path": "pwdChangeTime", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "pwdChangeTime", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 4100, "id": "root.validFrom", "level": 1, "path": "validFrom", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "validFrom", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 4200, "id": "root.validTo", "level": 1, "path": "validTo", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "validTo", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 4300, "id": "root.pwdValidType", "level": 1, "path": "pwdValidType", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "pwdValidType", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 4400, "id": "root.joinTime", "level": 1, "path": "joinTime", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "joinTime", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 4500, "id": "root.updateTime", "level": 1, "path": "updateTime", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "updateTime", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 4600, "id": "root.joinUser", "level": 1, "path": "joinUser", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "joinUser", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 4700, "id": "root.deleted", "level": 1, "path": "deleted", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "deleted", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 4800, "id": "root.userType", "level": 1, "path": "userType", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "userType", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 4900, "id": "root.userCategory", "level": 1, "path": "userCategory", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "userCategory", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 5000, "id": "root.userSource", "level": 1, "path": "userSource", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "userSource", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 5100, "id": "root.locked", "level": 1, "path": "locked", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "locked", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 5200, "id": "root.enabled", "level": 1, "path": "enabled", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "enabled", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.loginName.-vDuwcIs-", "x": 424.**************, "y": 199.*************, "order": 1, "level": 2, "source_type": "string", "path": "root.loginName.-vDuwcIs-", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "account.username"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "PVCW847Wj", "display_name": "账户", "description": "account.username", "x": 624.*************, "y": 199.*************, "label": "账户-账户名称", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.userName.ll8-pF6Go", "x": 489.*************, "y": 312.*************, "order": 2, "level": 2, "source_type": "string", "path": "root.userName.ll8-pF6Go", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "account.nickname"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "qDK6QhODY", "display_name": "账户", "description": "account.nickname", "x": 689.*************, "y": 312.*************, "label": "账户-账户昵称", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.email.rJJvwJsE8", "x": 405.*************, "y": 402.*************, "order": 3, "level": 2, "source_type": "string", "path": "root.email.rJJvwJsE8", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "将数据转化为列表类型", "description": "用于列表类型的转换，将非列表格式的数据转换为列表类型", "field": "将数据转化为列表类型", "input": {"asset_web_type": "list", "description": "用于列表类型的转换，将非列表格式的数据转换为列表类型"}, "action_type": "list", "attrs": {"text": "将数据转化为列表类型"}}, {"type": "asset", "asset_type": "action", "id": "root.email.rJJvwJsE8.QNDLrs8Xp", "x": 567.4356661819049, "y": 405.70580367911305, "order": 4, "level": 3, "path": "root.email.rJJvwJsE8.QNDLrs8Xp", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "空值过滤器", "description": "过滤空值", "field": "空值过滤器", "input": {"asset_web_type": "filter_empty", "description": "过滤空值"}, "action_type": "filter_empty", "attrs": {"text": "空值过滤器"}}, {"type": "asset", "asset_type": "action", "id": "root.email.rJJvwJsE8.QNDLrs8Xp.L2sQCHVDu", "x": 722.*************, "y": 407.*************, "order": 5, "level": 4, "path": "root.email.rJJvwJsE8.QNDLrs8Xp.L2sQCHVDu", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "account.email"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "EI7b467sL", "display_name": "账户", "description": "account.email", "x": 922.*************, "y": 407.*************, "label": "账户-邮箱", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.role.name.uSckjm5Uq", "x": 641.*************, "y": 1597.*************, "order": 7, "level": 3, "source_type": "string", "path": "root.role.name.uSckjm5Uq", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "account.position"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "nIHFzwgNT", "display_name": "账户", "description": "account.position", "x": 841.*************, "y": 1597.*************, "label": "账户-职务", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.state.sQWljwd0w", "x": 555.*************, "y": 3096.************, "order": 8, "level": 2, "source_type": "integer", "path": "root.state.sQWljwd0w", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "翻译", "description": "用于字段值翻译，常见用于枚举字段", "field": "翻译", "input": {"asset_web_type": "translate", "description": "用于字段值翻译，常见用于枚举字段", "values": [{"name": "0", "value": "活动"}, {"name": "1", "value": "密码过期"}, {"name": "2", "value": "账户过期"}, {"name": "3", "value": "禁用"}]}, "action_type": "translate", "attrs": {"text": "翻译"}}, {"type": "asset", "asset_type": "action", "id": "root.state.sQWljwd0w.ckJxNllIa", "x": 937.*************, "y": 3094.************, "order": 9, "level": 3, "path": "root.state.sQWljwd0w.ckJxNllIa", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "account.status"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "UVUTQtQXF", "display_name": "账户", "description": "account.status", "x": 1137.*************, "y": 3094.************, "label": "账户-账号状态", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.authInfo.phoneNumber.cp-iJJolN", "x": 653.*************, "y": 2899.*************, "order": 10, "level": 3, "source_type": "string", "path": "root.authInfo.phoneNumber.cp-iJJolN", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "将数据转化为列表类型", "description": "用于列表类型的转换，将非列表格式的数据转换为列表类型", "field": "将数据转化为列表类型", "input": {"asset_web_type": "list", "description": "用于列表类型的转换，将非列表格式的数据转换为列表类型"}, "action_type": "list", "attrs": {"text": "将数据转化为列表类型"}}, {"type": "asset", "asset_type": "action", "id": "root.authInfo.phoneNumber.cp-iJJolN.Fh6767bxz", "x": 871.2737341154331, "y": 2893.3785824990173, "order": 11, "level": 4, "path": "root.authInfo.phoneNumber.cp-iJJolN.Fh6767bxz", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "空值过滤器", "description": "过滤空值", "field": "空值过滤器", "input": {"asset_web_type": "filter_empty", "description": "过滤空值"}, "action_type": "filter_empty", "attrs": {"text": "空值过滤器"}}, {"type": "asset", "asset_type": "action", "id": "root.authInfo.phoneNumber.cp-iJJolN.Fh6767bxz.6-xiV-QyH", "x": 1085.************, "y": 2892.************, "order": 12, "level": 5, "path": "root.authInfo.phoneNumber.cp-iJJolN.Fh6767bxz.6-xiV-QyH", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "account.phone"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "8Il-60lWK", "display_name": "账户", "description": "account.phone", "x": 1285.************, "y": 2892.************, "label": "账户-手机号", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.usergroups.6VlkuWxbZ", "x": 741.*************, "y": 3778.*************, "order": 14, "level": 2, "source_type": "list", "path": "root.usergroups.6VlkuWxbZ", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "for循环", "description": "用于将数组进行循环操作", "field": "for循环", "input": {"asset_web_type": "for", "description": "用于将数组进行循环操作"}, "action_type": "for", "attrs": {"text": "for循环"}}, {"x": 941.*************, "y": 3878.*************, "id": "root.usergroups.6VlkuWxbZ.element", "level": 3, "path": "usergroups.element", "sub_fields": [{"field": "id", "path": "usergroups.element.id", "type": "integer", "x": 821.6563083587666, "y": 3992.8442954623515}, {"field": "name", "path": "usergroups.element.name", "type": "string", "x": 821.6563083587666, "y": 4092.8442954623515}, {"field": "type", "path": "usergroups.element.type", "type": "integer", "x": 821.6563083587666, "y": 4192.8442954623515}, {"field": "order", "path": "usergroups.element.order", "type": "integer", "x": 821.6563083587666, "y": 4292.8442954623515}, {"field": "department", "path": "usergroups.element.department", "type": "object", "sub_fields": [{"field": "id", "path": "usergroups.element.department.id", "type": "integer", "x": 1021.6563083587666, "y": 4492.8442954623515}, {"field": "name", "path": "usergroups.element.department.name", "type": "string", "x": 1021.6563083587666, "y": 4592.8442954623515}, {"field": "parent", "path": "usergroups.element.department.parent", "type": "object", "sub_fields": [{"field": "id", "path": "usergroups.element.department.parent.id", "type": "integer", "x": 1221.6563083587666, "y": 4792.8442954623515}, {"field": "name", "path": "usergroups.element.department.parent.name", "type": "string", "x": 1221.6563083587666, "y": 4892.8442954623515}, {"field": "description", "path": "usergroups.element.department.parent.description", "type": "string", "x": 1221.6563083587666, "y": 4992.8442954623515}], "x": 1021.6563083587666, "y": 4692.8442954623515}], "x": 821.6563083587666, "y": 4392.8442954623515}], "type": "asset", "datatype": "object", "asset_type": "asset", "field": "element", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 1141.5131839242379, "y": 3978.*************, "id": "root.usergroups.6VlkuWxbZ.element.id", "level": 4, "path": "usergroups.element.id", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "id", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 1141.5131839242379, "y": 4078.*************, "id": "root.usergroups.6VlkuWxbZ.element.name", "level": 4, "path": "usergroups.element.name", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "name", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 1141.5131839242379, "y": 4178.137136521, "id": "root.usergroups.6VlkuWxbZ.element.type", "level": 4, "path": "usergroups.element.type", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "type", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 1141.5131839242379, "y": 4278.137136521002, "id": "root.usergroups.6VlkuWxbZ.element.order", "level": 4, "path": "usergroups.element.order", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "order", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 1141.5131839242379, "y": 4378.137136521002, "id": "root.usergroups.6VlkuWxbZ.element.department", "level": 4, "path": "usergroups.element.department", "sub_fields": [{"field": "id", "path": "usergroups.element.department.id", "type": "integer", "x": 1021.6563083587666, "y": 4492.8442954623515}, {"field": "name", "path": "usergroups.element.department.name", "type": "string", "x": 1021.6563083587666, "y": 4592.8442954623515}, {"field": "parent", "path": "usergroups.element.department.parent", "type": "object", "sub_fields": [{"field": "id", "path": "usergroups.element.department.parent.id", "type": "integer", "x": 1221.6563083587666, "y": 4792.8442954623515}, {"field": "name", "path": "usergroups.element.department.parent.name", "type": "string", "x": 1221.6563083587666, "y": 4892.8442954623515}, {"field": "description", "path": "usergroups.element.department.parent.description", "type": "string", "x": 1221.6563083587666, "y": 4992.8442954623515}], "x": 1021.6563083587666, "y": 4692.8442954623515}], "type": "asset", "datatype": "object", "asset_type": "asset", "field": "department", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 1341.5131839242388, "y": 4478.137136521002, "id": "root.usergroups.6VlkuWxbZ.element.department.id", "level": 5, "path": "usergroups.element.department.id", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "id", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 1341.5131839242388, "y": 4578.137136521002, "id": "root.usergroups.6VlkuWxbZ.element.department.name", "level": 5, "path": "usergroups.element.department.name", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "name", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 1341.5131839242388, "y": 4678.137136521002, "id": "root.usergroups.6VlkuWxbZ.element.department.parent", "level": 5, "path": "usergroups.element.department.parent", "sub_fields": [{"field": "id", "path": "usergroups.element.department.parent.id", "type": "integer", "x": 1221.6563083587666, "y": 4792.8442954623515}, {"field": "name", "path": "usergroups.element.department.parent.name", "type": "string", "x": 1221.6563083587666, "y": 4892.8442954623515}, {"field": "description", "path": "usergroups.element.department.parent.description", "type": "string", "x": 1221.6563083587666, "y": 4992.8442954623515}], "type": "asset", "datatype": "object", "asset_type": "asset", "field": "parent", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 1541.5131839242388, "y": 4778.137136521002, "id": "root.usergroups.6VlkuWxbZ.element.department.parent.id", "level": 6, "path": "usergroups.element.department.parent.id", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "id", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 1541.5131839242388, "y": 4878.137136521002, "id": "root.usergroups.6VlkuWxbZ.element.department.parent.name", "level": 6, "path": "usergroups.element.department.parent.name", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "name", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 1541.5131839242388, "y": 4978.137136521002, "id": "root.usergroups.6VlkuWxbZ.element.department.parent.description", "level": 6, "path": "usergroups.element.department.parent.description", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "description", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.usergroups.6VlkuWxbZ.AgIUqzmdy", "x": 957.************, "y": 3787.*************, "order": 15, "level": 3, "path": "root.usergroups.6VlkuWxbZ.AgIUqzmdy", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "account.departments"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "KSFFpqnKd", "display_name": "账户", "description": "account.departments", "x": 1113.*************, "y": 3791.*************, "label": "账户-部门", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.lastLoginTime.v-h4Efb3Z", "x": 364.*************, "y": 3900.************, "order": 17, "level": 2, "source_type": "integer", "path": "root.lastLoginTime.v-h4Efb3Z", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "将数据转化为字符串", "description": "用于将将数据转化为字符串", "field": "将数据转化为字符串", "input": {"asset_web_type": "str", "description": "用于将将数据转化为字符串"}, "action_type": "str", "attrs": {"text": "将数据转化为字符串"}}, {"type": "asset", "asset_type": "action", "id": "root.joinTime.jSStmxqir.wd5mD7mwT", "x": 720.4423680953123, "y": 4394.97788255, "order": 24, "level": 3, "path": "root.joinTime.jSStmxqir.wd5mD7mwT", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "时间戳转为datetime", "description": "用于将时间戳转为datetime", "field": "时间戳转为datetime", "input": {"asset_web_type": "timestamp_to_datetime", "description": "用于将时间戳转为datetime"}, "action_type": "timestamp_to_datetime", "attrs": {"text": "时间戳转为datetime"}}, {"type": "asset", "asset_type": "action", "id": "root.joinTime.jSStmxqir.wd5mD7mwT.zaHh8Rr_0", "x": 879.7152357953123, "y": 4394.97788255, "order": 25, "level": 4, "path": "root.joinTime.jSStmxqir.wd5mD7mwT.zaHh8Rr_0", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "base.first_seen"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "wKn-a2yyU", "display_name": "基础", "description": "base.first_seen", "x": 1023.0485691286455, "y": 4391.644549216667, "label": "基础-首次出现时间", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.lastLoginTime.v-h4Efb3Z.pDSey8aUL", "x": 462.0655280730833, "y": 4089.8348574912357, "order": 2, "level": 3, "path": "root.lastLoginTime.v-h4Efb3Z.pDSey8aUL", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "用于将数据转化为整型", "description": "用于将数据转化为整型", "field": "用于将数据转化为整型", "input": {"asset_web_type": "int", "description": "用于将数据转化为整型"}, "action_type": "int", "attrs": {"text": "用于将数据转化为整型"}}, {"type": "asset", "asset_type": "action", "id": "root.lastLoginTime.v-h4Efb3Z.pDSey8aUL.kKjYp08lz", "x": 646.4886343068891, "y": 4086.7865416857185, "order": 3, "level": 4, "path": "root.lastLoginTime.v-h4Efb3Z.pDSey8aUL.kKjYp08lz", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "数据相除", "description": "用于数据相乘，可指定目标字段，并将结果赋值目标字段。目前只支持任意字段与数字相乘", "field": "数据相除", "input": {"asset_web_type": "division", "description": "用于数据相乘，可指定目标字段，并将结果赋值目标字段。目前只支持任意字段与数字相乘", "values": ["1000"]}, "action_type": "division", "attrs": {"text": "数据相除"}}, {"type": "asset", "asset_type": "action", "id": "root.lastLoginTime.v-h4Efb3Z.pDSey8aUL.kKjYp08lz.MgSRGVP-4", "x": 789.7594771662093, "y": 4088.3106995884773, "order": 4, "level": 5, "path": "root.lastLoginTime.v-h4Efb3Z.pDSey8aUL.kKjYp08lz.MgSRGVP-4", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "时间戳转为datetime", "description": "用于将时间戳转为datetime", "field": "时间戳转为datetime", "input": {"asset_web_type": "timestamp_to_datetime", "description": "用于将时间戳转为datetime"}, "action_type": "timestamp_to_datetime", "attrs": {"text": "时间戳转为datetime"}}, {"type": "asset", "asset_type": "action", "id": "root.lastLoginTime.v-h4Efb3Z.pDSey8aUL.kKjYp08lz.MgSRGVP-4.GzXE8C_KB", "x": 1853.6216932917996, "y": 4095.9314891022705, "order": 5, "level": 6, "path": "root.lastLoginTime.v-h4Efb3Z.pDSey8aUL.kKjYp08lz.MgSRGVP-4.GzXE8C_KB", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "base.last_seen"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "PqvsOYraS", "display_name": "基础", "description": "base.last_seen", "x": 2053.6216932917996, "y": 4095.9314891022705, "label": "基础-最近出现时间", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.joinTime.ol7X2rf8B", "x": 386.68600442005766, "y": 4399.237921048621, "order": 7, "level": 2, "source_type": "integer", "path": "root.joinTime.ol7X2rf8B", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "数据相除", "description": "用于数据相乘，可指定目标字段，并将结果赋值目标字段。目前只支持任意字段与数字相乘", "field": "数据相除", "input": {"asset_web_type": "division", "description": "用于数据相乘，可指定目标字段，并将结果赋值目标字段。目前只支持任意字段与数字相乘", "values": ["1000"]}, "action_type": "division", "attrs": {"text": "数据相除"}}, {"type": "asset", "asset_type": "action", "id": "root.usergroups.6VlkuWxbZ.element.department.name.EPs9rIEI7", "x": 1611.************, "y": 4542.************, "order": 8, "level": 6, "source_type": "string", "path": "root.usergroups.6VlkuWxbZ.element.department.name.EPs9rIEI7", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "account.departments.name"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "0nkxkzLtd", "display_name": "账户", "description": "account.departments.name", "x": 1811.************, "y": 4542.************, "label": "账户-部门-名称", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}], "edges": [{"source": "root", "target": "root.id", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.37329350708825661686641131316", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 100, "anchor_index": 0}}, {"source": "root", "target": "root.loginName", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.201579876957326041686641131316", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 200, "anchor_index": 0}}, {"source": "root", "target": "root.userName", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.59994863980063711686641131316", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 300, "anchor_index": 0}}, {"source": "root", "target": "root.email", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.44402561879447221686641131316", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 400, "anchor_index": 0}}, {"source": "root", "target": "root.authType", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.159738988079438431686641131316", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 500, "anchor_index": 0}}, {"source": "root.authType", "target": "root.authType.id", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.77502317687465181686641131316", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 525.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 600, "anchor_index": 0}}, {"source": "root.authType", "target": "root.authType.name", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.48182279889356661686641131316", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 525.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 700, "anchor_index": 0}}, {"source": "root.authType", "target": "root.authType.type", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.077815375677785291686641131316", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 525.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 800, "anchor_index": 0}}, {"source": "root.authType", "target": "root.authType.configurations", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.81695529518907041686641131316", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 525.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 900, "anchor_index": 0}}, {"source": "root.authType.configurations", "target": "root.authType.configurations.rela", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.34674389383163121686641131316", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 400, "y": 925.5, "anchor_index": 1}, "end_point": {"x": 549.5, "y": 1000, "anchor_index": 0}}, {"source": "root.authType.configurations", "target": "root.authType.configurations.authtype1", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.35629997533751381686641131316", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 400, "y": 925.5, "anchor_index": 1}, "end_point": {"x": 549.5, "y": 1100, "anchor_index": 0}}, {"source": "root.authType.configurations", "target": "root.authType.configurations.authtype2", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.290263448719954241686641131316", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 400, "y": 925.5, "anchor_index": 1}, "end_point": {"x": 549.5, "y": 1200, "anchor_index": 0}}, {"source": "root.authType", "target": "root.authType.enabled", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.89099120601754891686641131317", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 525.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 1300, "anchor_index": 0}}, {"source": "root", "target": "root.role", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.17390793695113451686641131317", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 1400, "anchor_index": 0}}, {"source": "root.role", "target": "root.role.id", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.00424871291910178251686641131317", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 1425.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 1500, "anchor_index": 0}}, {"source": "root.role", "target": "root.role.name", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.85742878451708071686641131317", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 1425.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 1600, "anchor_index": 0}}, {"source": "root.role", "target": "root.role.description", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.48665423971249511686641131317", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 1425.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 1700, "anchor_index": 0}}, {"source": "root.role", "target": "root.role.icon", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.50230445317442811686641131317", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 1425.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 1800, "anchor_index": 0}}, {"source": "root.role", "target": "root.role.enabled", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.83325614469209651686641131317", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 1425.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 1900, "anchor_index": 0}}, {"source": "root.role", "target": "root.role.a<PERSON><PERSON><PERSON><PERSON>", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.220490029877297381686641131317", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 1425.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 2000, "anchor_index": 0}}, {"source": "root.role.a<PERSON><PERSON><PERSON><PERSON>", "target": "root.role.apiWhitelist.enable", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.0097656172543436611686641131317", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 400, "y": 2025.5, "anchor_index": 1}, "end_point": {"x": 549.5, "y": 2100, "anchor_index": 0}}, {"source": "root", "target": "root.department", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.364783335214460051686641131317", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 2200, "anchor_index": 0}}, {"source": "root.department", "target": "root.department.id", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.65629303041977051686641131317", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2225.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 2300, "anchor_index": 0}}, {"source": "root.department", "target": "root.department.name", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.69545773016829451686641131317", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2225.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 2400, "anchor_index": 0}}, {"source": "root.department", "target": "root.department.description", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.404328643128427871686641131317", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2225.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 2500, "anchor_index": 0}}, {"source": "root", "target": "root.authInfo", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.211631880910285061686641131317", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 2600, "anchor_index": 0}}, {"source": "root.authInfo", "target": "root.authInfo.secret", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.56838628658168751686641131317", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2625.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 2700, "anchor_index": 0}}, {"source": "root.authInfo", "target": "root.authInfo.passwordType", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.43119058469631091686641131317", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2625.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 2800, "anchor_index": 0}}, {"source": "root.authInfo", "target": "root.authInfo.phoneNumber", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.0100172746228963661686641131318", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2625.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 2900, "anchor_index": 0}}, {"source": "root.authInfo", "target": "root.authInfo.ldap_username", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.74495420711101931686641131318", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2625.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 3000, "anchor_index": 0}}, {"source": "root", "target": "root.state", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.140187933935815631686641131318", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 3100, "anchor_index": 0}}, {"source": "root", "target": "root.extra", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.36556360161165261686641131318", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 3200, "anchor_index": 0}}, {"source": "root.extra", "target": "root.extra.hasAttribute", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.74648854203252361686641131319", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 3225.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 3300, "anchor_index": 0}}, {"source": "root.extra.hasAttribute", "target": "root.extra.hasAttribute.pin1", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.168157790555362171686641131319", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 400, "y": 3325.5, "anchor_index": 1}, "end_point": {"x": 549.5, "y": 3400, "anchor_index": 0}}, {"source": "root.extra.hasAttribute", "target": "root.extra.hasAttribute.pin2", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.92841010993605891686641131319", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 400, "y": 3325.5, "anchor_index": 1}, "end_point": {"x": 549.5, "y": 3500, "anchor_index": 0}}, {"source": "root.extra.hasAttribute", "target": "root.extra.hasAttribute.authToken", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.148832545022143711686641131319", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 400, "y": 3325.5, "anchor_index": 1}, "end_point": {"x": 549.5, "y": 3600, "anchor_index": 0}}, {"source": "root.extra.hasAttribute", "target": "root.extra.hasAttribute.password", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.24748420199978961686641131320", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 400, "y": 3325.5, "anchor_index": 1}, "end_point": {"x": 549.5, "y": 3700, "anchor_index": 0}}, {"source": "root", "target": "root.usergroups", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.0221028462592067761686641131320", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 3800, "anchor_index": 0}}, {"source": "root", "target": "root.lastLoginTime", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.133992683489527981686641131320", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 3900, "anchor_index": 0}}, {"source": "root", "target": "root.pwdChangeTime", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.085559749049917591686641131320", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 4000, "anchor_index": 0}}, {"source": "root", "target": "root.validFrom", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.199662449171978061686641131320", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 4100, "anchor_index": 0}}, {"source": "root", "target": "root.validTo", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.45091299227568761686641131320", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 4200, "anchor_index": 0}}, {"source": "root", "target": "root.pwdValidType", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.39361025611578371686641131320", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 4300, "anchor_index": 0}}, {"source": "root", "target": "root.joinTime", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.86069402656510421686641131320", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 4400, "anchor_index": 0}}, {"source": "root", "target": "root.updateTime", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.13546569376268681686641131320", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 4500, "anchor_index": 0}}, {"source": "root", "target": "root.joinUser", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.79335930472455881686641131320", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 4600, "anchor_index": 0}}, {"source": "root", "target": "root.deleted", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.80943753128532571686641131320", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 4700, "anchor_index": 0}}, {"source": "root", "target": "root.userType", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.68329675996641951686641131320", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 4800, "anchor_index": 0}}, {"source": "root", "target": "root.userCategory", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.58333289090427481686641131320", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 4900, "anchor_index": 0}}, {"source": "root", "target": "root.userSource", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.326668191747327531686641131320", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 5000, "anchor_index": 0}}, {"source": "root", "target": "root.locked", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.70071094742958961686641131320", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 5100, "anchor_index": 0}}, {"source": "root", "target": "root.enabled", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.042223324093538571686641131320", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 5200, "anchor_index": 0}}, {"source": "root.loginName", "target": "root.loginName.-vDuwcIs-", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.147833950515187551686641202791", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 200, "anchor_index": 1}, "end_point": {"x": 373.60233284857173, "y": 199.*************, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.loginName.-vDuwcIs-", "target": "PVCW847Wj", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.5292499031371441686641216717", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 474.60233284857173, "y": 199.*************, "anchor_index": 1}, "end_point": {"x": 573.6023328485717, "y": 199.*************, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.userName", "target": "root.userName.ll8-pF6Go", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.52565323462850371686641219053", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 300, "anchor_index": 1}, "end_point": {"x": 439.1578884041272, "y": 312.*************, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.userName.ll8-pF6Go", "target": "qDK6QhODY", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.151449612358369071686641232367", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 540.1578884041272, "y": 312.*************, "anchor_index": 1}, "end_point": {"x": 639.1578884041272, "y": 312.*************, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.email", "target": "root.email.rJJvwJsE8", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.61813658413918081686641235090", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 400, "anchor_index": 1}, "end_point": {"x": 354.7134439596829, "y": 402.*************, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.email.rJJvwJsE8", "target": "root.email.rJJvwJsE8.QNDLrs8Xp", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.46458869576042951686641245523", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 455.7134439596829, "y": 402.*************, "anchor_index": 1}, "end_point": {"x": 516.9356661819049, "y": 405.70580367911305, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.email.rJJvwJsE8.QNDLrs8Xp", "target": "root.email.rJJvwJsE8.QNDLrs8Xp.L2sQCHVDu", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.433203979378115061686641252117", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 617.9356661819049, "y": 405.70580367911305, "anchor_index": 1}, "end_point": {"x": 672.4912217374605, "y": 407.*************, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.email.rJJvwJsE8.QNDLrs8Xp.L2sQCHVDu", "target": "EI7b467sL", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.57029100361948791686641262200", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 773.4912217374605, "y": 407.*************, "anchor_index": 1}, "end_point": {"x": 872.4912217374605, "y": 407.*************, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.role.name", "target": "root.role.name.uSckjm5Uq", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.63231885953010371686641293167", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 450.5, "y": 1600, "anchor_index": 1}, "end_point": {"x": 591.1104444378998, "y": 1597.*************, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.role.name.uSckjm5Uq", "target": "nIHFzwgNT", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.4906433629805661686641303750", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 692.1104444378998, "y": 1597.*************, "anchor_index": 1}, "end_point": {"x": 791.1104444378998, "y": 1597.*************, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.state", "target": "root.state.sQWljwd0w", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.464099671705629161686641346142", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 3100, "anchor_index": 1}, "end_point": {"x": 505.3716389220997, "y": 3096.************, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.state.sQWljwd0w", "target": "root.state.sQWljwd0w.ckJxNllIa", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.98803591097407511686641434441", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 606.3716389220997, "y": 3096.************, "anchor_index": 1}, "end_point": {"x": 887.2218389220996, "y": 3094.************, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.state.sQWljwd0w.ckJxNllIa", "target": "UVUTQtQXF", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.89607914210236591686641443884", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 988.2218389220996, "y": 3094.************, "anchor_index": 1}, "end_point": {"x": 1087.2218389220996, "y": 3094.************, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.authInfo.phoneNumber", "target": "root.authInfo.phoneNumber.cp-iJJolN", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.45472114434392651686641447425", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 450.5, "y": 2900, "anchor_index": 1}, "end_point": {"x": 602.5268586454332, "y": 2899.*************, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.authInfo.phoneNumber.cp-iJJolN", "target": "root.authInfo.phoneNumber.cp-iJJolN.Fh6767bxz", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.28276405988729091686641476378", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 703.5268586454332, "y": 2899.*************, "anchor_index": 1}, "end_point": {"x": 820.7737341154331, "y": 2893.3785824990173, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.authInfo.phoneNumber.cp-iJJolN.Fh6767bxz", "target": "root.authInfo.phoneNumber.cp-iJJolN.Fh6767bxz.6-xiV-QyH", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.5349603727396331686641485592", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 921.7737341154331, "y": 2893.3785824990173, "anchor_index": 1}, "end_point": {"x": 1035.146404695433, "y": 2892.************, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.authInfo.phoneNumber.cp-iJJolN.Fh6767bxz.6-xiV-QyH", "target": "8Il-60lWK", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.8915740636126651686641495452", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1136.146404695433, "y": 2892.************, "anchor_index": 1}, "end_point": {"x": 1235.146404695433, "y": 2892.************, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.usergroups", "target": "root.usergroups.6VlkuWxbZ", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.051608681259674771686641535161", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 3800, "anchor_index": 1}, "end_point": {"x": 691.0131839242359, "y": 3778.*************, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.usergroups.6VlkuWxbZ", "target": "root.usergroups.6VlkuWxbZ.element", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.101043258040281361686641539942", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 792.0131839242359, "y": 3778.*************, "anchor_index": 1}, "end_point": {"x": 891.0131839242358, "y": 3878.*************, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.usergroups.6VlkuWxbZ.element", "target": "root.usergroups.6VlkuWxbZ.element.id", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.200628197247278321686641539943", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 941.*************, "y": 3903.6371365210016, "anchor_index": 1}, "end_point": {"x": 1091.0131839242379, "y": 3978.*************, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.usergroups.6VlkuWxbZ.element", "target": "root.usergroups.6VlkuWxbZ.element.name", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.69588132236741051686641539943", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 941.*************, "y": 3903.6371365210016, "anchor_index": 1}, "end_point": {"x": 1091.0131839242379, "y": 4078.*************, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.usergroups.6VlkuWxbZ.element", "target": "root.usergroups.6VlkuWxbZ.element.type", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.83632594469905561686641539943", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 941.*************, "y": 3903.6371365210016, "anchor_index": 1}, "end_point": {"x": 1091.0131839242379, "y": 4178.137136521, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.usergroups.6VlkuWxbZ.element", "target": "root.usergroups.6VlkuWxbZ.element.order", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.170033849650898631686641539943", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 941.*************, "y": 3903.6371365210016, "anchor_index": 1}, "end_point": {"x": 1091.0131839242379, "y": 4278.137136521002, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.usergroups.6VlkuWxbZ.element", "target": "root.usergroups.6VlkuWxbZ.element.department", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.73157647570546521686641539943", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 941.*************, "y": 3903.6371365210016, "anchor_index": 1}, "end_point": {"x": 1091.0131839242379, "y": 4378.137136521002, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.usergroups.6VlkuWxbZ.element.department", "target": "root.usergroups.6VlkuWxbZ.element.department.id", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.86712745446782511686641539943", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1141.5131839242379, "y": 4403.637136521002, "anchor_index": 1}, "end_point": {"x": 1291.0131839242388, "y": 4478.137136521002, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.usergroups.6VlkuWxbZ.element.department", "target": "root.usergroups.6VlkuWxbZ.element.department.name", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.0257904977453315981686641539943", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1141.5131839242379, "y": 4403.637136521002, "anchor_index": 1}, "end_point": {"x": 1291.0131839242388, "y": 4578.137136521002, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.usergroups.6VlkuWxbZ.element.department", "target": "root.usergroups.6VlkuWxbZ.element.department.parent", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.78800846024505081686641539943", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1141.5131839242379, "y": 4403.637136521002, "anchor_index": 1}, "end_point": {"x": 1291.0131839242388, "y": 4678.137136521002, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.usergroups.6VlkuWxbZ.element.department.parent", "target": "root.usergroups.6VlkuWxbZ.element.department.parent.id", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.4370437416994691686641539943", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1341.5131839242388, "y": 4703.637136521002, "anchor_index": 1}, "end_point": {"x": 1491.0131839242388, "y": 4778.137136521002, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.usergroups.6VlkuWxbZ.element.department.parent", "target": "root.usergroups.6VlkuWxbZ.element.department.parent.name", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.55015101030235771686641539943", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1341.5131839242388, "y": 4703.637136521002, "anchor_index": 1}, "end_point": {"x": 1491.0131839242388, "y": 4878.137136521002, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.usergroups.6VlkuWxbZ.element.department.parent", "target": "root.usergroups.6VlkuWxbZ.element.department.parent.description", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.15960440595346781686641539943", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1341.5131839242388, "y": 4703.637136521002, "anchor_index": 1}, "end_point": {"x": 1491.0131839242388, "y": 4978.137136521002, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.usergroups.6VlkuWxbZ", "target": "root.usergroups.6VlkuWxbZ.AgIUqzmdy", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.28822945699415971686641542722", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 792.0131839242359, "y": 3778.*************, "anchor_index": 1}, "end_point": {"x": 907.134475267446, "y": 3787.*************, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.usergroups.6VlkuWxbZ.AgIUqzmdy", "target": "KSFFpqnKd", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.187815743310828951686641559519", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1008.134475267446, "y": 3787.*************, "anchor_index": 1}, "end_point": {"x": 1062.8871812187558, "y": 3791.*************, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.lastLoginTime", "target": "root.lastLoginTime.v-h4Efb3Z", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.79601308091464621686641628755", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 3900, "anchor_index": 1}, "end_point": {"x": 313.8338273530709, "y": 3900.************, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.joinTime.jSStmxqir.wd5mD7mwT", "target": "root.joinTime.jSStmxqir.wd5mD7mwT.zaHh8Rr_0", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.90803594205727191686641986075", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 770.9423680953123, "y": 4394.97788255, "anchor_index": 1}, "end_point": {"x": 829.2152357953123, "y": 4394.97788255, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.joinTime.jSStmxqir.wd5mD7mwT.zaHh8Rr_0", "target": "wKn-a2yyU", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.445042706267916531686642003430", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 930.2152357953123, "y": 4394.97788255, "anchor_index": 1}, "end_point": {"x": 972.5485691286455, "y": 4391.644549216667, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.lastLoginTime.v-h4Efb3Z", "target": "root.lastLoginTime.v-h4Efb3Z.pDSey8aUL", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.30543689584193291686643681824", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 414.8338273530709, "y": 3900.************, "anchor_index": 1}, "end_point": {"x": 411.5655280730833, "y": 4089.834857491236, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.lastLoginTime.v-h4Efb3Z.pDSey8aUL", "target": "root.lastLoginTime.v-h4Efb3Z.pDSey8aUL.kKjYp08lz", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.310544427840967031686643699323", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 512.5655280730833, "y": 4089.834857491236, "anchor_index": 1}, "end_point": {"x": 595.9886343068891, "y": 4086.7865416857185, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.lastLoginTime.v-h4Efb3Z.pDSey8aUL.kKjYp08lz", "target": "root.lastLoginTime.v-h4Efb3Z.pDSey8aUL.kKjYp08lz.MgSRGVP-4", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.463793580093846641686643730693", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 696.9886343068891, "y": 4086.7865416857185, "anchor_index": 1}, "end_point": {"x": 739.2594771662093, "y": 4088.3106995884773, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.lastLoginTime.v-h4Efb3Z.pDSey8aUL.kKjYp08lz.MgSRGVP-4", "target": "root.lastLoginTime.v-h4Efb3Z.pDSey8aUL.kKjYp08lz.MgSRGVP-4.GzXE8C_KB", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.68008497550310461686643748859", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 840.2594771662093, "y": 4088.3106995884773, "anchor_index": 1}, "end_point": {"x": 1803.1216932917996, "y": 4095.9314891022705, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.lastLoginTime.v-h4Efb3Z.pDSey8aUL.kKjYp08lz.MgSRGVP-4.GzXE8C_KB", "target": "PqvsOYraS", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.71171827520093791686643764858", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1904.1216932917996, "y": 4095.9314891022705, "anchor_index": 1}, "end_point": {"x": 2003.1216932917996, "y": 4095.9314891022705, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.joinTime", "target": "root.joinTime.ol7X2rf8B", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.5051466879110541686643936627", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 4400, "anchor_index": 1}, "end_point": {"x": 336.18600442005766, "y": 4399.237921048621, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.joinTime.ol7X2rf8B", "target": "root.joinTime.jSStmxqir.wd5mD7mwT", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.64136743241224431686643953588", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 437.18600442005766, "y": 4399.237921048621, "anchor_index": 1}, "end_point": {"x": 669.9423680953123, "y": 4394.97788255, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.usergroups.6VlkuWxbZ.element.department.name", "target": "root.usergroups.6VlkuWxbZ.element.department.name.EPs9rIEI7", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.46793759049923111686644039606", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1392.0131839242388, "y": 4578.137136521002, "anchor_index": 1}, "end_point": {"x": 1561.************, "y": 4542.************, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.usergroups.6VlkuWxbZ.element.department.name.EPs9rIEI7", "target": "0nkxkzLtd", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.240557628089439481686644052802", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1662.************, "y": 4542.************, "anchor_index": 1}, "end_point": {"x": 1761.************, "y": 4542.************, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}]}, "rules": [{"name": "enter", "setting": {"values": "account.username", "field": "${loginName}"}, "sub_rules": [], "_id": "root.loginName.-vDuwcIs-"}, {"name": "enter", "setting": {"values": "account.nickname", "field": "${userName}"}, "sub_rules": [], "_id": "root.userName.ll8-pF6Go"}, {"name": "list", "setting": {"field": "email"}, "sub_rules": [], "_id": "root.email.rJJvwJsE8"}, {"name": "filter_empty", "setting": {"child_field": null, "child_is_remove": null, "field": "email"}, "sub_rules": [], "_id": "root.email.rJJvwJsE8.QNDLrs8Xp"}, {"name": "enter", "setting": {"values": "account.email", "field": "${email}"}, "sub_rules": [], "_id": "root.email.rJJvwJsE8.QNDLrs8Xp.L2sQCHVDu"}, {"name": "enter", "setting": {"values": "account.position", "field": "${role.name}"}, "sub_rules": [], "_id": "root.role.name.uSckjm5Uq"}, {"name": "list", "setting": {"field": "authInfo.phoneNumber"}, "sub_rules": [], "_id": "root.authInfo.phoneNumber.cp-iJJolN"}, {"name": "filter_empty", "setting": {"child_field": null, "child_is_remove": null, "field": "authInfo.phoneNumber"}, "sub_rules": [], "_id": "root.authInfo.phoneNumber.cp-iJJolN.Fh6767bxz"}, {"name": "enter", "setting": {"values": "account.phone", "field": "${authInfo.phoneNumber}"}, "sub_rules": [], "_id": "root.authInfo.phoneNumber.cp-iJJolN.Fh6767bxz.6-xiV-QyH"}, {"name": "translate", "setting": {"values": [{"name": 0, "value": "活动"}, {"name": 1, "value": "密码过期"}, {"name": 2, "value": "账户过期"}, {"name": 3, "value": "禁用"}], "default": null, "field": "state"}, "sub_rules": [], "_id": "root.state.sQWljwd0w"}, {"name": "enter", "setting": {"values": "account.status", "field": "${state}"}, "sub_rules": [], "_id": "root.state.sQWljwd0w.ckJxNllIa"}, {"name": "for", "setting": {"field": "usergroups"}, "sub_rules": [{"id": "root.usergroups.6VlkuWxbZ.element.department.name.EPs9rIEI7", "name": "enter", "setting": {"values": "name", "field": "${department.name}"}, "sub_rules": []}], "_id": "root.usergroups.6VlkuWxbZ"}, {"name": "enter", "setting": {"values": "account.departments", "field": "${usergroups}"}, "sub_rules": [], "_id": "root.usergroups.6VlkuWxbZ.AgIUqzmdy"}, {"name": "str", "setting": {"field": "lastLoginTime"}, "sub_rules": [], "_id": "root.lastLoginTime.v-h4Efb3Z"}, {"name": "int", "setting": {"field": "lastLoginTime"}, "sub_rules": [], "_id": "root.lastLoginTime.v-h4Efb3Z.pDSey8aUL"}, {"name": "division", "setting": {"values": [1000], "field": "${lastLoginTime}"}, "sub_rules": [], "_id": "root.lastLoginTime.v-h4Efb3Z.pDSey8aUL.kKjYp08lz"}, {"name": "timestamp_to_datetime", "setting": {"field": "${lastLoginTime}"}, "sub_rules": [], "_id": "root.lastLoginTime.v-h4Efb3Z.pDSey8aUL.kKjYp08lz.MgSRGVP-4"}, {"name": "enter", "setting": {"values": "base.last_seen", "field": "${lastLoginTime}"}, "sub_rules": [], "_id": "root.lastLoginTime.v-h4Efb3Z.pDSey8aUL.kKjYp08lz.MgSRGVP-4.GzXE8C_KB"}, {"name": "division", "setting": {"values": [1000], "field": "${joinTime}"}, "sub_rules": [], "_id": "root.joinTime.ol7X2rf8B"}, {"name": "timestamp_to_datetime", "setting": {"field": "${joinTime}"}, "sub_rules": [], "_id": "root.joinTime.jSStmxqir.wd5mD7mwT"}, {"name": "enter", "setting": {"values": "base.first_seen", "field": "${joinTime}"}, "sub_rules": [], "_id": "root.joinTime.jSStmxqir.wd5mD7mwT.zaHh8Rr_0"}], "adapter_name": "qzsec_jumpserver", "fetch_type": "account", "model_name": "account", "asset_type": "account", "internal": true}