from caasm_adapter.util.exception import AdapterFetchApiNotSupportException
from qzsec_jumpserver.clients.account import QzSecAccountApiClient
from qzsec_jumpserver.clients.asset import Qz<PERSON>ec<PERSON><PERSON><PERSON>piClient
from qzsec_jumpserver.clients.auth import QzSecAuthApiClient


class ApiType(object):
    ASSET = "computer"
    AUTH = "auth"
    ACCOUNT = "account"


class QzSecManager(object):
    client_mapper = {
        ApiType.ASSET: QzSecAssetApiClient,
        ApiType.AUTH: QzSecAuthApiClient,
        ApiType.ACCOUNT: QzSecAccountApiClient,
    }

    def __init__(self, connection, session=None):
        self._connection = connection
        self._session = session
        self._auth_data = None

    def find(self, fetch_type, page_index, page_size):
        self.auth()
        return self._instance(fetch_type).handle(page_index, page_size)

    def auth(self):
        self._auth_data = self._instance(ApiType.AUTH).handle()

    def _instance(self, api_type):
        clazz = self.client_mapper.get(api_type)
        if not clazz:
            raise AdapterFetchApiNotSupportException()
        return clazz(self._connection, self._session, self._auth_data)
