name: qzsec_jumpserver
display_name: "奇治科技堡垒机"
description: "齐治科技是一家数据中心安全管理解决方案提供商，专注于数据中心安全管理的产品的研发与推广，旗下主要产品为运维堡垒机，致力于简化运维操作系统，方便运维管理人员日常工作"
type: "堡垒机"
logo: "qzsec_jumpserver.png"
company: "奇治科技"
version: "v0.1"
priority: 10
properties:
  - 堡垒机

connection:
  - name: address
    type: url
    required: true
    display_name: "请求地址"
    description: "请求地址"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'

  - name: username
    type: string
    required: true
    display_name: "用户名"
    description: "用户名"
    validate_rules:
      - name: length
        error_hint: "用户名格式无效。长度最小不得小于2，最大不得大于100"
        setting:
          min: 2
          max: 100

  - name: password
    type: password
    required: true
    display_name: "密码"
    description: "密码"
    validate_rules:
      - name: length
        error_hint: "密码格式无效。长度最小不得小于6，最大不得大于200"
        setting:
          min: 6
          max: 200


fetch_setting:
  point: "qzsec_jumpserver.fetch:find_asset"
  is_need_test_service: true
  test_auth_point: "qzsec_jumpserver.fetch:get_auth_connection"
  size: 500
  fetch_type_mapper:
    asset:
      - computer
    account:
      - account

merge_setting:
  size: 500
  setting:
    asset:
      computer:
        fields:
          - id

convert_setting:
  size: 500
  before_executor_mapper: { }
  executor_mapper: { }

fabric_setting:
  choose_point_mapper:
    asset: "qzsec_jumpserver.fabric:choose_new_record"