import base64
import json
import logging
import time

from gmssl.sm4 import CryptSM4, SM4_ENCRYPT, SM4_DECRYPT
from pysmx.SM3 import SM3

from caasm_adapter.util.client import FetchJsonResultClient
from caasm_tool.util import get_random_string

log = logging.getLogger()


class XinDunIAMBaseClient(FetchJsonResultClient):
    METHOD = "post"

    def __init__(self, *args, **kwargs):
        super(XinDunIAMBaseClient, self).__init__(*args, **kwargs)
        self._timestamp = int(time.time() * 1000)

    def send_request(self, request_url, params, json_data, form_data, headers, cookies, auth):
        if form_data:
            form_data["sign"] = params["sign"]
            form_data = self.encrypt_sm4(self.secret[16:], json.dumps(form_data))
        res = super(XinDunIAMBaseClient, self).send_request(
            request_url, params, json_data, form_data, headers, cookies, auth
        )
        return res

    def build_request_header(self, *args, **kwargs):
        return {"Content-Type": "application/json"}

    def parse_response(self, response, *args, **kwargs):
        if response.status_code != 200:
            return self.error_handle(None, *args, **kwargs)
        content = self.decrypt_sm4(self.secret, response.content)
        return json.loads(str(content, "utf-8"))

    @property
    def flag_key_name(self):
        return "code"

    @property
    def data_key_name(self):
        return "data"

    @property
    def suc_flag(self):
        return 1000

    def build_request_params(self, *args, **kwargs):
        client_id = self.client_id
        nonce = self.nonce
        timestamp = self._timestamp

        params = {
            "client_id": client_id,
            "nonce": nonce,
            "timestamp": timestamp,
            "sign": self.sm3_str_hash(f"{client_id}{nonce}{timestamp}{self.secret}"),
        }
        return params

    @property
    def client_id(self):
        return self.connection.get("client_id")

    @property
    def secret(self):
        return self.connection.get("secret")

    @property
    def nonce(self):
        return get_random_string(8)

    @classmethod
    def sm3_str_hash(cls, msg):
        sm3 = SM3()
        sign_bytes = bytes(msg, encoding="utf-8")
        sm3.update(sign_bytes)
        return str(base64.b64encode(sm3.digest()), "utf-8")

    @classmethod
    def encrypt_sm4(cls, secret, cipher_text):
        sm4 = CryptSM4()
        sm4.set_key(secret.encode(), SM4_ENCRYPT)
        encrypt_value = sm4.crypt_ecb(cipher_text.encode())
        return str(base64.b64encode(encrypt_value), "utf-8")

    @classmethod
    def decrypt_sm4(cls, secret, cipher_text):
        sm4 = CryptSM4()
        sm4.set_key(secret[16:].encode(), SM4_DECRYPT)
        bytes_cipher = base64.b64decode(cipher_text)
        return sm4.crypt_ecb(bytes_cipher)
