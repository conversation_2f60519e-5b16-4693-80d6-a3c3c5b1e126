from caasm_adapter.sdk.adapter_fetch import fetch_sdk
from xindun_iam.manage import XinDunIAMManager


def find_asset(connection, fetch_type, page_index=0, session=None, **kwargs):
    records = XinDunIAMManager(connection, session).find_asset(page_index)
    result = fetch_sdk.build_asset(records, fetch_type)
    return fetch_sdk.return_success(result)


def get_auth_connection(connection, session=None):
    XinDunIAMManager(connection, session).test_auth_connection()
