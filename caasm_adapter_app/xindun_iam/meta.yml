name: "xindun_iam"
display_name: "芯盾IAM"
description: "芯盾时代用户身份与访问管理IAM-统一认证管理，支持传统认证、移动认证、生物认证、证书认证等技术，实现所知、所持、所有三个维度的认证能力。芯盾时代通过设备指纹、软件安全沙箱、终端安全防御、三层秘钥体系核心专利技术，保障移动认证安全性。"
type: "IAM"
company: "芯盾时代"
logo: "logo.png"
version: "v0.1"
priority: 1
properties:
  - "身份识别"
  - "访问控制"

connection:
  - name: client_id
    type: string
    required: true
    display_name: "客户端ID"
    description: "客户端ID"
    validate_rules:
      - name: length
        error_hint: "客户端ID长度必须大于等于2且小于等于100"
        setting:
          min: 2
          max: 100

  - name: secret
    type: password
    required: true
    display_name: "交互秘钥"
    description: "交互秘钥"
    validate_rules:
      - name: length
        error_hint: "交互秘钥长度必须大于等于6且小于等于100"
        setting:
          min: 6
          max: 100

  - name: address
    type: url
    required: true
    display_name: "地址"
    description: "地址信息"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'


fetch_setting:
  type: disposable
  point: "xindun_iam.fetch:find_asset"
  is_need_test_service: true
  test_auth_point: "xindun_iam.fetch:get_auth_connection"
  size: 1000
  fetch_type_mapper:
    account:
      - account

fabric_setting:
  choose_point_mapper:
    account: "xindun_iam.fabric:choose_new_record"

merge_setting:
  size: 1000
  setting:
    account:
      account:
        fields:
          - employeenum

convert_setting:
  size: 1000
  before_executor_mapper:
    account:
      - add:
          - field: asset_type
            value: "账户"
  executor_mapper:
    account:
      default:
        - rename:
            - src_field: realname
              dst_field: username
            - src_field: employeenum
              dst_field: account_id
            - src_field: department
              dst_field: departments
        - add:
            - field: email
              value:
                - "${user_detail.email}"
            - field: phone
              value:
                - "${user_detail.phone}"
                - "${user_detail.work_phone}"
        - filter:
            - field: email
              condition: empty_check
            - field: phone
              condition: empty_check
        - translate:
            - field: employee_status
              default: 999
              values:
                - field: 1
                  value: 1
                - field: 2
                  value: 2
            - field: type
              default: 999
              values:
                - field: sales
                  value: 6
                - field: staff
                  value: 1
                - field: supplier
                  value: 5
                - field: retired
                  value: 4
                - field: outsource_staff
                  value: 2
                - field: project_outsource
                  value: 3
        - for:
            - field: departments
              handler:
                - method: rename
                  setting:
                    - src_field: is_primary
                      dst_field: direct
                - method: translate
                  setting:
                    - field: direct
                      default: 999
                      values:
                        - field: True
                          value: 1
                        - field: False
                          value: 0


