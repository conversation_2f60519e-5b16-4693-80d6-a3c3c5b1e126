name: "huawei_cloud_ak"
display_name: "华为云AK获取"
description: "华为云立足于互联网领域，提供包括云主机、云托管、云存储等基础云服务、超算、内容分发与加速、视频托管与发布、企业IT、云电脑、云会议、游戏托管、应用托管等服务和解决方案。"
type: "云平台"
company: "华为"
logo: "huawei_cloud.jpg"
version: "v0.1"
priority: 1
properties:
  - "云平台"

connection:
  - name: access_key
    type: string
    required: true
    display_name: "access_key"
    description: "华为云访问Access key"
    validate_rules:
      - name: length
        error_hint: "access_key格式无效。长度最小不得小于2，最大不得大于100"
        setting:
          min: 2
          max: 100

  - name: secret_key
    type: password
    required: true
    display_name: "Secret Key"
    description: "华为云访问Secret Key"
    validate_rules:
      - name: length
        error_hint: "secret_key格式无效。长度最小不得小于6，最大不得大于200"
        setting:
          min: 6
          max: 200

  - name: region
    type: string
    required: false
    display_name: "区域"
    description: "区域"
    validate_rules:
      - name: length
        error_hint: "cloud_account格式无效。长度最小不得小于1，最大不得大于200"
        setting:
          min: 1
          max: 200

  - name: cloud_account
    type: string
    required: false
    display_name: "云账户"
    description: "华为云账户"
    validate_rules:
      - name: length
        error_hint: "cloud_account格式无效。长度最小不得小于1，最大不得大于200"
        setting:
          min: 1
          max: 200


fetch_setting:
  type: disposable
  point: "huawei_cloud_ak.fetch:find_asset"
  condition_point: "huawei_cloud_ak.fetch:build_query_condition"
  is_need_test_service: false
  test_auth_point: "huawei_cloud_ak.fetch:get_auth_connection"
  size: 100
  fetch_type_mapper:
    asset:
      - access_key

merge_setting:
  size: 300
  setting: { }

convert_setting:
  size: 300
  before_executor_mapper: { }
  executor_mapper: { }