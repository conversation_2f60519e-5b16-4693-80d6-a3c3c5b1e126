from caasm_adapter.sdk.adapter_fetch import fetch_sdk
from huawei_cloud_ak.manage import HuaweiCloudAKManager


def find_asset(connection, fetch_type, condition, page_index=None, page_size=20, **kwargs):
    manager = _build_manager(connection, condition=condition)
    records = manager.find_asset(fetch_type, page_index, page_size)
    result = fetch_sdk.build_asset(records, fetch_type)
    return fetch_sdk.return_success(result)


def get_auth_connection(connection, session):
    _build_manager(connection).auth()


def build_query_condition(connection, session, fetch_type):
    return {}


def _build_manager(connection, condition=None):
    return HuaweiCloudAKManager(connection, condition=condition)
