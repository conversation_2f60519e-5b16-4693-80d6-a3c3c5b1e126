import json
from functools import cached_property

from huaweicloudsdkcore.auth.credentials import GlobalCredentials
from huaweicloudsdkcore.http.http_config import HttpConfig
from huaweicloudsdkecs.v2.region.ecs_region import EcsRegion

from caasm_tool.sock import parse_address
from caasm_tool.util import extract


class HuaweiCloudBaseClient(object):
    DEFAULT_REGION = None
    IGNORE_ERROR_MESSAGES = ["No project id found", "status_code:404", "Unexpected region_id", "servname provided"]

    def __init__(self, connection, region=None):
        self._connection = connection
        self._region = region or self.DEFAULT_REGION
        self._client = None
        self._build_client()
        self._method = getattr(self.client, self.method_name, None) if self._client else None

    def _build_client(self):
        try:
            credentials = GlobalCredentials(self.access_key, self.secret_key)
            client = self.client_class.new_builder().with_credentials(credentials)
            if self._region:
                client = client.with_region(self.client_region.value_of(self._region))
            client.with_http_config(self._parse_http_config())
            self._client = client.build()
        except Exception as e:
            e_message = str(e)
            for _error_message in self.IGNORE_ERROR_MESSAGES:
                if _error_message in e_message:
                    return
            raise e

    def _parse_http_config(self):
        proxy_protocol, proxy_host, proxy_port = parse_address(self.proxy) if self.proxy else [None, None, None]
        return HttpConfig(
            proxy_protocol=proxy_protocol, proxy_host=proxy_host, proxy_port=proxy_port, ignore_ssl_verification=True
        )

    def handle(self, *args, **kwargs):
        if not self._method:
            return None
        try:
            request = self.build_request(*args, **kwargs)
            response = self._method(request)
            print(response)
        except Exception as e:
            if str(e) in self.IGNORE_ERROR_MESSAGES:
                return None
        else:
            return self.parse_response(response)

    def build_request(self, *args, **kwargs):
        raise NotImplementedError

    def parse_response(self, response):
        content = response.raw_content
        data = self.clean_content(content)
        biz_value = self.extract_biz_value(data)
        return self.clean_biz_value(biz_value)

    def extract_biz_value(self, result):
        if not self.data_key:
            return result
        return extract(result, self.data_key)

    def clean_biz_value(self, biz_value):
        return biz_value

    def clean_content(self, content):
        return json.loads(content)

    @property
    def data_key(self):
        return ""

    @property
    def method_name(self):
        raise NotImplementedError

    @property
    def client_class(self):
        raise NotImplementedError

    @property
    def client(self):
        return self._client

    @property
    def client_region(self):
        raise NotImplementedError

    @cached_property
    def address(self):
        address = self._connection.get("address")
        return [address] if address else []

    @property
    def access_key(self):
        return self._connection.get("access_key", "")

    @property
    def secret_key(self):
        return self._connection.get("secret_key", "")

    @property
    def proxy(self):
        return self._connection.get("proxy", "")
