from huaweicloudsdkiam.v3 import ListPermanentAccessKeysRequest, IamClient, KeystoneListPermissionsRequest
from huaweicloudsdkiam.v3.region.iam_region import IamRegion

from huawei_cloud_ak.clients.sdk.base import HuaweiCloudBaseClient


class HuaweiCloudPoliciesListClient(HuaweiCloudBaseClient):
    def build_request(self, domain_id=None, page_index=None, page_size=None, *args, **kwargs):
        return KeystoneListPermissionsRequest(domain_id=domain_id, page=page_index, per_page=page_size)

    @property
    def method_name(self):
        return "keystone_list_permissions"

    @property
    def data_key(self):
        return "roles"

    @property
    def client_class(self):
        return IamClient

    @property
    def client_region(self):
        return IamRegion
