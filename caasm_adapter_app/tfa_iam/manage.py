from tfa_iam.clients.device import TfaDeviceListClient


class ClientType(object):
    DEVICE = "device"


class FetchType(object):
    HOST = "terminal"


CLIENT_MAPPER = {
    ClientType.DEVICE: TfaDeviceListClient,
}


class TfaIamManager(object):
    def __init__(self, connection, session=None):
        self._connection = connection
        self._session = session
        self._fetch_method = {FetchType.HOST: self._find_terminal}

    def find_asset(self, fetch_type, page_index, page_size):
        page_index += 1
        find_method = self._fetch_method.get(fetch_type)
        if not find_method:
            return []

        return find_method(page_index, page_size)

    def _find_terminal(self, page_index, page_size):
        return self._call(ClientType.DEVICE, page_index=page_index, page_size=page_size)

    def _call(self, client_type, *args, **kwargs):
        return CLIENT_MAPPER[client_type](self._connection, self._session).handle(*args, **kwargs)
