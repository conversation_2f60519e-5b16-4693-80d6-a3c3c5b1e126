from typing import List
from tfa_iam.clients.base import TfaIamClient


class TfaDeviceListClient(TfaIamClient):
    METHOD = "get"
    URL = "/ngiam-rst/v1/sdk/tfa/device/getTfaPage"

    def build_request_params(self, page_index, page_size):
        return {"pageNumber": page_index, "pageSize": page_size}

    @property
    def data_key_name(self):
        return "content"

    def clean_result(self, result: list):
        """保留原始字段数据"""
        result = self.deal_department(result)
        return result

    def deal_department(self, result: list):
        for item in result:
            departments = item.get("groupChainName", [])
            index = 0
            if not departments:
                continue
            for i in range(len(departments)):
                if departments[i].endswith("总公司"):
                    index = i
                    break
            index_end = -1
            if len(departments) > index + 1:
                index_end = index + 2
            departments: List[str] = departments[index:index_end]
            end_name = departments[-1]
            name_group = end_name.split("/")
            if len(name_group) > 1:
                departments[-1] = name_group[0]
            if not departments:
                continue
            if departments[-1].endswith("分公司"):
                departments[-1] = departments[-1].replace("新华人寿", "")
            item["groupChainName"] = "-".join(departments)
        return result
