from requests import Response
from caasm_adapter.util.client import FetchRestfulClient
from caasm_adapter.util.exception import AdapterFetchApiRequestException
from caasm_tool.util import extract


class TfaIamClient(FetchRestfulClient):
    def build_request_header(self, *args, **kwargs):
        return {"Authorization": self.connection.get("authorization", "")}

    @property
    def suc_flag(self):
        return None

    @property
    def flag_key_name(self):
        return None

    @property
    def error_key_name(self):
        return "data.errorMsg"

    def parse_response(self, response, *args, **kwargs):
        response: Response
        if response.status_code != 200:
            err_msg = extract(response.json(), self.error_key_name)
            raise AdapterFetchApiRequestException(
                message=f"响应code:[{response.status_code}],data:{err_msg}",
            )
        return super().parse_response(response, *args, **kwargs)
