name: tfa_iam
display_name: "tfa_iam身份认证"
description: "TFA身份认证系统是企业统一身份认证管理系统,能够管理企业设备和用户身份信息"
type: "IAM"
company: "TFA科技有限公司"
version: "v0.1"
logo: "tfa_iam.png"
priority: 1
properties:
  - "IAM"
  - "账户"

connection:
  - name: address
    type: string
    required: true
    display_name: "服务地址"
    description: "TFA服务请求地址"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或https开头的地址信息"
        setting:
          reg: '^((http|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'

  - name: authorization
    type: string
    required: true
    display_name: "认证密钥"
    description: "统一认证发放的密钥"
    validate_rules:
      - name: length
        error_hint: "密钥格式无效。长度最小不得小于1，最大不得大于100"
        setting:
          min: 1

fetch_setting:
  fetch_type_mapper:
    # 资产类型
    asset:
      # 资产名称
      - terminal
  point: "tfa_iam.fetch:find_asset"
  is_need_test_service: true
  test_auth_point: "tfa_iam.fetch:get_auth_connection"
  size: 10
  cleaner_mapper: {}

merge_setting:
  size: 200
  setting: {}

convert_setting:
  size: 200
  before_executor_mapper: {}
  executor_mapper: {}

fabric_setting:
  choose_point_mapper: {} 