from caasm_adapter.util.client import FetchRestfulClient


class AsiaInfoESMClient(FetchRestfulClient):
    def build_request_header(self, *args, **kwargs):
        return {"Referer": self.referer}

    @property
    def referer(self):
        return self.build_url(self.address, "login")

    @property
    def data_key_name(self):
        return ""

    @property
    def suc_flag(self):
        return ""

    @property
    def flag_key_name(self):
        return ""
