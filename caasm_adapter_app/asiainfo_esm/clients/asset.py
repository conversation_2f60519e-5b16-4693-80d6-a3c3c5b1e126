from asiainfo_esm.clients.base import AsiaInfoESMClient


class AsiaInfoESMTerminalListClient(AsiaInfoESMClient):
    METHOD = "get"
    URL = "/web_console/client/client_management"

    def build_request_params(self, page_index, page_size):
        return {
            "page": page_index,
            "page_size": page_size,
            "group_id": 0,
            "with_group": 1,
        }

    @property
    def data_key_name(self):
        return "data.client_info"

    @property
    def suc_flag(self):
        return 0

    @property
    def flag_key_name(self):
        return "error_code"
