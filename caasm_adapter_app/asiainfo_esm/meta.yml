name: "asiainfo_esm"
display_name: "亚信信创防病毒"
description: "针对安全威胁升级变化，为用户提供的新一代国产终端系统安全管理平台，是亚信安全终端安全一体化方案中的核心产品"
type: "终端防护"
company: "zsbank"
logo: "asiainfo_esm.png"
version: "v0.1"
priority: 1
properties:
  - 终端防护

connection:
  - name: address
    type: url
    required: true
    display_name: "地址"
    description: "地址信息"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'

  - name: username
    type: string
    required: true
    display_name: "用户名"
    description: "用户名称"
    validate_rules:
      - name: length
        error_hint: "用户名长度必须大于等于2且小于等于100"
        setting:
          min: 2
          max: 100

  - name: password
    type: password
    required: true
    display_name: "密码"
    description: "密码信息"
    validate_rules:
      - name: length
        error_hint: "密码长度必须大于等于1且小于等于100"
        setting:
          min: 1
          max: 100


fetch_setting:
  type: disposable
  point: "asiainfo_esm.fetch:find_asset"
  is_need_test_service: true
  test_auth_point: "asiainfo_esm.fetch:auth"
  condition_point: "asiainfo_esm.fetch:build_query_condition"
  size: 100
  fetch_type_mapper:
    asset:
      - terminal


merge_setting:
  size: 300
  setting: { }

convert_setting:
  size: 300
  before_executor_mapper: { }
  executor_mapper: { }


fabric_setting:
  choose_point_mapper:
    asset: "asiainfo_esm.fabric:choose_new_record"