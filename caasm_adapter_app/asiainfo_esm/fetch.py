from caasm_adapter.sdk.adapter_fetch import fetch_sdk
from asiainfo_esm.manage import ZsBankESMManager


def find_asset(connection, fetch_type, session=None, page_index=0, page_size=100, **kwargs):
    records = _manager(connection, session).find(page_index + 1, page_size)
    result = fetch_sdk.build_asset(records, fetch_type)
    return fetch_sdk.return_success(result)


def auth(connection, session=None):
    _manager(connection, session).auth()


def build_query_condition(connection, session, **kwargs):
    _manager(connection, session).auth()
    return {}


def _manager(connection, session):
    return ZsBankESMManager(connection, session)
