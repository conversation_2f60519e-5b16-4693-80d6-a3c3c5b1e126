name: "gxt_zcwxgl"
display_name: "观星台资产威胁管理平台"
description: "数字观星是国内数字资产威胁管理运营服务提供商。基于海量威胁情报数据，帮您看清资产，看明威胁，看懂安全。。"
type: "互联网测绘"
company: "观星台"
logo: "gxt.png"
version: "v0.1"
priority: 1
properties:
  - "互联网测绘"

connection:
  - name: address
    type: url
    required: true
    display_name: "请求地址"
    description: "请求地址"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'

  - name: username
    type: string
    required: true
    display_name: "用户名"
    description: "用户名"
    validate_rules:
      - name: length
        error_hint: "用户名格式无效。长度最小不得小于2，最大不得大于100"
        setting:
          min: 2
          max: 100

  - name: password
    type: password
    required: true
    display_name: "密码"
    description: "密码"
    validate_rules:
      - name: length
        error_hint: "密码格式无效。长度最小不得小于6，最大不得大于200"
        setting:
          min: 6
          max: 200

  - name: private_key
    type: password
    required: false
    display_name: "密钥"
    description: "密钥，见产品API说明文档"
    validate_rules:
      - name: length
        error_hint: "密钥格式无效。长度最小不得小于6，最大不得大于1000"
        setting:
          min: 6
          max: 1000


fetch_setting:
  fetch_type_mapper:
    asset:
      - internet_ip
      - port_server
      - business
      - domain_name
  count_point: "gxt_zcwxgl.fetch:get_count"
  mode: "compute_page"
  point: "gxt_zcwxgl.fetch:find_asset"
  is_need_test_service: true
  test_auth_point: "gxt_zcwxgl.fetch:get_auth_connection"
  size: 200


merge_setting:
  size: 200
  setting: { }

convert_setting:
  size: 200
  before_executor_mapper: { }
  executor_mapper: { }