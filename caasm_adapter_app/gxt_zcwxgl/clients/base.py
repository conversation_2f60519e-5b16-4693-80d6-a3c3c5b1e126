from caasm_adapter.util.client import FetchJsonResultClient


class SzgxAssetBaseClient(FetchJsonResultClient):
    def __init__(self, connection, session=None, token=None):
        super(SzgxAssetBaseClient, self).__init__(connection, session)
        self._token = token

    def build_request_header(self, *args, **kwargs):
        headers = {}
        if self._token:
            headers["Authorization"] = f"Bearer {self._token}"
        return headers

    @property
    def data_key_name(self):
        return "data"

    @property
    def suc_flag(self) -> object:
        return 0

    @property
    def flag_key_name(self):
        return "code"
