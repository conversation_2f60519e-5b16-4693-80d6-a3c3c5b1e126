import base64
import io

from Crypto.Cipher import PKCS1_v1_5
from Crypto.PublicKey import RSA

from gxt_zcwxgl.clients.base import SzgxAssetBaseClient


class SzgxAssetAuthClient(SzgxAssetBaseClient):
    URL = "/tam/user/login"
    METHOD = "POST"

    KEY_ALGORITHM = "RSA"
    MAX_ENCRYPT_BLOCK = 245

    @property
    def username(self):
        return self.connection.get("username")

    @property
    def password(self):
        return self.connection.get("password")

    @property
    def private_key(self):
        return self.connection.get("private_key") or self._default_key

    _default_key = "MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBALwcyvYIGmhk+be320JWWsq1OYjiM0lzv8eHGMgSIOMLxzM/g9X7jguNe8thxJXR/CLqcTgsfZzk8E8Sc9+qnSDxNl5f5tga93vRxd5713zAeAGqLiTQnRffdzRmdbsmu5+0/K8mj056VhKh8FdBNzAj7e4iX9i+uBBG/oDmZbTVAgMBAAECgYEAmgNU5NTDkj9B+Pnt6UU8doSjw3+3j+bV2K2yS3QUOvAUus/Ax7x6ktjWxzCXvDY9IfUil2RNv9vtKEAqYLCWjc+lf8PV/yH1b7NEgyeAPBXtAJRoOnmYL2bdPW92kP9KgxJruF6Dz/C5AmMOncsvq8ABD+9Darn4p8dwj2ZC4O0CQQDf/AHmZsQokEItfCy4mHS9UbxbfIhEUv1ApPh/+Sr7NkJkHWYCtBQo+8jKO6zurAZQgWBPD1XX2UE4R+VIiZazAkEA1wAqtMvGhccyRZr+6kpkpDIa8+9jOE+nGUzqTDvgCID6as8AzOONFVVK6m/UUqkhcJ8Qu1pF36BGojy5BX2KVwJBAJSFpbji0hXXupowqfLp3RcgmNbNWAp+QUJZYhJx5cdYbmO2fssyH+AhPT6knYJR/YnqkDM8hv6vKCkqu2YDHjMCQAOA8TE5EOclM+CGghj3VWSHnIDVKdzFD4gOBNNxNlltIKeU8AJmwunSFgJ0CBXAw9a+ANvMwM7AIeaK7sj0HskCQAvxfDCq7gaNx+pfu0FHG8Gix08A/A6foggBl1fVu+L9sr9ZuOQ3HbXnl28F9ewuB9xdjnLUDjp7W7U0pB+vKoQ="

    def build_request_json(self, *args, **kwargs):
        username = self.encrypt_by_private_key(self.username, self.private_key)
        password = self.encrypt_by_private_key(self.password, self.private_key)
        return {"username": username, "password": password}

    def encrypt_by_private_key(self, data, private_key):
        key_bytes = base64.b64decode(private_key)
        rsa_key = RSA.import_key(key_bytes)
        cipher = PKCS1_v1_5.new(rsa_key)

        if isinstance(data, str):
            data = data.encode("utf-8")  # 将输入数据转换为字节类型

        input_len = len(data)
        out = io.BytesIO()
        offset = 0
        i = 0
        # 对数据进行分段加密
        while input_len - offset > 0:
            if input_len - offset > self.MAX_ENCRYPT_BLOCK:
                cache = cipher.encrypt(data[offset : offset + self.MAX_ENCRYPT_BLOCK])
            else:
                cache = cipher.encrypt(data[offset:])

            out.write(cache)
            i += 1
            offset = i * self.MAX_ENCRYPT_BLOCK

        encrypted_data = out.getvalue()
        out.close()

        # 将字节类型的加密结果转换为Base64编码的字符串
        encrypted_str = base64.b64encode(encrypted_data).decode("utf-8")
        return encrypted_str

    @property
    def data_key_name(self):
        return "data.token"
