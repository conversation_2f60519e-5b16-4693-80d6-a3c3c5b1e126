from caasm_adapter.sdk.adapter_fetch import fetch_sdk
from gxt_zcwxgl.manage import StarObservatoryZCWLMLManager


def find_asset(connection, fetch_type, page_index=0, page_size=1, session=None, **kwargs):
    records = _build_manager(connection, session).find(fetch_type, page_index, page_size)
    result = fetch_sdk.build_asset(records, fetch_type)
    return fetch_sdk.return_success(result)


def get_auth_connection(connection, session):
    _build_manager(connection, session)


def _build_manager(connection, session):
    return StarObservatoryZCWLMLManager(connection, session)


def get_count(connection, fetch_type=None, session=None, **kwargs):
    return _build_manager(connection, session).get_count(fetch_type)
