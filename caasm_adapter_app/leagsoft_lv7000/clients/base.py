import json

from caasm_adapter.util.client import FetchJsonResultClient


class LeagSoftLV7000BaseClient(FetchJsonResultClient):
    def __init__(self, connection, session=None, condition=None):
        super(LeagSoftLV7000BaseClient, self).__init__(connection, session)
        self._condition = condition

    def build_request_params(self, *args, **kwargs):
        return kwargs

    def build_request_header(self, *args, **kwargs):
        headers = super(LeagSoftLV7000BaseClient, self).build_request_header(*args, **kwargs)
        headers = headers if isinstance(headers, dict) else {}
        headers.update({"Content-Type": "text/plain; charset=utf-8"})
        return headers

    def parse_response(self, response, *args, **kwargs):
        content = response.content
        try:
            connect_str = content.decode("utf-8")
            connect_str = (
                connect_str.replace("\\r", "")
                .replace("null", "null")
                .replace("\\", "\\\\")
                .replace(" null", "null")
                .replace("\n", "\\n")
                .replace("\t", "\\t")
                .replace("\x0D", "\\r")
                .replace("\x00", "\\u0000")
            )
            result = json.loads(connect_str)
        except Exception as e:
            return self.parse_error_handle({}, *args, **kwargs)
        else:
            return result

    @property
    def data_key_name(self):
        return "rows"

    @property
    def flag_key_name(self):
        return "status"

    @property
    def suc_flag(self):
        return "SUCCESS"
