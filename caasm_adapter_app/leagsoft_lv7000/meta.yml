name: leagsoft_lv7000
display_name: "联软IT安全运维管理系统"
description: "联软IT安全运维管理软件，集网络准入控制、终端安全管理、BYOD设备管理、杀毒管理、服务器安全管理、数据防泄密、反APT攻击等系统于一体，通过一个平台，统一框架，数据集中，实现更强更智能的安全保护，减轻安全管理负担，降低采购和维护成本。"
type: "终端防护"
company: "深圳市联软科技股份有限公司"
logo: "lianruan.png"
version: "v0.1"
priority: 100
properties:
  - Agent
  - 终端防护

connection:
  - name: address
    type: url
    required: true
    display_name: "请求地址"
    description: "联软API调用请求地址"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http开头，端口为30098"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'

  - name: username
    type: string
    required: true
    display_name: "用户名称"
    description: "联软系统中的用户信息"
    validate_rules:
      - name: length
        error_hint: "用户密码格式无效。长度最小不得小于2，最大不得大于100"
        setting:
          min: 2
          max: 100

  - name: password
    type: password
    required: true
    display_name: "用户密码"
    description: "联软系统中的用户密码"
    validate_rules:
      - name: length
        error_hint: "用户密码格式无效。长度最小不得小于6，最大不得大于200"
        setting:
          min: 6
          max: 200


fetch_setting:
  point: "leagsoft_lv7000.fetch:find_asset"
  size: 100
  test_auth_point: "leagsoft_lv7000.fetch:get_auth_connection"
  condition_point: "leagsoft_lv7000.fetch:build_query_condition"
  is_need_test_service: true
  fetch_type_mapper:
    asset:
      - terminal
      - software
  cleaner_mapper:
    asset:
      terminal:
        - "leagsoft_lv7000.cleaners.asset_cleaner:AssetCleaner"

merge_setting:
  size: 1000
  setting: { }

convert_setting:
  size: 1000
  before_executor_mapper: { }
  executor_mapper: { }