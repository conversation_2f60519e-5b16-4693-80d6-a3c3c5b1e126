from caasm_adapter.sdk.adapter_fetch import fetch_sdk
from leagsoft_lv7000.manage import LeagsoftLv7000Manager, FetchType


def find_asset(connection, fetch_type, page_index=0, page_size=1, condition=None, **kwargs):
    session = kwargs.get("session")

    data = LeagsoftLv7000Manager(connection, session=session, condition=condition).find(
        fetch_type, page_index, page_size
    )
    result = fetch_sdk.build_asset(data, fetch_type)
    return fetch_sdk.return_success(result)


def build_query_condition(connection, session, fetch_type):
    return {"terminal": {"file": None, "status": False}}


def get_auth_connection(connection, session, condition=None):
    LeagsoftLv7000Manager(connection, session=session, condition=condition).auth()
