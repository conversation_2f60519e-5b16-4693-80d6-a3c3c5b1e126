name: "mcafee_epolicy_orchestrator"
display_name: "迈克菲ePolicy Orchestrator"
description: "McAfee ePolicy Orchestrator (ePO) 软件提供了强大的工作流功能，可以显著提高管理员的效率，让他们快速制定和部署安全措施，并及时响应出现的事件和问题。McAfee ePO (ePolicy Orchestrator) 是 McAfee Security Management Platform 的关键组件，也是唯一能够提供终端、网络和数据安全统一管理的企业级软件。凭借可有效缩短事件响应时间的端到端监控和强大的自动化功能，McAfee ePO 软件能够显著增强保护、降低风险和安全管理的成本及复杂性。"
type: "终端防护"
company: "迈克菲"
logo: "mcafee_epolicy_orchestrator.webp"
version: "v0.1"
priority: 1
properties:
  - "终端防护"

connection:
  - name: username
    type: string
    required: true
    display_name: "用户名"
    description: "用户名称"
    validate_rules:
      - name: length
        error_hint: "用户名长度必须大于等于2且小于等于100"
        setting:
          min: 2
          max: 100

  - name: password
    type: password
    required: true
    display_name: "密码"
    description: "密码信息"
    validate_rules:
      - name: length
        error_hint: "密码长度必须大于等于6且小于等于100"
        setting:
          min: 6
          max: 100

  - name: address
    type: url
    required: true
    display_name: "地址"
    description: "地址信息"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'


fetch_setting:
  type: disposable
  point: "mcafee_epolicy_orchestrator.fetch:find_asset"
  size: 1000
  fetch_type_mapper:
    asset:
      - asset

fabric_setting:
  choose_point_mapper:
    asset: "mcafee_epolicy_orchestrator.fabric:choose_new_record"

merge_setting:
  size: 1000
  setting: { }

convert_setting:
  size: 1000
  before_executor_mapper: { }
  executor_mapper: { }