import logging
import time

from selenium.webdriver.common.by import By

from caasm_adapter.sdk.adapter_fetch import fetch_sdk
from caasm_tool.util import build_url
from vrv_webservice_web.manage import VRVWebServiceWebManager

log = logging.getLogger()


def get_vrv_service_web_cookie(connection, condition):
    driver = condition.get("driver")
    driver.get(build_url(connection.get("address"), "/CEMS/welcomeAction_main.do"))
    size = len(driver.requests)
    for i in range(0, size - 1):
        if "<PERSON><PERSON>" not in driver.requests[size - 1 - i].headers:
            continue
        return driver.requests[size - 1 - i].headers["Cookie"]


def get_asset_count(connection, session=None, condition=None, **kwargs):
    cookie = get_vrv_service_web_cookie(connection, condition)
    count = VRVWebServiceWebManager(connection, session=session, cookie=cookie).get_asset_count()
    return count


def find_asset(connection, fetch_type, page_index, page_size, session=None, condition=None, **kwargs):
    page_index = page_index + 1
    cookie = get_vrv_service_web_cookie(connection, condition)
    records = VRVWebServiceWebManager(connection, session=session, cookie=cookie).find_asset(
        fetch_type, page_index, page_size
    )
    result = fetch_sdk.build_asset(records, fetch_type)

    return fetch_sdk.return_success(result)


def build_query_condition(connection, session=None, fetch_type=None):
    driver = fetch_sdk.init_driver()
    password = connection.get("password")
    username = connection.get("username")
    login_url = "/CEMS/welcomeAction_welcome.do?request_locale=zh_CN"
    driver.get(build_url(connection.get("address"), login_url))
    time.sleep(5)
    account_ele = driver.find_element(By.ID, "account")
    account_ele.send_keys(username)
    password_ele = driver.find_element(By.XPATH, '//input[@id="password"]')
    password_ele.send_keys(password)
    button_ele = driver.find_element(By.ID, "login_button")
    button_ele.click()
    time.sleep(5)
    return {"driver": driver}


def free_condition(connection=None, session=None, condition=None):
    driver = condition.get("driver")
    if driver:
        driver.quit()


def get_auth_connection(connection=None, session=None):
    condition = {}
    try:
        condition = build_query_condition(connection=connection, session=session)
        cookie = get_vrv_service_web_cookie(connection, condition)
        VRVWebServiceWebManager(connection, session=session, cookie=cookie).get_asset_count()
    except Exception as e:
        driver = condition.get("driver")
        if driver:
            driver.quit()
        raise e
