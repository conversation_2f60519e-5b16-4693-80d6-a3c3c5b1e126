from caasm_adapter.util.client import FetchJsonResultClient


class VRVWebServiceWebBaseClient(FetchJsonResultClient):
    def __init__(self, connection, session=None, cookie=None):
        super(VRVWebServiceWebBaseClient, self).__init__(connection, session)
        self.cookie = cookie

    def build_request_header(self, *args, **kwargs):
        header = {
            "Origin": self.address,
            "Cookie": self.cookie,
            "Sec-Fetch-Site": "same-origin",
            "sec-ch-ua-platform": "macOS",
            "sec-ch-ua-mobile": "?0",
            "X-Requested-With": "XMLHttpRequest",
        }
        return header

    @property
    def data_key_name(self):
        return ""

    @property
    def suc_flag(self):
        return ""

    @property
    def flag_key_name(self):
        return ""
