from caasm_tool.util import build_url
from vrv_webservice_web.clients.base import VRVWebServiceWebBaseClient


class VRVWebServiceAllDepartmentClient(VRVWebServiceWebBaseClient):
    URL = "/CEMS/mgr/organizationAction_getOrgJsonTree.do?id=org"
    METHOD = "get"

    def build_request_header(self, *args, **kwargs):
        header = super(VRVWebServiceAllDepartmentClient, self).build_request_header()
        REFERER_URL = "h/CEMS/assets/deviceInfoQueryAction_listUI.do?Operate=fashionhomeGA_totalNumberTerminals&cftk=83b1ccc9a16cc41b3d7a6e37f850754c"
        header["Referer"] = build_url(self.address, REFERER_URL)
        return header


class VRVWebServiceDepartmentClient(VRVWebServiceWebBaseClient):
    URL = "/CEMS/mgr/organizationAction_getOrgJsonTree.do?id={}"
    METHOD = "get"

    def build_request_url(self, department_id=None):
        url = super(VRVWebServiceDepartmentClient, self).build_request_url()
        return url.format(department_id)

    def build_request_header(self, *args, **kwargs):
        header = super(VRVWebServiceDepartmentClient, self).build_request_header()
        REFERER_URL = "/CEMS/assets/deviceInfoQueryAction_listUI.do?resourceId=f1eddedcac334c51b9e4079ce5940096&cftk=b411b7be1f2c098922151f07fffb6915"
        header["Referer"] = build_url(self.address, REFERER_URL)
        return header
