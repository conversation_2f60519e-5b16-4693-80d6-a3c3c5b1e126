{"canvas": {"nodes": [{"id": "root", "field": "根节点", "path": "root", "datatype": "object", "type": "asset", "level": 0, "sub_fields": [], "x": 0, "y": 0, "asset_type": "asset", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 100, "id": "root.protectState", "level": 1, "path": "protectState", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "protectState", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 200, "id": "root.antivirusSoftVersion", "level": 1, "path": "antivirusSoftVersion", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "antivirusSoftVersion", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 300, "id": "root.organizationId", "level": 1, "path": "organizationId", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "organizationId", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 400, "id": "root.tel", "level": 1, "path": "tel", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "tel", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 500, "id": "root.id", "level": 1, "path": "id", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "id", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 600, "id": "root.osLanguage", "level": 1, "path": "osLanguage", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "osLanguage", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 700, "id": "root.registerTime", "level": 1, "path": "registerTime", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "registerTime", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 800, "id": "root.ip", "level": 1, "path": "ip", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "ip", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 900, "id": "root.groupName", "level": 1, "path": "groupName", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "groupName", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 1000, "id": "root.pkiUnit", "level": 1, "path": "pkiUnit", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "pkiUnit", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 1100, "id": "root.userAccount", "level": 1, "path": "userAccount", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "userAccount", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 1200, "id": "root.serviceNo", "level": 1, "path": "serviceNo", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "serviceNo", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 1300, "id": "root.reportTime", "level": 1, "path": "reportTime", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "reportTime", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 1400, "id": "root.softVersion", "level": 1, "path": "softVersion", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "softVersion", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 1500, "id": "root.agentType", "level": 1, "path": "agentType", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "agentType", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 1600, "id": "root.osDescription", "level": 1, "path": "osDescription", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "osDescription", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 1700, "id": "root.pkiName", "level": 1, "path": "pkiName", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "pkiName", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 1800, "id": "root.clientName", "level": 1, "path": "clientName", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "clientName", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 1900, "id": "root.patchState", "level": 1, "path": "patchState", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "patchState", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 2000, "id": "root.mac", "level": 1, "path": "mac", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "mac", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 2100, "id": "root.osVersion", "level": 1, "path": "osVersion", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "osVersion", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 2200, "id": "root.email", "level": 1, "path": "email", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "email", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 2300, "id": "root.holder<PERSON>ame", "level": 1, "path": "<PERSON><PERSON><PERSON>", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "<PERSON><PERSON><PERSON>", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 2400, "id": "root.clientId", "level": 1, "path": "clientId", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "clientId", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 2500, "id": "root.lastActiveTime", "level": 1, "path": "lastActiveTime", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "lastActiveTime", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 2600, "id": "root.updateTime", "level": 1, "path": "updateTime", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "updateTime", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 2700, "id": "root.osName", "level": 1, "path": "osName", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "osName", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 2800, "id": "root.communicateIp", "level": 1, "path": "communicateIp", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "communicateIp", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 2900, "id": "root.diskSerial", "level": 1, "path": "diskSerial", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "diskSerial", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 3000, "id": "root.cpuHz", "level": 1, "path": "cpuHz", "sub_fields": [], "type": "asset", "datatype": "float", "asset_type": "asset", "field": "cpuHz", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 3100, "id": "root.ipType", "level": 1, "path": "ipType", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "ipType", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 3200, "id": "root.isMutiOs", "level": 1, "path": "isMutiOs", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "isMutiOs", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 3300, "id": "root.ksvVersion", "level": 1, "path": "ksvVersion", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "ksvVersion", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 3400, "id": "root.ipNumber", "level": 1, "path": "ipNumber", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "ipNumber", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 3500, "id": "root.loginDomainName", "level": 1, "path": "loginDomainName", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "loginDomainName", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 3600, "id": "root.antivirusSoftwareName", "level": 1, "path": "antivirusSoftwareName", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "antivirusSoftwareName", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 3700, "id": "root.clientVersion", "level": 1, "path": "clientVersion", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "clientVersion", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 3800, "id": "root.osSerial", "level": 1, "path": "osSerial", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "osSerial", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 3900, "id": "root.diskSize", "level": 1, "path": "diskSize", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "diskSize", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 4000, "id": "root.clientSign", "level": 1, "path": "clientSign", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "clientSign", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 4100, "id": "root.devOnlyId", "level": 1, "path": "devOnlyId", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "devOnlyId", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 4200, "id": "root.switchPort", "level": 1, "path": "switchPort", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "switchPort", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 4300, "id": "root.computerModel", "level": 1, "path": "computerModel", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "computerModel", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 4400, "id": "root.orgName", "level": 1, "path": "orgName", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "orgName", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 4500, "id": "root.deviceTypeName", "level": 1, "path": "deviceTypeName", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "deviceTypeName", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 4600, "id": "root.osInstall", "level": 1, "path": "osInstall", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "osInstall", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 4700, "id": "root.isVm", "level": 1, "path": "isVm", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "isVm", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 4800, "id": "root.computerManufacturer", "level": 1, "path": "computerManufacturer", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "computerManufacturer", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 4900, "id": "root.name", "level": 1, "path": "name", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "name", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 5000, "id": "root.assetStatus", "level": 1, "path": "assetStatus", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "assetStatus", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 5100, "id": "root.ksvDesInfo", "level": 1, "path": "ksvDesInfo", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "ksvDesInfo", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 5200, "id": "root.cpuType", "level": 1, "path": "cpuType", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "cpuType", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 5300, "id": "root.isOpened", "level": 1, "path": "isOpened", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "isOpened", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 5400, "id": "root.organizationCodeUI", "level": 1, "path": "organizationCodeUI", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "organizationCodeUI", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 5500, "id": "root.roamState", "level": 1, "path": "roamState", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "roamState", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 5600, "id": "root.description", "level": 1, "path": "description", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "description", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 5700, "id": "root.<PERSON><PERSON><PERSON><PERSON>d", "level": 1, "path": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 5800, "id": "root.pkiId", "level": 1, "path": "pkiId", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "pkiId", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 5900, "id": "root.ipv6", "level": 1, "path": "ipv6", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "ipv6", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 6000, "id": "root.deleteState", "level": 1, "path": "deleteState", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "deleteState", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 6100, "id": "root.accessNetworkState", "level": 1, "path": "accessNetworkState", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "accessNetworkState", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 6200, "id": "root.approvalState", "level": 1, "path": "approvalState", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "approvalState", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 6300, "id": "root.userName", "level": 1, "path": "userName", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "userName", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 6400, "id": "root.memorySize", "level": 1, "path": "memorySize", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "memorySize", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 6500, "id": "root.antivirusSoftware", "level": 1, "path": "antivirusSoftware", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "antivirusSoftware", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 6600, "id": "root.isControl", "level": 1, "path": "isControl", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "isControl", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 6700, "id": "root.regState", "level": 1, "path": "regState", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "regState", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 6800, "id": "root.networkTypeAgentPackType", "level": 1, "path": "networkTypeAgentPackType", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "networkTypeAgentPackType", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 6900, "id": "root.networkTypeTypeName", "level": 1, "path": "networkTypeTypeName", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "networkTypeTypeName", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 7000, "id": "root.software_info", "level": 1, "path": "software_info", "sub_fields": [{"type": "object", "sub_fields": [{"field": "installTime", "path": "software_info.element.installTime", "type": "string"}, {"field": "sid", "path": "software_info.element.sid", "type": "string"}, {"field": "softSize", "path": "software_info.element.softSize", "type": "string"}, {"field": "company", "path": "software_info.element.company", "type": "string"}, {"field": "id", "path": "software_info.element.id", "type": "string"}, {"field": "authType", "path": "software_info.element.authType", "type": "integer"}, {"field": "wow64", "path": "software_info.element.wow64", "type": "integer"}, {"field": "devOnlyId", "path": "software_info.element.devOnlyId", "type": "string"}, {"field": "devRegCodeUI", "path": "software_info.element.devRegCodeUI", "type": "string"}, {"field": "uninstallPath", "path": "software_info.element.uninstallPath", "type": "string"}, {"field": "<PERSON>v<PERSON><PERSON><PERSON><PERSON>", "path": "software_info.element.devRegPath", "type": "string"}, {"field": "installPath", "path": "software_info.element.installPath", "type": "string"}, {"field": "amount", "path": "software_info.element.amount", "type": "integer"}, {"field": "devRegId", "path": "software_info.element.devRegId", "type": "string"}, {"field": "regguid", "path": "software_info.element.regguid", "type": "string"}, {"field": "version", "path": "software_info.element.version", "type": "string"}, {"field": "devOrgCodeUI", "path": "software_info.element.devOrgCodeUI", "type": "string"}, {"field": "authResult", "path": "software_info.element.authResult", "type": "integer"}, {"field": "name", "path": "software_info.element.name", "type": "string"}, {"field": "devOrgId", "path": "software_info.element.devOrgId", "type": "string"}], "field": "element", "path": "software_info.element"}], "type": "asset", "datatype": "list", "asset_type": "asset", "field": "software_info", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 7100, "id": "root.account_info", "level": 1, "path": "account_info", "sub_fields": [{"type": "object", "sub_fields": [{"field": "specialAccount", "path": "account_info.element.specialAccount", "type": "integer"}, {"field": "accountComment", "path": "account_info.element.accountComment", "type": "string"}, {"field": "accountName", "path": "account_info.element.accountName", "type": "string"}, {"field": "accountEnable", "path": "account_info.element.accountEnable", "type": "integer"}, {"field": "displayName", "path": "account_info.element.displayName", "type": "string"}, {"field": "accountType", "path": "account_info.element.accountType", "type": "integer"}, {"field": "ip", "path": "account_info.element.ip", "type": "string"}, {"field": "accountGroup", "path": "account_info.element.accountGroup", "type": "string"}, {"field": "optType", "path": "account_info.element.optType", "type": "integer"}, {"field": "name", "path": "account_info.element.name", "type": "string"}, {"field": "id", "path": "account_info.element.id", "type": "string"}, {"field": "accountSid", "path": "account_info.element.accountSid", "type": "string"}, {"field": "devOnlyId", "path": "account_info.element.devOnlyId", "type": "string"}], "field": "element", "path": "account_info.element"}], "type": "asset", "datatype": "list", "asset_type": "asset", "field": "account_info", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.name.LykGgCnXj", "x": 545.*********, "y": 4897.********, "order": 1, "level": 2, "source_type": "string", "path": "root.name.LykGgCnXj", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "asset_base.name"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "9g8yr6ep8", "display_name": "资产基础", "description": "asset_base.name", "x": 745.*********, "y": 4897.********, "label": "资产基础-资产名称", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.name.a19tLrCcq", "x": 550.*********, "y": 4976.********, "order": 2, "level": 2, "source_type": "string", "path": "root.name.a19tLrCcq", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "computer.host_name"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "kOrOvytNE", "display_name": "计算机", "description": "computer.host_name", "x": 750.*********, "y": 4976.********, "label": "计算机-主机名称", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.name.iyH0sof2c", "x": 556.*********, "y": 4794.********, "order": 3, "level": 2, "source_type": "string", "path": "root.name.iyH0sof2c", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "computer.name"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "Zz9H22RMf", "display_name": "计算机", "description": "computer.name", "x": 756.*********, "y": 4794.********, "label": "计算机-名称", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.szXRLk6dH", "x": 301.*********00017, "y": -91.12109375, "order": 5, "level": 1, "source_type": "object", "path": "root.szXRLk6dH", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "生成对象数组", "description": "生成对象数组", "field": "生成对象数组", "input": {"asset_web_type": "add_list_object", "description": "生成对象数组", "size": "1", "field": "ips", "mapping": [{"name": "addr", "value": "${ip}"}, {"name": "mac", "value": "${mac}"}]}, "action_type": "add_list_object", "attrs": {"text": "生成对象数组"}}, {"type": "asset", "asset_type": "action", "id": "root.szXRLk6dH.TBHtnh0yd", "x": 588.*********0002, "y": -93.12109375, "order": 6, "level": 2, "path": "root.szXRLk6dH.TBHtnh0yd", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "network.ips"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "xRuO9KIKn", "display_name": "网络", "description": "network.ips", "x": 788.*********0002, "y": -93.12109375, "label": "网络-IP地址", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.name.meEStq5iz", "x": 545.*********, "y": 5061.********, "order": 7, "level": 2, "source_type": "string", "path": "root.name.meEStq5iz", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "asset_base.device.name"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "XDTOGi7Mi", "display_name": "资产基础", "description": "asset_base.device.name", "x": 745.*********, "y": 5061.********, "label": "资产基础-设备-主机名", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.isOpened.gpf39pPdc", "x": 566.*********, "y": 5294.878906249999, "order": 8, "level": 2, "source_type": "integer", "path": "root.isOpened.gpf39pPdc", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "翻译", "description": "用于字段值翻译，常见用于枚举字段", "field": "翻译", "input": {"asset_web_type": "translate", "description": "用于字段值翻译，常见用于枚举字段", "default": "0", "values": [{"name": "0", "value": "2"}, {"name": "1", "value": "1"}]}, "action_type": "translate", "attrs": {"text": "翻译"}}, {"type": "asset", "asset_type": "action", "id": "root.isOpened.gpf39pPdc.6ZiWNh_Ae", "x": 766.*********, "y": 5292.878906249999, "order": 9, "level": 3, "path": "root.isOpened.gpf39pPdc.6ZiWNh_Ae", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "asset_base.device.status"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "O4VQnsyFb", "display_name": "资产基础", "description": "asset_base.device.status", "x": 966.*********, "y": 5292.878906249999, "label": "资产基础-设备-状态", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 89.02500000000003, "y": 63.22500000000002, "id": "root.holderNameTel", "level": 1, "path": "holderNameTel", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "holderNameTel", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 1097.975, "y": 63.22500000000002, "id": "root.locationName", "level": 1, "path": "locationName", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "locationName", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.locationName.fKmM7FA3L", "x": 1334.*********, "y": 55.7578125, "order": 1, "level": 2, "source_type": "string", "path": "root.locationName.fKmM7FA3L", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "asset_base.location.detail"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "D-syGinX6", "display_name": "资产基础", "description": "asset_base.location.detail", "x": 1534.*********, "y": 55.7578125, "label": "资产基础-位置-详情", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.osDescription.vkJhGYw3o", "x": 612.*********, "y": 1592.********, "order": 2, "level": 2, "source_type": "string", "path": "root.osDescription.vkJhGYw3o", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "computer.os.full"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "zXLRhosGL", "display_name": "计算机", "description": "computer.os.full", "x": 812.*********, "y": 1592.********, "label": "计算机-操作系统-完整名称", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.osDescription.QUuKP_oyl", "x": 591.*********, "y": 1476.********, "order": 3, "level": 2, "source_type": "string", "path": "root.osDescription.QUuKP_oyl", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "computer.os.distribution"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "VEVx6Brko", "display_name": "计算机", "description": "computer.os.distribution", "x": 791.*********, "y": 1476.********, "label": "计算机-操作系统-发行版", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.osInstall.xi2Gc2U92", "x": 596.*********, "y": 4597.********, "order": 4, "level": 2, "source_type": "string", "path": "root.osInstall.xi2Gc2U92", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "computer.os.install_time"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "XW-AQssIu", "display_name": "计算机", "description": "computer.os.install_time", "x": 796.*********, "y": 4597.********, "label": "计算机-操作系统-安装时间", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.osVersion.qWK4vwJOQ", "x": 574.*********0001, "y": 2032.********, "order": 5, "level": 2, "source_type": "string", "path": "root.osVersion.qWK4vwJOQ", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "computer.os.version"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "bOi4-VQE6", "display_name": "计算机", "description": "computer.os.version", "x": 774.*********0001, "y": 2032.********, "label": "计算机-操作系统-发行版本", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.computerManufacturer.k-8NLMDv2", "x": 540.*********0002, "y": 4704.********0003, "order": 6, "level": 2, "source_type": "string", "path": "root.computerManufacturer.k-8NLMDv2", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "computer.os.vendor"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "7RCKsJ4wu", "display_name": "计算机", "description": "computer.os.vendor", "x": 740.*********0002, "y": 4704.********0003, "label": "计算机-操作系统-厂商", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.osLanguage.u8ss6wIyd", "x": 594.*********, "y": 596.********00002, "order": 7, "level": 2, "source_type": "string", "path": "root.osLanguage.u8ss6wIyd", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "computer.os.lang"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "pXQBhUkjt", "display_name": "计算机", "description": "computer.os.lang", "x": 794.*********, "y": 596.********00002, "label": "计算机-操作系统-语言", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.account_info.zGAhi6oCi", "x": 598.*********, "y": 7089.************, "order": 8, "level": 2, "source_type": "list", "path": "root.account_info.zGAhi6oCi", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "for循环", "description": "用于将数组进行循环操作", "field": "for循环", "input": {"asset_web_type": "for", "description": "用于将数组进行循环操作"}, "action_type": "for", "attrs": {"text": "for循环"}}, {"x": 798.*********, "y": 7189.************, "id": "root.account_info.zGAhi6oCi.element", "level": 3, "path": "account_info.element", "sub_fields": [{"field": "specialAccount", "path": "account_info.element.specialAccount", "type": "integer", "x": 998.*********, "y": 7289.************}, {"field": "accountComment", "path": "account_info.element.accountComment", "type": "string", "x": 998.*********, "y": 7389.************}, {"field": "accountName", "path": "account_info.element.accountName", "type": "string", "x": 998.*********, "y": 7489.************}, {"field": "accountEnable", "path": "account_info.element.accountEnable", "type": "integer", "x": 998.*********, "y": 7589.************}, {"field": "displayName", "path": "account_info.element.displayName", "type": "string", "x": 998.*********, "y": 7689.************}, {"field": "accountType", "path": "account_info.element.accountType", "type": "integer", "x": 998.*********, "y": 7789.************}, {"field": "ip", "path": "account_info.element.ip", "type": "string", "x": 998.*********, "y": 7889.************}, {"field": "accountGroup", "path": "account_info.element.accountGroup", "type": "string", "x": 998.*********, "y": 7989.************}, {"field": "optType", "path": "account_info.element.optType", "type": "integer", "x": 998.*********, "y": 8089.************}, {"field": "name", "path": "account_info.element.name", "type": "string", "x": 998.*********, "y": 8189.************}, {"field": "id", "path": "account_info.element.id", "type": "string", "x": 998.*********, "y": 8289.********}, {"field": "accountSid", "path": "account_info.element.accountSid", "type": "string", "x": 998.*********, "y": 8389.********}, {"field": "devOnlyId", "path": "account_info.element.devOnlyId", "type": "string", "x": 998.*********, "y": 8489.********}], "type": "asset", "datatype": "object", "asset_type": "asset", "field": "element", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 998.*********, "y": 7289.************, "id": "root.account_info.zGAhi6oCi.element.specialAccount", "level": 4, "path": "account_info.element.specialAccount", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "specialAccount", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 998.*********, "y": 7389.************, "id": "root.account_info.zGAhi6oCi.element.accountComment", "level": 4, "path": "account_info.element.accountComment", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "accountComment", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 998.*********, "y": 7489.************, "id": "root.account_info.zGAhi6oCi.element.accountName", "level": 4, "path": "account_info.element.accountName", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "accountName", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 998.*********, "y": 7589.************, "id": "root.account_info.zGAhi6oCi.element.accountEnable", "level": 4, "path": "account_info.element.accountEnable", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "accountEnable", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 998.*********, "y": 7689.************, "id": "root.account_info.zGAhi6oCi.element.displayName", "level": 4, "path": "account_info.element.displayName", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "displayName", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 998.*********, "y": 7789.************, "id": "root.account_info.zGAhi6oCi.element.accountType", "level": 4, "path": "account_info.element.accountType", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "accountType", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 998.*********, "y": 7889.************, "id": "root.account_info.zGAhi6oCi.element.ip", "level": 4, "path": "account_info.element.ip", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "ip", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 998.*********, "y": 7989.************, "id": "root.account_info.zGAhi6oCi.element.accountGroup", "level": 4, "path": "account_info.element.accountGroup", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "accountGroup", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 998.*********, "y": 8089.************, "id": "root.account_info.zGAhi6oCi.element.optType", "level": 4, "path": "account_info.element.optType", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "optType", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 998.*********, "y": 8189.************, "id": "root.account_info.zGAhi6oCi.element.name", "level": 4, "path": "account_info.element.name", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "name", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 998.*********, "y": 8289.********, "id": "root.account_info.zGAhi6oCi.element.id", "level": 4, "path": "account_info.element.id", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "id", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 998.*********, "y": 8389.********, "id": "root.account_info.zGAhi6oCi.element.accountSid", "level": 4, "path": "account_info.element.accountSid", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "accountSid", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 998.*********, "y": 8489.********, "id": "root.account_info.zGAhi6oCi.element.devOnlyId", "level": 4, "path": "account_info.element.devOnlyId", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "devOnlyId", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.account_info.zGAhi6oCi.BqVNE5s9b", "x": 1028.*********, "y": 7085.************, "order": 9, "level": 3, "path": "root.account_info.zGAhi6oCi.BqVNE5s9b", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "computer.accounts"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "_ANuq9bOY", "display_name": "计算机", "description": "computer.accounts", "x": 1228.*********, "y": 7085.************, "label": "计算机-账号", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.account_info.zGAhi6oCi.element.accountName.weNrIzYIf", "x": 1326.********00002, "y": 7492.************, "order": 10, "level": 5, "source_type": "string", "path": "root.account_info.zGAhi6oCi.element.accountName.weNrIzYIf", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "computer.accounts.name"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "RTK-gysJs", "display_name": "计算机", "description": "computer.accounts.name", "x": 1526.********00002, "y": 7492.************, "label": "计算机-账号-名称", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.account_info.zGAhi6oCi.element.accountGroup.4mZjBcmhP", "x": 1371.********00002, "y": 7988.************, "order": 11, "level": 5, "source_type": "string", "path": "root.account_info.zGAhi6oCi.element.accountGroup.4mZjBcmhP", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "将数据转化为列表类型", "description": "用于列表类型的转换，将非列表格式的数据转换为列表类型", "field": "将数据转化为列表类型", "input": {"asset_web_type": "list", "description": "用于列表类型的转换，将非列表格式的数据转换为列表类型"}, "action_type": "list", "attrs": {"text": "将数据转化为列表类型"}}, {"type": "asset", "asset_type": "action", "id": "root.account_info.zGAhi6oCi.element.accountGroup.4mZjBcmhP.eVxaLfNN0", "x": 1644.********00002, "y": 7988.************, "order": 12, "level": 6, "path": "root.account_info.zGAhi6oCi.element.accountGroup.4mZjBcmhP.eVxaLfNN0", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "computer.accounts.groups"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "Awp4bBgbw", "display_name": "计算机", "description": "computer.accounts.groups", "x": 1844.********00002, "y": 7988.************, "label": "计算机-账号-用户组", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.account_info.zGAhi6oCi.element.accountComment.KiMh6o4Oe", "x": 1403.********, "y": 7383.7578125, "order": 13, "level": 5, "source_type": "string", "path": "root.account_info.zGAhi6oCi.element.accountComment.KiMh6o4Oe", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "computer.accounts.description"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "UDkKJDC_u", "display_name": "计算机", "description": "computer.accounts.description", "x": 1603.********, "y": 7383.7578125, "label": "计算机-账号-描述", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.account_info.zGAhi6oCi.element.accountEnable.kZvHEgQK5", "x": 1322.********, "y": 7577.7578125, "order": 15, "level": 5, "source_type": "integer", "path": "root.account_info.zGAhi6oCi.element.accountEnable.kZvHEgQK5", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "翻译", "description": "用于字段值翻译，常见用于枚举字段", "field": "翻译", "input": {"asset_web_type": "translate", "description": "用于字段值翻译，常见用于枚举字段", "default": "999", "values": [{"name": "0", "value": "0"}, {"name": "1", "value": "2"}]}, "action_type": "translate", "attrs": {"text": "翻译"}}, {"type": "asset", "asset_type": "action", "id": "root.account_info.zGAhi6oCi.element.accountEnable.kZvHEgQK5.zgm3p7_bR", "x": 1545.********, "y": 7574.7578125, "order": 16, "level": 6, "path": "root.account_info.zGAhi6oCi.element.accountEnable.kZvHEgQK5.zgm3p7_bR", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "computer.accounts.interactive_login_type"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "xnsdb1bpY", "display_name": "计算机", "description": "computer.accounts.interactive_login_type", "x": 1745.********, "y": 7574.7578125, "label": "计算机-账号-交互登录方式", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.account_info.zGAhi6oCi.element.displayName.yMgtkPCW2", "x": 1291.********, "y": 7688.7578125, "order": 17, "level": 5, "source_type": "string", "path": "root.account_info.zGAhi6oCi.element.displayName.yMgtkPCW2", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "computer.accounts.full_name"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "vnuGZOQ_d", "display_name": "计算机", "description": "computer.accounts.full_name", "x": 1491.********, "y": 7688.7578125, "label": "计算机-账号-全名", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.software_info.uWUPaGyox", "x": 1749.********, "y": 6915.7578125, "order": 18, "level": 2, "source_type": "list", "path": "root.software_info.uWUPaGyox", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "for循环", "description": "用于将数组进行循环操作", "field": "for循环", "input": {"asset_web_type": "for", "description": "用于将数组进行循环操作"}, "action_type": "for", "attrs": {"text": "for循环"}}, {"x": 1949.********, "y": 7015.7578125, "id": "root.software_info.uWUPaGyox.element", "level": 3, "path": "software_info.element", "sub_fields": [{"field": "installTime", "path": "software_info.element.installTime", "type": "string", "x": 1646.********, "y": 7170.7578125}, {"field": "sid", "path": "software_info.element.sid", "type": "string", "x": 1646.********, "y": 7270.7578125}, {"field": "softSize", "path": "software_info.element.softSize", "type": "string", "x": 1646.********, "y": 7370.7578125}, {"field": "company", "path": "software_info.element.company", "type": "string", "x": 1646.********, "y": 7470.7578125}, {"field": "id", "path": "software_info.element.id", "type": "string", "x": 1646.********, "y": 7570.7578125}, {"field": "authType", "path": "software_info.element.authType", "type": "integer", "x": 1646.********, "y": 7670.7578125}, {"field": "wow64", "path": "software_info.element.wow64", "type": "integer", "x": 1646.********, "y": 7770.7578125}, {"field": "devOnlyId", "path": "software_info.element.devOnlyId", "type": "string", "x": 1646.********, "y": 7870.7578125}, {"field": "devRegCodeUI", "path": "software_info.element.devRegCodeUI", "type": "string", "x": 1646.********, "y": 7970.7578125}, {"field": "uninstallPath", "path": "software_info.element.uninstallPath", "type": "string", "x": 1646.********, "y": 8070.7578125}, {"field": "<PERSON>v<PERSON><PERSON><PERSON><PERSON>", "path": "software_info.element.devRegPath", "type": "string", "x": 1646.********, "y": 8170.7578125}, {"field": "installPath", "path": "software_info.element.installPath", "type": "string", "x": 1646.********, "y": 8270.7578125}, {"field": "amount", "path": "software_info.element.amount", "type": "integer", "x": 1646.********, "y": 8370.7578125}, {"field": "devRegId", "path": "software_info.element.devRegId", "type": "string", "x": 1646.********, "y": 8470.7578125}, {"field": "regguid", "path": "software_info.element.regguid", "type": "string", "x": 1646.********, "y": 8570.7578125}, {"field": "version", "path": "software_info.element.version", "type": "string", "x": 1646.********, "y": 8670.7578125}, {"field": "devOrgCodeUI", "path": "software_info.element.devOrgCodeUI", "type": "string", "x": 1646.********, "y": 8770.7578125}, {"field": "authResult", "path": "software_info.element.authResult", "type": "integer", "x": 1646.********, "y": 8870.7578125}, {"field": "name", "path": "software_info.element.name", "type": "string", "x": 1646.********, "y": 8970.7578125}, {"field": "devOrgId", "path": "software_info.element.devOrgId", "type": "string", "x": 1646.********, "y": 9070.7578125}], "type": "asset", "datatype": "object", "asset_type": "asset", "field": "element", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 2149.********, "y": 7115.7578125, "id": "root.software_info.uWUPaGyox.element.installTime", "level": 4, "path": "software_info.element.installTime", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "installTime", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 2149.********, "y": 7215.7578125, "id": "root.software_info.uWUPaGyox.element.sid", "level": 4, "path": "software_info.element.sid", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "sid", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 2149.********, "y": 7315.7578125, "id": "root.software_info.uWUPaGyox.element.softSize", "level": 4, "path": "software_info.element.softSize", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "softSize", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 2149.********, "y": 7415.7578125, "id": "root.software_info.uWUPaGyox.element.company", "level": 4, "path": "software_info.element.company", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "company", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 2149.********, "y": 7515.7578125, "id": "root.software_info.uWUPaGyox.element.id", "level": 4, "path": "software_info.element.id", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "id", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 2149.********, "y": 7615.7578125, "id": "root.software_info.uWUPaGyox.element.authType", "level": 4, "path": "software_info.element.authType", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "authType", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 2149.********, "y": 7715.7578125, "id": "root.software_info.uWUPaGyox.element.wow64", "level": 4, "path": "software_info.element.wow64", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "wow64", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 2149.********, "y": 7815.7578125, "id": "root.software_info.uWUPaGyox.element.devOnlyId", "level": 4, "path": "software_info.element.devOnlyId", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "devOnlyId", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 2149.********, "y": 7915.7578125, "id": "root.software_info.uWUPaGyox.element.devRegCodeUI", "level": 4, "path": "software_info.element.devRegCodeUI", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "devRegCodeUI", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 2149.********, "y": 8015.7578125, "id": "root.software_info.uWUPaGyox.element.uninstallPath", "level": 4, "path": "software_info.element.uninstallPath", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "uninstallPath", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 2149.********, "y": 8115.7578125, "id": "root.software_info.uWUPaGyox.element.devRegPath", "level": 4, "path": "software_info.element.devRegPath", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "<PERSON>v<PERSON><PERSON><PERSON><PERSON>", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 2149.********, "y": 8215.7578125, "id": "root.software_info.uWUPaGyox.element.installPath", "level": 4, "path": "software_info.element.installPath", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "installPath", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 2149.********, "y": 8315.7578125, "id": "root.software_info.uWUPaGyox.element.amount", "level": 4, "path": "software_info.element.amount", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "amount", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 2149.********, "y": 8415.7578125, "id": "root.software_info.uWUPaGyox.element.devRegId", "level": 4, "path": "software_info.element.devRegId", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "devRegId", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 2149.********, "y": 8515.7578125, "id": "root.software_info.uWUPaGyox.element.regguid", "level": 4, "path": "software_info.element.regguid", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "regguid", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 2149.********, "y": 8615.7578125, "id": "root.software_info.uWUPaGyox.element.version", "level": 4, "path": "software_info.element.version", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "version", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 2149.********, "y": 8715.7578125, "id": "root.software_info.uWUPaGyox.element.devOrgCodeUI", "level": 4, "path": "software_info.element.devOrgCodeUI", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "devOrgCodeUI", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 2149.********, "y": 8815.7578125, "id": "root.software_info.uWUPaGyox.element.authResult", "level": 4, "path": "software_info.element.authResult", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "authResult", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 2149.********, "y": 8915.7578125, "id": "root.software_info.uWUPaGyox.element.name", "level": 4, "path": "software_info.element.name", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "name", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 2149.********, "y": 9015.7578125, "id": "root.software_info.uWUPaGyox.element.devOrgId", "level": 4, "path": "software_info.element.devOrgId", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "devOrgId", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.software_info.uWUPaGyox.element.installTime.SCNlFnILv", "x": 2465.*********003, "y": 7116.636718750002, "order": 19, "level": 5, "source_type": "string", "path": "root.software_info.uWUPaGyox.element.installTime.SCNlFnILv", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "字段值相加", "description": "用于字段值相加，可指定目标字段，并将结果赋值目标字段，目前支持数字、数组、字符串、字典类型相加", "field": "字段值相加", "input": {"asset_web_type": "add_up", "description": "用于字段值相加，可指定目标字段，并将结果赋值目标字段，目前支持数字、数组、字符串、字典类型相加", "values": ["${installTime} 00:00:00"], "dst_field": "aa"}, "action_type": "add_up", "attrs": {"text": "字段值相加"}}, {"type": "asset", "asset_type": "action", "id": "root.software_info.uWUPaGyox.element.installTime.SCNlFnILv.B9vDdvgTw", "x": 2747.*********0027, "y": 7114.636718750002, "order": 20, "level": 6, "path": "root.software_info.uWUPaGyox.element.installTime.SCNlFnILv.B9vDdvgTw", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "computer.softwares.install_time"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"type": "asset", "asset_type": "action", "id": "root.software_info.uWUPaGyox.JbqBOuIP_", "x": 2290.*********0027, "y": 6909.636718750002, "order": 22, "level": 3, "path": "root.software_info.uWUPaGyox.JbqBOuIP_", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "computer.softwares"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "C7W1ZgZzf", "display_name": "计算机", "description": "computer.softwares", "x": 2490.*********0027, "y": 6909.636718750002, "label": "计算机-安装软件", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"asset_type": "target", "id": "imNWNeggK", "display_name": "计算机", "description": "computer.softwares.install_time", "x": 2947.*********0027, "y": 7114.636718750002, "label": "计算机-安装软件-安装时间", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.software_info.uWUPaGyox.element.name.2pCax4-IR", "x": 2620.*********, "y": 8915.63671875, "order": 23, "level": 5, "source_type": "string", "path": "root.software_info.uWUPaGyox.element.name.2pCax4-IR", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "computer.softwares.name"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "TL7cJuQzA", "display_name": "计算机", "description": "computer.softwares.name", "x": 2820.*********, "y": 8915.63671875, "label": "计算机-安装软件-名称", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.software_info.uWUPaGyox.element.installPath.-U-9AbC3X", "x": 2599.*********0014, "y": 8215.636718749998, "order": 24, "level": 5, "source_type": "string", "path": "root.software_info.uWUPaGyox.element.installPath.-U-9AbC3X", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "computer.softwares.path"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "M6JKucBGw", "display_name": "计算机", "description": "computer.softwares.path", "x": 2799.*********0014, "y": 8215.636718749998, "label": "计算机-安装软件-路径", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.software_info.uWUPaGyox.element.version.TVz2AaOUO", "x": 2562.*********, "y": 8617.636718749996, "order": 25, "level": 5, "source_type": "string", "path": "root.software_info.uWUPaGyox.element.version.TVz2AaOUO", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "computer.softwares.version"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "j05ySeOeg", "display_name": "计算机", "description": "computer.softwares.version", "x": 2762.*********, "y": 8617.636718749996, "label": "计算机-安装软件-版本", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.software_info.uWUPaGyox.element.company.2AfDoM0KH", "x": 2589.*********, "y": 7411.63671875, "order": 26, "level": 5, "source_type": "string", "path": "root.software_info.uWUPaGyox.element.company.2AfDoM0KH", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "computer.softwares.vendor"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "Qf2qSb6Li", "display_name": "计算机", "description": "computer.softwares.vendor", "x": 2789.*********, "y": 7411.63671875, "label": "计算机-安装软件-开发商", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.cpuType.LTIN395Kq", "x": 617.*********, "y": 5199.********0002, "order": 27, "level": 2, "source_type": "string", "path": "root.cpuType.LTIN395Kq", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "computer.hardware.cpu.device_model"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "dvRiiK44l", "display_name": "计算机", "description": "computer.hardware.cpu.device_model", "x": 817.*********, "y": 5199.********0002, "label": "计算机-硬件-CPU-型号", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.cpuHz.P1VuHFzB6", "x": 638.*********0001, "y": 2994.************, "order": 28, "level": 2, "source_type": "float", "path": "root.cpuHz.P1VuHFzB6", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "computer.hardware.cpu.frequency"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "c4hOQPvFi", "display_name": "计算机", "description": "computer.hardware.cpu.frequency", "x": 838.*********0001, "y": 2994.************, "label": "计算机-硬件-CPU-频率", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.uVvDXS5Ds", "x": 332.51757812499994, "y": -238.12109375, "order": 29, "level": 1, "source_type": "object", "path": "root.uVvDXS5Ds", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "生成对象数组", "description": "生成对象数组", "field": "生成对象数组", "input": {"asset_web_type": "add_list_object", "description": "生成对象数组", "size": "1", "field": "disk", "mapping": [{"name": "capacity", "value": "${diskSize}"}, {"name": "sn", "value": "${diskSerial}"}]}, "action_type": "add_list_object", "attrs": {"text": "生成对象数组"}}, {"type": "asset", "asset_type": "action", "id": "root.uVvDXS5Ds.xK1Bee9G1", "x": 752.*********, "y": -241.12109375, "order": 30, "level": 2, "path": "root.uVvDXS5Ds.xK1Bee9G1", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "computer.hardware.disks"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "ZyLN6VgGt", "display_name": "计算机", "description": "computer.hardware.disks", "x": 952.*********, "y": -241.12109375, "label": "计算机-硬件-磁盘", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.memorySize.aVgMgxx8Y", "x": 560.*********, "y": 6398.********, "order": 31, "level": 2, "source_type": "integer", "path": "root.memorySize.aVgMgxx8Y", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "computer.hardware.mem.capacity"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "0676DDB75", "display_name": "计算机", "description": "computer.hardware.mem.capacity", "x": 760.*********, "y": 6398.********, "label": "计算机-硬件-内存-容量", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.holderNameTel.z1HfCNRX8", "x": 522.5425781250001, "y": 101.10390625000002, "order": 4, "level": 2, "source_type": "string", "path": "root.holderNameTel.z1HfCNRX8", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "将数据转化为列表类型", "description": "用于列表类型的转换，将非列表格式的数据转换为列表类型", "field": "将数据转化为列表类型", "input": {"asset_web_type": "list", "description": "用于列表类型的转换，将非列表格式的数据转换为列表类型"}, "action_type": "list", "attrs": {"text": "将数据转化为列表类型"}}, {"type": "asset", "asset_type": "action", "id": "root.email.fFJnWdRRp", "x": 657.*********, "y": 2198.********, "order": 5, "level": 2, "source_type": "string", "path": "root.email.fFJnWdRRp", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "将数据转化为列表类型", "description": "用于列表类型的转换，将非列表格式的数据转换为列表类型", "field": "将数据转化为列表类型", "input": {"asset_web_type": "list", "description": "用于列表类型的转换，将非列表格式的数据转换为列表类型"}, "action_type": "list", "attrs": {"text": "将数据转化为列表类型"}}, {"x": 200, "y": 7200, "id": "root.branch_name", "level": 1, "path": "branch_name", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "branch_name", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.86emV1qgn", "x": 264.90625, "y": -438.62109375, "order": 1, "level": 1, "source_type": "object", "path": "root.86emV1qgn", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "新增字段", "description": "新增字段，字段值必须是字符串类型", "field": "新增字段", "input": {"asset_web_type": "add", "description": "新增字段，字段值必须是字符串类型", "field": "_depart", "values": ["${branch_name}"], "multi_flag": true}, "action_type": "add", "attrs": {"text": "新增字段"}}, {"type": "asset", "asset_type": "action", "id": "root.2xxM76oUD", "x": 263.90625, "y": -388.62109375, "order": 2, "level": 1, "source_type": "object", "path": "root.2xxM76oUD", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "新增字段", "description": "新增字段，字段值必须是字符串类型", "field": "新增字段", "input": {"asset_web_type": "add", "description": "新增字段，字段值必须是字符串类型", "field": "_phone", "values": ["${holderNameTel}"], "multi_flag": true}, "action_type": "add", "attrs": {"text": "新增字段"}}, {"type": "asset", "asset_type": "action", "id": "root.TWgRT4BGl", "x": 261.90625, "y": -320.62109375, "order": 3, "level": 1, "source_type": "object", "path": "root.TWgRT4BGl", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "新增字段", "description": "新增字段，字段值必须是字符串类型", "field": "新增字段", "input": {"asset_web_type": "add", "description": "新增字段，字段值必须是字符串类型", "field": "_email", "values": ["${email}"], "multi_flag": true}, "action_type": "add", "attrs": {"text": "新增字段"}}, {"type": "asset", "asset_type": "action", "id": "root.xg4pfYi3m", "x": 421.90625, "y": -299.62109375, "order": 4, "level": 1, "source_type": "object", "path": "root.xg4pfYi3m", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "生成对象数组", "description": "生成对象数组", "field": "生成对象数组", "input": {"asset_web_type": "add_list_object", "description": "生成对象数组", "size": "1", "field": "owner", "mapping": [{"name": "username", "value": "${holderName}"}, {"name": "nickname", "value": "${holderName}"}, {"name": "departments", "value": "${_depart}"}, {"name": "phones", "value": "${_phone}"}, {"name": "emails", "value": "${_email}"}]}, "action_type": "add_list_object", "attrs": {"text": "生成对象数组"}}, {"type": "asset", "asset_type": "action", "id": "root.xg4pfYi3m.xptv7hxQk", "x": 623.90625, "y": -311.62109375, "order": 5, "level": 2, "path": "root.xg4pfYi3m.xptv7hxQk", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "asset_base.owners"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "5bGYnGw_H", "display_name": "管理属性", "description": "asset_base.owners", "x": 823.90625, "y": -311.62109375, "label": "管理属性-责任人", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.EytRs8zHD", "x": 264.9140625, "y": -511.12109375, "order": 1, "level": 1, "source_type": "object", "path": "root.EytRs8zHD", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "生成对象数组", "description": "生成对象数组", "field": "生成对象数组", "input": {"asset_web_type": "add_list_object", "description": "生成对象数组", "size": "1", "field": "_departments", "mapping": [{"name": "name", "value": "${orgName}"}]}, "action_type": "add_list_object", "attrs": {"text": "生成对象数组"}}, {"type": "asset", "asset_type": "action", "id": "root.EytRs8zHD.58_Hy_2zm", "x": 433.9140625, "y": -510.12109375, "order": 2, "level": 2, "path": "root.EytRs8zHD.58_Hy_2zm", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "asset_base.departments"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "tZE6iqMhU", "display_name": "管理属性", "description": "asset_base.departments", "x": 633.9140625, "y": -510.12109375, "label": "管理属性-行政部门", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.orgName.TuF9h0NuV", "x": 431.4140625, "y": 4399, "order": 2, "level": 2, "source_type": "string", "path": "root.orgName.TuF9h0NuV", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "asset_base.comment"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "LVwe-oGtR", "display_name": "管理属性", "description": "asset_base.comment", "x": 631.4140625, "y": 4399, "label": "管理属性-备注", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}], "edges": [{"source": "root", "target": "root.protectState", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.83916055513493731676892907727", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 100, "anchor_index": 0}}, {"source": "root", "target": "root.antivirusSoftVersion", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.50099668504206931676892907728", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 200, "anchor_index": 0}}, {"source": "root", "target": "root.organizationId", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.6409184869871251676892907728", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 300, "anchor_index": 0}}, {"source": "root", "target": "root.tel", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.35269969613181121676892907728", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 400, "anchor_index": 0}}, {"source": "root", "target": "root.id", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.81450822150940261676892907728", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 500, "anchor_index": 0}}, {"source": "root", "target": "root.osLanguage", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.91393189283223241676892907728", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 600, "anchor_index": 0}}, {"source": "root", "target": "root.registerTime", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.89677296434474571676892907728", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 700, "anchor_index": 0}}, {"source": "root", "target": "root.ip", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.89115773526912271676892907728", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 800, "anchor_index": 0}}, {"source": "root", "target": "root.groupName", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.32256416377561091676892907728", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 900, "anchor_index": 0}}, {"source": "root", "target": "root.pkiUnit", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.73195982622674281676892907729", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 1000, "anchor_index": 0}}, {"source": "root", "target": "root.userAccount", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.097194426263566891676892907729", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 1100, "anchor_index": 0}}, {"source": "root", "target": "root.serviceNo", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.141324464081699741676892907729", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 1200, "anchor_index": 0}}, {"source": "root", "target": "root.reportTime", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.09663452414947061676892907729", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 1300, "anchor_index": 0}}, {"source": "root", "target": "root.softVersion", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.87186609377817191676892907729", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 1400, "anchor_index": 0}}, {"source": "root", "target": "root.agentType", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.82871426067397881676892907729", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 1500, "anchor_index": 0}}, {"source": "root", "target": "root.osDescription", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.43084280443088521676892907729", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 1600, "anchor_index": 0}}, {"source": "root", "target": "root.pkiName", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.28374730926381721676892907729", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 1700, "anchor_index": 0}}, {"source": "root", "target": "root.clientName", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.89759490039577221676892907729", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 1800, "anchor_index": 0}}, {"source": "root", "target": "root.patchState", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.69460750964018431676892907729", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 1900, "anchor_index": 0}}, {"source": "root", "target": "root.mac", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.18352132797441391676892907729", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 2000, "anchor_index": 0}}, {"source": "root", "target": "root.osVersion", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.66653695215229351676892907729", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 2100, "anchor_index": 0}}, {"source": "root", "target": "root.email", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.35186408380635561676892907729", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 2200, "anchor_index": 0}}, {"source": "root", "target": "root.holder<PERSON>ame", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.187897533490485771676892907729", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 2300, "anchor_index": 0}}, {"source": "root", "target": "root.clientId", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.069564876974224531676892907729", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 2400, "anchor_index": 0}}, {"source": "root", "target": "root.lastActiveTime", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.044250269665793821676892907729", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 2500, "anchor_index": 0}}, {"source": "root", "target": "root.updateTime", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.91630076619615891676892907729", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 2600, "anchor_index": 0}}, {"source": "root", "target": "root.osName", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.67706028212513971676892907729", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 2700, "anchor_index": 0}}, {"source": "root", "target": "root.communicateIp", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.57599876295348261676892907729", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 2800, "anchor_index": 0}}, {"source": "root", "target": "root.diskSerial", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.83279370287821891676892907729", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 2900, "anchor_index": 0}}, {"source": "root", "target": "root.cpuHz", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.9855835111998211676892907729", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 3000, "anchor_index": 0}}, {"source": "root", "target": "root.ipType", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.63092250050058271676892907729", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 3100, "anchor_index": 0}}, {"source": "root", "target": "root.isMutiOs", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.67463135044020311676892907729", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 3200, "anchor_index": 0}}, {"source": "root", "target": "root.ksvVersion", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.137152454897503831676892907729", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 3300, "anchor_index": 0}}, {"source": "root", "target": "root.ipNumber", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.58071769541381221676892907730", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 3400, "anchor_index": 0}}, {"source": "root", "target": "root.loginDomainName", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.28862082880880571676892907730", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 3500, "anchor_index": 0}}, {"source": "root", "target": "root.antivirusSoftwareName", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.064945555253507871676892907730", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 3600, "anchor_index": 0}}, {"source": "root", "target": "root.clientVersion", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.478458420954016361676892907730", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 3700, "anchor_index": 0}}, {"source": "root", "target": "root.osSerial", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.67508289499900181676892907730", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 3800, "anchor_index": 0}}, {"source": "root", "target": "root.diskSize", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.172967312350957411676892907730", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 3900, "anchor_index": 0}}, {"source": "root", "target": "root.clientSign", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.96774464434556371676892907730", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 4000, "anchor_index": 0}}, {"source": "root", "target": "root.devOnlyId", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.88100281098833941676892907730", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 4100, "anchor_index": 0}}, {"source": "root", "target": "root.switchPort", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.73536320518123621676892907730", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 4200, "anchor_index": 0}}, {"source": "root", "target": "root.computerModel", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.7808166268115631676892907730", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 4300, "anchor_index": 0}}, {"source": "root", "target": "root.orgName", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.185860911485997041676892907730", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 4400, "anchor_index": 0}}, {"source": "root", "target": "root.deviceTypeName", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.01194201944207741676892907730", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 4500, "anchor_index": 0}}, {"source": "root", "target": "root.osInstall", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.4085970241648321676892907730", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 4600, "anchor_index": 0}}, {"source": "root", "target": "root.isVm", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.165278823043891741676892907730", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 4700, "anchor_index": 0}}, {"source": "root", "target": "root.computerManufacturer", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.0190607636312583041676892907730", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 4800, "anchor_index": 0}}, {"source": "root", "target": "root.name", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.92075583575965551676892907730", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 4900, "anchor_index": 0}}, {"source": "root", "target": "root.assetStatus", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.685101181365441676892907730", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 5000, "anchor_index": 0}}, {"source": "root", "target": "root.ksvDesInfo", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.374982369675475051676892907730", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 5100, "anchor_index": 0}}, {"source": "root", "target": "root.cpuType", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.311892360107375751676892907730", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 5200, "anchor_index": 0}}, {"source": "root", "target": "root.isOpened", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.121150619883844351676892907730", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 5300, "anchor_index": 0}}, {"source": "root", "target": "root.organizationCodeUI", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.29805816551053391676892907730", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 5400, "anchor_index": 0}}, {"source": "root", "target": "root.roamState", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.393539335551224451676892907730", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 5500, "anchor_index": 0}}, {"source": "root", "target": "root.description", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.47193340218609991676892907730", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 5600, "anchor_index": 0}}, {"source": "root", "target": "root.<PERSON><PERSON><PERSON><PERSON>d", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.456715940005117061676892907730", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 5700, "anchor_index": 0}}, {"source": "root", "target": "root.pkiId", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.133411034315812141676892907730", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 5800, "anchor_index": 0}}, {"source": "root", "target": "root.ipv6", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.51600489897215971676892907730", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 5900, "anchor_index": 0}}, {"source": "root", "target": "root.deleteState", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.12041219739485821676892907730", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 6000, "anchor_index": 0}}, {"source": "root", "target": "root.accessNetworkState", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.74116148402254091676892907731", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 6100, "anchor_index": 0}}, {"source": "root", "target": "root.approvalState", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.95600621021563391676892907731", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 6200, "anchor_index": 0}}, {"source": "root", "target": "root.userName", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.226712399646023631676892907731", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 6300, "anchor_index": 0}}, {"source": "root", "target": "root.memorySize", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.52997067994385481676892907731", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 6400, "anchor_index": 0}}, {"source": "root", "target": "root.antivirusSoftware", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.0581956744432394761676892907731", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 6500, "anchor_index": 0}}, {"source": "root", "target": "root.isControl", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.97230046318358411676892907731", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 6600, "anchor_index": 0}}, {"source": "root", "target": "root.regState", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.76106073933235631676892907731", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 6700, "anchor_index": 0}}, {"source": "root", "target": "root.networkTypeAgentPackType", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.81686507683891161676892907731", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 6800, "anchor_index": 0}}, {"source": "root", "target": "root.networkTypeTypeName", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.6998214278965461676892907731", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 6900, "anchor_index": 0}}, {"source": "root", "target": "root.software_info", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.079953964011133971676892907731", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 7000, "anchor_index": 0}}, {"source": "root", "target": "root.account_info", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.374245640178741731676892907731", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 7100, "anchor_index": 0}}, {"source": "root.name", "target": "root.name.LykGgCnXj", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.37411392689063641676893048727", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 4900, "anchor_index": 1}, "end_point": {"x": 495.*********, "y": 4897.********, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.name.LykGgCnXj", "target": "9g8yr6ep8", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.43460524405523151676893059921", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 596.*********, "y": 4897.********, "anchor_index": 1}, "end_point": {"x": 695.*********, "y": 4897.********, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.name", "target": "root.name.a19tLrCcq", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.076232820425636621676893063171", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 4900, "anchor_index": 1}, "end_point": {"x": 500.*********, "y": 4976.********, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.name.a19tLrCcq", "target": "kOrOvytNE", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.187597727887731881676893079753", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 601.*********, "y": 4976.********, "anchor_index": 1}, "end_point": {"x": 700.*********, "y": 4976.********, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.name", "target": "root.name.iyH0sof2c", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.158916804628951751676893082162", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 4900, "anchor_index": 1}, "end_point": {"x": 506.*********, "y": 4794.********, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.name.iyH0sof2c", "target": "Zz9H22RMf", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.64767313944215381676893100358", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 607.*********, "y": 4794.********, "anchor_index": 1}, "end_point": {"x": 706.*********, "y": 4794.********, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root", "target": "root.szXRLk6dH", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.70127879674578561676893201328", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 50.5, "y": 0, "anchor_index": 1}, "end_point": {"x": 251.*********00017, "y": -91.12109375, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.szXRLk6dH", "target": "root.szXRLk6dH.TBHtnh0yd", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.39735820637590871676893228914", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 352.*********00017, "y": -91.12109375, "anchor_index": 1}, "end_point": {"x": 538.*********0002, "y": -93.12109375, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.szXRLk6dH.TBHtnh0yd", "target": "xRuO9KIKn", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.26564883044101941676893246199", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 639.*********0002, "y": -93.12109375, "anchor_index": 1}, "end_point": {"x": 738.*********0002, "y": -93.12109375, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.name", "target": "root.name.meEStq5iz", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.273054667668121141676893748132", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 4900, "anchor_index": 1}, "end_point": {"x": 495.*********, "y": 5061.********, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.name.meEStq5iz", "target": "XDTOGi7Mi", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.97513315057712061676893760398", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 596.*********, "y": 5061.********, "anchor_index": 1}, "end_point": {"x": 695.*********, "y": 5061.********, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.isOpened", "target": "root.isOpened.gpf39pPdc", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.479969837937746831676893859378", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 5300, "anchor_index": 1}, "end_point": {"x": 516.*********, "y": 5294.878906249999, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.isOpened.gpf39pPdc", "target": "root.isOpened.gpf39pPdc.6ZiWNh_Ae", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.426475326983776661676893908496", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 617.*********, "y": 5294.878906249999, "anchor_index": 1}, "end_point": {"x": 716.*********, "y": 5292.878906249999, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.isOpened.gpf39pPdc.6ZiWNh_Ae", "target": "O4VQnsyFb", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.0017364599701177321676893917648", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 817.*********, "y": 5292.878906249999, "anchor_index": 1}, "end_point": {"x": 916.*********, "y": 5292.878906249999, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root", "target": "root.holderNameTel", "source_anchor": 0, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.296202120092699861676894460171", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 38.525000000000034, "y": 63.22500000000002, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root", "target": "root.locationName", "source_anchor": 0, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.84861942293241671676894933523", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 1047.475, "y": 63.22500000000002, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.locationName", "target": "root.locationName.fKmM7FA3L", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.56845006613253891676894955910", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1148.475, "y": 63.22500000000002, "anchor_index": 1}, "end_point": {"x": 1284.*********, "y": 55.7578125, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.locationName.fKmM7FA3L", "target": "D-syGinX6", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.187586118761911221676894984351", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1385.*********, "y": 55.7578125, "anchor_index": 1}, "end_point": {"x": 1484.*********, "y": 55.7578125, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.osDescription", "target": "root.osDescription.vkJhGYw3o", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.0115535817908367641676895145468", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 1600, "anchor_index": 1}, "end_point": {"x": 562.*********, "y": 1592.********, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.osDescription.vkJhGYw3o", "target": "zXLRhosGL", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.80511281987856551676895154286", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 663.*********, "y": 1592.********, "anchor_index": 1}, "end_point": {"x": 762.*********, "y": 1592.********, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.osDescription", "target": "root.osDescription.QUuKP_oyl", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.89503258671378581676895217252", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 1600, "anchor_index": 1}, "end_point": {"x": 541.*********, "y": 1476.********, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.osDescription.QUuKP_oyl", "target": "VEVx6Brko", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.98701489720256341676895238544", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 642.*********, "y": 1476.********, "anchor_index": 1}, "end_point": {"x": 741.*********, "y": 1476.********, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.osInstall", "target": "root.osInstall.xi2Gc2U92", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.89039164817250851676895261608", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 4600, "anchor_index": 1}, "end_point": {"x": 546.*********, "y": 4597.********, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.osInstall.xi2Gc2U92", "target": "XW-AQssIu", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.301019219367927971676895270167", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 647.*********, "y": 4597.********, "anchor_index": 1}, "end_point": {"x": 746.*********, "y": 4597.********, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.osVersion", "target": "root.osVersion.qWK4vwJOQ", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.53631760900658661676895304615", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 2100, "anchor_index": 1}, "end_point": {"x": 524.*********0001, "y": 2032.********, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.osVersion.qWK4vwJOQ", "target": "bOi4-VQE6", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.28065745151251131676895317419", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 625.*********0001, "y": 2032.********, "anchor_index": 1}, "end_point": {"x": 724.*********0001, "y": 2032.********, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.computerManufacturer", "target": "root.computerManufacturer.k-8NLMDv2", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.68698130090725161676895410669", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 4800, "anchor_index": 1}, "end_point": {"x": 490.*********0002, "y": 4704.********0003, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.computerManufacturer.k-8NLMDv2", "target": "7RCKsJ4wu", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.89694222628768341676895423434", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 591.*********0002, "y": 4704.********0003, "anchor_index": 1}, "end_point": {"x": 690.*********0002, "y": 4704.********0003, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.osLanguage", "target": "root.osLanguage.u8ss6wIyd", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.57154116806025871676895755733", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 600, "anchor_index": 1}, "end_point": {"x": 544.*********, "y": 596.********00002, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.osLanguage.u8ss6wIyd", "target": "pXQBhUkjt", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.047018718662586781676895793498", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 645.*********, "y": 596.********00002, "anchor_index": 1}, "end_point": {"x": 744.*********, "y": 596.********00002, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.account_info", "target": "root.account_info.zGAhi6oCi", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.92233006607457411676895851580", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 7100, "anchor_index": 1}, "end_point": {"x": 548.*********, "y": 7089.************, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.account_info.zGAhi6oCi", "target": "root.account_info.zGAhi6oCi.element", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.89602570764889311676895858067", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 649.*********, "y": 7089.************, "anchor_index": 1}, "end_point": {"x": 748.*********, "y": 7189.************, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.account_info.zGAhi6oCi.element", "target": "root.account_info.zGAhi6oCi.element.specialAccount", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.25227909404177031676895858067", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 798.*********, "y": 7215.********0001, "anchor_index": 1}, "end_point": {"x": 948.*********, "y": 7289.************, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.account_info.zGAhi6oCi.element", "target": "root.account_info.zGAhi6oCi.element.accountComment", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.68920744471524231676895858067", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 798.*********, "y": 7215.********0001, "anchor_index": 1}, "end_point": {"x": 948.*********, "y": 7389.************, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.account_info.zGAhi6oCi.element", "target": "root.account_info.zGAhi6oCi.element.accountName", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.53382223486971951676895858067", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 798.*********, "y": 7215.********0001, "anchor_index": 1}, "end_point": {"x": 948.*********, "y": 7489.************, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.account_info.zGAhi6oCi.element", "target": "root.account_info.zGAhi6oCi.element.accountEnable", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.33513384174918491676895858067", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 798.*********, "y": 7215.********0001, "anchor_index": 1}, "end_point": {"x": 948.*********, "y": 7589.************, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.account_info.zGAhi6oCi.element", "target": "root.account_info.zGAhi6oCi.element.displayName", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.112667889196736091676895858067", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 798.*********, "y": 7215.********0001, "anchor_index": 1}, "end_point": {"x": 948.*********, "y": 7689.************, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.account_info.zGAhi6oCi.element", "target": "root.account_info.zGAhi6oCi.element.accountType", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.86282212566099031676895858067", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 798.*********, "y": 7215.********0001, "anchor_index": 1}, "end_point": {"x": 948.*********, "y": 7789.************, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.account_info.zGAhi6oCi.element", "target": "root.account_info.zGAhi6oCi.element.ip", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.4787507926688331676895858067", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 798.*********, "y": 7215.********0001, "anchor_index": 1}, "end_point": {"x": 948.*********, "y": 7889.************, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.account_info.zGAhi6oCi.element", "target": "root.account_info.zGAhi6oCi.element.accountGroup", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.90538564401784031676895858067", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 798.*********, "y": 7215.********0001, "anchor_index": 1}, "end_point": {"x": 948.*********, "y": 7989.************, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.account_info.zGAhi6oCi.element", "target": "root.account_info.zGAhi6oCi.element.optType", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.99894575911927791676895858067", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 798.*********, "y": 7215.********0001, "anchor_index": 1}, "end_point": {"x": 948.*********, "y": 8089.************, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.account_info.zGAhi6oCi.element", "target": "root.account_info.zGAhi6oCi.element.name", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.414450579506255861676895858067", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 798.*********, "y": 7215.********0001, "anchor_index": 1}, "end_point": {"x": 948.*********, "y": 8189.********, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.account_info.zGAhi6oCi.element", "target": "root.account_info.zGAhi6oCi.element.id", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.75779906863466981676895858067", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 798.*********, "y": 7215.********0001, "anchor_index": 1}, "end_point": {"x": 948.*********, "y": 8289.********, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.account_info.zGAhi6oCi.element", "target": "root.account_info.zGAhi6oCi.element.accountSid", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.084444492342320651676895858067", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 798.*********, "y": 7215.********0001, "anchor_index": 1}, "end_point": {"x": 948.*********, "y": 8389.********, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.account_info.zGAhi6oCi.element", "target": "root.account_info.zGAhi6oCi.element.devOnlyId", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.230876914916594121676895858067", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 798.*********, "y": 7215.********0001, "anchor_index": 1}, "end_point": {"x": 948.*********, "y": 8489.********, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.account_info.zGAhi6oCi", "target": "root.account_info.zGAhi6oCi.BqVNE5s9b", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.57309803189493321676895863050", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 649.*********, "y": 7089.************, "anchor_index": 1}, "end_point": {"x": 978.*********, "y": 7085.************, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.account_info.zGAhi6oCi.BqVNE5s9b", "target": "_ANuq9bOY", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.93122483061435651676895936572", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1079.*********, "y": 7085.************, "anchor_index": 1}, "end_point": {"x": 1178.*********, "y": 7085.************, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.account_info.zGAhi6oCi.element.accountName", "target": "root.account_info.zGAhi6oCi.element.accountName.weNrIzYIf", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.0117080353937406571676896098447", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1049.*********, "y": 7489.************, "anchor_index": 1}, "end_point": {"x": 1275.*************, "y": 7492.************, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.account_info.zGAhi6oCi.element.accountName.weNrIzYIf", "target": "RTK-gysJs", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.32312121411534611676896111575", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1376.*************, "y": 7492.************, "anchor_index": 1}, "end_point": {"x": 1475.*************, "y": 7492.************, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.account_info.zGAhi6oCi.element.accountGroup", "target": "root.account_info.zGAhi6oCi.element.accountGroup.4mZjBcmhP", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.65304956363931521676896247991", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1049.*********, "y": 7989.************, "anchor_index": 1}, "end_point": {"x": 1320.*************, "y": 7988.************, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.account_info.zGAhi6oCi.element.accountGroup.4mZjBcmhP", "target": "root.account_info.zGAhi6oCi.element.accountGroup.4mZjBcmhP.eVxaLfNN0", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.61168723807440651676896271818", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1421.*************, "y": 7988.************, "anchor_index": 1}, "end_point": {"x": 1593.*************, "y": 7988.************, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.account_info.zGAhi6oCi.element.accountGroup.4mZjBcmhP.eVxaLfNN0", "target": "Awp4bBgbw", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.66140910601343971676896285168", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1694.*************, "y": 7988.************, "anchor_index": 1}, "end_point": {"x": 1793.*************, "y": 7988.************, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.account_info.zGAhi6oCi.element.accountComment", "target": "root.account_info.zGAhi6oCi.element.accountComment.KiMh6o4Oe", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.54128527031628581676896422803", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1049.*********, "y": 7389.************, "anchor_index": 1}, "end_point": {"x": 1352.********, "y": 7383.7578125, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.account_info.zGAhi6oCi.element.accountComment.KiMh6o4Oe", "target": "UDkKJDC_u", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.79804300668968911676896430598", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1453.********, "y": 7383.7578125, "anchor_index": 1}, "end_point": {"x": 1552.********, "y": 7383.7578125, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.account_info.zGAhi6oCi.element.accountEnable", "target": "root.account_info.zGAhi6oCi.element.accountEnable.kZvHEgQK5", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.50835325248786291676896496665", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1049.*********, "y": 7589.************, "anchor_index": 1}, "end_point": {"x": 1271.********, "y": 7577.7578125, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.account_info.zGAhi6oCi.element.accountEnable.kZvHEgQK5", "target": "root.account_info.zGAhi6oCi.element.accountEnable.kZvHEgQK5.zgm3p7_bR", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.57248540525777211676896527554", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1372.********, "y": 7577.7578125, "anchor_index": 1}, "end_point": {"x": 1494.********, "y": 7574.7578125, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.account_info.zGAhi6oCi.element.accountEnable.kZvHEgQK5.zgm3p7_bR", "target": "xnsdb1bpY", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.73565039980398851676896544343", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1595.********, "y": 7574.7578125, "anchor_index": 1}, "end_point": {"x": 1694.********, "y": 7574.7578125, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.account_info.zGAhi6oCi.element.displayName", "target": "root.account_info.zGAhi6oCi.element.displayName.yMgtkPCW2", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.71872064214837321676896548216", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1049.*********, "y": 7689.************, "anchor_index": 1}, "end_point": {"x": 1240.********, "y": 7688.7578125, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.account_info.zGAhi6oCi.element.displayName.yMgtkPCW2", "target": "vnuGZOQ_d", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.116602292291662971676896559430", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1341.********, "y": 7688.7578125, "anchor_index": 1}, "end_point": {"x": 1440.********, "y": 7688.7578125, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.software_info", "target": "root.software_info.uWUPaGyox", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.54931503209172191676896600346", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 7000, "anchor_index": 1}, "end_point": {"x": 1698.********, "y": 6915.7578125, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.software_info.uWUPaGyox", "target": "root.software_info.uWUPaGyox.element", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.69817644559752811676896606643", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1799.********, "y": 6915.7578125, "anchor_index": 1}, "end_point": {"x": 1898.********, "y": 7015.7578125, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.software_info.uWUPaGyox.element", "target": "root.software_info.uWUPaGyox.element.installTime", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.51912851731282131676896606643", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1949.********, "y": 7041.2578125, "anchor_index": 1}, "end_point": {"x": 2098.********, "y": 7115.7578125, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.software_info.uWUPaGyox.element", "target": "root.software_info.uWUPaGyox.element.sid", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.74211806601171531676896606643", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1949.********, "y": 7041.2578125, "anchor_index": 1}, "end_point": {"x": 2098.********, "y": 7215.7578125, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.software_info.uWUPaGyox.element", "target": "root.software_info.uWUPaGyox.element.softSize", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.78322052286210251676896606643", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1949.********, "y": 7041.2578125, "anchor_index": 1}, "end_point": {"x": 2098.********, "y": 7315.7578125, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.software_info.uWUPaGyox.element", "target": "root.software_info.uWUPaGyox.element.company", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.71938738847472821676896606643", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1949.********, "y": 7041.2578125, "anchor_index": 1}, "end_point": {"x": 2098.********, "y": 7415.7578125, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.software_info.uWUPaGyox.element", "target": "root.software_info.uWUPaGyox.element.id", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.186353608551149241676896606643", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1949.********, "y": 7041.2578125, "anchor_index": 1}, "end_point": {"x": 2098.********, "y": 7515.7578125, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.software_info.uWUPaGyox.element", "target": "root.software_info.uWUPaGyox.element.authType", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.60085464189760511676896606643", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1949.********, "y": 7041.2578125, "anchor_index": 1}, "end_point": {"x": 2098.********, "y": 7615.7578125, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.software_info.uWUPaGyox.element", "target": "root.software_info.uWUPaGyox.element.wow64", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.2808768681762951676896606643", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1949.********, "y": 7041.2578125, "anchor_index": 1}, "end_point": {"x": 2098.********, "y": 7715.7578125, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.software_info.uWUPaGyox.element", "target": "root.software_info.uWUPaGyox.element.devOnlyId", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.89651360908387191676896606643", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1949.********, "y": 7041.2578125, "anchor_index": 1}, "end_point": {"x": 2098.********, "y": 7815.7578125, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.software_info.uWUPaGyox.element", "target": "root.software_info.uWUPaGyox.element.devRegCodeUI", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.89029565525625351676896606643", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1949.********, "y": 7041.2578125, "anchor_index": 1}, "end_point": {"x": 2098.********, "y": 7915.7578125, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.software_info.uWUPaGyox.element", "target": "root.software_info.uWUPaGyox.element.uninstallPath", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.206985020354264251676896606643", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1949.********, "y": 7041.2578125, "anchor_index": 1}, "end_point": {"x": 2098.********, "y": 8015.7578125, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.software_info.uWUPaGyox.element", "target": "root.software_info.uWUPaGyox.element.devRegPath", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.18535041455088621676896606643", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1949.********, "y": 7041.2578125, "anchor_index": 1}, "end_point": {"x": 2098.********, "y": 8115.7578125, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.software_info.uWUPaGyox.element", "target": "root.software_info.uWUPaGyox.element.installPath", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.46705644397099281676896606643", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1949.********, "y": 7041.2578125, "anchor_index": 1}, "end_point": {"x": 2098.********, "y": 8215.7578125, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.software_info.uWUPaGyox.element", "target": "root.software_info.uWUPaGyox.element.amount", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.3695801526755481676896606643", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1949.********, "y": 7041.2578125, "anchor_index": 1}, "end_point": {"x": 2098.********, "y": 8315.7578125, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.software_info.uWUPaGyox.element", "target": "root.software_info.uWUPaGyox.element.devRegId", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.74565548819122831676896606643", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1949.********, "y": 7041.2578125, "anchor_index": 1}, "end_point": {"x": 2098.********, "y": 8415.7578125, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.software_info.uWUPaGyox.element", "target": "root.software_info.uWUPaGyox.element.regguid", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.46462995864206461676896606643", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1949.********, "y": 7041.2578125, "anchor_index": 1}, "end_point": {"x": 2098.********, "y": 8515.7578125, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.software_info.uWUPaGyox.element", "target": "root.software_info.uWUPaGyox.element.version", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.412433580276920341676896606644", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1949.********, "y": 7041.2578125, "anchor_index": 1}, "end_point": {"x": 2098.********, "y": 8615.7578125, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.software_info.uWUPaGyox.element", "target": "root.software_info.uWUPaGyox.element.devOrgCodeUI", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.43704979891244821676896606644", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1949.********, "y": 7041.2578125, "anchor_index": 1}, "end_point": {"x": 2098.********, "y": 8715.7578125, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.software_info.uWUPaGyox.element", "target": "root.software_info.uWUPaGyox.element.authResult", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.85603712374447991676896606644", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1949.********, "y": 7041.2578125, "anchor_index": 1}, "end_point": {"x": 2098.********, "y": 8815.7578125, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.software_info.uWUPaGyox.element", "target": "root.software_info.uWUPaGyox.element.name", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.102542004793877431676896606644", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1949.********, "y": 7041.2578125, "anchor_index": 1}, "end_point": {"x": 2098.********, "y": 8915.7578125, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.software_info.uWUPaGyox.element", "target": "root.software_info.uWUPaGyox.element.devOrgId", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.223822351619713981676896606644", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1949.********, "y": 7041.2578125, "anchor_index": 1}, "end_point": {"x": 2098.********, "y": 9015.7578125, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.software_info.uWUPaGyox.element.installTime", "target": "root.software_info.uWUPaGyox.element.installTime.SCNlFnILv", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.62016438895063871676896711234", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 2199.********, "y": 7115.7578125, "anchor_index": 1}, "end_point": {"x": 2415.*********003, "y": 7116.636718750002, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.software_info.uWUPaGyox.element.installTime.SCNlFnILv", "target": "root.software_info.uWUPaGyox.element.installTime.SCNlFnILv.B9vDdvgTw", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.78002959767117951676896779773", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 2516.*********003, "y": 7116.636718750002, "anchor_index": 1}, "end_point": {"x": 2697.*********0027, "y": 7114.636718750002, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.software_info.uWUPaGyox", "target": "root.software_info.uWUPaGyox.JbqBOuIP_", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.397786566782585061676896808477", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1799.********, "y": 6915.7578125, "anchor_index": 1}, "end_point": {"x": 2240.*********0027, "y": 6909.636718750002, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.software_info.uWUPaGyox.JbqBOuIP_", "target": "C7W1ZgZzf", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.364635181550741371676896817815", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 2341.*********0027, "y": 6909.636718750002, "anchor_index": 1}, "end_point": {"x": 2440.*********0027, "y": 6909.636718750002, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.software_info.uWUPaGyox.element.installTime.SCNlFnILv.B9vDdvgTw", "target": "imNWNeggK", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.050533186284560161676896826876", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 2798.*********0027, "y": 7114.636718750002, "anchor_index": 1}, "end_point": {"x": 2897.*********0027, "y": 7114.636718750002, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.software_info.uWUPaGyox.element.name", "target": "root.software_info.uWUPaGyox.element.name.2pCax4-IR", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.55749612593524181676896869382", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 2199.********, "y": 8915.7578125, "anchor_index": 1}, "end_point": {"x": 2570.*********, "y": 8915.63671875, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.software_info.uWUPaGyox.element.name.2pCax4-IR", "target": "TL7cJuQzA", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.59472639933629521676896876091", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 2671.*********, "y": 8915.63671875, "anchor_index": 1}, "end_point": {"x": 2770.*********, "y": 8915.63671875, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.software_info.uWUPaGyox.element.installPath", "target": "root.software_info.uWUPaGyox.element.installPath.-U-9AbC3X", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.72919998572912871676896881199", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 2199.********, "y": 8215.7578125, "anchor_index": 1}, "end_point": {"x": 2549.*********0014, "y": 8215.636718749998, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.software_info.uWUPaGyox.element.installPath.-U-9AbC3X", "target": "M6JKucBGw", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.31007012294069991676896888522", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 2650.*********0014, "y": 8215.636718749998, "anchor_index": 1}, "end_point": {"x": 2749.*********0014, "y": 8215.636718749998, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.software_info.uWUPaGyox.element.version", "target": "root.software_info.uWUPaGyox.element.version.TVz2AaOUO", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.9722912053770271676896918707", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 2199.********, "y": 8615.7578125, "anchor_index": 1}, "end_point": {"x": 2512.*********, "y": 8617.636718749996, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.software_info.uWUPaGyox.element.version.TVz2AaOUO", "target": "j05ySeOeg", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.70786770895065241676896926505", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 2613.*********, "y": 8617.636718749996, "anchor_index": 1}, "end_point": {"x": 2712.*********, "y": 8617.636718749996, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.software_info.uWUPaGyox.element.company", "target": "root.software_info.uWUPaGyox.element.company.2AfDoM0KH", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.0143524993986288151676896977553", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 2199.********, "y": 7415.7578125, "anchor_index": 1}, "end_point": {"x": 2539.*********, "y": 7411.63671875, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.software_info.uWUPaGyox.element.company.2AfDoM0KH", "target": "Qf2qSb6Li", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.73290242503337421676896985413", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 2640.*********, "y": 7411.63671875, "anchor_index": 1}, "end_point": {"x": 2739.*********, "y": 7411.63671875, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.cpuType", "target": "root.cpuType.LTIN395Kq", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.018777418978834561676897097428", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 5200, "anchor_index": 1}, "end_point": {"x": 567.*********, "y": 5199.********0002, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.cpuType.LTIN395Kq", "target": "dvRiiK44l", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.5107572635548371676897108262", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 668.*********, "y": 5199.********0002, "anchor_index": 1}, "end_point": {"x": 767.*********, "y": 5199.********0002, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.cpuHz", "target": "root.cpuHz.P1VuHFzB6", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.150190441662063861676897125272", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 3000, "anchor_index": 1}, "end_point": {"x": 588.*********0001, "y": 2994.************, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.cpuHz.P1VuHFzB6", "target": "c4hOQPvFi", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.3723497583649321676897135879", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 689.*********0001, "y": 2994.************, "anchor_index": 1}, "end_point": {"x": 788.*********0001, "y": 2994.************, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root", "target": "root.uVvDXS5Ds", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.69328165840482311676897297802", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 50.5, "y": 0, "anchor_index": 1}, "end_point": {"x": 282.01757812499994, "y": -238.12109375, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.uVvDXS5Ds", "target": "root.uVvDXS5Ds.xK1Bee9G1", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.47437081952849281676897379993", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 383.01757812499994, "y": -238.12109375, "anchor_index": 1}, "end_point": {"x": 702.*********, "y": -241.12109375, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.uVvDXS5Ds.xK1Bee9G1", "target": "ZyLN6VgGt", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.329989115673422931676897399692", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 803.*********, "y": -241.12109375, "anchor_index": 1}, "end_point": {"x": 902.*********, "y": -241.12109375, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.memorySize", "target": "root.memorySize.aVgMgxx8Y", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.63167877659658481676897453928", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 6400, "anchor_index": 1}, "end_point": {"x": 510.*********, "y": 6398.********, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.memorySize.aVgMgxx8Y", "target": "0676DDB75", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.7737296645723121676897462092", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 611.*********, "y": 6398.********, "anchor_index": 1}, "end_point": {"x": 710.*********, "y": 6398.********, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.holderNameTel", "target": "root.holderNameTel.z1HfCNRX8", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.85520613671509141676906216733", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 139.52500000000003, "y": 63.22500000000002, "anchor_index": 1}, "end_point": {"x": 472.0425781250001, "y": 101.10390625000002, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.email", "target": "root.email.fFJnWdRRp", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.7224436607612571676906281644", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 2200, "anchor_index": 1}, "end_point": {"x": 607.*********, "y": 2198.********, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root", "target": "root.branch_name", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.1760311381322811690974583225", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 7200, "anchor_index": 0}}, {"source": "root", "target": "root.86emV1qgn", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.127653925376791431690974604784", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 50.5, "y": 0, "anchor_index": 1}, "end_point": {"x": 214.40625, "y": -438.62109375, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root", "target": "root.2xxM76oUD", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.64195576634835791690974628745", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 50.5, "y": 0, "anchor_index": 1}, "end_point": {"x": 213.40625, "y": -388.62109375, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root", "target": "root.TWgRT4BGl", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.65045188581546491690974731927", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 50.5, "y": 0, "anchor_index": 1}, "end_point": {"x": 211.40625, "y": -320.62109375, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root", "target": "root.xg4pfYi3m", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.476232306099261261690974754407", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 50.5, "y": 0, "anchor_index": 1}, "end_point": {"x": 371.40625, "y": -299.62109375, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.xg4pfYi3m", "target": "root.xg4pfYi3m.xptv7hxQk", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.84662480966171351690974900150", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 472.40625, "y": -299.62109375, "anchor_index": 1}, "end_point": {"x": 573.40625, "y": -311.62109375, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.xg4pfYi3m.xptv7hxQk", "target": "5bGYnGw_H", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.072845173356791861690974909521", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 674.40625, "y": -311.62109375, "anchor_index": 1}, "end_point": {"x": 773.40625, "y": -311.62109375, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root", "target": "root.EytRs8zHD", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.256103603482227941723015745671", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 50.5, "y": 0, "anchor_index": 1}, "end_point": {"x": 214.4140625, "y": -511.12109375, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.EytRs8zHD", "target": "root.EytRs8zHD.58_Hy_2zm", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.223800934092320381723015809998", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 315.4140625, "y": -511.12109375, "anchor_index": 1}, "end_point": {"x": 383.4140625, "y": -510.12109375, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.EytRs8zHD.58_Hy_2zm", "target": "tZE6iqMhU", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.33949327110634921723015818751", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 484.4140625, "y": -510.12109375, "anchor_index": 1}, "end_point": {"x": 583.4140625, "y": -510.12109375, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.orgName", "target": "root.orgName.TuF9h0NuV", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.65611067979253731723113891987", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 4400, "anchor_index": 1}, "end_point": {"x": 380.9140625, "y": 4399, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.orgName.TuF9h0NuV", "target": "LVwe-oGtR", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.92826429939491641723113903870", "style": {}, "start_point": {"x": 481.9140625, "y": 4399, "anchor_index": 1}, "end_point": {"x": 580.9140625, "y": 4399, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}]}, "rules": [{"name": "add_list_object", "setting": {"size": 1, "field": "ips", "mapping": [{"name": "addr", "value": "${ip}"}, {"name": "mac", "value": "${mac}"}]}, "sub_rules": [], "_id": "root.szXRLk6dH"}, {"name": "enter", "setting": {"values": "network.ips", "field": "${ips}"}, "sub_rules": [], "_id": "root.szXRLk6dH.TBHtnh0yd"}, {"name": "add_list_object", "setting": {"size": 1, "field": "disk", "mapping": [{"name": "capacity", "value": "${diskSize}"}, {"name": "sn", "value": "${diskSerial}"}]}, "sub_rules": [], "_id": "root.uVvDXS5Ds"}, {"name": "enter", "setting": {"values": "computer.hardware.disks", "field": "${disk}"}, "sub_rules": [], "_id": "root.uVvDXS5Ds.xK1Bee9G1"}, {"name": "add", "setting": {"field": "_depart", "values": ["${branch_name}"], "multi_flag": true, "is_output_root": null}, "sub_rules": [], "_id": "root.86emV1qgn"}, {"name": "add", "setting": {"field": "_phone", "values": ["${holderNameTel}"], "multi_flag": true, "is_output_root": null}, "sub_rules": [], "_id": "root.2xxM76oUD"}, {"name": "add", "setting": {"field": "_email", "values": ["${email}"], "multi_flag": true, "is_output_root": null}, "sub_rules": [], "_id": "root.TWgRT4BGl"}, {"name": "add_list_object", "setting": {"size": 1, "field": "owner", "mapping": [{"name": "username", "value": "${holderName}"}, {"name": "nickname", "value": "${holderName}"}, {"name": "departments", "value": "${_depart}"}, {"name": "phones", "value": "${_phone}"}, {"name": "emails", "value": "${_email}"}]}, "sub_rules": [], "_id": "root.xg4pfYi3m"}, {"name": "enter", "setting": {"values": "asset_base.owners", "field": "${owner}"}, "sub_rules": [], "_id": "root.xg4pfYi3m.xptv7hxQk"}, {"name": "add_list_object", "setting": {"size": 1, "field": "_departments", "mapping": [{"name": "name", "value": "${orgName}"}]}, "sub_rules": [], "_id": "root.EytRs8zHD"}, {"name": "enter", "setting": {"values": "asset_base.departments", "field": "${_departments}"}, "sub_rules": [], "_id": "root.EytRs8zHD.58_Hy_2zm"}, {"name": "enter", "setting": {"values": "computer.os.lang", "field": "${osLanguage}"}, "sub_rules": [], "_id": "root.osLanguage.u8ss6wIyd"}, {"name": "list", "setting": {"field": "holderNameTel"}, "sub_rules": [], "_id": "root.holderNameTel.z1HfCNRX8"}, {"name": "enter", "setting": {"values": "asset_base.location.detail", "field": "${locationName}"}, "sub_rules": [], "_id": "root.locationName.fKmM7FA3L"}, {"name": "enter", "setting": {"values": "computer.os.full", "field": "${osDescription}"}, "sub_rules": [], "_id": "root.osDescription.vkJhGYw3o"}, {"name": "enter", "setting": {"values": "computer.os.distribution", "field": "${osDescription}"}, "sub_rules": [], "_id": "root.osDescription.QUuKP_oyl"}, {"name": "enter", "setting": {"values": "computer.os.version", "field": "${osVersion}"}, "sub_rules": [], "_id": "root.osVersion.qWK4vwJOQ"}, {"name": "list", "setting": {"field": "email"}, "sub_rules": [], "_id": "root.email.fFJnWdRRp"}, {"name": "enter", "setting": {"values": "computer.hardware.cpu.frequency", "field": "${cpuHz}"}, "sub_rules": [], "_id": "root.cpuHz.P1VuHFzB6"}, {"name": "enter", "setting": {"values": "asset_base.comment", "field": "${orgName}"}, "sub_rules": [], "_id": "root.orgName.TuF9h0NuV"}, {"name": "enter", "setting": {"values": "computer.os.install_time", "field": "${osInstall}"}, "sub_rules": [], "_id": "root.osInstall.xi2Gc2U92"}, {"name": "enter", "setting": {"values": "computer.os.vendor", "field": "${computerManufacturer}"}, "sub_rules": [], "_id": "root.computerManufacturer.k-8NLMDv2"}, {"name": "enter", "setting": {"values": "asset_base.name", "field": "${name}"}, "sub_rules": [], "_id": "root.name.LykGgCnXj"}, {"name": "enter", "setting": {"values": "computer.host_name", "field": "${name}"}, "sub_rules": [], "_id": "root.name.a19tLrCcq"}, {"name": "enter", "setting": {"values": "computer.name", "field": "${name}"}, "sub_rules": [], "_id": "root.name.iyH0sof2c"}, {"name": "enter", "setting": {"values": "asset_base.device.name", "field": "${name}"}, "sub_rules": [], "_id": "root.name.meEStq5iz"}, {"name": "enter", "setting": {"values": "computer.hardware.cpu.device_model", "field": "${cpuType}"}, "sub_rules": [], "_id": "root.cpuType.LTIN395Kq"}, {"name": "translate", "setting": {"values": [{"name": 0, "value": 2}, {"name": 1, "value": 1}], "default": 0, "field": "isOpened"}, "sub_rules": [], "_id": "root.isOpened.gpf39pPdc"}, {"name": "enter", "setting": {"values": "asset_base.device.status", "field": "${isOpened}"}, "sub_rules": [], "_id": "root.isOpened.gpf39pPdc.6ZiWNh_Ae"}, {"name": "enter", "setting": {"values": "computer.hardware.mem.capacity", "field": "${memorySize}"}, "sub_rules": [], "_id": "root.memorySize.aVgMgxx8Y"}, {"name": "for", "setting": {"field": "software_info"}, "sub_rules": [{"id": "root.software_info.uWUPaGyox.element.installTime.SCNlFnILv", "name": "add_up", "setting": {"values": ["${installTime} 00:00:00", "${installTime}"], "dst_field": "aa"}, "sub_rules": []}, {"id": "root.software_info.uWUPaGyox.element.installTime.SCNlFnILv.B9vDdvgTw", "name": "enter", "setting": {"values": "install_time", "field": "${aa}"}, "sub_rules": []}, {"id": "root.software_info.uWUPaGyox.element.company.2AfDoM0KH", "name": "enter", "setting": {"values": "vendor", "field": "${company}"}, "sub_rules": []}, {"id": "root.software_info.uWUPaGyox.element.installPath.-U-9AbC3X", "name": "enter", "setting": {"values": "path", "field": "${installPath}"}, "sub_rules": []}, {"id": "root.software_info.uWUPaGyox.element.version.TVz2AaOUO", "name": "enter", "setting": {"values": "version", "field": "${version}"}, "sub_rules": []}, {"id": "root.software_info.uWUPaGyox.element.name.2pCax4-IR", "name": "enter", "setting": {"values": "name", "field": "${name}"}, "sub_rules": []}], "_id": "root.software_info.uWUPaGyox"}, {"name": "enter", "setting": {"values": "computer.softwares", "field": "${software_info}"}, "sub_rules": [], "_id": "root.software_info.uWUPaGyox.JbqBOuIP_"}, {"name": "for", "setting": {"field": "account_info"}, "sub_rules": [{"id": "root.account_info.zGAhi6oCi.element.accountComment.KiMh6o4Oe", "name": "enter", "setting": {"values": "description", "field": "${accountComment}"}, "sub_rules": []}, {"id": "root.account_info.zGAhi6oCi.element.accountName.weNrIzYIf", "name": "enter", "setting": {"values": "name", "field": "${accountName}"}, "sub_rules": []}, {"id": "root.account_info.zGAhi6oCi.element.accountEnable.kZvHEgQK5", "name": "translate", "setting": {"values": [{"name": 0, "value": 0}, {"name": 1, "value": 2}], "default": 999, "field": "accountEnable"}, "sub_rules": []}, {"id": "root.account_info.zGAhi6oCi.element.accountEnable.kZvHEgQK5.zgm3p7_bR", "name": "enter", "setting": {"values": "interactive_login_type", "field": "${accountEnable}"}, "sub_rules": []}, {"id": "root.account_info.zGAhi6oCi.element.displayName.yMgtkPCW2", "name": "enter", "setting": {"values": "full_name", "field": "${displayName}"}, "sub_rules": []}, {"id": "root.account_info.zGAhi6oCi.element.accountGroup.4mZjBcmhP", "name": "list", "setting": {"field": "accountGroup"}, "sub_rules": []}, {"id": "root.account_info.zGAhi6oCi.element.accountGroup.4mZjBcmhP.eVxaLfNN0", "name": "enter", "setting": {"values": "groups", "field": "${accountGroup}"}, "sub_rules": []}], "_id": "root.account_info.zGAhi6oCi"}, {"name": "enter", "setting": {"values": "computer.accounts", "field": "${account_info}"}, "sub_rules": [], "_id": "root.account_info.zGAhi6oCi.BqVNE5s9b"}], "adapter_name": "vrv_webservice_web", "fetch_type": "terminal", "model_name": "computer", "asset_type": "terminal", "internal": true}