from vrv_webservice_web.clients.account import VRVWebServiceAccountClient
from vrv_webservice_web.clients.asset import VRVWebServiceAssetClient
from vrv_webservice_web.clients.asset_count import VRVWebServiceAssetCountClient
from vrv_webservice_web.clients.department import VRVWebServiceDepartmentClient, VRVWebServiceAllDepartmentClient
from vrv_webservice_web.clients.software import VRVWebServiceSoftwareClient


class FetchType(object):
    ASSET = "terminal"
    PROCESS = "process"
    SOFTWARE = "software"
    RESOURCE = "resource"
    ACCOUNT = "account"
    DEPARTMENT = "department"
    DEPARTMENT_DETAIL = "department_detail"
    ASSET_COUNT = "asset_count"


class VRVWebServiceWebManager(object):
    _client_mapper = {
        FetchType.ASSET: VRVWebServiceAssetClient,
        FetchType.SOFTWARE: VRVWebServiceSoftwareClient,
        FetchType.ACCOUNT: VRVWebServiceAccountClient,
        FetchType.ASSET_COUNT: VRVWebServiceAssetCountClient,
        FetchType.DEPARTMENT: VRVWebServiceAllDepartmentClient,
        FetchType.DEPARTMENT_DETAIL: VRVWebServiceDepartmentClient,
    }

    ORG_KEYWORD = ["总公司部门", "分公司"]

    def __init__(self, connection, session=None, cookie=None):
        self._connection = connection
        self._session = session
        self._cookie = cookie
        self._client_instance_mapper = {}
        self._func = {
            FetchType.ASSET: self._find_asset,
            FetchType.DEPARTMENT: self._find_department,
        }

    def _find_asset(self, page_index=None, page_size=None):
        asset_info = self.__call(fetch_type=FetchType.ASSET, page_index=page_index, page_size=page_size)
        for asset in asset_info:
            org_name = asset.get("orgName")
            if org_name:
                _data = org_name.split("/")
                asset["branch_name"] = _data[-1]
            asset_id = asset.get("devOnlyId")
            org_name = asset.get("orgName")
            if org_name:
                _data = org_name.split("/")
                for _item in _data:
                    for item_key in self.ORG_KEYWORD:
                        if _item.find(item_key) != -1:
                            asset["branch_name"] = _item
                            break
            software = self.__call(fetch_type=FetchType.SOFTWARE, asset_id=asset_id)
            account = self.__call(
                fetch_type=FetchType.ACCOUNT, page_index=page_index, page_size=page_size, dev_onlyid=asset_id
            )
            if software:
                asset["software_info"] = software
            if account:
                asset["account_info"] = account
        return asset_info

    def _find_department(self, page_index=None, page_size=None):
        departments = self.__call(fetch_type=FetchType.DEPARTMENT) or []
        for department in departments:
            data = []
            department_id = department.get("id")
            if not department_id:
                continue
            data = self.__call(fetch_type=FetchType.DEPARTMENT_DETAIL, department_id=department_id) or []
            if data:
                departments.extend(data)
        return departments

    def find_asset(self, fetch_type, page_index=None, page_size=None):
        """
        查询信息
        """
        data = self._func.get(fetch_type)(page_index=page_index, page_size=page_size)
        return data

    def get_asset_count(self):
        asset = self.__call(fetch_type=FetchType.ASSET_COUNT, page_index=1, page_size=50)
        return asset

    def __call(self, fetch_type, *args, **kwargs):
        if fetch_type not in self._client_instance_mapper:
            self._client_instance_mapper[fetch_type] = self._client_mapper[fetch_type](
                self._connection, self._session, self._cookie
            )

        return self._client_instance_mapper[fetch_type].handle(*args, **kwargs)
