name: vrv_webservice_web
display_name: "北信源内网安全管理系统-web抓取"
description: "北信源内网安全管理系统以终端管理为核心，形成集主机监控审计、补丁管理、桌面应用管理、信息安全管理、终端行为管控等终端安全行为一体的管理体系，为企业管理者提供终端多位一体、统一管理的解决方案，为用户创建一个安全、可靠、稳定的办公网络。"
type: "终端防护"
company: "北信源"
logo: "vrv_webservice.png"
version: "v0.1"
priority: 10
properties:
  - 终端防护

connection:
  - name: username
    type: string
    required: true
    display_name: "用户名称"
    validate_rules:
      - name: length
        error_hint: "用户密码格式无效。长度最小不得小于2，最大不得大于100"
        setting:
          min: 2
          max: 100

  - name: password
    type: password
    required: true
    display_name: "用户密码"
    validate_rules:
      - name: length
        error_hint: "用户密码格式无效。长度最小不得小于6，最大不得大于200"
        setting:
          min: 6
          max: 200

  - name: address
    type: url
    required: true
    display_name: "请求地址"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'


fetch_setting:
  point: "vrv_webservice_web.fetch:find_asset"
  condition_point: "vrv_webservice_web.fetch:build_query_condition"
  mode: "compute_page"
  count_point: "vrv_webservice_web.fetch:get_asset_count"
  size: 30
  test_auth_point: "vrv_webservice_web.fetch:get_auth_connection"
  finish_point: "vrv_webservice_web.fetch:free_condition"
  is_need_test_service: true
  fetch_type_mapper:
    asset:
      - terminal
    department:
      - department

merge_setting:
  size: 50
  setting: {}

convert_setting:
  size: 50
  before_executor_mapper: { }
  executor_mapper: { }

fabric_setting:
  choose_point_mapper:
    asset: "vrv_webservice_web.fabric:choose_new_record"