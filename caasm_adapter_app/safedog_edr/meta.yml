name: "safedog_edr"
display_name: "安全狗云眼"
description: "云眼系统由资产管理、安全体检、安全监控、漏洞风险、入侵威胁、合规基线、威胁情报等多个功能模块组成，各个模块进行联动，模块间数据联通，形成闭环系统，为企业提供强有力的采集、检测、监测、防御、捕获能力，对主机进行全方位的安全防护。"
type: "主机防护"
company: "安全狗"
logo: "safedog_edr.png"
version: "v0.1"
priority: 1
properties:
  - "主机防护"

connection:
  - name: username
    type: string
    required: true
    display_name: "用户名"
    description: "用户名称"
    validate_rules:
      - name: length
        error_hint: "用户名长度必须大于等于2且小于等于100"
        setting:
          min: 2
          max: 100

  - name: password
    type: password
    required: true
    display_name: "密码"
    description: "密码信息"
    validate_rules:
      - name: length
        error_hint: "密码长度必须大于等于6且小于等于100"
        setting:
          min: 6
          max: 100

  - name: address
    type: url
    required: true
    display_name: "地址"
    description: "地址信息"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'


fetch_setting:
  type: disposable
  point: "safedog_edr.fetch:find_asset"
  size: 1000
  fetch_type_mapper:
    asset:
      - asset

fabric_setting:
  choose_point_mapper:
    asset: "safedog_edr.fabric:choose_new_record"

merge_setting:
  size: 1000
  setting: { }

convert_setting:
  size: 1000
  before_executor_mapper: { }
  executor_mapper: { }