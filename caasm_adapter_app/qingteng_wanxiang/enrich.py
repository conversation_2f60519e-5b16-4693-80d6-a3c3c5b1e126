import logging

from caasm_tool.reflect import parse_instance_callable
from qingteng_wanxiang.constant import OsType, FetchType, LINUX_DETAIL_CLASS_MAPPER, WINDOWS_DETAIL_CLASS_MAPPER
from qingteng_wanxiang.util import find_loop

log = logging.getLogger()


class Enricher(object):
    _ENRICH_KEY = "_enrich_"

    def __init__(self, connection, session, condition, call_entry):
        self._enrich_mapper = parse_instance_callable(self, self._ENRICH_KEY)
        self._connection = connection
        self._session = session
        self._condition = condition
        self._call_entry = call_entry

    def enrich(self, fetch_type, record):
        enrich_method = self._enrich_mapper.get(fetch_type)
        if not enrich_method:
            return
        enrich_method(record)

    def _enrich_host(self, record):
        if record["bizType"] != OsType.LINUX:
            return
        self._build_jar(record)
        if self.frontend_latest_task_id:
            self._build_ip_relation(record)

    def _build_ip_relation(self, record):
        agent_id = record["agentId"]
        relations = find_loop(
            self.frontend_manager.find_public_ip_relation,
            task_id=self.frontend_latest_task_id,
            agent_id=agent_id,
            page_index=1,
        )
        record[FetchType.FRONTEND_PUBLIC_IP_RELATION] = relations

    def _build_jar(self, record):
        jar_records = record[FetchType.JAR]
        jar_cleaned_records = []

        for jar_record in jar_records:
            jar_cleaned_records.extend(self._clean_sub_jar(jar_record))
        jar_cleaned_records.extend(jar_records)

        record[FetchType.JAR] = jar_cleaned_records

    def _enrich_vuls(self, record):
        record_id = record["id"]
        record["vul"] = self._call_entry(
            FetchType.VUL_DETAIL,
            LINUX_DETAIL_CLASS_MAPPER,
            record_id,
        )

    def _enrich_patches(self, record):
        os_type = record["bizType"]
        record_id = record["id"]

        if os_type == OsType.WINDOWS:
            patch_detail = self._call_entry(FetchType.PATCH_DETAIL, WINDOWS_DETAIL_CLASS_MAPPER, record_id)
        else:
            patch_detail = self._call_entry(FetchType.PATCH_DETAIL, LINUX_DETAIL_CLASS_MAPPER, record_id)
        record["patch"] = patch_detail

    def _clean_sub_jar(self, record):
        jar_name = record.get("name")
        agent_id = record["agentId"]
        if not self.frontend_parent_jars:
            return []
        if jar_name not in self.frontend_parent_jars:
            return []

        sub_jar_data = find_loop(
            self.frontend_manager.find_sub_jar_package,
            names=[jar_name],
            agent_id=agent_id,
            page_index=1,
        )

        for item in sub_jar_data:
            item["parent_package"] = jar_name
        return sub_jar_data

    @property
    def frontend_manager(self):
        return self._condition.get("frontend")

    @property
    def frontend_parent_jars(self):
        return self._condition.get("frontend_parent_jars")

    @property
    def frontend_latest_task_id(self):
        return self._condition.get("frontend_latest_task_id")
