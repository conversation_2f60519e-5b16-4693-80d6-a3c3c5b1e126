{"agentId": "测试ID", "displayIp": "127.0.0.1", "connectionIp": "127.0.0.1", "externalIp": "*******", "internalIp": "127.0.0.1", "bizGroupId": 1, "bizGroup": "未分组主机", "remark": "", "hostTagList": ["test"], "hostname": "测试-HOST", "proxyIp": null, "platform": "CentOS Linux release 7.9.2009 (Core)", "kernelVersion": "3.10.0-1160.25.1.el7.x86_64", "cpu": {"core": 4, "producer": "GenuineIntel", "brand": "Intel(R) Xeon(R) Gold 6240 CPU @ 2.60GHz", "description": "GenuineIntel 4 Intel(R) Xeon(R) Gold 6240 CPU @ 2.60GHz", "loadAvgFifteen": 0.1}, "memoryUsage": 0.3878, "memorySize": 7783, "onlineStatus": 1, "agentStatus": 0, "lastOnlineTime": "2022-07-06 20:02:16", "installTime": "2022-06-20 13:34:19", "agentVersion": "3.4.1-3.401.302-Rel-2022-06-15_14-47-48-8", "bashVersion": null, "bashPluginInstalled": false, "systemLoad": 1, "offlineDays": 24, "hostLocation": null, "chargeName": null, "memories": [{"type": null, "producer": "QEMU", "size": 7783, "speed": 0, "description": "7.6GB(QEMU None 0MHZ)", "producerSize": 8192, "bank": "QEMU 8.0GB 0MHZ"}], "manufacturer": "QEMU", "productName": "Standard PC (i440FX + PIIX, 1996)", "serialNumber": "74a8f914-fcd8-4bcf-a5a9-980c91b723bc", "networkCards": [{"name": "virbr0", "mac": "52:54:00:04:33:20", "ipv4": "***************", "ipv6": "", "gateway": "", "dnsserver": null, "status": "1"}], "diskCount": 2, "diskSize": 204800, "diskUsage": 0.3796, "osType": null, "bizType": "Linux", "ports": [{"agentId": "test_agent_id", "displayIp": "127.0.0.1", "connectionIp": "127.0.0.1", "externalIp": "*******", "internalIp": "127.0.0.1", "bizGroupId": 1, "bizGroup": "未分组主机", "remark": "test", "hostTagList": ["test"], "hostname": "测试-HOST", "proto": "tcp", "port": 8091, "pid": 241019, "processName": "java(org.apache.catalina.startup.Bootstrap)", "bindIp": "0000:0000:0000:0000:0000:0000:0000:0000", "status": -1, "bizType": "Linux"}], "accounts": [{"agentId": "test_agent_id", "displayIp": "127.0.0.1", "connectionIp": "127.0.0.1", "externalIp": "*******", "internalIp": "127.0.0.1", "bizGroupId": 1, "bizGroup": "未分组主机", "remark": null, "hostTagList": ["test"], "hostname": "测试-HOST", "uid": 992, "gid": 986, "groups": ["geoclue"], "name": "geoclue", "status": 0, "home": "/var/lib/geoclue", "shell": "/sbin/nologin", "loginStatus": 0, "lastLoginTime": "1970-01-01 08:00:00", "pwdMaxDays": -1, "pwdMinDays": -1, "pwdWarnDays": -1, "sshAcl": "", "comment": "User for geoclue", "lastLoginTty": "", "lastLoginIp": "", "expireTime": null, "expired": false, "fullName": null, "sudoAccesses": [], "root": false, "description": null, "type": null, "lastChangPwdTime": "2021-01-28 08:00:00", "accountLoginType": 0, "interactiveLoginType": 0, "passwordInactiveDays": null, "sudo": false, "authorizedKeys": [], "bizType": "Linux"}], "processes": [{"agentId": "test_agent_id", "displayIp": "127.0.0.1", "connectionIp": "127.0.0.1", "externalIp": "*******", "internalIp": "127.0.0.1", "bizGroupId": 1, "bizGroup": "未分组主机", "remark": null, "hostTagList": ["test"], "hostname": "测试-HOST", "startTime": "2022-07-21 14:17:47", "version": null, "root": false, "prtCount": null, "md5": "708c8760385810080c4d17fa84d325ca", "packageName": "bash", "packageVersion": "4.2.46-34.el7", "installByPm": true, "pid": 241862, "ppid": 241861, "path": "/usr/bin/bash", "startArgs": "/bin/bash /opt/jfrog/artifactory/app/bin/../event/bin/event.sh start", "state": "S", "uname": "artifactory", "uid": 1002, "gname": "artifactory", "gid": 1004, "tty": "?", "name": "event.sh", "sessionId": null, "sessionName": null, "type": null, "description": null, "groups": null, "size": null, "bizType": "Linux"}], "apps": [{"agentId": "test_agent_id", "displayIp": "127.0.0.1", "connectionIp": "127.0.0.1", "externalIp": "*******", "internalIp": "127.0.0.1", "bizGroupId": 1, "bizGroup": "未分组主机", "remark": null, "hostTagList": ["test"], "hostname": "测试-HOST", "name": "SFTP", "version": "", "uname": "root", "binPath": "/usr/libexec/sftp-server", "configPath": "", "processes": [{"pid": 188922, "name": "sftp-server", "uname": "root"}], "bizType": "Linux"}], "groups": [{"agentId": "test_agent_id", "displayIp": "127.0.0.1", "connectionIp": "127.0.0.1", "externalIp": "*******", "internalIp": "127.0.0.1", "bizGroupId": 1, "bizGroup": "未分组主机", "remark": null, "hostTagList": ["test"], "hostname": "测试-HOST", "name": "abrt", "gid": 173, "members": [{"name": "abrt", "type": null}], "description": null, "bizType": "Linux"}], "webapps": [{"agentId": "test_agent_id", "displayIp": "127.0.0.1", "connectionIp": "127.0.0.1", "externalIp": "*******", "internalIp": "127.0.0.1", "bizGroupId": 2, "bizGroup": "未分组主机", "remark": null, "hostTagList": [], "hostname": "测试-HOST", "version": "1.4.3", "webroot": null, "serverName": "tomcat", "domainName": "localhost", "appName": "UEditor", "rootPath": "D:\\wjf\\apache-tomcat-9.0.35\\webapps\\ks\\ueditor", "plugins": [], "bizType": "Windows"}], "jars": [{"agentId": "test_agent_id", "displayIp": "127.0.0.1", "connectionIp": "127.0.0.1", "externalIp": "*******", "internalIp": "127.0.0.1", "bizGroupId": 1, "bizGroup": "未分组主机", "remark": null, "hostTagList": ["test"], "hostname": "测试-HOST", "name": "artifactory-config-7.17.13.jar", "version": "7.17.13", "path": "/opt/jfrog/artifactory/app/artifactory/tomcat/webapps/artifactory/WEB-INF/lib/artifactory-config-7.17.13.jar", "executable": false, "type": 8, "bizType": "Linux", "parent_package": "artifactory-config.jar"}], "websites": [{"agentId": "test_agent_id", "displayIp": "127.0.0.1", "connectionIp": "127.0.0.1", "externalIp": "*******", "internalIp": "127.0.0.1", "bizGroupId": 1, "bizGroup": "未分组主机", "remark": null, "hostTagList": ["test"], "hostname": "测试-HOST", "pid": 241019, "allow": null, "deny": null, "cmd": "/opt/jfrog/artifactory/app/third-party/java/bin/java -Djava.util.logging.config.file=/opt/jfrog/artifactory/app/artifactory/tomcat/conf/logging.properties -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager -Djdk.tls.ephemeralDHKeySize=2048 -Djava.protocol.handler.pkgs=org.apache.catalina.webresources -Dorg.apache.catalina.security.SecurityListener.UMASK=0027 -server -Xms512m -Xmx2g -XX:+UseG1GC -XX:OnOutOfMemoryError=kill -9 %p --add-opens java.base/java.util=ALL-UNNAMED --add-opens java.base/java.lang.reflect=ALL-UNNAMED --add-opens java.base/java.lang.invoke=ALL-UNNAMED --add-opens java.base/java.text=ALL-UNNAMED --add-opens java.base/java.nio=ALL-UNNAMED --add-opens java.desktop/java.awt.font=ALL-UNNAMED -Dfile.encoding=UTF8 -Djruby.compile.invokedynamic=False -Djruby.bytecode.version=1.8 -Dorg.apache.tomcat.util.buf.UDecoder.ALLOW_ENCODED_SLASH=true -Djava.security.egd=file:/dev/./urandom -Dartdist=zip -Djf.product.home=/opt/jfrog/artifactory -Djruby.bytecode.version=1.8 -Dignore.endorsed.dirs= -classpath /opt/jfrog/artifactory/app/artifactory/tomcat/bin/bootstrap.jar:/opt/jfrog/artifactory/app/artifactory/tomcat/bin/tomcat-juli.jar -Dcatalina.base=/opt/jfrog/artifactory/app/artifactory/tomcat -Dcatalina.home=/opt/jfrog/artifactory/app/artifactory/tomcat -Djava.io.tmpdir=/opt/jfrog/artifactory/var/work/artifactory/tomcat/temp org.apache.catalina.startup.Bootstrap start", "domains": [{"name": "localhost", "title": "", "ip": null}], "user": "artifactory", "type": "java", "port": 8081, "proto": "http", "portStatus": -1, "securityEnabled": false, "virtualDirs": [], "root": {"path": "/", "physicalPath": "/opt/jfrog/artifactory/app/artifactory/tomcat/webapps/ROOT", "root": true, "owner": "artifactory", "group": "artifactory", "permission": "rwxr-xr-x", "acls": [], "appPath": null, "appPool": {}}, "virtualDirsCount": null, "bindingCount": 0, "deployPath": "/opt/jfrog/artifactory/app/artifactory/tomcat/webapps", "configName": null, "state": 0, "path": "/opt/jfrog/artifactory/app/artifactory/tomcat/webapps/ROOT", "bizType": "Linux"}], "webframes": [{"agentId": "test_agent_id", "displayIp": "127.0.0.1", "connectionIp": "127.0.0.1", "externalIp": "*******", "internalIp": "127.0.0.1", "bizGroupId": 1, "bizGroup": "未分组主机", "remark": null, "hostTagList": ["test"], "hostname": "测试-HOST", "name": "jackson", "version": "2.11.4,2.9.10,2.10.1", "type": "java", "serverName": "tomcat", "domainName": null, "webAppDir": null, "bizType": "Linux"}], "dbs": [{"agentId": "test_agent_id", "displayIp": "127.0.0.1", "connectionIp": "127.0.0.1", "externalIp": "*******", "internalIp": "127.0.0.1", "bizGroupId": 2, "bizGroup": "未分组主机", "remark": null, "hostTagList": [], "hostname": "测试-HOST", "name": "oracle", "version": "********", "port": -1, "protoType": "", "user": "SYSTEM", "bindIp": "", "confPath": "D:\u0007pp\\oracle\\product\t.2.0\\dbhome_1\network\u0007dmin\\listener.ora", "logPath": "D:\\oracle\\diag\rdbms\testdb\testdb\trace\u0007lert_testdb.log", "dataDir": "D:\\oracle\\oradata\testdb", "pluginDir": null, "rest": null, "auth": false, "web": false, "webPort": null, "webAddress": "https://localhost:1158/em", "regionServer": null, "dbName": null, "loginModel": 0, "auditLevel": 0, "sysLogPath": null, "mainDbPath": null, "bizType": "Windows"}], "services": [{"agentId": "test_agent_id", "displayIp": "127.0.0.1", "connectionIp": "127.0.0.1", "externalIp": "*******", "internalIp": "127.0.0.1", "bizGroupId": 1, "bizGroup": "未分组主机", "remark": null, "hostTagList": ["test"], "hostname": "测试-HOST", "name": "vm-agent", "initLevel": 3, "defaultOpen": true, "rc0": 0, "rc1": 0, "rc2": 1, "rc3": 1, "rc4": 1, "rc5": 1, "rc6": 0, "rc7": 0, "xinetd": false, "user": null, "enable": null, "startType": null, "publisher": null, "bizType": "Linux"}], "tasks": [{"agentId": "test_agent_id", "displayIp": "127.0.0.1", "connectionIp": "127.0.0.1", "externalIp": "*******", "internalIp": "127.0.0.1", "bizGroupId": 1, "bizGroup": "未分组主机", "remark": null, "hostTagList": ["test"], "hostname": "测试-HOST", "user": "root", "execTime": "01 * * * * ", "execPath": "/etc/cron.hourly/0anacron", "conf": "/etc/cron.d/0hourly", "taskTime": "2019-08-09 07:07:24", "taskId": 0, "taskType": "CRONTAB", "crondOpen": true, "bizType": "Linux"}], "envs": [{"agentId": "test_agent_id", "displayIp": "127.0.0.1", "connectionIp": "127.0.0.1", "externalIp": "*******", "internalIp": "127.0.0.1", "bizGroupId": 1, "bizGroup": "未分组主机", "remark": null, "hostTagList": ["test"], "hostname": "测试-HOST", "user": "root", "key": "BASH_VERSION", "value": "'4.2.46(2)-release'", "sysEnv": false, "bizType": "Linux"}], "kernelmodules": [{"agentId": "test_agent_id", "displayIp": "127.0.0.1", "connectionIp": "127.0.0.1", "externalIp": "*******", "internalIp": "127.0.0.1", "bizGroupId": 1, "bizGroup": "未分组主机", "remark": null, "hostTagList": ["test"], "hostname": "测试-HOST", "moduleName": "sch_tbf", "description": "", "path": "/lib/modules/3.10.0-1160.25.1.el7.x86_64/kernel/net/sched/sch_tbf.ko.xz", "version": "", "size": 13107, "depends": [], "holders": [], "bizType": "Linux"}], "packages": [{"agentId": "test_agent_id", "displayIp": "127.0.0.1", "connectionIp": "127.0.0.1", "externalIp": "*******", "internalIp": "127.0.0.1", "bizGroupId": 1, "bizGroup": "未分组主机", "remark": null, "hostTagList": ["test"], "hostname": "测试-HOST", "name": "perl-Email-Address", "version": "1.898", "release": "3.el7", "type": "rpm", "installTime": "2021-01-28 13:08:25", "summary": "RFC 2822 Address Parsing and Creation ", "publisher": null, "path": null, "productVersion": null, "pkgSize": null, "bizType": "Linux"}], "patches": [{"id": "659b2779a57fc424787ebe65", "agentId": "48e9196e7e28d8a4", "displayIp": "**************", "internalIp": "**************", "externalIp": null, "hostname": "WIN-A1EOQBIRJPK", "group": 2, "remark": null, "hostTags": null, "patchId": "99a0b90d-2519-4700-be0c-e6c7b5bd04ec", "whiteRuleEffect": null, "businessImpact": null, "source": 2, "bizType": "windows", "patch": {"patchId": "99a0b90d-2519-4700-be0c-e6c7b5bd04ec", "kbNum": "4103723", "bulletId": "", "patchName": "2018-05 Cumulative Update for Windows Server 2016 for x64-based Systems", "severity": 1, "publishTime": "2018-05-08 17:00:05", "apps": null, "desc": "脚本引擎在 Microsoft 浏览器中处理内存中对象的方式中存在远程执行代码漏洞。该漏洞可能以一种攻击者可以在当前用户的上下文中执行任意代码的方式损坏内存。成功利用该漏洞的攻击者可以获得与当前用户相同的用户权限。如果当前用户使用管理用户权限登录，成功利用此漏洞的攻击者便可控制受影响的系统。\r\n当 .NET 和 .NET Core 不正确地处理 XML 文档时，存在拒绝服务漏洞。成功利用此漏洞的攻击者可能会导致 .NET 应用程序拒绝服务。\r\nWindows 中存在一个安全功能绕过漏洞，可能允许攻击者绕过 Device Guard。成功利用此漏洞的攻击者可以避开计算机上的用户模式代码完整性 (UMCI) 策略。\r\n当 Win32k 组件无法正确处理内存中的对象时，Windows 中存在特权提升漏洞。成功利用此漏洞的攻击者可以在内核模式中运行任意代码。\r\n当受影响的 Microsoft 浏览器不正确地处理内存中的对象时，存在信息泄漏漏洞。成功利用此漏洞的攻击者可以获取信息，从而进一步入侵用户系统。", "check": null, "remedDesc": null, "remedCmd": null, "installPackage": "http://www.catalog.update.microsoft.com/Search.aspx?q=99a0b90d-2519-4700-be0c-e6c7b5bd04ec", "cvssScore": "9", "cvssDetail": "CVSS2#AV:N/AC:M/Au:N/C:C/I:C/A:C", "ref": "[{\"url\": \"https://nvd.nist.gov/vuln/detail/\", \"name\": \"cve\", \"values\": [\"CVE-2018-0765\", \"CVE-2018-0824\", \"CVE-2018-0854\", \"CVE-2018-0943\", \"CVE-2018-0951\", \"CVE-2018-0953\", \"CVE-2018-0954\", \"CVE-2018-0955\", \"CVE-2018-0958\", \"CVE-2018-0959\", \"CVE-2018-0961\", \"CVE-2018-1022\", \"CVE-2018-1025\", \"CVE-2018-1039\", \"CVE-2018-8112\", \"CVE-2018-8114\", \"CVE-2018-8122\", \"CVE-2018-8124\", \"CVE-2018-8126\", \"CVE-2018-8127\", \"CVE-2018-8129\", \"CVE-2018-8132\", \"CVE-2018-8133\", \"CVE-2018-8134\", \"CVE-2018-8136\", \"CVE-2018-8137\", \"CVE-2018-8145\", \"CVE-2018-8164\", \"CVE-2018-8165\", \"CVE-2018-8166\", \"CVE-2018-8167\", \"CVE-2018-8174\", \"CVE-2018-8178\", \"CVE-2018-8179\", \"CVE-2018-8897\", \"CVE-2018-0886\", \"CVE-2017-11927\"]}]", "restartOpts": 0, "hasExp": false, "isKernel": null, "isLocalEscalation": null, "isRemote": null, "hasPoc": null, "pocRefs": null, "impacts": [2, 3, 4, 5, 6], "firstCheckTime": null, "businessImpact": null, "source": 2}}], "vuls": [{"id": "62e595640bf0458200a2c81c", "agentId": "test_agent_id", "displayIp": "127.0.0.1", "connectionIp": "127.0.0.1", "externalIp": "*******", "internalIp": "127.0.0.1", "bizGroupId": 1, "bizGroup": "未分组主机", "remark": null, "hostTagList": null, "hostname": "测试-HOST", "vul": {"vulId": "QT042016002525", "vulName": "ImageMagick远程代码执行漏洞(CVE-2016-8707)", "apps": ["ImageMagick"], "desc": "在 ImageMagick的转换实用程序中， TIFF 图像压缩处理存在一个写边界的问题。攻击者能够利用一个精心构造的 TIFF 文件，导致内存越界写入问题，在特定情况下可以被用于远程代码执行。任何用户都可以利用特殊构造的TIFF触发这个漏洞。", "restartOpts": 2, "publicDate": "2016-12-03 00:00:00", "firstCheckTime": "2022-07-31 04:31:18", "family": 4, "severity": 2, "checkInfo": "版本比对检测原理：检查当前系统中ImageMagick版本是否在受影响版本内|版本比对检测结果：- ImageMagick\r\n  当前安装版本：*********-5.el7_9\r\n  漏洞修复版本：7.0.3-9.el7_2\r\n- ImageMagick-c++\r\n  当前安装版本：*********-5.el7_9\r\n  漏洞修复版本：7.0.3-9.el7_2\r\n- ImageMagick-perl\r\n  当前安装版本：*********-5.el7_9\r\n  漏洞修复版本：7.0.3-9.el7_2\r\n该主机存在此漏洞", "pocCheckInfo": "", "checkResult": null, "data": null, "remedDescription": "将漏洞检测结果中的软件包升级到对应漏洞修复版本及以上。\r\n参照安全补丁功能中该漏洞的修复命令进行升级，或者参照以下修复命令进行升级：\r\nCentOS/RHEL/Oracle Linux : sudo yum update -y 需要升级的软件包名(参考检测结果)\r\nSUSE : sudo zypper update -y 需要升级的软件包名(参考检测结果)\r\nUbuntu/Debian : sudo apt-get update && sudo apt-get install --only-upgrade -y 需要升级的软件包名(参考检测结果)\r\n例：若漏洞的检测结果中主机系统为 Debian 8，软件包名称为 imagemagick-6.q16，当前安装版本为 8:*******-5+deb8u7，对应漏洞修复版本为 8:*******-5+deb8u7，则漏洞修复命令为 sudo apt-get update && sudo apt-get install --only-upgrade -y imagemagick-6.q16", "hasExp": false, "kernel": false, "localEscalation": false, "remote": true, "hasPoc": false, "cvssScore": 7, "cvss": "AV:N/AC:M/Au:N/C:P/I:P/A:P", "refs": "CNVD-2016-11927,BID-94727", "expRefs": "CWE-787:\r\nhttps://cwe.mitre.org/data/definitions/787.html\r\nCNNVD-201612-171:\r\nhttp://www.cnnvd.org.cn/web/xxk/ldxqById.tag?CNNVD=CNNVD-201612-171\r\nhttp://www.talosintelligence.com/reports/TALOS-2016-0216/", "pocRefs": "", "cves": ["CVE-2016-8707"], "category": [9], "condition": "", "appVersion": "ImageMagick＜7.0.3-9\r\n (不同操作系统影响的应用版本不同，具体以检测结果为准)", "pocCheckResults": [{"executeType": 1, "checkResult": "- ImageMagick\r\n  当前安装版本：*********-5.el7_9\r\n  漏洞修复版本：7.0.3-9.el7_2\r\n- ImageMagick-c++\r\n  当前安装版本：*********-5.el7_9\r\n  漏洞修复版本：7.0.3-9.el7_2\r\n- ImageMagick-perl\r\n  当前安装版本：*********-5.el7_9\r\n  漏洞修复版本：7.0.3-9.el7_2\r\n该主机存在此漏洞"}]}, "vulId": "QT042016002525", "whiteRuleEffect": false, "bizType": "Linux"}], "publicIpRelation": [{"data-local_port": "728", "host-assetLevel": 10, "host-hostname": "test-ku-2", "host-hasDeleted": false, "data-foreign_ip": "::", "host-displayIp": "************", "data-iptype": "ipv6", "host-ip": "************", "host-hostTagList": [], "data-local_ip": "::", "data-pid": "6921", "host-agentId": "ff34735a860fd6cc", "data-foreign_port": "*", "data-proc_name": "rpcbind", "_trimmed_fields": [], "host-agentStatus": 0}]}