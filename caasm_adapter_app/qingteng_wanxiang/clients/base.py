import hashlib
import json
import logging
import time
from functools import cached_property

from caasm_adapter.util.client import FetchJsonResultClient

log = logging.getLogger()


class QTWXFetchClient(FetchJsonResultClient):
    TIMEOUT = 300

    def __init__(self, connection, com_id=None, jwt_token=None, sign_key=None, session=None):
        super(QTWXFetchClient, self).__init__(connection, session)
        self.com_id = com_id
        self.jwt_token = jwt_token
        self.timestamp = str(int(time.time()))
        self.sign_key = sign_key

    def build_request_header(self, *args, **kwargs):
        header = {}
        if self.com_id and self.jwt_token and self.sign_key:
            sign = self._compute_request_sign(*args, **kwargs)
            header = {
                "Content-Type": "application/json",
                "comId": self.com_id,
                "timestamp": self.timestamp,
                "sign": sign,
                "Authorization": "Bearer " + self.jwt_token,
            }
        return header

    def _compute_request_sign(self, *args, **kwargs):
        info = ""
        if self.METHOD == "get":
            data = self.build_request_params(*args, **kwargs)
            for key in sorted(data.keys()):
                if data.get(key) is None:
                    continue
                info = info + key + str(data.get(key))
        else:
            data = self.build_request_json(*args, **kwargs)
            info = json.dumps(data)

        to_sign = self.com_id + info + self.timestamp + self.sign_key
        log.debug(f"sign str is {to_sign}")
        return hashlib.sha1(to_sign.encode()).hexdigest()

    @property
    def flag_key_name(self):
        return "success"

    @property
    def suc_flag(self):
        return True

    @property
    def data_key_name(self):
        return "data"

    @cached_property
    def username(self):
        return self.connection.get("username")

    @cached_property
    def password(self):
        return self.connection.get("password")


class QTWXPageClient(QTWXFetchClient):
    METHOD = "get"

    def build_request_params(self, page_index, page_size, record=None):
        params = {"page": page_index, "size": page_size}
        if record:
            params["agentId"] = record["agentId"]
        return params

    @property
    def data_key_name(self):
        return "rows"

    def check_biz_result(self, result):
        return True

    def clean_result(self, result):
        return super(QTWXPageClient, self).clean_result(result) or []


class QTWXDetailClient(QTWXFetchClient):
    METHOD = "get"

    def build_request_params(self, _id):
        return {}

    def build_request_url(self, _id):
        url = super(QTWXDetailClient, self).build_request_url()
        return url + f"/{_id}"

    def check_biz_result(self, result):
        return True

    @property
    def data_key_name(self):
        return ""
