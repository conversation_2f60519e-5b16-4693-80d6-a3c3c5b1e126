from caasm_adapter.util.exception import AdapterFetchAuthFailedException
from qingteng_wanxiang.clients.base import QTWXFetchClient


class QTWXAuthClient(QTWXFetchClient):
    URL = "/v1/api/auth"
    METHOD = "post"

    def build_request_json(self):
        return {"username": self.username, "password": self.password}

    def parse_biz_result(self, result, *args, **kwargs):
        result = super(QTWXAuthClient, self).parse_biz_result(result)
        return {"com_id": result["comId"], "sign_key": result["signKey"], "jwt_token": result["jwt"]}

    def error_handle(self, err, *args, **kwargs):
        raise AdapterFetchAuthFailedException

    def parse_error_handle(self, result, *args, **kwargs):
        raise AdapterFetchAuthFailedException
