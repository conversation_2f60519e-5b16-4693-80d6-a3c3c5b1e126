from qingteng_wanxiang.clients.account import QTWXWindowsAccountClient, QTWXLinuxAccountClient
from qingteng_wanxiang.clients.app import QTWXWindowsAppClient, QTWXLinuxAppClient
from qingteng_wanxiang.clients.asset import QTWXWindowsAssetClient, QTWXLinuxAssetClient
from qingteng_wanxiang.clients.auth import QTWXAuthClient
from qingteng_wanxiang.clients.db import QTWXWindowsDBClient, QTWXLinuxDBClient
from qingteng_wanxiang.clients.env import QTWXLinuxEnvClient
from qingteng_wanxiang.clients.frontend import (
    QTWXFrontendAuthClient,
    QTWXFrontendConfigClient,
    QTWXFrontendPublicIPRelationClient,
    QTWXFrontendGetLatestTaskIdClient,
    QTWXFrontendParentPackageClient,
    QTWXFrontendSubPackageClient,
)
from qingteng_wanxiang.clients.group import QTWXWindowsGroupClient, QTWXLinuxGroupClient
from qingteng_wanxiang.clients.jar import QTWX<PERSON>inuxJarClient
from qingteng_wanxiang.clients.kernelmodule import QTWXLinuxKernelModuleClient
from qingteng_wanxiang.clients.patch import (
    QTWXWindowsPatchClient,
    QTWXLinuxPatchClient,
    QTWXWindowsPatchDetailClient,
    QTWXLinuxPatchDetailClient,
)
from qingteng_wanxiang.clients.pkg import QTWXWindowsPkgClient, QTWXLinuxPkgClient
from qingteng_wanxiang.clients.port import QTWXWindowsPortClient, QTWXLinuxPortClient
from qingteng_wanxiang.clients.process import QTWXWindowsProcessClient, QTWXLinuxProcessClient
from qingteng_wanxiang.clients.service import QTWXWindowsServiceClient, QTWXLinuxServiceClient
from qingteng_wanxiang.clients.task import QTWXLinuxTaskClient
from qingteng_wanxiang.clients.vul import QTWXLinuxVulClient, QTWXLinuxVulDetailClient
from qingteng_wanxiang.clients.webapp import QTWXWindowsWebAppClient, QTWXLinuxWebAppClient
from qingteng_wanxiang.clients.webframe import QTWXWindowsWebFrameClient, QTWXLinuxWebFrameClient
from qingteng_wanxiang.clients.website import QTWXWindowsWebsiteClient, QTWXLinuxWebsiteClient


class QueryType:
    DIRECT = "direct"
    INDIRECT = "indirect"


class FetchType(object):
    AUTH = "auth"
    ACCOUNT = "accounts"
    APP = "apps"
    ASSET = "host"
    DB = "dbs"
    ENV = "envs"
    GROUP = "groups"
    JAR = "jars"
    KERNEL_MODULE = "kernelmodules"
    PATCH = "patches"
    PATCH_DETAIL = "patch"
    VUL = "vuls"
    VUL_DETAIL = "vul"
    PORT = "ports"
    PKG = "packages"
    PROCESS = "processes"
    SERVICE = "services"
    TASK = "tasks"
    WEB_APP = "webapps"
    WEB_FRAME = "webframes"
    WEB_SITE = "websites"

    FRONTEND_PUBLIC_KEY = "frontend_public_key"
    FRONTEND_AUTH = "frontend_auth"
    FRONTEND_LATEST_TASK_ID = "frontend_latest_task_id"
    FRONTEND_PUBLIC_IP_RELATION = "publicIpRelation"
    FRONTEND_PARENT_PACKAGE = "frontend_parent_package"
    FRONTEND_SUB_PACKAGE = "frontend_sub_package"


class OsType(object):
    LINUX = "linux"
    WINDOWS = "windows"


WINDOWS_CLASS_MAPPER = {
    FetchType.ACCOUNT: QTWXWindowsAccountClient,
    FetchType.ASSET: QTWXWindowsAssetClient,
    FetchType.APP: QTWXWindowsAppClient,
    FetchType.DB: QTWXWindowsDBClient,
    FetchType.GROUP: QTWXWindowsGroupClient,
    FetchType.PORT: QTWXWindowsPortClient,
    FetchType.PKG: QTWXWindowsPkgClient,
    FetchType.PROCESS: QTWXWindowsProcessClient,
    FetchType.SERVICE: QTWXWindowsServiceClient,
    FetchType.WEB_APP: QTWXWindowsWebAppClient,
    FetchType.WEB_FRAME: QTWXWindowsWebFrameClient,
    FetchType.WEB_SITE: QTWXWindowsWebsiteClient,
    FetchType.PATCH: QTWXWindowsPatchClient,
}

LINUX_CLASS_MAPPER = {
    FetchType.ACCOUNT: QTWXLinuxAccountClient,
    FetchType.ASSET: QTWXLinuxAssetClient,
    FetchType.APP: QTWXLinuxAppClient,
    FetchType.DB: QTWXLinuxDBClient,
    FetchType.ENV: QTWXLinuxEnvClient,
    FetchType.GROUP: QTWXLinuxGroupClient,
    FetchType.JAR: QTWXLinuxJarClient,
    FetchType.KERNEL_MODULE: QTWXLinuxKernelModuleClient,
    FetchType.PORT: QTWXLinuxPortClient,
    FetchType.PKG: QTWXLinuxPkgClient,
    FetchType.PROCESS: QTWXLinuxProcessClient,
    FetchType.SERVICE: QTWXLinuxServiceClient,
    FetchType.TASK: QTWXLinuxTaskClient,
    FetchType.WEB_APP: QTWXLinuxWebAppClient,
    FetchType.WEB_FRAME: QTWXLinuxWebFrameClient,
    FetchType.WEB_SITE: QTWXLinuxWebsiteClient,
    FetchType.VUL: QTWXLinuxVulClient,
    FetchType.PATCH: QTWXLinuxPatchClient,
}

AUTH_CLASS_MAPPER = {
    FetchType.AUTH: QTWXAuthClient,
}

FRONT_CLASS_MAPPER = {
    FetchType.FRONTEND_PUBLIC_KEY: QTWXFrontendConfigClient,
    FetchType.FRONTEND_PUBLIC_IP_RELATION: QTWXFrontendPublicIPRelationClient,
    FetchType.FRONTEND_LATEST_TASK_ID: QTWXFrontendGetLatestTaskIdClient,
    FetchType.FRONTEND_PARENT_PACKAGE: QTWXFrontendParentPackageClient,
    FetchType.FRONTEND_SUB_PACKAGE: QTWXFrontendSubPackageClient,
    FetchType.FRONTEND_AUTH: QTWXFrontendAuthClient,
}

WINDOWS_DETAIL_CLASS_MAPPER = {
    FetchType.PATCH_DETAIL: QTWXWindowsPatchDetailClient,
}

LINUX_DETAIL_CLASS_MAPPER = {
    FetchType.VUL_DETAIL: QTWXLinuxVulDetailClient,
    FetchType.PATCH_DETAIL: QTWXLinuxPatchDetailClient,
}

_ASSET_INDIRECT_TYPES = [
    FetchType.ACCOUNT,
    FetchType.APP,
    FetchType.DB,
    FetchType.ENV,
    FetchType.GROUP,
    FetchType.JAR,
    FetchType.KERNEL_MODULE,
    FetchType.PORT,
    FetchType.PKG,
    FetchType.PROCESS,
    FetchType.SERVICE,
    FetchType.TASK,
    FetchType.WEB_APP,
    FetchType.WEB_FRAME,
    FetchType.WEB_SITE,
]

INDIRECT_TYPE_MAPPER = {
    FetchType.ASSET: _ASSET_INDIRECT_TYPES,
}

OS_TYPE_CLASS_MAPPER = {
    OsType.WINDOWS: WINDOWS_CLASS_MAPPER,
    OsType.LINUX: LINUX_CLASS_MAPPER,
}
