name: qingteng_wanxiang
display_name: "青藤万相"
description: "青藤云安全以服务器安全为核心，采用自适应安全架构，将预测、防御、监控和响应能力融为一体，构建基于主机端的安全态势感知平台，为用户提供持续的安全监控、分析和快速响应能力"
type: "主机防护"
company: "青藤云"
logo: "qingteng_wanxiang.png"
version: "v0.1"
priority: 100
properties:
  - Agent
  - 主机防护
  - HIDS
  - 漏洞扫描

connection:
  - name: address
    type: url
    required: true
    display_name: "请求地址"
    description: "青藤console系统地址,通过调用万相后端API方式接入，默认端口为6000"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'

  - name: frontend_address
    type: url
    required: false
    display_name: "管理界面地址"
    description: "青藤管理界面地址。如果需要采集管理界面上的数据，请填写该地址。"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'

  - name: username
    type: string
    required: true
    display_name: "用户名称"
    description: "青藤console系统中的用户信息"
    validate_rules:
      - name: length
        error_hint: "用户密码格式无效。长度最小不得小于2，最大不得大于100"
        setting:
          min: 2
          max: 100

  - name: password
    type: password
    required: true
    display_name: "用户密码"
    description: "青藤console系统中的用户密码"
    validate_rules:
      - name: length
        error_hint: "用户密码格式无效。长度最小不得小于6，最大不得大于200"
        setting:
          min: 6
          max: 200


fetch_setting:
  point: "qingteng_wanxiang.fetch:find_asset"
  size: 100
  test_auth_point: "qingteng_wanxiang.fetch:get_auth_connection"
  is_need_test_service: true
  test_connection_point: "qingteng_wanxiang.fetch:check_connection"
  worker: 1
  condition_point: "qingteng_wanxiang.fetch:build_query_condition"
  fetch_type_mapper:
    asset:
      - host
      - vuls
  cleaner_mapper:
    asset:
      host:
        - "qingteng_wanxiang.cleaners.patch_cleaner:PatchCleaner"
        - "qingteng_wanxiang.cleaners.vul_cleaner:VulCleaner"


merge_setting:
  size: 300
  setting:
    asset:
      host:
        fields:
          - agentId

convert_setting:
  size: 100
  before_executor_mapper: { }
  executor_mapper: { }

fabric_setting:
  choose_point_mapper:
    asset: "qingteng_wanxiang.fabric:choose_new_record"