from caasm_adapter.sdk.adapter_connection import connection_sdk
from caasm_adapter.sdk.adapter_fetch import fetch_sdk
from qingteng_wanxiang.enrich import Enricher
from qingteng_wanxiang.frontend import FrontendManager
from qingteng_wanxiang.manage import QTManager


def find_asset(fetch_type, page_index=0, page_size=1, condition=None, **kwargs):
    manager = condition.get("manager")
    data = manager.find(fetch_type, page_index, page_size)
    result = fetch_sdk.build_asset(data, fetch_type)
    return fetch_sdk.return_success(result)


def get_auth_connection(connection, session=None):
    QTManager(connection, session=session).auth()


def check_connection(connection, session=None):
    address = connection.get("address")
    return connection_sdk("http", address=address).handle()


def build_query_condition(connection, session, fetch_type):
    condition = _check_condition_build(connection)
    if condition:
        return condition

    _build_condition_class(condition, connection, session)
    return condition


def _check_condition_build(connection):
    if "condition" in connection:
        return connection["condition"]
    condition = {}
    connection["condition"] = condition
    return condition


def _build_condition_class(result, connection, session):
    manager = QTManager(connection, session, result)
    enricher = Enricher(connection, session, result, manager.call)
    frontend = FrontendManager(connection, session, result)

    result["manager"] = manager
    result["enricher"] = enricher
    result["frontend"] = frontend
