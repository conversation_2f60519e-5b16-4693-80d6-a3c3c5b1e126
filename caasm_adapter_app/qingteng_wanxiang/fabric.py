from caasm_adapter.sdk.adapter_fabric import fabric_sdk

_special_versions = ["3.0.0b3-3.13.0-Rel-2017-09-22_10-57-39-45"]
_words = ["WIN-Rel", "Rel", "aarch", "WIN"]


def choose_new_record(records):
    clean_func = _clean_version
    check_func = _check_version

    return fabric_sdk.choose_record_by_agent_version(records, clean_func=clean_func, my_check=check_func)


def _check_version(x_version, y_version):
    if x_version in _special_versions:
        return False, 1

    if y_version in _special_versions:
        return False, 1
    return True, None


def _clean_version(version):
    versions = version.split("-")
    new_versions = []
    for info in versions:
        ignore = False
        for word in _words:
            if word in info:
                ignore = True
                break

        if ignore:
            continue
        new_versions.append(info)

    new_version = "-".join(new_versions)
    return new_version.replace("-", ".").replace("_", ".")
