import logging
from functools import wraps

from qingteng_wanxiang.constant import FRONT_CLASS_MAPPER, FetchType

log = logging.getLogger()


def _check_work(default):
    def inner(func):
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            if not self.address:
                return default()
            return func(self, *args, **kwargs)

        return wrapper

    return inner


class FrontendManager(object):
    def __init__(self, connection, session=None, condition=None):
        self._connection = connection
        self._condition = {} if condition is None else condition
        self._session = session
        self._auth()
        self._init()

    @_check_work(lambda: None)
    def _init(self):
        self._initialize_parent_jar()
        self._initialize_frontend_latest_task_id()

    def _initialize_parent_jar(self):
        if "frontend_parent_jars" in self._condition:
            return
        self._condition["frontend_parent_jars"] = self.find_loop(self.find_parent_jar_package)

    def _initialize_frontend_latest_task_id(self):
        if "frontend_latest_task_id" in self._condition:
            return
        self._condition["frontend_latest_task_id"] = self.get_latest_task_id()

    @_check_work(lambda: None)
    def _auth(self):
        if self.cookies:
            return
        public_key = self._call(FetchType.FRONTEND_PUBLIC_KEY)
        self._condition["frontend_cookies"] = self._call(FetchType.FRONTEND_AUTH, public_key=public_key)

    @_check_work(list)
    def find_public_ip_relation(self, task_id, agent_id, page_index=1, page_size=500):
        return self._call(
            FetchType.FRONTEND_PUBLIC_IP_RELATION,
            task_id=task_id,
            page_index=page_index,
            page_size=page_size,
            agent_id=agent_id,
        )

    @_check_work(list)
    def find_parent_jar_package(self, page_index=1, page_size=500):
        return self._call(FetchType.FRONTEND_PARENT_PACKAGE, page_index=page_index, page_size=page_size)

    @_check_work(list)
    def find_sub_jar_package(self, page_index=1, page_size=500, names=None, agent_id=None):
        return self._call(
            FetchType.FRONTEND_SUB_PACKAGE,
            page_index=page_index,
            page_size=page_size,
            names=names,
            agent_id=agent_id,
        )

    @_check_work(lambda: None)
    def get_latest_task_id(self):
        return self._call(FetchType.FRONTEND_LATEST_TASK_ID)

    def _call(self, client_type, *args, **kwargs):
        instance = FRONT_CLASS_MAPPER[client_type](self._connection, self._session, cookies=self.cookies)
        return instance.handle(*args, **kwargs)

    @property
    def cookies(self):
        return self._condition.get("frontend_cookies")

    @property
    def address(self):
        return self._connection.get("frontend_address")
