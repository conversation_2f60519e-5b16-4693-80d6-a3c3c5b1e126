from alibabacloud_rds20140815 import models

from ali_cloud.clients.db.rds._base import RDSDetailMixIn, RDSMixIn


class RDSSSLListClient(RDSDetailMixIn):
    def send_openapi_request(self, model, runtime):
        return self.client.describe_dbinstance_sslwith_options(model, runtime)

    def build_request_model(self, record):
        db_instance_id = record["DBInstanceId"]
        return models.DescribeDBInstanceSSLRequest(dbinstance_id=db_instance_id)

    def parse_biz_core(self, result):
        return result.body


class RDSTDEListClient(RDSDetailMixIn):
    def send_openapi_request(self, model, runtime):
        return self.client.describe_dbinstance_tdewith_options(model, runtime)

    def build_request_model(self, record):
        db_instance_id = record["DBInstanceId"]
        return models.DescribeDBInstanceTDERequest(dbinstance_id=db_instance_id)

    def parse_biz_core(self, result):
        return result.body
