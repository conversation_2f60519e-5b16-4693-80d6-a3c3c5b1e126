from alibabacloud_ecs20140526 import models
from alibabacloud_nas20170626 import models

from ali_cloud.clients.nas._base import NASMixIn


class NasFileSystemListClient(NASMixIn):
    def send_openapi_request(self, model, runtime):
        return self.client.describe_file_systems_with_options(model, runtime)

    def build_request_model(self, page_size, page_index):
        return models.DescribeFileSystemsRequest(page_size=page_size, page_number=page_index)

    def parse_biz_core(self, result):
        return result.body.file_systems.file_system
