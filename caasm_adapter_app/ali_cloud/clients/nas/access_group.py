from alibabacloud_ecs20140526 import models
from alibabacloud_nas20170626 import models

from ali_cloud.clients.nas._base import NASMixIn


class NasAccessGroupListClient(NASMixIn):
    def send_openapi_request(self, model, runtime):
        return self.client.describe_access_groups_with_options(model, runtime)

    def build_request_model(self, page_size, page_index):
        return models.DescribeAccessGroupsRequest(page_size=page_size, page_number=page_index)

    def parse_biz_core(self, result):
        return result.body.access_groups.access_group


class NasAccessGroupRuleListClient(NASMixIn):
    def send_openapi_request(self, model, runtime):
        return self.client.describe_access_rules_with_options(model, runtime)

    def build_request_model(self, access_group, page_size, page_index):
        access_group_name = access_group["AccessGroupName"]
        return models.DescribeAccessRulesRequest(
            access_group_name=access_group_name, page_size=page_size, page_number=page_index
        )

    def parse_biz_core(self, result):
        return result.body.access_rules.access_rule
