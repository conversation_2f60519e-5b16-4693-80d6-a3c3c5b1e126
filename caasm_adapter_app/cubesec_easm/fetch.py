from caasm_adapter.sdk.adapter_fetch import fetch_sdk
from cubesec_easm.manage import CUBESECEASMManager, ClientType


def find_asset(connection, fetch_type, page_index=0, page_size=1, session=None, **kwargs):
    records = _build_manager(connection, session).find(fetch_type, page_index, page_size)

    result = fetch_sdk.build_asset(records, fetch_type)
    return fetch_sdk.return_success(result)


def get_auth_connection(connection, session):
    _build_manager(connection, session).find(ClientType.IP, 0, 1)


def _build_manager(connection, session):
    return CUBESECEASMManager(connection, session)
