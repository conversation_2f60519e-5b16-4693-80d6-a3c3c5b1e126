from caasm_adapter.util.exception import AdapterFetchApiNotSupportException
from cubesec_easm.clients.domain import CUBESECEASMDomainClient
from cubesec_easm.clients.ip import CUBESECEASMIPClient
from cubesec_easm.clients.port import CUBESECEASMPortClient


class ClientType(object):
    IP = "internet_ip"
    DOMAIN = "domain_name"
    PORT = "port_server"


class CUBESECEASMManager(object):
    _CLASS_DEFINE = {
        ClientType.IP: CUBESECEASMIPClient,
        ClientType.DOMAIN: CUBESECEASMDomainClient,
        ClientType.PORT: CUBESECEASMPortClient,
    }

    def __init__(self, connection, session=None):
        self._connection = connection
        self._session = session

    def find(self, fetch_type, page_index, page_size):
        limit = page_size
        offset = page_index * page_size
        return self._instance(fetch_type).handle(limit=limit, offset=offset)

    def _instance(self, api_type):
        clazz = self._CLASS_DEFINE.get(api_type)
        if not clazz:
            raise AdapterFetchApiNotSupportException()
        return clazz(self._connection, self._session)
