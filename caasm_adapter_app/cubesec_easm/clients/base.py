import logging

from caasm_adapter.util.client import FetchJsonResultClient
from caasm_adapter.util.exception import AdapterFetchAuthFailedException
from caasm_tool.util import extract

log = logging.getLogger()


class CUBESECEASMClient(FetchJsonResultClient):
    _auth_errors = ("ACCESSKEY or SECRETKEY error.", "Authentication credentials were not provided.")

    def build_request_header(self, *args, **kwargs):
        headers = {
            "Accept": "application/json",
            "Content-type": "application/json",
            "ACCESSKEY": self.access_key,
            "SECRETKEY": self.secret_key,
        }
        return headers

    @property
    def access_key(self):
        return self.connection.get("access_key", "")

    @property
    def secret_key(self):
        return self.connection.get("secret_key", "")

    @property
    def flag_key_name(self):
        return "status"

    @property
    def suc_flag(self):
        return ""

    def check_biz_result(self, result):
        return bool(extract(result, self.flag_key_name))

    @property
    def data_key_name(self):
        return "info"

    def parse_error_handle(self, result, *args, **kwargs):
        if extract(result, "info.detail") in self._auth_errors:
            raise AdapterFetchAuthFailedException
        super(CUBESECEASMClient, self).parse_error_handle(result, *args, **kwargs)
