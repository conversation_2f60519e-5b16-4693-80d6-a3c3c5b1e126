name: cubesec_easm
display_name: "魔方企业互联网安全监控平台"
description: "魔方企业互联网安全监控平台，全方位互联网资产发现，自动化应用识别。系统支持自动化及自定义发现两种任务模式。用户可通过输入主域名，如cubesec.cn，系统将根据主域名对整个互联网空间进行资产自动化发现与扫描，也可以通过手工导入IP或URL进行精准定向扫描。"
type: "互联网测绘"
company: "深圳市魔方安全科技有限公司"
version: "v0.1"
logo: "cubesec_easm.png"
priority: 1
properties:
  - EASM
  - 互联网测绘

connection:
  - name: address
    type: url
    required: true
    display_name: "请求地址"
    description: "请求地址"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'

  - name: access_key
    type: string
    required: true
    display_name: "access_key"
    description: "access_key"
    validate_rules:
      - name: length
        error_hint: "access_key格式无效。长度最小不得小于2，最大不得大于100"
        setting:
          min: 2
          max: 100

  - name: secret_key
    type: password
    required: true
    display_name: "secret_key"
    description: "secret_key"
    validate_rules:
      - name: length
        error_hint: "secret_key格式无效。长度最小不得小于6，最大不得大于200"
        setting:
          min: 6
          max: 200

fetch_setting:
  type: disposable
  fetch_type_mapper:
    asset:
      - port_server
      - internet_ip
      - domain_name

  worker: 5
  point: "cubesec_easm.fetch:find_asset"
  is_need_test_service: true
  test_auth_point: "cubesec_easm.fetch:get_auth_connection"
  size: 200
  cleaner_mapper:
    asset:
      internet_ip:
        - "cubesec_easm.cleaner.public_ip:PublicIPCleaner"
      port_server:
        - "cubesec_easm.cleaner.port_server:PortServerCleaner"

merge_setting:
  size: 200
  setting: { }

convert_setting:
  size: 200
  before_executor_mapper: { }
  executor_mapper: { }

fabric_setting:
  choose_point_mapper:
    asset: "cubesec_easm.fabric:choose_new_record"