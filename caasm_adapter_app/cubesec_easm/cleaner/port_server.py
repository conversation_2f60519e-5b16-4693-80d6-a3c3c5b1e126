import logging
import traceback

import yaml

from caasm_adapter.fetcher.cleaners.base import FetchBaseCleaner
from caasm_tool.util import hump2underline

log = logging.getLogger()


class PortServerCleaner(FetchBaseCleaner):
    def __init__(self, *args, **kwargs):
        self._clean_property_method = {
            "webfp": {"method": self.clean_webfp, "error_value": []},
            "ssl": {"method": self.clean_ssl, "error_value": {}},
        }
        super(PortServerCleaner, self).__init__(*args, **kwargs)

    @classmethod
    def clean_ssl(cls, data):
        if not data:
            return {}
        if not isinstance(data, dict):
            return {}
        data = {hump2underline(field): value for field, value in data.items()}

        subject_alt_names = data.get("subject_alt_name")
        if subject_alt_names:
            data["subject_alt_name"] = cls._build_name_value(subject_alt_names)

        subject = data.get("subject")
        if subject:
            data["subject"] = cls._build_name_value(subject)

        issuer = data.get("issuer")
        if issuer:
            new_issuer = None
            if isinstance(issuer, str):
                new_issuer = [{"name": "签署者", "value": issuer}]
            elif isinstance(issuer, list):
                new_issuer = cls._build_name_value(issuer)

            if new_issuer:
                data["issuer"] = new_issuer

        data["serial_number"] = str(data.get("serial_number") or "")

        version = data.get("version")
        if version is not None:
            data["version"] = str(version)
        return data

    @classmethod
    def clean_webfp(cls, data):
        return cls._build_name_value(data) if data else []

    def clean_single(self, record):
        result = {}
        for param_name, clean_method_detail in self._clean_property_method.items():
            clean_method = clean_method_detail.get("method")
            error_value = clean_method_detail.get("error_value")
            param_value = error_value
            try:
                ori_param_info = record.get(param_name)
                if isinstance(ori_param_info, str):
                    param_info = yaml.safe_load(ori_param_info)
                else:
                    param_info = ori_param_info
                param_value = clean_method(param_info) or param_value
            except Exception as e:
                log.warning(f"Parse error is {e}, detail is {traceback.format_exc()}")

            result[param_name] = param_value

        return result

    @classmethod
    def _build_name_value(cls, data):
        if not data:
            return
        result = []
        for info in cls._flag(data):
            if not isinstance(info, dict):
                info = {"name": info[0], "value": info[1]}

            if isinstance(info, dict):
                result.append(info)
        return result

    @classmethod
    def _flag(cls, data, result=None):
        if result is None:
            result = []

        for info in data:
            if not info:
                continue
            if isinstance(info, list) and isinstance(info[0], list):
                cls._flag(info, result)
            else:
                result.append(info)

        return result
