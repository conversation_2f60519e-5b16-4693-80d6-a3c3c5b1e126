# 需要修改的是肯定要改的，具体怎么改参考https://e.gitee.com/weilansec/projects/433637/docs?directory=1188256&page=1&program_id=433637&scope=root&sub_id=7656957
# 需要确认的是可能不改，根据实际业务情况决定

name: zhizhangyi_sdp
display_name: "SDP安全网关"
description: "指掌易SDP是一个基于“零信任”理念的安全接入网关，提供SPA单包授权、最小授权原则、持续信任评估等技术，将企业业务应用从互联网上“隐身”，避免被扫描和攻击，保证业务访问的安全性。"
type: "零信任"
company: "指掌易"
logo: "zhizhangyi_sdp.png" # 需要修改

version: "v1.0"
priority: 100
properties:
  - 零信任
  - VPN

connection: # 需要修改
  - name: username
    type: string
    required: true
    display_name: "用户名称"
    description: "用户信息"
    validate_rules:
      - name: length
        error_hint: "用户信息格式无效。长度最小不得小于2，最大不得大于200"
        setting:
          min: 2
          max: 100

  - name: password
    type: password
    required: true
    display_name: "用户密码"
    description: "用户密码"
    validate_rules:
      - name: length
        error_hint: "密码格式无效。长度最小不得小于6，最大不得大于200"
        setting:
          min: 6
          max: 200

  - name: address
    type: url
    required: true
    display_name: "地址"
    description: "地址信息"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'

  # 需要按照address的样例补全业务所需的剩余参数（比如username、password）

fetch_setting:
  type: disposable
  point: "zhizhangyi_sdp.fetch:find_asset"
  is_need_test_service: true
  test_auth_point: "zhizhangyi_sdp.fetch:auth"
  fetch_type_mapper:
    account:
      - account
  cleaner_mapper: {}