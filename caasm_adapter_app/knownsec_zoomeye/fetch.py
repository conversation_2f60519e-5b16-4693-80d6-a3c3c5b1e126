from caasm_adapter.sdk.adapter_fetch import fetch_sdk
from knownsec_zoomeye.manage import Manager


def find_asset(connection, fetch_type, page_index=0, session=None, **kwargs):
    manager = _build_manager(connection, session)
    records = manager.find_asset(page_index + 1)
    result = fetch_sdk.build_asset(records, fetch_type)
    return fetch_sdk.return_success(result)


def auth(connection, session):
    return _build_manager(connection, session)


def _build_manager(connection, session):
    return Manager(connection, session)
