name: "knownsec_zoomeye"
display_name: "zoomeye网络空间雷达系统"
description: "网络空间雷达系统是非常高效的私有化网络空间测绘系统，可以快速提供全面的网络空间资产探测和精准漏洞测绘，并将数据进行视觉化展现和集中输出，为政府、企事业及军工单位客户进行网络空间资产安全监管、管理及建立主动防御攻击系统提供决策依据和数据支撑。"
type: "互联网测绘"
company: "知道创宇"
logo: "knownsec_zoomeye.png"
version: "v0.1"
priority: 1
properties:
  - "互联网测绘"

connection:
  - name: address
    type: url
    required: true
    display_name: "地址"
    description: "地址信息"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'

  - name: username
    type: string
    required: true
    display_name: "用户名"
    description: "用户名称"
    validate_rules:
      - name: length
        error_hint: "用户名长度必须大于等于2且小于等于100"
        setting:
          min: 2
          max: 100

  - name: password
    type: password
    required: true
    display_name: "密码"
    description: "密码信息"
    validate_rules:
      - name: length
        error_hint: "密码长度必须大于等于6且小于等于100"
        setting:
          min: 6
          max: 100

  - name: domain
    type: string
    required: true
    display_name: "域名"
    description: "域名信息"
    validate_rules:
      - name: length
        error_hint: "域名信息长度必须大于等于2且小于等于200"
        setting:
          min: 2
          max: 200

fetch_setting:
  type: disposable
  point: "knownsec_zoomeye.fetch:find_asset"
  is_need_test_service: true
  test_auth_point: "knownsec_zoomeye.fetch:auth"
  size: 200
  fetch_type_mapper:
    asset:
      - port_server

fabric_setting:
  choose_point_mapper:
    asset: "knownsec_zoomeye.fabric:choose_new_record"

merge_setting:
  size: 1000
  setting: { }

convert_setting:
  size: 1000
  before_executor_mapper: { }
  executor_mapper: { }