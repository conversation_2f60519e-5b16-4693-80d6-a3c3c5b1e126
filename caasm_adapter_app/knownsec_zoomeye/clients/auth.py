from caasm_adapter.util.exception import AdapterFetchAuthFailedException
from knownsec_zoomeye.clients.base import KnownSecZoomEyeBaseClient


class KnownSecZoomEyeAuthClient(KnownSecZoomEyeBaseClient):
    URL = "/user/login"
    METHOD = "post"

    def build_request_json(self):
        username = self.connection.get("username")
        password = self.connection.get("password")
        return {"username": username, "password": password}

    @property
    def data_key_name(self):
        return "data.access_token"

    def parse_error_handle(self, result, *args, **kwargs):
        raise AdapterFetchAuthFailedException

    def error_handle(self, err, *args, **kwargs):
        raise AdapterFetchAuthFailedException

    def clean_result(self, result):
        if not result:
            return self.parse_error_handle(result)
        return super(KnownSecZoomEyeAuthClient, self).clean_result(result)
