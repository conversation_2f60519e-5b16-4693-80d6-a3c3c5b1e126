import abc

from caasm_adapter.util.client import FetchJsonResultClient


class KnownSecZoomEyeBaseClient(FetchJsonResultClient, metaclass=abc.ABCMeta):
    def __init__(self, connection, session=None, token=None):
        super(KnownSecZoomEyeBaseClient, self).__init__(connection, session)
        self._token = token

    def build_request_header(self, *args, **kwargs):
        headers = {}
        if self._token:
            headers["Authorization"] = "jwt " + self._token
        return headers

    def parse_response(self, response, *args, **kwargs):
        status_code = response.status_code

        result = {"code": False, "msg": "", "status_code": status_code, "data": None}

        if status_code != 200:
            result["msg"] = f"请求失败，http响应码是{status_code}"
            return result

        result["code"] = True
        result["data"] = response.json()
        return result

    @property
    def data_key_name(self):
        return "data"

    @property
    def suc_flag(self):
        return True

    @property
    def flag_key_name(self):
        return "code"
