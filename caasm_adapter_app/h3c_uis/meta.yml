name: "h3c_uis"
display_name: "超融合"
description: "H3C UIS超融合是新华三集团自主研发的面向下一代企业和行业数据中心的软件定义基础设施解决方案。在通用X86或ARM服务器上无缝集成计算虚拟化、存储虚拟化、网络虚拟化、安全虚拟化、数据库服务、运维监控管理、容灾备份、云业务流程交付等软件技术。H3C UIS全无损超融合将传统超融合内核升级为全新的超融合云原生内核，更好的满足云原生业务需求，灵活构建以应用为核心的云化数据中心。可自适应部署于私有云、混合云、边缘云的全域云场景，为客户提供极致融合、极优交付、极简上云、极宽场景、极速性能的全无损云计算基础架构，满足数据中心未来十年信息化发展任意阶段、任意规模、任意场景的技术架构变革需求。"
type: "主机防护"
company: "H3C"
logo: "h3c_uis.png"
version: "v0.1"
priority: 1
properties:
  - "主机防护"

connection:
  - name: username
    type: string
    required: true
    display_name: "用户名"
    description: "用户名称"
    validate_rules:
      - name: length
        error_hint: "用户名长度必须大于等于2且小于等于100"
        setting:
          min: 2
          max: 100

  - name: password
    type: password
    required: true
    display_name: "密码"
    description: "密码信息"
    validate_rules:
      - name: length
        error_hint: "密码长度必须大于等于6且小于等于100"
        setting:
          min: 6
          max: 100

  - name: address
    type: url
    required: true
    display_name: "地址"
    description: "地址信息"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'

fetch_setting:
  type: disposable
  point: "h3c_uis.fetch:find_asset"
  size: 1000
  is_need_test_service: true
  fetch_type_mapper:
    asset:
      - host
      - virtual_machine

fabric_setting:
  choose_point_mapper:
    asset: "h3c_uis.fabric:choose_new_record"

merge_setting:
  size: 50
  setting: {}

convert_setting:
  size: 50
  before_executor_mapper: {}
  executor_mapper: {}
