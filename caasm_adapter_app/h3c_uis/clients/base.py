import time

from caasm_adapter.util.client import FetchJsonResultClient


class H3cUisBase(FetchJsonResultClient):
    METHOD = "GET"

    def build_request_header(self, *args, **kwargs):
        return {
            "Cookie": f'AC_TOKEN={self._connection.get("token")}',
            "Content-Type": "application/json",
            "consumes": "application/json",
        }

    @property
    def data_key_name(self):
        return "data"

    @property
    def suc_flag(self):
        return "success"

    @property
    def flag_key_name(self):
        return ""
