from caasm_adapter.util.client import FetchJsonResultClient


class UisAuth(FetchJsonResultClient):
    URL = "/uis/spring_check?encrypt=False&loginType=authorCenter&name={}&password={}"
    METHOD = "post"

    def build_request_url(self, *args, **kwargs):
        url = super(UisAuth, self).build_request_url()
        return url.format(self._connection.get("username"), self._connection.get("password"))

    def build_request_header(self, *args, **kwargs):
        return {"Content-Type": "application/json"}

    @property
    def data_key_name(self):
        return "acToken"

    @property
    def flag_key_name(self):
        return "acToken"

    @property
    def suc_flag(self):
        return "acToken"

    def parse_biz_result(self, result, *args, **kwargs):
        token = result.get(self.data_key_name)
        if not token:
            return self.parse_error_handle(result, *args, **kwargs)
        else:
            return result.get(self.data_key_name)
