from caasm_adapter.sdk.adapter_fetch import fetch_sdk
from h3c_uis.manage import H3cUisManager
import json


def find_asset(connection, fetch_type, page_index=0, page_size=30, session=None, condition=None, **kwargs):
    result = []
    uis = H3cUisManager(connection, None, fetch_type)
    result = uis.find_asset(page_index, page_size, **kwargs)
    result = fetch_sdk.build_asset(result, fetch_type)
    return fetch_sdk.return_success(result)


# find_asset({"username":"gongjimian","password":"<PERSON>jimian1@","address":"https://************/"},"host")
