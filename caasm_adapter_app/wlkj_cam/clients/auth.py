from caasm_adapter.util.exception import AdapterFetchAuthFailedException
from wlkj_cam.clients.base import WLKJBaseClient


class WLKJAuthClient(WLKJBaseClient):
    URL = "/api/auth/login"
    METHOD = "POST"

    @property
    def password(self):
        return self.connection.get("password", "")

    @property
    def username(self):
        return self.connection.get("username", "")

    def build_request_data(self):
        return {"username": self.username, "password": self.password}

    @property
    def data_key_name(self):
        return "token"

    @property
    def suc_flag(self):
        return self.username

    @property
    def flag_key_name(self):
        return "username"

    def clean_result(self, result):
        if not result:
            raise AdapterFetchAuthFailedException
        return result

    def parse_error_handle(self, result, *args, **kwargs):
        raise AdapterFetchAuthFailedException

    def error_handle(self, err, *args, **kwargs):
        raise AdapterFetchAuthFailedException
