from caasm_adapter.util.client import FetchJsonResultClient


class WLKJBaseClient(FetchJsonResultClient):
    def __init__(self, connection, session=None, token=None):
        super(WLKJBaseClient, self).__init__(connection, session)
        self.token = token

    def build_request_header(self, *args, **kwargs):
        return {"Authorization": self.token}

    @property
    def flag_key_name(self):
        return ""

    @property
    def suc_flag(self):
        return ""

    @property
    def data_key_name(self):
        return ""
