from caasm_adapter.sdk.adapter_fetch import fetch_sdk
from wlkj_cam.manage import WLKJManager


def find_asset(connection, fetch_type, page_index=1, page_size=1, session=None, **kwargs):
    page_index += 1
    records = WLKJManager(connection=connection, session=session).find_asset(page_index, page_size)
    result = fetch_sdk.build_asset(records, fetch_type)
    return fetch_sdk.return_success(result)


def get_auth_connection(connection, session=None):
    WLKJManager(connection, session).auth()
