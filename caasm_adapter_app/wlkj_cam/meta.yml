name: wlkj_cam
display_name: "未岚科技资产探测及漏洞检测系统"
description: "未岚科技资产探测及漏洞检测系统查询相关企业的信息系统暴露面、移动端应用、邮箱地址和凭据泄露、代码和文档泄露等。为甲方和乙方的信息安全工程师进行暴露面收敛工作提供技术支持。"
type: "内网资产测绘"
logo: "wlkj_cam.png"
company: "北京未岚科技有限公司"
version: "v0.1"
priority: 10
properties:
  - "内网测绘"

connection:
  - name: address
    type: url
    required: true
    display_name: "请求地址"
    description: "管理系统平台管理员请求地址"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'

  - name: username
    type: string
    required: true
    display_name: "用户名称"
    description: "管理系统中的用户名，此处填写为平台管理员账号，非租户账号"
    validate_rules:
      - name: length
        error_hint: "用户密码格式无效。长度最小不得小于2，最大不得大于100"
        setting:
          min: 2
          max: 100

  - name: password
    type: password
    required: true
    display_name: "用户密码"
    description: "管理系统中平台账号对应的用户密码"
    validate_rules:
      - name: length
        error_hint: "用户密码格式无效。长度最小不得小于6，最大不得大于200"
        setting:
          min: 6
          max: 200


fetch_setting:
  point: "wlkj_cam.fetch:find_asset"
  size: 50
  is_need_test_service: true
  test_auth_point: "wlkj_cam.fetch:get_auth_connection"
  fetch_type_mapper:
    asset:
      - computer

fabric_setting:
  choose_point_mapper:
    asset: "wlkj_cam.fabric:choose_new_record"

merge_setting:
  size: 50
  setting:
    asset:
      computer:
        fields:
          - _id


convert_setting:
  size: 50
  before_executor_mapper: { }
  executor_mapper: { }









