name: "cloud_star_cmdb"
display_name: "佳杰云星CMDB"
description: "佳杰云星CMDB以应用系统为中心的配置管理，即应用系统管理平台，作为建立CMDB资源管理的核心驱动力。自定义CMDB模型，包括对象类型自定义、对象属性自定义和对象关系自定义。给予管理员更大额操作空间及灵活度。配置自动发现，CMDB采用多种自动化手段保证配置信息的准确性。"
type: "CMDB"
company: "佳杰云星"
logo: "cloud_star_cmdb.webp"
version: "v0.1"
priority: 1
properties:
  - "CMDB"

connection:
  - name: username
    type: string
    required: true
    display_name: "用户名"
    description: "用户名称"
    validate_rules:
      - name: length
        error_hint: "用户名长度必须大于等于2且小于等于100"
        setting:
          min: 2
          max: 100

  - name: password
    type: password
    required: true
    display_name: "密码"
    description: "密码信息"
    validate_rules:
      - name: length
        error_hint: "密码长度必须大于等于6且小于等于100"
        setting:
          min: 6
          max: 100

  - name: address
    type: url
    required: true
    display_name: "地址"
    description: "地址信息"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'


fetch_setting:
  type: disposable
  point: "cloud_star_cmdb.fetch:find_asset"
  is_need_test_service: true
  size: 1000
  fetch_type_mapper:
    asset:
      - asset

fabric_setting:
  choose_point_mapper:
    asset: "cloud_star_cmdb.fabric:choose_new_record"

merge_setting:
  size: 1000
  setting: { }

convert_setting:
  size: 1000
  before_executor_mapper: { }
  executor_mapper: { }