name: "bocloud_boc"
display_name: "BeyondContainer容器云平台"
description: "基于容器技术的企业级PaaS解决方案，利用微服务思想和DevOps理念，基于Docker和Kubernetes提供对应用“开发态”，“部署态”，“运行态”的应用全生命周期管理能力，利用可视化，可配置，自动化持续交付流水线结合DevOps咨询，帮助企业DevOps落地，通过容器化和服务治理对微服务架构业务落地进行支撑，帮助企业实现应用云化，能力平台化，管理互联网化。 "
type: "容器安全"
company: "博云"
logo: "bocloud_boc.jpeg"
version: "v0.1"
priority: 1
properties:
  - "容器安全"

connection:
  - name: username
    type: string
    required: true
    display_name: "用户名"
    description: "用户名称"
    validate_rules:
      - name: length
        error_hint: "用户名长度必须大于等于2且小于等于100"
        setting:
          min: 2
          max: 100

  - name: password
    type: password
    required: true
    display_name: "密码"
    description: "密码信息"
    validate_rules:
      - name: length
        error_hint: "密码长度必须大于等于6且小于等于100"
        setting:
          min: 6
          max: 100

  - name: address
    type: url
    required: true
    display_name: "地址"
    description: "地址信息"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'


fetch_setting:
  type: disposable
  point: "bocloud_boc.fetch:find_asset"
  is_need_test_service: true
  size: 1000
  fetch_type_mapper:
    asset:
      - asset

fabric_setting:
  choose_point_mapper:
    asset: "bocloud_boc.fabric:choose_new_record"

merge_setting:
  size: 1000
  setting: { }

convert_setting:
  size: 1000
  before_executor_mapper: { }
  executor_mapper: { }