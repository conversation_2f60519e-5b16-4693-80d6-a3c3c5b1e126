name: volcengine_containersec_saas
display_name: "火山引擎容器安全平台SAAS版"
description: "火山引擎容器安全防护SAAS平台通过深度融合云原生特性，将安全能力左延到构建阶段，利用数据驱动安全的创新技术路线，主动持续开展风险分析，并通过独有的近源端控制实现安全防护，构建独特的云原生安全防护体系。" # 需要修改
type: "容器安全"
company: "字节跳动"
logo: "volcengine.png"
version: "v0.1"
priority: 1
properties:
  - 容器安全

connection: # 需要修改
  - name: address
    type: url
    required: true
    display_name: "地址"
    description: "地址信息"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'

  - name: region
    type: string
    required: true
    display_name: "区域名称"
    description: "租户所属的区域名称"
    validate_rules:
      - name: length
        error_hint: "用户密码格式无效。长度最小不得小于2，最大不得大于100"
        setting:
          min: 2
          max: 100

  - name: api_date
    type: string
    required: true
    default: "2021-07-01"
    display_name: "API版本日期"
    description: "目前支持的3.10.0【2021-07-01】"
    validate_rules:
      - name: length
        error_hint: "用户密码格式无效。长度最小不得小于2，最大不得大于100"
        setting:
          min: 2
          max: 100

  - name: username
    type: string
    required: true
    display_name: "用户名称"
    description: "登陆管理界面的用户名称"
    validate_rules:
      - name: length
        error_hint: "用户密码格式无效。长度最小不得小于2，最大不得大于100"
        setting:
          min: 2
          max: 100

  - name: password
    type: password
    required: true
    display_name: "用户密码"
    description: "登陆管理界面的用户密码"
    validate_rules:
      - name: length
        error_hint: "用户密码格式无效。长度最小不得小于6，最大不得大于200"
        setting:
          min: 6
          max: 200

fetch_setting:
  is_need_test_service: true
  point: "volcengine_saas_containersec.fetch:find_asset"
  test_auth_point: "volcengine_saas_containersec.fetch:auth"
  size: 100 # 需要确认
  fetch_type_mapper:
    asset:
      - kube_cluster
      - container_image
      - kube
      - host
      - kube_pod
      - container
      - kube_service
      - kube_endpoint
      - kube_ingress


merge_setting:
  size: 100  # 需要确认
  setting: { } # 需要修改

convert_setting:
  size: 100 # 需要确认
  before_executor_mapper: { }
  executor_mapper: { }