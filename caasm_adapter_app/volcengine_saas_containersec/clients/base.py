from caasm_adapter.util.client import FetchJsonResultClient


class VolcengineContainerSaasBaseClient(FetchJsonResultClient):

    TIMEOUT = 600

    def __init__(self, connection, session=None, token=None):
        super(VolcengineContainerSaasBaseClient, self).__init__(connection, session)
        self._token = token

    def build_request_url(self, *args, **kwargs):
        url = super(VolcengineContainerSaasBaseClient, self).build_request_url(*args, **kwargs)
        return url.format(self.region, self.api_date)

    def build_request_header(self, *args, **kwargs):
        return {"Accept": "*/*", "Content-type": "application/json", "token": self._token}

    @property
    def flag_key_name(self):
        return "Error"

    @property
    def suc_flag(self):
        return None

    @property
    def region(self):
        return self.connection.get("region", "")

    @property
    def api_date(self):
        return self.connection.get("api_date", "")

    @property
    def username(self):
        return self.connection.get("username", "")

    @property
    def password(self):
        return self.connection.get("password", "")
