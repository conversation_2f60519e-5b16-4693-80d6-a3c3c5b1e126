from caasm_adapter.sdk.adapter_fetch import fetch_sdk
from xmaze_iast.manage import XmIastManager


def find_asset(connection, fetch_type, page_index, page_size, **kwargs):
    session = kwargs.get("session")

    manager = _build_manager(connection, session)
    result = manager.find(fetch_type, page_index, page_size)
    fetch_sdk.build_asset(result, fetch_type)
    return fetch_sdk.return_success(result)


def get_auth_connection(connection, session=None):
    _build_manager(connection, session).auth()


def _build_manager(connection, session):
    return XmIastManager(connection, session)
