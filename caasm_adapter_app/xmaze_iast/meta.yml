name: xmaze_iast
display_name: "悬镜安全"
description: "悬镜灵脉IAST灰盒安全测试平台作为一款次世代智慧交互式应用安全测试产品，采用前沿的深度学习技术，融合领先的IAST产品架构，使安全能力左移前置，将精准化的应用安全测试高效无感地应用于从开发到测试的DevSecOps全流程之中。"
type: "安全测试"
company: "北京安普诺信息技术有限公司"
version: "v0.1"
logo: "xmaze_iast.png"
priority: 50
properties:
  - 应用安全
  - 安全测试

connection:
  - name: username
    type: string
    required: true
    display_name: "用户名称"
    description: "用户名称"
    validate_rules:
      - name: length
        error_hint: "用户名称格式无效。长度最小不得小于2，最大不得大于100"
        setting:
          min: 2
          max: 100

  - name: password
    type: password
    required: true
    display_name: "用户密码"
    description: "用户密码"
    validate_rules:
      - name: length
        error_hint: "用户密码格式无效。长度最小不得小于6，最大不得大于200"
        setting:
          min: 6
          max: 200

  - name: address
    type: url
    required: true
    display_name: "请求地址"
    description: "请求地址"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'

fetch_setting:
  point: "xmaze_iast.fetch:find_asset"
  is_need_test_service: true
  test_auth_point: "xmaze_iast.fetch:get_auth_connection"
  size: 200
  fetch_type_mapper:
    asset:
      - webapp

merge_setting:
  size: 200
  setting:
    asset:
      webapp:
        fields:
          - applicationId

convert_setting:
  size: 200
  before_executor_mapper:
    asset:
      - add:
          - field: "asset_type"
            value: "web应用"

  executor_mapper:
    asset:
      web应用:
        - add:
            - field: address_vulners
              value: "${addresses}"
            - field: component_vulners
              value: "${component}"
        - rename:
            - src_field: applicationName
              dst_field: webapp.name

            - src_field: firstCreateDate
              dst_field: webapp.vulner_first_seen

            - src_field: maxCreateDate
              dst_field: webapp.vulner_last_seen

            - src_field: component
              dst_field: webapp.packages

            - src_field: addresses
              dst_field: webapp.app_assets

            - src_field: nodes
              dst_field: webapp.nodes

        - unwind:
            - src_field: address_vulners
              son_field: vulners
            - src_field: component_vulners
              son_field: vulners

        - for:
            - field: webapp.app_assets
              handler:
                - method: rename
                  setting:
                    - src_field: address
                      dst_field: url

                - method: add
                  setting:
                    - field: node.name
                      value: "${nodeName}"
                    - field: node.addr
                      value: "${nodeIp}"
            - field: webapp.nodes
              handler:
                - method: rename
                  setting:
                    - src_field: nodeName
                      dst_field: name
                    - src_field: nodeIp
                      dst_field: addr
                    - src_field: requestNumber
                      dst_field: request_count
                    - src_field: cpuLoad
                      dst_field: cpu_usage
                    - src_field: memLoad
                      dst_field: memory_usage
                    - src_field: firstCreateDate
                      dst_field: vulner_first_seen
                    - src_field: vulMaxCreateDate
                      dst_field: vulner_last_seen
                - method: convert
                  setting:
                    - field: memory_usage
                      type: int
                    - field: cpu_usage
                      type: int
                - method: compute
                  setting:
                    - op: multiply
                      values:
                        - 1024
                        - 1024
                        - ${memory_usage}
                      dst_field: memory_usage
            - field: webapp_vulners
              handler:
                - method: rename
                  setting:
                    - src_field: vulName
                      dst_field: name
                    - src_field: applicationName
                      dst_field: webapp.name
            - field: address_vulners
              handler:
                - method: rename
                  setting:
                    - src_field: vulners.vulName
                      dst_field: name
                    - src_field: address
                      dst_field: url

            - field: component_vulners
              handler:
                - method: rename
                  setting:
                    - src_field: name
                      dst_field: package.name
                    - src_field: version
                      dst_field: package.version
                    - src_field: vulners.name
                      dst_field: name

        - compute:
            - op: add_list
              values:
                - "${webapp_vulners}"
                - "${component_vulners}"
                - "${address_vulners}"
              dst_field: vulners


fabric_setting:
  choose_point_mapper:
    asset: "xmaze_iast.fabric:choose_new_record"