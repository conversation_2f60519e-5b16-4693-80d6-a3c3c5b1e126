from caasm_adapter.util.client import FetchJsonResultClient


class XmIastBaseClient(FetchJsonResultClient):
    METHOD = "get"

    def __init__(self, connection, session=None, token=None):
        super(XmIastBaseClient, self).__init__(connection, session)
        self._token = token

    def build_request_header(self, *args, **kwargs):
        if not self._token:
            return {}
        return {"Authorization": f"Bearer {self._token}"}

    @property
    def data_key_name(self):
        return "data"

    @property
    def suc_flag(self):
        return 0

    @property
    def flag_key_name(self):
        return "code"


class XmIastWebappQueryBaseClient(XmIastBaseClient):
    def build_request_params(self, page_index, page_size, application_id):
        params = {
            "pageNum": page_index,
            "pageSize": page_size,
            "applicationId": application_id,
        }

        return params

    @property
    def data_key_name(self):
        return "data.records"
