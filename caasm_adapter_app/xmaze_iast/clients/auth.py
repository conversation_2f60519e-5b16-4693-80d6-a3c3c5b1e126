from caasm_adapter.util.exception import AdapterFetchAuthFailedException
from xmaze_iast.clients.base import XmIastBaseClient


class XmIastAuthClient(XmIastBaseClient):
    URL = "/iast/api-v1/system/user/login"
    METHOD = "post"

    def build_request_json(self, *args, **kwargs):
        return {
            "username": self.username,
            "password": self.password,
            "rememberMe": 1,
            "openLogin": 1,  # 开放登录 1: 跳过滑块验证码  其他: 正常登录,
            "forceLogin": 1,
        }

    @property
    def username(self):
        return self.connection.get("username", "")

    @property
    def password(self):
        return self.connection.get("password", "")

    @property
    def data_key_name(self):
        return "data.token"

    def clean_result(self, result):
        if not result:
            raise AdapterFetchAuthFailedException
        return result

    def parse_error_handle(self, result, *args, **kwargs):
        raise AdapterFetchAuthFailedException

    def error_handle(self, err, *args, **kwargs):
        raise AdapterFetchAuthFailedException
