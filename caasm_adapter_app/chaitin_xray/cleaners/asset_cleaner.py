from collections import defaultdict

from caasm_adapter.fetcher.cleaners.base import FetchTotalBaseCleaner


class AssetCleaner(FetchTotalBaseCleaner):
    _DEFAULT_DATA_TYPE = "host"

    def build(self, records):
        service_by_id, host_by_id = self._group_records(records)
        result = []

        for host_id, host in host_by_id.items():
            services = service_by_id[host_id]
            host["services"] = services
            result.append(host)
        return result

    def clean_internal(self, record, new_record):
        record["fetch_type"] = "host"
        return record

    @classmethod
    def _group_records(cls, records):
        services, hosts = [], []

        for record in records:
            if "banner" in record:
                services.append(record)
            else:
                hosts.append(record)

        service_by_id, host_by_id = defaultdict(list), {}
        for service in services:
            ip = service.get("ip") or {}
            ip_id = ip.get("id")
            service_by_id[ip_id].append(service)

        for host in hosts:
            host_id = host.get("id")
            host_by_id[host_id] = host
        return service_by_id, host_by_id
