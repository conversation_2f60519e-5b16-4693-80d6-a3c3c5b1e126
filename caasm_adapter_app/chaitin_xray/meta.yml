name: "chaitin_xray"
display_name: "洞鉴安全评估系统"
description: "洞鉴（X-Ray）是由长亭安全团队基于扎实攻防经验，历经多年实战打磨的一款集合资产管理、Web扫描、主机扫描为一体的安全评估系统。"
type: "脆弱性评估"
company: "长亭科技"
logo: "logo.png"
version: "v0.1"
priority: 1
properties:
  - "漏洞扫描"

connection:
  - name: token
    type: password
    required: true
    display_name: "token"
    description: "token信息"
    validate_rules:
      - name: length
        error_hint: "token长度必须大于等于6且小于等于100"
        setting:
          min: 6
          max: 100

  - name: address
    type: url
    required: true
    display_name: "地址"
    description: "地址信息"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'

fetch_setting:
  type: disposable
  point: "chaitin_xray.fetch:find_asset"
  test_auth_point: "chaitin_xray.fetch:test_auth_point"
  is_need_test_service: true
  condition_point: "chaitin_xray.fetch:build_query_condition"
  size: 1000
  fetch_type_mapper:
    asset:
      - host
      - service
  cleaner_mapper:
    asset:
      host:
        - "chaitin_xray.cleaners.asset_cleaner:AssetCleaner"

fabric_setting:
  choose_point_mapper:
    asset: "chaitin_xray.fabric:choose_new_record"

merge_setting:
  size: 1000
  setting: { }

convert_setting:
  size: 1000
  before_executor_mapper: { }
  executor_mapper: { }