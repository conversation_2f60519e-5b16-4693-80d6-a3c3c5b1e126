import copy

from caasm_adapter.sdk.adapter_fetch import fetch_sdk
from chaitin_xray.manage import ChaitinXrayManager


def find_asset(connection, fetch_type, page_index, page_size, condition, session=None, **kwargs):
    manager = ChaitinXrayManager(connection, session)
    if page_index == 0:
        _init_condition(condition, page_size)
    result = _find_asset_loop(manager, fetch_type, condition, page_size, init_condition=False)
    result = fetch_sdk.build_asset(result, fetch_type)
    return fetch_sdk.return_success(result)


def build_query_condition(connection, session=None, fetch_type=None):
    manager = ChaitinXrayManager(connection, session)

    page_index = 0
    page_size = 100

    project_ids = []
    result = {"project_ids": project_ids}
    while True:
        data = manager.find_project(page_index=page_index, page_size=page_size)
        if not data:
            break
        for info in data:
            project_id = info.get("id")
            if not project_id:
                continue
            project_ids.append(project_id)
        page_index += 1
    return result


def _find_asset_loop(manager, fetch_type, condition, page_size, init_condition=False):
    if init_condition is True:
        _init_condition(condition, page_size)

    current_project_ids = condition["current_project_ids"]
    if not current_project_ids:
        return []

    result = _find_asset(manager, fetch_type, condition)

    if not result:
        _rebuild_page(condition)
        result = _find_asset_loop(manager, fetch_type, condition, page_size, init_condition=False)
    else:
        condition["page_index"] += 1
    return result


def _find_asset(manager, fetch_type, condition):
    result = []
    current_project_ids = condition["current_project_ids"]
    current_page_index = condition["page_index"]
    current_page_size = condition["page_size"]

    if not current_project_ids:
        return result

    current_project_id = condition["current_project_ids"].pop()

    result = manager.find(fetch_type, current_project_id, page_index=current_page_index, page_size=current_page_size)
    return result


def _init_condition(condition, page_size):
    condition["current_project_ids"] = copy.deepcopy(condition["project_ids"])
    condition["page_index"] = 0
    condition["page_size"] = page_size


def _rebuild_page(condition):
    condition["page_index"] = 0


def test_auth_point(connection, session=None):
    ChaitinXrayManager(connection, session).find_project(page_index=0, page_size=1)
