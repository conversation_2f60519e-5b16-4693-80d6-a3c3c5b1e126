import logging

from caasm_adapter.util.client import FetchJsonResultClient

log = logging.getLogger()


class ChaitinXrayBaseClient(FetchJsonResultClient):
    @property
    def token(self):
        return self.connection.get("token", "")

    def build_request_header(self, *args, **kwargs):
        return {"token": self.token}

    @property
    def suc_flag(self):
        return None

    @property
    def flag_key_name(self):
        return "err"


class ChaitinXrayPageClient(ChaitinXrayBaseClient):
    METHOD = "post"

    @property
    def data_key_name(self):
        return "data.content"

    @property
    def flag_key_name(self):
        return "err"
