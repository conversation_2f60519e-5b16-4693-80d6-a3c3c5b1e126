from caasm_adapter.util.client import FetchJsonResultClient


class DoSecContainerBaseClient(FetchJsonResultClient):
    success_code = 0

    def __init__(self, connection, session=None, auth_data=None):
        super(DoSecContainerBaseClient, self).__init__(connection, session)
        self._auth_data = auth_data

    @property
    def flag_key_name(self):
        return "code"

    @property
    def suc_flag(self):
        return 0

    @property
    def data_key_name(self):
        return "data"

    def build_request_header(self, *args, **kwargs):
        return self._auth_data

    @property
    def auth_data(self):
        return self._auth_data


class DoDecContainerQueryBaseClient(DoSecContainerBaseClient):
    METHOD = "post"

    def build_request_json(self, page, page_size, **kwargs):
        return {"page_info": {"page": page, "page_size": page_size}}

    @property
    def data_key_name(self):
        return "data.list"

    def clean_result(self, result):
        return result or []
