import base64
import json

from Crypto.Cipher import ARC4

from caasm_adapter.util.exception import AdapterFetchAuthFailedException
from dosec_container.clients.base import DoSecContainerBaseClient


class DoSecContainerAuthClient(DoSecContainerBaseClient):
    URL = "/v1/user/login"
    METHOD = "POST"
    TOKEN_NAME = "Access-Token"
    DEFAULT_KEY = "LAtZFRCFmBTJ"

    def build_request_data(self, *args, **kwargs):
        return json.dumps({"username": self.username, "password": self.password})

    def parse_response(self, response, *args, **kwargs):
        result = super(DoSecContainerAuthClient, self).parse_response(response, *args, **kwargs)
        return result, response.headers

    def parse_biz_result(self, result, *args, **kwargs):
        result, headers = result
        result = super(DoSecContainerAuthClient, self).parse_biz_result(result, *args, **kwargs)
        access_token = headers.get(self.TOKEN_NAME)
        if not access_token:
            return self.parse_error_handle(result, *args, **kwargs)
        return {self.TOKEN_NAME: access_token}

    def error_handle(self, err, *args, **kwargs):
        raise AdapterFetchAuthFailedException

    def parse_error_handle(self, result, *args, **kwargs):
        raise AdapterFetchAuthFailedException

    @property
    def username(self):
        return self.connection.get("username", "")

    @property
    def password(self):
        password = self.connection.get("password", "")
        return self.encrypt(password, self.key)

    @property
    def key(self):
        return self.connection.get("key", self.DEFAULT_KEY)

    @classmethod
    def encrypt(cls, data, key):
        key = bytes(key, encoding="utf-8")
        enc = ARC4.new(key)
        res = enc.encrypt(data.encode("utf-8"))
        res = base64.b64encode(res)
        return str(res, "utf8")
