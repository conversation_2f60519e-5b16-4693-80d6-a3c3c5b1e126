from caasm_adapter.sdk.adapter_fetch import fetch_sdk
from dosec_container.manage import DoSecContainerManager


def find_asset(connection, fetch_type, page_index=0, page_size=1, session=None, **kwargs):
    records = DoSecContainerManager(connection, session).find(fetch_type, page_index, page_size)
    result = fetch_sdk.build_asset(records, fetch_type)
    return fetch_sdk.return_success(result)


def get_auth_connection(connection=None, session=None):
    DoSecContainerManager(connection, session).auth()
