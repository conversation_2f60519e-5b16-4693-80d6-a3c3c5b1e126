name: dosec_container
display_name: "小佑境界容器安全平台"
description: "北京小佑科技专注于云计算安全的科技公司，我们开发了自主知识产权的PAAS容器安全防护产品，解决容器全生命周期的安全问题，同时为客户提供高品质的安全建设咨询、安全风险评估、安全事件处理、合规咨询等安全服务，以产品加服务的方式为客户提供高价值的安全解决方案。"
type: "容器安全"
company: "小佑科技"
logo: "dosec_container.png"
version: "v0.1"
priority: 1
properties:
  - 容器安全
  - Agent

connection:
  - name: address
    type: url
    required: true
    display_name: "请求地址"
    description: "请求地址"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'
  - name: username
    type: string
    required: true
    display_name: "用户名"
    description: "用户名"
    validate_rules:
      - name: length
        error_hint: "用户名格式无效。长度最小不得小于6，最大不得大于200"
        setting:
          min: 2
          max: 100
  - name: password
    type: password
    required: true
    display_name: "密码"
    description: "密码"
    validate_rules:
      - name: length
        error_hint: "密码格式无效。长度最小不得小于6，最大不得大于200"
        setting:
          min: 6
          max: 200

fetch_setting:
  fetch_type_mapper:
    asset:
      - container
      - host
  point: "dosec_container.fetch:find_asset"
  is_need_test_service: true
  size: 100
  test_auth_point: "dosec_container.fetch:get_auth_connection"

fabric_setting:
  choose_point_mapper:
    asset: "dosec_container.fabric:choose_new_record"

merge_setting:
  size: 100
  setting:
    asset:
      容器:
        fields:
          - id
      主机:
        fields:
          - id

convert_setting:
  size: 100
  before_executor_mapper: { }
  executor_mapper: { }