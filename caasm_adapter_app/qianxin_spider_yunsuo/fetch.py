from typing import Dict

from caasm_adapter.sdk.adapter_fetch import fetch_sdk
from qianxin_spider_yunsuo.manage import QAXSpiderYSManager


def find_asset(connection: Dict, fetch_type, page_index=0, page_size=1, **kwargs):
    session = kwargs.get("session")
    result = _build_manager(connection, session=session).find_asset(page_index, page_size)
    fetch_sdk.build_asset(result, fetch_type)
    return fetch_sdk.return_success(result)


def get_auth_connection(connection, session):
    _build_manager(connection, session).auth()


def _build_manager(connection, session):
    return QAXSpiderYSManager(connection, session)
