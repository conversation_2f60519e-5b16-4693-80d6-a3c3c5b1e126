import base64
import logging
import time

from Crypto.Cipher import AES

from caasm_adapter.util.client import FetchJsonResultClient

log = logging.getLogger()


class QAXSpiderYSClient(FetchJsonResultClient):
    def __init__(self, connection, session, token=None):
        super(QAXSpiderYSClient, self).__init__(connection, session)
        self._token = token

    @property
    def token(self):
        timestamp = f"{int(time.time())}000"
        jwt_token = "JWT" + timestamp
        enc_token = self._encrypt(jwt_token, self._token)
        return enc_token + f"{timestamp}"

    @property
    def suc_flag(self):
        return "1"

    @property
    def data_key_name(self):
        return "data"

    @property
    def flag_key_name(self):
        return "code"

    @classmethod
    def _pad(cls, text):
        count = len(text.encode("utf-8"))
        add = AES.block_size - (count % AES.block_size)
        enc_text = text + (chr(add) * add)
        return enc_text

    @classmethod
    def _encrypt(cls, key, enc_data):
        aes = AES.new(key.encode(), AES.MODE_ECB)
        res = aes.encrypt(cls._pad(enc_data).encode("utf8"))
        msg = str(base64.b64encode(res), encoding="utf8")
        return msg


class QAXSpiderYSQueryClient(QAXSpiderYSClient):
    METHOD = "post"

    def build_request_json(self, *args, **kwargs):
        result = {"token": self.token}
        return result


class QAXSpiderYSQueryPageClient(QAXSpiderYSQueryClient):
    def build_request_json(self, page_index, page_size):
        result = super(QAXSpiderYSQueryPageClient, self).build_request_json()
        result.update({"keyWord": "", "currentPage": page_index, "maxResults": page_size})
        return result

    @property
    def data_key_name(self):
        return "data.list"


class QAXSpiderYSQueryDetailPageClient(QAXSpiderYSQueryClient):
    def build_request_json(self, page_index, page_size, machine_id):
        result = super(QAXSpiderYSQueryDetailPageClient, self).build_request_json(page_index, page_size)
        result["machineUuid"] = machine_id
        return result

    @property
    def data_key_name(self):
        return "data.list"


class QAXSpiderYSQueryDetailClient(QAXSpiderYSQueryClient):
    def build_request_json(self, machine_id):
        result = super(QAXSpiderYSQueryDetailClient, self).build_request_json()
        result["machineUuid"] = machine_id
        return result
