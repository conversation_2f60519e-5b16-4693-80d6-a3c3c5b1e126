import logging
import string

import ddddocr

from caasm_adapter.util.exception import AdapterFetchAuthFailedException
from caasm_tool.util import rsa_encrypt
from qianxin_spider_yunsuo.clients.base import QAXSpiderYSQueryClient

log = logging.getLogger()


class QAXSpiderYSCodeClient(QAXSpiderYSQueryClient):
    URL = "/api/userSrv/sessionController/loginValidateCode"
    LETTERS = string.ascii_letters + string.digits
    OCR = ddddocr.DdddOcr(beta=True, show_ad=False)

    def build_request_json(self, random_code):
        return {"randomCode": random_code}

    def parse_response(self, response, *args, **kwargs):
        if response.status_code != 200:
            return self.error_handle(None, *args, **kwargs)
        return self.OCR.classification(response.content)

    def clean_result(self, result):
        if not result:
            raise AdapterFetchAuthFailedException
        new_result = ""
        for i in result:
            if i not in self.LETTERS:
                continue
            new_result += i
        return new_result


class QAXSpiderYSPublicKeyClient(QAXSpiderYSQueryClient):
    URL = "/api/userSrv/sessionController/createPublicKey"

    def build_request_json(self, *args, **kwargs):
        json_params = {"username": self.connection.get("username"), "token": False}
        return json_params

    @property
    def data_key_name(self):
        return "data.publicKey"

    def clean_result(self, result):
        if not result:
            raise AdapterFetchAuthFailedException
        return result


class QAXSpiderYSLoginClient(QAXSpiderYSQueryClient):
    URL = "/api/userSrv/sessionController/login"

    def build_request_json(self, code, public_key, random_code):
        return {
            "password": self.enc_password(public_key),
            "token": False,
            "username": self.connection.get("username"),
            "validateCode": code,
            "randomCode": random_code,
        }

    def enc_password(self, public_key):
        public_key_str = f"-----BEGIN PUBLIC KEY-----\n{public_key}\n-----END PUBLIC KEY-----"
        return rsa_encrypt(public_key_str, self.connection.get("password", ""))

    @property
    def data_key_name(self):
        return "data.token"

    def error_handle(self, err, *args, **kwargs):
        raise AdapterFetchAuthFailedException

    def parse_error_handle(self, result, *args, **kwargs):
        raise AdapterFetchAuthFailedException

    def clean_result(self, result):
        if not result:
            raise AdapterFetchAuthFailedException
        return result
