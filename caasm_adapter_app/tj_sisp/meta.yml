name: "tj_sisp"
display_name: "Red Queen”安全智能服务平台（SISP）"
description: "Red Queen”安全智能服务平台（SISP）作为天际友盟为客户提供各项基于情报应用的安全智能服务的核心枢纽，不仅是国内首个的云端一体化安全智能综合服务平台，也是国内最大的安全情报聚合、分析与交换平台。"
type: "情报"
company: "天际友盟"
logo: "tj_sisp.webp"
version: "v0.1"
priority: 1
properties:
  - "情报"

connection:
  - name: username
    type: string
    required: true
    display_name: "用户名"
    description: "用户名称"
    validate_rules:
      - name: length
        error_hint: "用户名长度必须大于等于2且小于等于100"
        setting:
          min: 2
          max: 100

  - name: password
    type: password
    required: true
    display_name: "密码"
    description: "密码信息"
    validate_rules:
      - name: length
        error_hint: "密码长度必须大于等于6且小于等于100"
        setting:
          min: 6
          max: 100

  - name: address
    type: url
    required: true
    display_name: "地址"
    description: "地址信息"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'


fetch_setting:
  type: disposable
  point: "tj_sisp.fetch:find_asset"
  size: 1000
  fetch_type_mapper:
    asset:
      - asset

fabric_setting:
  choose_point_mapper:
    asset: "tj_sisp.fabric:choose_new_record"

merge_setting:
  size: 1000
  setting: { }

convert_setting:
  size: 1000
  before_executor_mapper: { }
  executor_mapper: { }