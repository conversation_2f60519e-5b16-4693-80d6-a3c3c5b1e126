from caasm_adapter.sdk.adapter_fetch import fetch_sdk
from quanzhi_api_v3.manage import QzAPIV3Manager


def find_asset(connection, fetch_type, page_index=0, page_size=1, session=None, **kwargs):
    result = _manager(connection, session).find(page_index + 1, page_size)
    result = fetch_sdk.build_asset(result, fetch_type)
    return fetch_sdk.return_success(result)


def auth(connection, session=None):
    _manager(connection, session).auth()


def _manager(connection, session=None):
    return QzAPIV3Manager(connection, session)
