import hashlib
import time

from caasm_adapter.util.client import FetchJsonResultClient


class QzAPIV3Client(FetchJsonResultClient):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._timestamp = int(time.time() * 1000)

    def build_request_header(self, *args, **kwargs):
        return {
            "audit-open-api-signType": "md5",
            "audit-open-api-accessKey": self.access_key,
            "audit-open-api-signValue": self.sign_value,
            "audit-open-api-timestamp": str(self._timestamp),
        }

    @property
    def sign_value(self):
        sign_records = [
            ("signType", "md5"),
            ("accessKey", self.access_key),
            ("secretKey", self.secret_key),
            ("timestamp", self._timestamp),
        ]
        return hashlib.md5("&".join(f"{i[0]}={i[1]}" for i in sign_records).encode("utf-8")).hexdigest().upper()

    @property
    def access_key(self):
        return self.connection.get("access_key", "")

    @property
    def secret_key(self):
        return self.connection.get("secret_key")

    @property
    def suc_flag(self):
        return 0

    @property
    def flag_key_name(self):
        return "errorCode"


class QzAPIPageClient(QzAPIV3Client):
    METHOD = "post"

    @property
    def data_key_name(self):
        return "data.rows"
