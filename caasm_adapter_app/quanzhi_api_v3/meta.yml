name: quanzhi_api_v3
display_name: "全知API网关V3"
description: "“API风险监测系统” 是一款以数据为中心的流量分析系统，它通过对web、APP、小程序、IoT、开放平台等应用系统的流量进行分析，从而实现对API数据暴露面的治理和对数据攻击行为持续发现。产品适用于部署在企业的互联网出口，监控企业API的数据暴露面以及被攻击情况。通过帮助企业分析API接口的变化，及时掌握存在的API风险隐患。"
type: "API"
company: "全知科技"
logo: "quanzhi_api.png"
version: "v3"
priority: 100
properties:
  - API

connection:
  - name: address
    type: url
    required: true
    display_name: "请求地址"
    description: "请求地址"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'

  - name: access_key
    type: string
    required: true
    display_name: "access_key"
    description: "access_key"
    validate_rules:
      - name: length
        error_hint: "access_key格式无效。长度最小不得小于2，最大不得大于100"
        setting:
          min: 2
          max: 100

  - name: secret_key
    type: password
    required: true
    display_name: "secret_key"
    description: "secret_key"
    validate_rules:
      - name: length
        error_hint: "secret_key格式无效。长度最小不得小于6，最大不得大于200"
        setting:
          min: 6
          max: 200


fetch_setting:
  point: "quanzhi_api_v3.fetch:find_asset"
  size: 200
  test_auth_point: "quanzhi_api_v3.fetch:auth"
  is_need_test_service: true
  fetch_type_mapper:
    asset:
      - api

merge_setting:
  size: 500
  setting: { }

convert_setting:
  size: 500
  before_executor_mapper: { }
  executor_mapper: { }

fabric_setting:
  choose_point_mapper: { }