from caasm_adapter.util.client import FetchJsonResultClient


class VRVWebServiceBaseClient(FetchJsonResultClient):
    def __init__(self, connection, session=None, token=None):
        super(VRVWebServiceBaseClient, self).__init__(connection, session)
        self.token = token

    def build_request_header(self, *args, **kwargs):
        return {"Authorization": f"Bearer {self.token}"}

    @property
    def data_key_name(self):
        return "data.records"

    @property
    def suc_flag(self):
        return "0"

    @property
    def flag_key_name(self):
        return "code"
