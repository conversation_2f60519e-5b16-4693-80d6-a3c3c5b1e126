from caasm_adapter.util.exception import AdapterFetchAuthFailedException
from vrv_webservice.clients.base import VRVWebServiceBaseClient


class VRVWebServiceAuthClient(VRVWebServiceBaseClient):
    URL = "/auth/token"
    METHOD = "POST"

    @property
    def password(self):
        return self.connection.get("password", "")

    @property
    def username(self):
        return self.connection.get("username", "")

    def build_request_data(self):
        return {"username": self.username, "password": self.password}

    @property
    def data_key_name(self):
        return "token"

    @property
    def suc_flag(self):
        return "0"

    @property
    def flag_key_name(self):
        return "code"

    def clean_result(self, result):
        if not result:
            raise AdapterFetchAuthFailedException
        return result

    def parse_error_handle(self, result, *args, **kwargs):
        raise AdapterFetchAuthFailedException

    def error_handle(self, err, *args, **kwargs):
        raise AdapterFetchAuthFailedException
