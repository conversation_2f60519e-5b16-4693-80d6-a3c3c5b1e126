import traceback

from caasm_adapter.sdk.adapter_fetch import fetch_sdk
from vrv_webservice.manage import VRVWebServiceManager
import logging

log = logging.getLogger()


def find_asset(connection, fetch_type, page_index, page_size, session=None, **kwargs):
    page_index = page_index + 1
    records = VRVWebServiceManager(connection, session=session).find_asset(page_index, page_size)
    # page_index 默认是0，但是优维是从1开始
    result = fetch_sdk.build_asset(records, fetch_type)

    return fetch_sdk.return_success(result)


def find_hardware(connection, page_index=0, page_size=1, session=None):
    page_index = page_index + 1
    result = []
    try:
        result = VRVWebServiceManager(connection, session=session).find_hardware(
            page_index=page_index, page_size=page_size
        )
    except Exception as e:
        log.warning(f"Vul find error({e}). detail is {traceback.format_exc()}")
    return result


def find_software(connection, page_index=0, page_size=1, session=None):
    page_index = page_index + 1
    result = []
    try:
        result = VRVWebServiceManager(connection, session=session).find_software(
            page_index=page_index, page_size=page_size
        )
    except Exception as e:
        log.warning(f"Vul find error({e}). detail is {traceback.format_exc()}")
    return result


def find_resource(connection, page_index=0, page_size=1, session=None):
    page_index = page_index + 1
    result = []
    try:
        result = VRVWebServiceManager(connection, session=session).find_resource(
            page_index=page_index, page_size=page_size
        )
    except Exception as e:
        log.warning(f"Vul find error({e}). detail is {traceback.format_exc()}")
    return result


def find_process(connection, page_index=0, page_size=1, session=None):
    page_index = page_index + 1
    result = []
    try:
        result = VRVWebServiceManager(connection, session=session).find_process(
            page_index=page_index, page_size=page_size
        )
    except Exception as e:
        log.warning(f"Vul find error({e}). detail is {traceback.format_exc()}")
    return result


def get_auth_connection(connection, session=None):
    VRVWebServiceManager(connection, session).auth()
