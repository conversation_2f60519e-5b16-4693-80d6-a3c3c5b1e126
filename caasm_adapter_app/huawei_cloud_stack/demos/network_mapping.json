{"tenant_id": "ac41a86bfbd84af3b701df4177d431af", "provisioning_status": "ACTIVE", "region_vip_address": "*************", "description": "长护应用服务器包括中间件和业务", "created_at": "2021-11-22T13:18:00", "l7_flavor_id": null, "vip_subnet_id": "059c7501-baf3-402f-b180-02fda6396c8c", "tags": [], "vip_address": "************", "updated_at": "2021-11-22T13:18:02", "operating_status": "ONLINE", "provider": "vlb", "l4_flavor_id": null, "admin_state_up": true, "name": "elb-长护核心02", "router_id": "9cc82834-799a-4851-b781-130b7e4c0b28", "id": "151327d2-8b86-4b3b-8a59-21fa6c6d4e4a", "vip_port_id": "ef8ae1d9-dbce-4f5f-aeb3-91bbe5584a83", "floating_ips": [], "clusters": [], "listener": {"id": "4173419d-ef2b-409d-ad90-8902237f623a", "tenant_id": "ac41a86bfbd84af3b701df4177d431af", "name": "listener-8080", "description": "", "default_pool_id": "36a767c9-514e-46c4-8d76-8f53fa3a6acf", "protocol": "HTTP", "protocol_port": 8080, "connection_limit": -1, "admin_state_up": true, "client_ca_tls_container_ref": null, "http2_enable": false, "keepalive_timeout": 300, "enhance_l7policy_enable": false, "loadbalancers": [{"id": "151327d2-8b86-4b3b-8a59-21fa6c6d4e4a"}], "sni_container_refs": [], "default_tls_container_ref": null, "timeout_client_data": 60, "timeout_member_data": 300, "created_at": "2022-01-13T10:38:27", "updated_at": "2022-01-13T10:38:28", "tags": []}, "members": [{"id": "07a22a12-aa94-4782-8aca-3c4cfe7cdf39", "tenant_id": "ac41a86bfbd84af3b701df4177d431af", "address": "************", "protocol_port": 8302, "weight": 1, "admin_state_up": true, "subnet_id": "7e5c70cb-f714-483a-a4dd-3c4884f29e36", "operating_status": "ONLINE", "name": "member-77zq"}, {"id": "76259b49-a4dc-461d-a65e-3a3c7a01787e", "tenant_id": "ac41a86bfbd84af3b701df4177d431af", "address": "************", "protocol_port": 8302, "weight": 1, "admin_state_up": true, "subnet_id": "7e5c70cb-f714-483a-a4dd-3c4884f29e36", "operating_status": "ONLINE", "name": "member-u9cp"}], "entry_ports": [8302, 8302], "destinations": [{"ip": "************", "instance_id": "07a22a12-aa94-4782-8aca-3c4cfe7cdf39", "port": 8302}, {"ip": "************", "instance_id": "76259b49-a4dc-461d-a65e-3a3c7a01787e", "port": 8302}]}