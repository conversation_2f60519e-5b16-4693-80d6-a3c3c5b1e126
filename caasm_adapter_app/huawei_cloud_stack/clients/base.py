import abc

from caasm_adapter.util.client import FetchJsonResultClient


class HuaweiCloudStackBaseClient(FetchJsonResultClient, abc.ABC):
    def __init__(self, project_id, connection, session=None, token=None):
        super(HuaweiCloudStackBaseClient, self).__init__(connection, session)
        self._token = token
        self._project_id = project_id

    def build_request_header(self, *args, **kwargs):
        return {"X-Auth-Token": self._token} if self._token else {}

    def parse_response(self, response, *args, **kwargs):
        code = response.status_code
        return {"code": code, "data": response.json() if code == self.suc_flag else {}}

    def build_request_url(self, *args, **kwargs):
        request_url = super(HuaweiCloudStackBaseClient, self).build_request_url()
        return request_url.format(project_id=self.project_id, *args, **kwargs)

    @property
    def data_key_name(self):
        return "data"

    @property
    def flag_key_name(self):
        return "code"

    @property
    def suc_flag(self):
        return 200

    @property
    def token(self):
        return self._token

    @property
    def project_id(self):
        return self._project_id
