import time

from huawei_cloud_stack.clients.base import HuaweiCloudStackBaseClient


class HuaweiCloudStackAuthClient(HuaweiCloudStackBaseClient):
    URL = "/v3/auth/tokens"
    METHOD = "post"
    RETRY_TIMES = 4
    RETRY_SLEEP = 5

    def handle(self, *args, **kwargs):
        times = self.RETRY_TIMES
        error = None
        while times:
            try:
                result = super(HuaweiCloudStackAuthClient, self).handle(*args, **kwargs)
            except Exception as e:
                error = e
                time.sleep(self.RETRY_SLEEP)
            else:
                return result
            times -= 1
        raise error

    def build_request_json(self):
        return {
            "auth": {
                "identity": {
                    "methods": ["password"],
                    "password": {
                        "user": {"name": self.username, "password": self.password, "domain": {"name": self.tenant}}
                    },
                },
                "scope": {"project": {"id": self.project_id}},
            }
        }

    def parse_response(self, response, *args, **kwargs):
        code = self.suc_flag if response.status_code == 201 else None
        return {"code": code, "data": response.headers.get("x-subject-token")}

    @property
    def username(self):
        return self.connection.get("username", "")

    @property
    def password(self):
        return self.connection.get("password", "")

    @property
    def tenant(self):
        return self.connection.get("tenant", "")
