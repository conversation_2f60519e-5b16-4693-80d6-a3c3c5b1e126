from huawei_cloud_stack.clients.base import HuaweiCloudStackBaseClient


class HuaweiCloudStackELBListClient(HuaweiCloudStackBaseClient):
    URL = "/v2/{project_id}/elb/loadbalancers"
    METHOD = "get"

    def build_request_params(self, page_index, page_size):
        return {"offset": page_index * page_size, "limit": page_size}

    @property
    def data_key_name(self):
        return "data.loadbalancers"


class HuaweiCloudStackELBListenerClient(HuaweiCloudStackBaseClient):
    URL = "/v2/{project_id}/elb/listeners/{listener_id}"
    METHOD = "get"

    def build_request_url(self, listener_id):
        return super(HuaweiCloudStackELBListenerClient, self).build_request_url(listener_id=listener_id)

    @property
    def data_key_name(self):
        return "data.listener"


class HuaweiCloudStackELBPoolClient(HuaweiCloudStackBaseClient):
    URL = "/v2.0/lbaas/pools/{pool_id}"
    METHOD = "get"

    def build_request_url(self, pool_id):
        return super(HuaweiCloudStackELBPoolClient, self).build_request_url(pool_id=pool_id)

    @property
    def data_key_name(self):
        return "data.pool"


class HuaweiCloudStackELBPoolMembersClient(HuaweiCloudStackBaseClient):
    URL = "/v2.0/lbaas/pools/{pool_id}/members/{member_id}"
    METHOD = "get"

    def build_request_url(self, pool_id, member_id):
        return super(HuaweiCloudStackELBPoolMembersClient, self).build_request_url(pool_id=pool_id, member_id=member_id)

    @property
    def data_key_name(self):
        return "data.member"
