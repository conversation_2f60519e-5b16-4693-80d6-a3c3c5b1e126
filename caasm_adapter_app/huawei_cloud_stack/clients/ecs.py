from huawei_cloud_stack.clients.base import HuaweiCloudStackBaseClient


class HuaweiCloudStackECSListClient(HuaweiCloudStackBaseClient):
    URL = "/v1/{project_id}/cloudservers/detail"
    METHOD = "get"

    def build_request_params(self, page_index, page_size):
        return {"offset": page_index * page_size, "limit": page_size}

    @property
    def data_key_name(self):
        return "data.servers"

    def clean_result(self, biz_value):
        if not biz_value:
            return []

        for server in biz_value:
            addresses = server.get("addresses")
            if not addresses:
                continue
            new_addresses = list(addresses.values())
            server["addresses"] = new_addresses[0] if new_addresses else []
        return biz_value
