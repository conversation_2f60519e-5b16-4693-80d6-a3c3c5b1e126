name: "huawei_cloud_stack"
display_name: "华为云（私有化部署）"
description: "华为云立足于互联网领域，提供包括云主机、云托管、云存储等基础云服务、超算、内容分发与加速、视频托管与发布、企业IT、云电脑、云会议、游戏托管、应用托管等服务和解决方案。"
type: "云平台"
company: "华为"
logo: "huawei_cloud_stack.jpg"
version: "v0.1"
priority: 1
properties:
  - "云平台"

connection:
  - name: address
    type: url
    required: true
    default: ""
    display_name: "地址"
    description: "地址信息"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'


  - name: username
    type: string
    required: true
    display_name: "username"
    description: "用户名称"
    validate_rules:
      - name: length
        error_hint: "username格式无效。长度最小不得小于2，最大不得大于100"
        setting:
          min: 2
          max: 100

  - name: tenant
    type: string
    required: true
    display_name: "租户信息"
    description: "租户信息"
    validate_rules:
      - name: length
        error_hint: "租户信息格式无效。长度最小不得小于2，最大不得大于100"
        setting:
          min: 2
          max: 100


  - name: password
    type: password
    required: true
    display_name: "password"
    description: "密码"
    validate_rules:
      - name: length
        error_hint: "passwrod格式无效。长度最小不得小于6，最大不得大于200"
        setting:
          min: 6
          max: 200

  - name: project_ids
    type: list
    required: false
    display_name: "项目ID集合"
    description: "项目ID集合"
    validate_rules:
      - name: element_length
        error_hint: "项目ID长度不合法。长度最小不得小于1，最大不得大于100"
        setting:
          type: string
          min: 1
          max: 100

  - name: delay
    type: integer
    required: false
    display_name: "延时时间"
    description: "针对于数据量过多时导致请求次数增多造成华为云返回429（请求次数受限的问题）"
    validate_rules:
      - name: number
        error_hint: "延时时间格式无效。最小值不得小于1，最大值不得大于1000"
        setting:
          min: 1
          max: 1000



fetch_setting:
  type: disposable
  point: "huawei_cloud_stack.fetch:find_asset"
  is_need_test_service: true
  test_auth_point: "huawei_cloud_stack.fetch:get_auth_connection"
  size: 300
  fetch_type_mapper:
    asset:
      - host
    network:
      - network_mapping

fabric_setting:
  choose_point_mapper: { }

merge_setting:
  size: 300
  setting: { }

convert_setting:
  size: 300
  before_executor_mapper: { }
  executor_mapper: { }