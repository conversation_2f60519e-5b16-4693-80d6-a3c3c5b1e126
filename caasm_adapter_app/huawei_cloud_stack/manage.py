import copy
import time
from functools import cached_property

from huawei_cloud_stack.cleaners.elb import <PERSON>LBCLeaner
from huawei_cloud_stack.clients.auth import HuaweiCloudStackAuthClient
from huawei_cloud_stack.clients.ecs import HuaweiCloudStackECSListClient
from huawei_cloud_stack.clients.elb import (
    HuaweiCloudStackELBListClient,
    HuaweiCloudStackELBListenerClient,
    HuaweiCloudStackELBPoolClient,
    HuaweiCloudStackELBPoolMembersClient,
)


class ClientType(object):
    ECS = "ecs"
    ELB = "elb"
    ELB_LISTENER = "elb_listener"
    ELB_POOL = "elb_pool"
    ELB_POOL_MEMBER = "elb_pool_member"
    AUTH = "auth"


class FetchType(object):
    HOST = "host"
    NETWORK_MAPPING = "network_mapping"


class HuaweiCloutStackManager(object):
    CLASS_MAPPER = {
        ClientType.ECS: HuaweiCloudStackECSListClient,
        ClientType.ELB: HuaweiCloudStackELBListClient,
        ClientType.ELB_LISTENER: HuaweiCloudStackELBListenerClient,
        ClientType.ELB_POOL: HuaweiCloudStackELBPoolClient,
        ClientType.ELB_POOL_MEMBER: HuaweiCloudStackELBPoolMembersClient,
        ClientType.AUTH: HuaweiCloudStackAuthClient,
    }

    def __init__(self, connection, session=None, condition=None):
        self._connection = connection
        self._condition = condition
        self._session = session
        self._find_method_mapper = {
            FetchType.HOST: self._find_host,
            FetchType.NETWORK_MAPPING: self._find_network_mapping,
        }
        self._clean_method_mapper = {
            FetchType.NETWORK_MAPPING: ELBCLeaner.clean,
        }

    def auth(self, project_id=None):
        project_id = project_id or self.project_ids[0]
        return self._call(ClientType.AUTH, project_id)

    def find(self, fetch_type, page_size):
        current_page_index, current_project_id, current_token = self._init_find(fetch_type)
        if not current_project_id:
            return []

        if self.delay:
            time.sleep(self.delay)
        find_method = self._find_method_mapper[fetch_type]
        clean_method = self._clean_method_mapper.get(fetch_type)
        result = find_method(current_project_id, current_token, current_page_index, page_size)
        if result:
            self._record_find(fetch_type)
            if clean_method:
                result = clean_method(result)
            return result
        self._reset_find(fetch_type)
        return self.find(fetch_type, page_size)

    def _find_host(self, project_id, auth_token, page_index, page_size):
        return self._call(ClientType.ECS, project_id, page_index=page_index, page_size=page_size, auth_token=auth_token)

    def _find_network_mapping(self, project_id, auth_token, page_index, page_size):
        network_mappings = self._call(
            ClientType.ELB, project_id, page_index=page_index, page_size=page_size, auth_token=auth_token
        )
        for network_mapping in network_mappings:
            self._enrich_listener(project_id, network_mapping, auth_token)
            self._enrich_pool(project_id, network_mapping, auth_token)
        return network_mappings

    def _enrich_pool(self, project_id, network_mapping, auth_token):
        result = []

        for pool in network_mapping.get("pools") or []:
            pool_detail = self._call(ClientType.ELB_POOL, project_id, pool_id=pool["id"], auth_token=auth_token)
            self._enrich_pool_member(project_id, pool_detail, auth_token) if pool_detail else ...
            result.append(pool_detail) if pool_detail else ...
        network_mapping["pools"] = result

    def _enrich_pool_member(self, project_id, pool, auth_token):
        result = []
        for member in pool.get("members") or []:
            detail = self._call(
                ClientType.ELB_POOL_MEMBER,
                project_id,
                member_id=member["id"],
                pool_id=pool["id"],
                auth_token=auth_token,
            )
            result.append(detail) if detail else ...
        pool["members"] = result

    def _enrich_listener(self, project_id, network_mapping, auth_token):
        result = []
        for listener in network_mapping.get("listeners") or []:
            data = self._call(ClientType.ELB_LISTENER, project_id, auth_token=auth_token, listener_id=listener["id"])
            result.append(data)
        network_mapping["listeners"] = result

    def _init_find(self, fetch_type):
        if fetch_type not in self._condition:
            project_ids = copy.copy(self.project_ids)
            current_project_id = project_ids.pop()
            self._condition[fetch_type] = {
                "current_page_index": 0,
                "current_project_id": current_project_id,
                "project_ids": project_ids,
            }

        condition = self._condition[fetch_type]
        current_project_id = condition["current_project_id"]
        if not condition.get("auth_token"):
            condition["auth_token"] = self.auth(current_project_id)
        current_page_index = condition["current_page_index"]
        return current_page_index, current_project_id, condition["auth_token"]

    def _record_find(self, fetch_type):
        condition = self._condition[fetch_type]
        condition["current_page_index"] += 1

    def _reset_find(self, fetch_type):
        condition = self._condition[fetch_type]
        project_ids = condition["project_ids"]

        condition["current_project_id"] = project_ids.pop() if project_ids else None
        condition["current_page_index"] = 0
        condition["auth_token"] = None

    def _call(self, client_type, project_id, auth_token=None, *args, **kwargs):
        instance = self.CLASS_MAPPER[client_type](project_id, self._connection, self._session, auth_token)
        return instance.handle(*args, **kwargs)

    @cached_property
    def project_ids(self):
        return self._connection["project_ids"]

    @property
    def delay(self):
        return self._connection.get("delay")
