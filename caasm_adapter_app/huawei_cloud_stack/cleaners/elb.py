import copy
from collections import defaultdict


class ELBCLeaner(object):
    @classmethod
    def clean(cls, records):
        result = []

        for record in records:
            new_record = cls._clean_single(record)
            result.extend(new_record) if isinstance(new_record, list) else result.append(new_record)

        for record in result:
            cls._enrich_entry_port(record)
            cls._enrich_destination(record)

        return result

    @classmethod
    def _enrich_entry_port(cls, record):
        entry_port = record.get("listener").get("protocol_port")
        record["entry_ports"] = [entry_port] if entry_port else []

    @classmethod
    def _enrich_destination(cls, record):
        destinations = []
        members = record["members"]
        for member in members:
            destinations.append({"ip": member["address"], "instance_id": member["id"], "port": member["protocol_port"]})
        record["destinations"] = destinations

    @classmethod
    def _clean_single(cls, record):
        result = []

        listeners = record.pop("listeners", []) or []
        pools = record.pop("pools", []) or []

        pool_listener_by_listener_id = defaultdict(list)

        for pool in pools:
            tmp_listeners = pool.get("listeners") or []
            tmp_members = pool.get("members") or []
            for tmp_listener in tmp_listeners:
                tmp_listener_id = tmp_listener["id"]
                pool_listener_by_listener_id[tmp_listener_id].extend(tmp_members)

        for listener in listeners:
            new_record = copy.deepcopy(record)
            listener_id = listener["id"]
            new_record["listener"] = listener
            new_record["members"] = pool_listener_by_listener_id[listener_id]
            result.append(new_record)
        if not result:
            record["listener"] = {}
            record["members"] = []
            result = record

        return result
