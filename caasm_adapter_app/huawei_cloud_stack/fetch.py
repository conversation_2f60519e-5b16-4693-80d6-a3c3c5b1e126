from caasm_adapter.sdk.adapter_fetch import fetch_sdk
from huawei_cloud_stack.manage import HuaweiCloutStackManager


def find_asset(connection, fetch_type, condition, session=None, page_size=100, **kwargs):
    manager = _build_manager(connection, session, condition=condition)
    records = manager.find(fetch_type, page_size)
    result = fetch_sdk.build_asset(records, fetch_type)
    return fetch_sdk.return_success(result)


def get_auth_connection(connection, session):
    _build_manager(connection, session, condition=connection).auth()


def _build_manager(connection, session=None, condition=None):
    return HuaweiCloutStackManager(connection, session, condition=condition)
