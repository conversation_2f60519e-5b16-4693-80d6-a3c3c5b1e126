{"canvas": {"nodes": [{"id": "root", "field": "根节点", "path": "root", "datatype": "object", "type": "asset", "level": 0, "sub_fields": [], "x": 0, "y": 0, "asset_type": "asset", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 100, "id": "root.tenant_id", "level": 1, "path": "tenant_id", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "tenant_id", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 200, "id": "root.provisioning_status", "level": 1, "path": "provisioning_status", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "provisioning_status", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 300, "id": "root.region_vip_address", "level": 1, "path": "region_vip_address", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "region_vip_address", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 400, "id": "root.description", "level": 1, "path": "description", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "description", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 500, "id": "root.created_at", "level": 1, "path": "created_at", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "created_at", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 600, "id": "root.l7_flavor_id", "level": 1, "path": "l7_flavor_id", "sub_fields": [], "type": "asset", "datatype": "any", "asset_type": "asset", "field": "l7_flavor_id", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 700, "id": "root.vip_subnet_id", "level": 1, "path": "vip_subnet_id", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "vip_subnet_id", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 800, "id": "root.tags", "level": 1, "path": "tags", "sub_fields": [{"type": "any", "field": "element", "path": "tags.element"}], "type": "asset", "datatype": "list", "asset_type": "asset", "field": "tags", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 900, "id": "root.vip_address", "level": 1, "path": "vip_address", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "vip_address", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 1000, "id": "root.updated_at", "level": 1, "path": "updated_at", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "updated_at", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 1100, "id": "root.operating_status", "level": 1, "path": "operating_status", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "operating_status", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 1200, "id": "root.provider", "level": 1, "path": "provider", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "provider", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 1300, "id": "root.l4_flavor_id", "level": 1, "path": "l4_flavor_id", "sub_fields": [], "type": "asset", "datatype": "any", "asset_type": "asset", "field": "l4_flavor_id", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 1400, "id": "root.admin_state_up", "level": 1, "path": "admin_state_up", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "admin_state_up", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 1500, "id": "root.name", "level": 1, "path": "name", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "name", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 1600, "id": "root.router_id", "level": 1, "path": "router_id", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "router_id", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 1700, "id": "root.id", "level": 1, "path": "id", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "id", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 1800, "id": "root.vip_port_id", "level": 1, "path": "vip_port_id", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "vip_port_id", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 1900, "id": "root.floating_ips", "level": 1, "path": "floating_ips", "sub_fields": [{"type": "any", "field": "element", "path": "floating_ips.element"}], "type": "asset", "datatype": "list", "asset_type": "asset", "field": "floating_ips", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 2000, "id": "root.clusters", "level": 1, "path": "clusters", "sub_fields": [{"type": "any", "field": "element", "path": "clusters.element"}], "type": "asset", "datatype": "list", "asset_type": "asset", "field": "clusters", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 2100, "id": "root.listener", "level": 1, "path": "listener", "sub_fields": [{"field": "id", "path": "listener.id", "type": "string", "x": 400, "y": 2200}, {"field": "tenant_id", "path": "listener.tenant_id", "type": "string", "x": 400, "y": 2300}, {"field": "name", "path": "listener.name", "type": "string", "x": 400, "y": 2400}, {"field": "description", "path": "listener.description", "type": "string", "x": 400, "y": 2500}, {"field": "default_pool_id", "path": "listener.default_pool_id", "type": "string", "x": 400, "y": 2600}, {"field": "protocol", "path": "listener.protocol", "type": "string", "x": 400, "y": 2700}, {"field": "protocol_port", "path": "listener.protocol_port", "type": "integer", "x": 400, "y": 2800}, {"field": "connection_limit", "path": "listener.connection_limit", "type": "integer", "x": 400, "y": 2900}, {"field": "admin_state_up", "path": "listener.admin_state_up", "type": "integer", "x": 400, "y": 3000}, {"field": "client_ca_tls_container_ref", "path": "listener.client_ca_tls_container_ref", "type": "any", "sub_fields": [], "x": 400, "y": 3100}, {"field": "http2_enable", "path": "listener.http2_enable", "type": "integer", "x": 400, "y": 3200}, {"field": "keepalive_timeout", "path": "listener.keepalive_timeout", "type": "integer", "x": 400, "y": 3300}, {"field": "enhance_l7policy_enable", "path": "listener.enhance_l7policy_enable", "type": "integer", "x": 400, "y": 3400}, {"field": "loadbalancers", "path": "listener.loadbalancers", "type": "list", "sub_fields": [{"type": "object", "sub_fields": [{"field": "id", "path": "listener.loadbalancers.element.id", "type": "string"}], "field": "element", "path": "listener.loadbalancers.element"}], "x": 400, "y": 3500}, {"field": "sni_container_refs", "path": "listener.sni_container_refs", "type": "list", "sub_fields": [{"type": "any", "field": "element", "path": "listener.sni_container_refs.element"}], "x": 400, "y": 3600}, {"field": "default_tls_container_ref", "path": "listener.default_tls_container_ref", "type": "any", "sub_fields": [], "x": 400, "y": 3700}, {"field": "timeout_client_data", "path": "listener.timeout_client_data", "type": "integer", "x": 400, "y": 3800}, {"field": "timeout_member_data", "path": "listener.timeout_member_data", "type": "integer", "x": 400, "y": 3900}, {"field": "created_at", "path": "listener.created_at", "type": "string", "x": 400, "y": 4000}, {"field": "updated_at", "path": "listener.updated_at", "type": "string", "x": 400, "y": 4100}, {"field": "tags", "path": "listener.tags", "type": "list", "sub_fields": [{"type": "any", "field": "element", "path": "listener.tags.element"}], "x": 400, "y": 4200}], "type": "asset", "datatype": "object", "asset_type": "asset", "field": "listener", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 2200, "id": "root.listener.id", "level": 2, "path": "listener.id", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "id", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 2300, "id": "root.listener.tenant_id", "level": 2, "path": "listener.tenant_id", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "tenant_id", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 2400, "id": "root.listener.name", "level": 2, "path": "listener.name", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "name", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 2500, "id": "root.listener.description", "level": 2, "path": "listener.description", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "description", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 2600, "id": "root.listener.default_pool_id", "level": 2, "path": "listener.default_pool_id", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "default_pool_id", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 2700, "id": "root.listener.protocol", "level": 2, "path": "listener.protocol", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "protocol", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 2800, "id": "root.listener.protocol_port", "level": 2, "path": "listener.protocol_port", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "protocol_port", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 2900, "id": "root.listener.connection_limit", "level": 2, "path": "listener.connection_limit", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "connection_limit", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 3000, "id": "root.listener.admin_state_up", "level": 2, "path": "listener.admin_state_up", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "admin_state_up", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 3100, "id": "root.listener.client_ca_tls_container_ref", "level": 2, "path": "listener.client_ca_tls_container_ref", "sub_fields": [], "type": "asset", "datatype": "any", "asset_type": "asset", "field": "client_ca_tls_container_ref", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 3200, "id": "root.listener.http2_enable", "level": 2, "path": "listener.http2_enable", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "http2_enable", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 3300, "id": "root.listener.keepalive_timeout", "level": 2, "path": "listener.keepalive_timeout", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "keepalive_timeout", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 3400, "id": "root.listener.enhance_l7policy_enable", "level": 2, "path": "listener.enhance_l7policy_enable", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "enhance_l7policy_enable", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 3500, "id": "root.listener.loadbalancers", "level": 2, "path": "listener.loadbalancers", "sub_fields": [{"type": "object", "sub_fields": [{"field": "id", "path": "listener.loadbalancers.element.id", "type": "string"}], "field": "element", "path": "listener.loadbalancers.element"}], "type": "asset", "datatype": "list", "asset_type": "asset", "field": "loadbalancers", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 3600, "id": "root.listener.sni_container_refs", "level": 2, "path": "listener.sni_container_refs", "sub_fields": [{"type": "any", "field": "element", "path": "listener.sni_container_refs.element"}], "type": "asset", "datatype": "list", "asset_type": "asset", "field": "sni_container_refs", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 3700, "id": "root.listener.default_tls_container_ref", "level": 2, "path": "listener.default_tls_container_ref", "sub_fields": [], "type": "asset", "datatype": "any", "asset_type": "asset", "field": "default_tls_container_ref", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 3800, "id": "root.listener.timeout_client_data", "level": 2, "path": "listener.timeout_client_data", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "timeout_client_data", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 3900, "id": "root.listener.timeout_member_data", "level": 2, "path": "listener.timeout_member_data", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "timeout_member_data", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 4000, "id": "root.listener.created_at", "level": 2, "path": "listener.created_at", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "created_at", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 4100, "id": "root.listener.updated_at", "level": 2, "path": "listener.updated_at", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "updated_at", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 4200, "id": "root.listener.tags", "level": 2, "path": "listener.tags", "sub_fields": [{"type": "any", "field": "element", "path": "listener.tags.element"}], "type": "asset", "datatype": "list", "asset_type": "asset", "field": "tags", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 4300, "id": "root.members", "level": 1, "path": "members", "sub_fields": [{"type": "object", "sub_fields": [{"field": "id", "path": "members.element.id", "type": "string"}, {"field": "tenant_id", "path": "members.element.tenant_id", "type": "string"}, {"field": "address", "path": "members.element.address", "type": "string"}, {"field": "protocol_port", "path": "members.element.protocol_port", "type": "integer"}, {"field": "weight", "path": "members.element.weight", "type": "integer"}, {"field": "admin_state_up", "path": "members.element.admin_state_up", "type": "integer"}, {"field": "subnet_id", "path": "members.element.subnet_id", "type": "string"}, {"field": "operating_status", "path": "members.element.operating_status", "type": "string"}, {"field": "name", "path": "members.element.name", "type": "string"}], "field": "element", "path": "members.element"}], "type": "asset", "datatype": "list", "asset_type": "asset", "field": "members", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 4400, "id": "root.entry_ports", "level": 1, "path": "entry_ports", "sub_fields": [{"type": "integer", "field": "element", "path": "entry_ports.element"}], "type": "asset", "datatype": "list", "asset_type": "asset", "field": "entry_ports", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 4500, "id": "root.destinations", "level": 1, "path": "destinations", "sub_fields": [{"type": "object", "sub_fields": [{"field": "ip", "path": "destinations.element.ip", "type": "string"}, {"field": "instance_id", "path": "destinations.element.instance_id", "type": "string"}, {"field": "port", "path": "destinations.element.port", "type": "integer"}], "field": "element", "path": "destinations.element"}], "type": "asset", "datatype": "list", "asset_type": "asset", "field": "destinations", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.destinations.y94tc_8Y5", "x": 364.0859375, "y": 4503.5, "order": 1, "level": 2, "source_type": "list", "path": "root.destinations.y94tc_8Y5", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "network_mapping.destinations"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "1uMTSf1Jf", "display_name": "网络映射", "description": "network_mapping.destinations", "x": 564.0859375, "y": 4503.5, "label": "网络映射-映射目标列表", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.entry_ports.g7VjxEXVd", "x": 360.0859375, "y": 4400.5, "order": 2, "level": 2, "source_type": "list", "path": "root.entry_ports.g7VjxEXVd", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "network_mapping.entry_ports"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "xamtDxLZR", "display_name": "网络映射", "description": "network_mapping.entry_ports", "x": 560.0859375, "y": 4400.5, "label": "网络映射-入口端口列表", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.name.qhnpuTJuk", "x": 382.0859375, "y": 1499.5, "order": 3, "level": 2, "source_type": "string", "path": "root.name.qhnpuTJuk", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "cloud.name"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "JX0iq0jy_", "display_name": "云", "description": "cloud.name", "x": 582.0859375, "y": 1499.5, "label": "云-名称", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.id.441MiFm6y", "x": 387.0859375, "y": 1700.5, "order": 4, "level": 2, "source_type": "string", "path": "root.id.441MiFm6y", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "cloud.id"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "jxmjSAUmw", "display_name": "云", "description": "cloud.id", "x": 587.0859375, "y": 1700.5, "label": "云-资源ID", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.duwp5Hwl3", "x": 365.0859375, "y": -258.4999999999998, "order": 6, "level": 1, "source_type": "object", "path": "root.duwp5Hwl3", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "新增字段", "description": "新增字段，字段值必须是字符串类型", "field": "新增字段", "input": {"asset_web_type": "add", "description": "新增字段，字段值必须是字符串类型", "field": "_source", "values": ["4"]}, "action_type": "add", "attrs": {"text": "新增字段"}}, {"type": "asset", "asset_type": "action", "id": "root.duwp5Hwl3.yQil2oRVk", "x": 537.0859375, "y": -259.4999999999998, "order": 7, "level": 2, "path": "root.duwp5Hwl3.yQil2oRVk", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "base.resource_type"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "RQmIfXow_", "display_name": "基础", "description": "base.resource_type", "x": 737.0859375, "y": -259.4999999999998, "label": "基础-资源类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.EhLpsQgFz", "x": 363.0859375, "y": -186.49999999999977, "order": 8, "level": 1, "source_type": "object", "path": "root.EhLpsQgFz", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "新增字段", "description": "新增字段，字段值必须是字符串类型", "field": "新增字段", "input": {"asset_web_type": "add", "description": "新增字段，字段值必须是字符串类型", "field": "_class", "values": ["弹性负载均衡"]}, "action_type": "add", "attrs": {"text": "新增字段"}}, {"type": "asset", "asset_type": "action", "id": "root.EhLpsQgFz.pIasvteAx", "x": 531.0859375, "y": -186.49999999999977, "order": 9, "level": 2, "path": "root.EhLpsQgFz.pIasvteAx", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "network_mapping.class"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "844bY8T0Z", "display_name": "网络映射", "description": "network_mapping.class", "x": 731.0859375, "y": -186.49999999999977, "label": "网络映射-映射类别", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.XfnHPNor-", "x": 369.0859375, "y": -118.49999999999977, "order": 10, "level": 1, "source_type": "object", "path": "root.XfnHPNor-", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "新增字段", "description": "新增字段，字段值必须是字符串类型", "field": "新增字段", "input": {"asset_web_type": "add", "description": "新增字段，字段值必须是字符串类型", "field": "__name", "values": ["ELB"]}, "action_type": "add", "attrs": {"text": "新增字段"}}, {"type": "asset", "asset_type": "action", "id": "root.XfnHPNor-.lV9RRavYg", "x": 540.0859375, "y": -121.49999999999977, "order": 11, "level": 2, "path": "root.XfnHPNor-.lV9RRavYg", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "cloud.service_name"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "VuN53j5HS", "display_name": "云", "description": "cloud.service_name", "x": 740.0859375, "y": -121.49999999999977, "label": "云-云服务名称", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.OD69jhQxN", "x": 375.0859375, "y": -51.49999999999977, "order": 12, "level": 1, "source_type": "object", "path": "root.OD69jhQxN", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "新增字段", "description": "新增字段，字段值必须是字符串类型", "field": "新增字段", "input": {"asset_web_type": "add", "description": "新增字段，字段值必须是字符串类型", "field": "_boss", "values": ["华为云"]}, "action_type": "add", "attrs": {"text": "新增字段"}}, {"type": "asset", "asset_type": "action", "id": "root.OD69jhQxN.JXwStuZCO", "x": 533.0859375, "y": -50.49999999999977, "order": 13, "level": 2, "path": "root.OD69jhQxN.JXwStuZCO", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "cloud.vendor"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "03UcxXAhE", "display_name": "云", "description": "cloud.vendor", "x": 733.0859375, "y": -50.49999999999977, "label": "云-云供应商", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.OspHCqxoP", "x": 371.0859375, "y": 23.500000000000227, "order": 14, "level": 1, "source_type": "object", "path": "root.OspHCqxoP", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "新增字段", "description": "新增字段，字段值必须是字符串类型", "field": "新增字段", "input": {"asset_web_type": "add", "description": "新增字段，字段值必须是字符串类型", "field": "_key", "values": ["${_class}-${vip_address}-${entry_ip_v6}-${entry_ports}-${destinations}"]}, "action_type": "add", "attrs": {"text": "新增字段"}}, {"type": "asset", "asset_type": "action", "id": "root.OspHCqxoP.dsWAXYsIl", "x": 534.0859375, "y": 25.500000000000227, "order": 15, "level": 2, "path": "root.OspHCqxoP.dsWAXYsIl", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "base.unique_key"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "yzRRrOW0B", "display_name": "基础", "description": "base.unique_key", "x": 734.0859375, "y": 25.500000000000227, "label": "基础-唯一键", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.created_at.JAb-jSfiw", "x": 398.0859375, "y": 501.5, "order": 16, "level": 2, "source_type": "string", "path": "root.created_at.JAb-jSfiw", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "将数据转化为datetime类型", "description": "用于时间类型的转换，将其他格式转换为datatime类型，并可设定转换格式，并在此基础上新增或删除多少秒", "field": "将数据转化为datetime类型", "input": {"asset_web_type": "datetime", "description": "用于时间类型的转换，将其他格式转换为datatime类型，并可设定转换格式，并在此基础上新增或删除多少秒", "format": "%Y-%m-%dT%H:%M:%S"}, "action_type": "datetime", "attrs": {"text": "将数据转化为datetime类型"}}, {"type": "asset", "asset_type": "action", "id": "root.created_at.JAb-jSfiw.uifCqhrDb", "x": 589.0859375, "y": 501.5, "order": 17, "level": 3, "path": "root.created_at.JAb-jSfiw.uifCqhrDb", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "base.first_seen"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "kw-33x8KS", "display_name": "基础", "description": "base.first_seen", "x": 789.0859375, "y": 501.5, "label": "基础-首次出现时间", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.vip_address.CgRZCnCkB", "x": 405.0859375, "y": 901.5, "order": 18, "level": 2, "source_type": "string", "path": "root.vip_address.CgRZCnCkB", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "network_mapping.entry_ip"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "JRHCpLx5j", "display_name": "网络映射", "description": "network_mapping.entry_ip", "x": 605.0859375, "y": 901.5, "label": "网络映射-入口IPv4地址", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}], "edges": [{"source": "root", "target": "root.tenant_id", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.213314173384987481685605418383", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 100, "anchor_index": 0}}, {"source": "root", "target": "root.provisioning_status", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.98357701015179671685605418386", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 200, "anchor_index": 0}}, {"source": "root", "target": "root.region_vip_address", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.73172932398034951685605418387", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 300, "anchor_index": 0}}, {"source": "root", "target": "root.description", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.85651857958466081685605418387", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 400, "anchor_index": 0}}, {"source": "root", "target": "root.created_at", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.56683038969215721685605418387", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 500, "anchor_index": 0}}, {"source": "root", "target": "root.l7_flavor_id", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.94802125959295341685605418387", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 600, "anchor_index": 0}}, {"source": "root", "target": "root.vip_subnet_id", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.58915125833461391685605418388", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 700, "anchor_index": 0}}, {"source": "root", "target": "root.tags", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.0131968739514451981685605418388", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 800, "anchor_index": 0}}, {"source": "root", "target": "root.vip_address", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.29023717343615551685605418388", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 900, "anchor_index": 0}}, {"source": "root", "target": "root.updated_at", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.64332424738111741685605418388", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 1000, "anchor_index": 0}}, {"source": "root", "target": "root.operating_status", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.4041089460074981685605418388", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 1100, "anchor_index": 0}}, {"source": "root", "target": "root.provider", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.62662033907164941685605418389", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 1200, "anchor_index": 0}}, {"source": "root", "target": "root.l4_flavor_id", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.054301952476236261685605418389", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 1300, "anchor_index": 0}}, {"source": "root", "target": "root.admin_state_up", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.12988585829758771685605418389", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 1400, "anchor_index": 0}}, {"source": "root", "target": "root.name", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.172840357106642361685605418389", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 1500, "anchor_index": 0}}, {"source": "root", "target": "root.router_id", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.20696678455845931685605418389", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 1600, "anchor_index": 0}}, {"source": "root", "target": "root.id", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.36570528398865011685605418389", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 1700, "anchor_index": 0}}, {"source": "root", "target": "root.vip_port_id", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.63359496525374781685605418390", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 1800, "anchor_index": 0}}, {"source": "root", "target": "root.floating_ips", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.084557523034925361685605418390", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 1900, "anchor_index": 0}}, {"source": "root", "target": "root.clusters", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.37997458447455391685605418390", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 2000, "anchor_index": 0}}, {"source": "root", "target": "root.listener", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.79269465713468471685605418390", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 2100, "anchor_index": 0}}, {"source": "root.listener", "target": "root.listener.id", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.89344767437549181685605418390", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2125.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 2200, "anchor_index": 0}}, {"source": "root.listener", "target": "root.listener.tenant_id", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.94430719311821521685605418391", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2125.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 2300, "anchor_index": 0}}, {"source": "root.listener", "target": "root.listener.name", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.68575021370583691685605418391", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2125.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 2400, "anchor_index": 0}}, {"source": "root.listener", "target": "root.listener.description", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.33460915629180411685605418391", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2125.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 2500, "anchor_index": 0}}, {"source": "root.listener", "target": "root.listener.default_pool_id", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.87158872421000071685605418392", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2125.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 2600, "anchor_index": 0}}, {"source": "root.listener", "target": "root.listener.protocol", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.170345142984803081685605418392", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2125.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 2700, "anchor_index": 0}}, {"source": "root.listener", "target": "root.listener.protocol_port", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.298873987152022071685605418392", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2125.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 2800, "anchor_index": 0}}, {"source": "root.listener", "target": "root.listener.connection_limit", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.130833181739540731685605418393", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2125.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 2900, "anchor_index": 0}}, {"source": "root.listener", "target": "root.listener.admin_state_up", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.67503389771896521685605418393", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2125.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 3000, "anchor_index": 0}}, {"source": "root.listener", "target": "root.listener.client_ca_tls_container_ref", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.35966358410202551685605418393", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2125.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 3100, "anchor_index": 0}}, {"source": "root.listener", "target": "root.listener.http2_enable", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.187920064094355471685605418393", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2125.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 3200, "anchor_index": 0}}, {"source": "root.listener", "target": "root.listener.keepalive_timeout", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.5571186405541931685605418394", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2125.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 3300, "anchor_index": 0}}, {"source": "root.listener", "target": "root.listener.enhance_l7policy_enable", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.63745023201678271685605418394", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2125.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 3400, "anchor_index": 0}}, {"source": "root.listener", "target": "root.listener.loadbalancers", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.0495562707638816931685605418394", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2125.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 3500, "anchor_index": 0}}, {"source": "root.listener", "target": "root.listener.sni_container_refs", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.58787443954304151685605418394", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2125.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 3600, "anchor_index": 0}}, {"source": "root.listener", "target": "root.listener.default_tls_container_ref", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.98015062521542771685605418395", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2125.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 3700, "anchor_index": 0}}, {"source": "root.listener", "target": "root.listener.timeout_client_data", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.63123664867698431685605418395", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2125.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 3800, "anchor_index": 0}}, {"source": "root.listener", "target": "root.listener.timeout_member_data", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.67853819894502811685605418395", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2125.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 3900, "anchor_index": 0}}, {"source": "root.listener", "target": "root.listener.created_at", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.57958717292741451685605418395", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2125.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 4000, "anchor_index": 0}}, {"source": "root.listener", "target": "root.listener.updated_at", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.60862763325786261685605418395", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2125.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 4100, "anchor_index": 0}}, {"source": "root.listener", "target": "root.listener.tags", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.78732595132692081685605418395", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 2125.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 4200, "anchor_index": 0}}, {"source": "root", "target": "root.members", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.355532292868188771685605418396", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 4300, "anchor_index": 0}}, {"source": "root", "target": "root.entry_ports", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.070201100524867721685605418396", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 4400, "anchor_index": 0}}, {"source": "root", "target": "root.destinations", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.52184913280390741685605418396", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 4500, "anchor_index": 0}}, {"source": "root.destinations", "target": "root.destinations.y94tc_8Y5", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.42295050256033641685605425012", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 4500, "anchor_index": 1}, "end_point": {"x": 313.5859375, "y": 4503.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.destinations.y94tc_8Y5", "target": "1uMTSf1Jf", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.65399407922459821685605433246", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 414.5859375, "y": 4503.5, "anchor_index": 1}, "end_point": {"x": 513.5859375, "y": 4503.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.entry_ports", "target": "root.entry_ports.g7VjxEXVd", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.89178862525465721685605434876", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 4400, "anchor_index": 1}, "end_point": {"x": 309.5859375, "y": 4400.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.entry_ports.g7VjxEXVd", "target": "xamtDxLZR", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.77191691707991691685605446414", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 410.5859375, "y": 4400.5, "anchor_index": 1}, "end_point": {"x": 509.5859375, "y": 4400.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.name", "target": "root.name.qhnpuTJuk", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.93241147872889711685605454531", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 1500, "anchor_index": 1}, "end_point": {"x": 331.5859375, "y": 1499.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.name.qhnpuTJuk", "target": "JX0iq0jy_", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.48417018800080471685605462837", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 432.5859375, "y": 1499.5, "anchor_index": 1}, "end_point": {"x": 531.5859375, "y": 1499.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.id", "target": "root.id.441MiFm6y", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.122608070341662321685605465266", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 1700, "anchor_index": 1}, "end_point": {"x": 336.5859375, "y": 1700.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.id.441MiFm6y", "target": "jxmjSAUmw", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.313957742074415071685605471769", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 437.5859375, "y": 1700.5, "anchor_index": 1}, "end_point": {"x": 536.5859375, "y": 1700.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root", "target": "root.duwp5Hwl3", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.45437646986921521685605557902", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 50.5, "y": 0, "anchor_index": 1}, "end_point": {"x": 314.5859375, "y": -258.4999999999998, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.duwp5Hwl3", "target": "root.duwp5Hwl3.yQil2oRVk", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.68148947778700111685605583000", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 415.5859375, "y": -258.4999999999998, "anchor_index": 1}, "end_point": {"x": 486.5859375, "y": -259.4999999999998, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.duwp5Hwl3.yQil2oRVk", "target": "RQmIfXow_", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.405669866099678031685605590068", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 587.5859375, "y": -259.4999999999998, "anchor_index": 1}, "end_point": {"x": 686.5859375, "y": -259.4999999999998, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root", "target": "root.EhLpsQgFz", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.00336536091980765841685605605054", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 50.5, "y": 0, "anchor_index": 1}, "end_point": {"x": 312.5859375, "y": -186.49999999999977, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.EhLpsQgFz", "target": "root.EhLpsQgFz.pIasvteAx", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.76902108098677811685605621410", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 413.5859375, "y": -186.49999999999977, "anchor_index": 1}, "end_point": {"x": 480.5859375, "y": -186.49999999999977, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.EhLpsQgFz.pIasvteAx", "target": "844bY8T0Z", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.350280499622597441685605640034", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 581.5859375, "y": -186.49999999999977, "anchor_index": 1}, "end_point": {"x": 680.5859375, "y": -186.49999999999977, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root", "target": "root.XfnHPNor-", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.82584161407243831685605654555", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 50.5, "y": 0, "anchor_index": 1}, "end_point": {"x": 318.5859375, "y": -118.49999999999977, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.XfnHPNor-", "target": "root.XfnHPNor-.lV9RRavYg", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.51716467692911231685605665301", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 419.5859375, "y": -118.49999999999977, "anchor_index": 1}, "end_point": {"x": 489.5859375, "y": -121.49999999999977, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.XfnHPNor-.lV9RRavYg", "target": "VuN53j5HS", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.029874354021612871685605679660", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 590.5859375, "y": -121.49999999999977, "anchor_index": 1}, "end_point": {"x": 689.5859375, "y": -121.49999999999977, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root", "target": "root.OD69jhQxN", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.24719345449267351685605696905", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 50.5, "y": 0, "anchor_index": 1}, "end_point": {"x": 324.5859375, "y": -51.49999999999977, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.OD69jhQxN", "target": "root.OD69jhQxN.JXwStuZCO", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.39815350109869721685605713993", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 425.5859375, "y": -51.49999999999977, "anchor_index": 1}, "end_point": {"x": 482.5859375, "y": -50.49999999999977, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.OD69jhQxN.JXwStuZCO", "target": "03UcxXAhE", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.72041199382037281685605727794", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 583.5859375, "y": -50.49999999999977, "anchor_index": 1}, "end_point": {"x": 682.5859375, "y": -50.49999999999977, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root", "target": "root.OspHCqxoP", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.4398404472554641685605742509", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 50.5, "y": 0, "anchor_index": 1}, "end_point": {"x": 320.5859375, "y": 23.500000000000227, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.OspHCqxoP", "target": "root.OspHCqxoP.dsWAXYsIl", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.451673556747727471685605774044", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 421.5859375, "y": 23.500000000000227, "anchor_index": 1}, "end_point": {"x": 483.5859375, "y": 25.500000000000227, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.OspHCqxoP.dsWAXYsIl", "target": "yzRRrOW0B", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.284224080063510741685605779491", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 584.5859375, "y": 25.500000000000227, "anchor_index": 1}, "end_point": {"x": 683.5859375, "y": 25.500000000000227, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.created_at", "target": "root.created_at.JAb-jSfiw", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.83973496610152921685605813331", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 500, "anchor_index": 1}, "end_point": {"x": 347.5859375, "y": 501.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.created_at.JAb-jSfiw", "target": "root.created_at.JAb-jSfiw.uifCqhrDb", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.127195385179179171685605827526", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 448.5859375, "y": 501.5, "anchor_index": 1}, "end_point": {"x": 538.5859375, "y": 501.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.created_at.JAb-jSfiw.uifCqhrDb", "target": "kw-33x8KS", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.48676711151604411685605833752", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 639.5859375, "y": 501.5, "anchor_index": 1}, "end_point": {"x": 738.5859375, "y": 501.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.vip_address", "target": "root.vip_address.CgRZCnCkB", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.64997104308457981685606133522", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 900, "anchor_index": 1}, "end_point": {"x": 354.5859375, "y": 901.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.vip_address.CgRZCnCkB", "target": "JRHCpLx5j", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.13101081675256721685606141253", "style": {}, "start_point": {"x": 455.5859375, "y": 901.5, "anchor_index": 1}, "end_point": {"x": 554.5859375, "y": 901.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}]}, "rules": [{"name": "add", "setting": {"field": "_source", "values": [4], "multi_flag": null, "is_output_root": null}, "sub_rules": [], "_id": "root.duwp5Hwl3"}, {"name": "enter", "setting": {"values": "base.resource_type", "field": "${_source}"}, "sub_rules": [], "_id": "root.duwp5Hwl3.yQil2oRVk"}, {"name": "add", "setting": {"field": "_class", "values": ["弹性负载均衡"], "multi_flag": null, "is_output_root": null}, "sub_rules": [], "_id": "root.EhLpsQgFz"}, {"name": "enter", "setting": {"values": "network_mapping.class", "field": "${_class}"}, "sub_rules": [], "_id": "root.EhLpsQgFz.pIasvteAx"}, {"name": "add", "setting": {"field": "__name", "values": ["ELB"], "multi_flag": null, "is_output_root": null}, "sub_rules": [], "_id": "root.XfnHPNor-"}, {"name": "enter", "setting": {"values": "cloud.service_name", "field": "${__name}"}, "sub_rules": [], "_id": "root.XfnHPNor-.lV9RRavYg"}, {"name": "add", "setting": {"field": "_boss", "values": ["华为云"], "multi_flag": null, "is_output_root": null}, "sub_rules": [], "_id": "root.OD69jhQxN"}, {"name": "enter", "setting": {"values": "cloud.vendor", "field": "${_boss}"}, "sub_rules": [], "_id": "root.OD69jhQxN.JXwStuZCO"}, {"name": "add", "setting": {"field": "_key", "values": ["${_class}-${vip_address}-${entry_ip_v6}-${entry_ports}-${destinations}"], "multi_flag": null, "is_output_root": null}, "sub_rules": [], "_id": "root.OspHCqxoP"}, {"name": "enter", "setting": {"values": "base.unique_key", "field": "${_key}"}, "sub_rules": [], "_id": "root.OspHCqxoP.dsWAXYsIl"}, {"name": "datetime", "setting": {"format": "%Y-%m-%dT%H:%M:%S", "field": "created_at"}, "sub_rules": [], "_id": "root.created_at.JAb-jSfiw"}, {"name": "enter", "setting": {"values": "base.first_seen", "field": "${created_at}"}, "sub_rules": [], "_id": "root.created_at.JAb-jSfiw.uifCqhrDb"}, {"name": "enter", "setting": {"values": "network_mapping.entry_ip", "field": "${vip_address}"}, "sub_rules": [], "_id": "root.vip_address.CgRZCnCkB"}, {"name": "enter", "setting": {"values": "cloud.name", "field": "${name}"}, "sub_rules": [], "_id": "root.name.qhnpuTJuk"}, {"name": "enter", "setting": {"values": "cloud.id", "field": "${id}"}, "sub_rules": [], "_id": "root.id.441MiFm6y"}, {"name": "enter", "setting": {"values": "network_mapping.entry_ports", "field": "${entry_ports}"}, "sub_rules": [], "_id": "root.entry_ports.g7VjxEXVd"}, {"name": "enter", "setting": {"values": "network_mapping.destinations", "field": "${destinations}"}, "sub_rules": [], "_id": "root.destinations.y94tc_8Y5"}], "adapter_name": "huawei_cloud_stack", "fetch_type": "network_mapping", "model_name": "network_mapping", "asset_type": "network_mapping", "internal": true}