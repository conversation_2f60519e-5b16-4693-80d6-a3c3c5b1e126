name: asiainfo_dsm
display_name: "亚信安全云主机深度安全防护系统"
description: "虚拟化已让数据中心发生根本性变革，当前企业正将部分或全部工作负载转移至私有云和公有云。要想充分利用混合云计算的优势，就必须确保构建可保护所有服务器的安全系统，包括物理服务器、虚拟服务器或云服务器。而且，这种安全系统不应妨碍主机性能和虚拟机 (VM) 密度，也不应影响虚拟化和云计算的投资回报率 (ROI)。亚信安全™服务器深度安全防护系统™是一款可提供全面安全防护的解决方案，专为虚拟环境和云环境而打造，因此不存在安全漏洞或性能影响。"
type: "主机防护"
company: "亚信"
logo: "asiainfo_dsm.png"
version: "v0.1"
priority: 1
properties:
  - Agent
  - 主机防护
  - 威胁检测
  - 防病毒

connection:
  - name: username
    type: string
    required: true
    display_name: "用户名"
    description: "用户名称"
    validate_rules:
      - name: length
        error_hint: "用户名长度必须大于等于2且小于等于100"
        setting:
          min: 2
          max: 100

  - name: password
    type: password
    required: true
    display_name: "密码"
    description: "密码信息"
    validate_rules:
      - name: length
        error_hint: "密码长度必须大于等于6且小于等于100"
        setting:
          min: 6
          max: 100

  - name: address
    type: url
    required: true
    display_name: "地址"
    description: "地址信息"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'


fetch_setting:
  type: disposable
  point: "asiainfo_dsm.fetch:find_asset"
  condition_point: "asiainfo_dsm.fetch:build_query_condition"
  is_need_test_service: true
  test_auth_point: "asiainfo_dsm.fetch:get_auth_connection"
  test_connection_point: "asiainfo_dsm.fetch:check_connection"
  size: 1000
  fetch_type_mapper:
    asset:
      - host

fabric_setting:
  choose_point_mapper:
    asset: "asiainfo_dsm.fabric:choose_new_record"

merge_setting:
  size: 1000
  setting:
    asset:
      host:
        fields:
          - id

convert_setting:
  size: 1000
  before_executor_mapper: { }
  executor_mapper: { }