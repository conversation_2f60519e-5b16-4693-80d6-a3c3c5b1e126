from caasm_adapter.util.client import FetchJsonResultClient


class AsiaInfoDsmBaseApiClient(FetchJsonResultClient):
    def __init__(self, connection, session=None, sid=None):
        super(AsiaInfoDsmBaseApiClient, self).__init__(connection, session)
        self._connection = connection
        self._sid = sid

    def build_request_header(self, *args, **kwargs):
        header = {}
        if not self._sid:
            return header
        header["<PERSON>ie"] = f"sID={self._sid}"
        header["Accept"] = "application/json"
        return header

    def check_biz_result(self, result):
        return True

    @property
    def flag_key_name(self):
        return ""

    @property
    def suc_flag(self):
        return ""

    @property
    def data_key_name(self):
        return ""

    @property
    def username(self):
        return self._connection.get("username")

    @property
    def password(self):
        return self._connection.get("password")
