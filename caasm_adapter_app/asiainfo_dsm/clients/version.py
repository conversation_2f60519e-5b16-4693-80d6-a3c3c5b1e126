import logging
import re
import traceback
from functools import cmp_to_key

from asiainfo_dsm.clients.base import AsiaInfoDsmBaseApiClient
from caasm_tool.util import compare_version

log = logging.getLogger()


class VirusDatabaseVersionApiClient(AsiaInfoDsmBaseApiClient):
    _re = r"<td.*>病毒码<.*</td>"
    _version_re = r">(\d+(.\d+)*)<"
    _split_flag = '<tr id="mainTable_row'
    _flag = ">病毒码<"

    def handle(self, *args, **kwargs):
        self._session.post(self.login_url, data=self.login_data, verify=False)
        content = self._session.get(self.version_url).content.decode("utf-8")
        return self.parse_content(content)

    @classmethod
    def parse_content(cls, content):
        versions = []
        try:
            td_info = re.compile(cls._re)

            data = td_info.findall(content)
            for detail in data:
                info = detail.split(cls._split_flag)
                for single in info:
                    if cls._flag not in single:
                        continue
                    try:
                        version = re.search(cls._version_re, single).group(1)
                        versions.append(version)
                    except Exception as e:
                        log.warning(f"search version error({e})")
        except Exception as e:
            log.warning(f"Parse version content error({e}), detail is {traceback.format_exc()}")
        else:
            if not versions:
                return None
            return sorted(versions, key=cmp_to_key(compare_version), reverse=True)[0]

    @property
    def login_data(self):
        return {
            "requestedLocale": "",
            "goToHash": "",
            "filters": "",
            "area": "",
            "username": self.username,
            "password": self.password,
            "mfaCode": "",
            "signinButton": "登陆",
        }

    @property
    def login_url(self):
        return self.build_url(self.address, "/SignIn.screen")

    @property
    def version_url(self):
        return self.build_url(self.address, "/PatternUpdates.screen?setHash=patternUpdates")
