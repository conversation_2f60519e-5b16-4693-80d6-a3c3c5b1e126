import logging

from asiainfo_dsm.clients.base import AsiaInfoDsmBaseApiClient
from caasm_adapter.util.exception import AdapterFetchAuthFailedException

log = logging.getLogger()


class AsiaInfoAuthApiClient(AsiaInfoDsmBaseApiClient):
    URL = "/rest/authentication/login"
    METHOD = "post"

    def build_request_json(self, *args, **kwargs):
        return {"dsCredentials": {"userName": self.username, "password": self.password}}

    def parse_response(self, response, *args, **kwargs):
        if response.status_code != 200:
            return self.parse_error_handle(None, *args, **kwargs)
        return response.content.decode()

    def parse_error_handle(self, result, *args, **kwargs):
        raise AdapterFetchAuthFailedException

    def error_handle(self, err, *args, **kwargs):
        raise AdapterFetchAuthFailedException

    def clean_result(self, result):
        if not result:
            raise AdapterFetchAuthFailedException
        return result
