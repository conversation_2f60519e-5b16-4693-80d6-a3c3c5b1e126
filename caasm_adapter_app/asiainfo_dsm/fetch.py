from asiainfo_dsm.manage import AsiaInfoDsmManager
from caasm_adapter.sdk.adapter_connection import connection_sdk
from caasm_adapter.sdk.adapter_fetch import fetch_sdk


def find_asset(connection, fetch_type, page_index=0, page_size=1, session=None, condition=None, **kwargs):
    sid = condition.get("sid")
    result = _build_manager(connection, session, sid).find_asset(page_index, page_size, condition)
    result = fetch_sdk.build_asset(result, fetch_type)
    return fetch_sdk.return_success(result)


def build_query_condition(connection, session=None, fetch_type=None):
    sid = _build_manager(connection, session).auth()
    return {"sid": sid}


def get_auth_connection(connection, session=None):
    _build_manager(connection, session).auth()


def _build_manager(connection, session=None, sid=None):
    return AsiaInfoDsmManager(connection, session, sid)


def check_connection(connection, session=None):
    address = connection.get("address")
    return connection_sdk("http", address=address)
