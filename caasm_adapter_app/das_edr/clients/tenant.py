from caasm_adapter.util.exception import AdapterFetchAuthFailedException
from das_edr.clients.base import DasEdrListClient


class DasTenantCountClient(DasEdrListClient):
    URL = "/user/list_user"
    METHOD = "GET"

    def build_request_params(self, offset, limit, order, sort):
        return {"offset": offset, "limit": limit, "order": order, "sort": sort}

    @property
    def data_key_name(self):
        return "data.total"


class DasEdrTenantListClient(DasEdrListClient):
    URL = "/user/list_user"
    METHOD = "GET"

    def build_request_params(self, offset, limit, order, sort):
        return {"offset": offset, "limit": limit, "order": order, "sort": sort}

    @property
    def data_key_name(self):
        return "data.list"


class DasEdrTenantLoginClient(DasEdrListClient):
    URL = "/user/login"

    def build_request_params(self, userName):
        return {"userName": userName}

    @property
    def data_key_name(self):
        return "data.token"

    def parse_error_handle(self, result, *args, **kwargs):
        raise AdapterFetchAuthFailedException

    def error_handle(self, err, *args, **kwargs):
        raise AdapterFetchAuthFailedException
