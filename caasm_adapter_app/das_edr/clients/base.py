import time
import logging
from caasm_adapter.util.client import FetchJsonResultClient
from caasm_tool.constants import GMT_FORMAT

log = logging.getLogger()


class DasBaseClient(FetchJsonResultClient):
    def __init__(self, connection, session=None, token=None):
        super(DasBaseClient, self).__init__(connection, session)
        self.timestamp = str(int(time.time() * 1000))
        self.token = token

    def build_request_url(self, *args, **kwargs):
        result = super(DasBaseClient, self).build_request_url(*args, **kwargs)
        return result + f"?_t={self.timestamp}"

    @property
    def flag_key_name(self):
        return "error_code"

    @property
    def suc_flag(self):
        return 200

    @property
    def data_key_name(self):
        return ""

    def build_request_header(self, *args, **kwargs):
        bearer_token = "Bearer"
        if self.token:
            bearer_token += " " + self.token

        return {"Authorization": bearer_token}


class DasEdrListClient(DasBaseClient):
    METHOD = "get"

    def build_request_params(self, *args, **kwargs):
        return kwargs

    @property
    def data_key_name(self):
        return "data"
