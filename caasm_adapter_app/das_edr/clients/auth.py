import base64
import hashlib
from Crypto.Cipher import AES
import ddddocr

from caasm_adapter.util.exception import AdapterFetchAuthFailedException, AdapterFetchApiResponseException
from das_edr.clients.base import DasBaseClient


class DasCodeClient(DasBaseClient):
    URL = "/captcha"
    METHOD = "GET"
    _ocr = ddddocr.DdddOcr(beta=True, show_ad=False)

    @property
    def data_key_name(self):
        raise "data.base64"

    def parse_error_handle(self, result, *args, **kwargs):
        raise AdapterFetchAuthFailedException

    def error_handle(self, err, *args, **kwargs):
        raise AdapterFetchAuthFailedException

    def clean_result(self, result):
        if not result:
            raise AdapterFetchAuthFailedException
        return {"captcha": self._ocr.classification(base64.b64decode(result))}


class DasEdrLoginClient(DasBaseClient):
    URL = "/login"
    METHOD = "POST"

    def build_request_header(self, captcha):
        headers = super(DasEdrLoginClient, self).build_request_header(captcha)

        headers.update(
            {
                "Times": self.timestamp,
                "Encrypt": "true",
                "User-Type": "admin",
                "Hash": "undefined",
                "Content-Type": "application/x-www-form-urlencoded",
                "X-Requested-With": "XMLHttpRequest",
            }
        )
        return headers

    def _encrypt_password(self):
        password = self.password
        enc_key = hashlib.md5(self.timestamp.encode()).hexdigest()
        return Encryptor(enc_key.encode()).encrypt(password)

    def build_request_data(self, captcha):
        return {"username": self.username, "password": self._encrypt_password(), "captcha": captcha}

    @property
    def password(self):
        return self.connection.get("password", "")

    @property
    def username(self):
        return self.connection.get("username", "")

    @property
    def data_key_name(self):
        raise "token"

    def parse_error_handle(self, result, *args, **kwargs):
        raise AdapterFetchAuthFailedException

    def error_handle(self, err, *args, **kwargs):
        raise AdapterFetchAuthFailedException


class DasEdrLogoutClient(DasBaseClient):
    URL = "/logout"
    METHOD = "delete"


class Encryptor(object):
    def __init__(self, key):
        self.key = key
        self.length = AES.block_size
        self.aes = AES.new(self.key, AES.MODE_ECB)

    def pad(self, text):
        count = len(text.encode("utf-8"))
        add = self.length - (count % self.length)
        enc_text = text + (chr(add) * add)
        return enc_text

    def encrypt(self, data):  # 加密函数
        res = self.aes.encrypt(self.pad(data).encode("utf8"))
        return str(base64.b64encode(res), encoding="utf8")
