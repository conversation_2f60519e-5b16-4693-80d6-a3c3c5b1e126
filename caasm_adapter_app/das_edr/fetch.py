from caasm_adapter.sdk.adapter_fetch import fetch_sdk
from das_edr.manage import DasEdrManager


def find_asset(connection, fetch_type, page_index=0, page_size=1, session=None, **kwargs):
    result = DasEdrManager(connection, session).find_asset(page_index, page_size)
    fetch_sdk.build_asset(result, fetch_type)
    return fetch_sdk.return_success(result)


def get_asset_count(connection, session=None, **kwargs):
    return DasEdrManager(connection, session).get_asset_count()


def get_auth_connection(connection, session=None):
    DasEdrManager(connection, session).auth()
