name: das_edr
display_name: "明御主机安全及管理系统"
description: "明御®主机安全及管理系统是一款集成了丰富的系统防护与加固、网络防护与加固等功能的主机安全产品。EDR通过自主研发的文件诱饵引擎，有着业界领先的勒索专防专杀能力；能通过内核级东西向流量隔离技术，实现网络隔离与防护；并拥有补丁修复、外设管控、文件审计、违规外联检测与阻断等主机安全能力。目前产品广泛应用在服务器、桌面PC、虚拟机、工控系统、国产操作系统、容器安全等各个场景。"
type: "主机防护"
logo: "das_edr.png"
company: "杭州安恒信息技术股份有限公司"
version: "v0.1"
priority: 10
properties:
  - "主机防护"

connection:
  - name: username
    type: string
    required: true
    display_name: "用户名称"
    description: "管理系统中的用户名，此处填写为平台管理员账号，非租户账号"
    validate_rules:
      - name: length
        error_hint: "用户密码格式无效。长度最小不得小于2，最大不得大于100"
        setting:
          min: 2
          max: 100

  - name: password
    type: password
    required: true
    display_name: "用户密码"
    description: "管理系统中平台账号对应的用户密码"
    validate_rules:
      - name: length
        error_hint: "用户密码格式无效。长度最小不得小于6，最大不得大于200"
        setting:
          min: 6
          max: 200

  - name: address
    type: url
    required: true
    display_name: "请求地址"
    description: "管理系统平台管理员请求地址"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'


fetch_setting:
  point: "das_edr.fetch:find_asset"
  size: 1
  mode: "compute_page"
  is_need_test_service: true
  count_point: "das_edr.fetch:get_asset_count"
  test_auth_point: "das_edr.fetch:get_auth_connection"
  fetch_type_mapper:
    asset:
      - asset

fabric_setting:
  choose_point_mapper:
    asset: "das_edr.fabric:choose_new_record"

merge_setting:
  size: 50
  setting:
    asset:
      asset:
        fields:
          - id


convert_setting:
  size: 50
  before_executor_mapper:
    asset:
      - add:
        - field: asset_type
          value: "主机"

      - rename:
        - src_field: last_time
          dst_field: lastSeen

        - src_field: process_detail
          dst_field: processes

        - src_field: disk_detail
          dst_field: hardware.disks

        - src_field: account_detail
          dst_field: accounts

        - src_field: software_detail
          dst_field: softwares


  executor_mapper:
    asset:
      主机:
        - add:
            - field: host_name
              value: ${name}

        - rename:

            - src_field: os_str
              dst_field: os.full

            - src_field: machine_detail.env_info.kernel_ver
              dst_field: os.kernel_version

            - src_field: machine_detail.env_info.memory
              dst_field: hardware.mem.capacity

            - src_field: machine_detail.env_info.processor
              dst_field: hardware.cpu.device_model

            - src_field: machine_detail.env_info.mainboard
              dst_field: hardware.board.product_name

        - replace:
            - field: hardware.mem.capacity
              src_value: "GB"
              dst_value: "G"

            - field: hardware.mem.capacity
              src_value: "KB"
              dst_value: "K"

            - field: hardware.mem.capacity
              src_value: "PB"
              dst_value: "P"

            - field: hardware.mem.capacity
              src_value: "MB"
              dst_value: "M"

            - field: hardware.mem.capacity
              src_value: "TB"
              dst_value: "T"


        - for_add:
          - size: 1
            field: ips
            setting:
              - field: addr
                value: ${ip}
              - field: mac
                value: ${mac}



        - for:
          - field: processes
            handler:
              - method: rename
                setting:
                  - src_field: user
                    dst_field: uname

                  - src_field: command_line
                    dst_field: cmd

              - method: add
                setting:
                  - field: start_args
                    value: ${cmd}

              - method: replace
                setting:
                  - field: start_time
                    src_value: "/"
                    dst_value: "-"


          - field: hardware.disks
            handler:
              - method: rename
                setting:
                  - src_field: total
                    dst_field: capacity

              - method: replace
                setting:
                  - field: capacity
                    src_value: "GB"
                    dst_value: "G"

                  - field: capacity
                    src_value: "MB"
                    dst_value: "M"

                  - field: capacity
                    src_value: "TB"
                    dst_value: "T"

                  - field: capacity
                    src_value: "KB"
                    dst_value: "K"

                  - field: capacity
                    src_value: "PB"
                    dst_value: "P"


          - field: hardware.graphics_cards
            handler:
              - method: rename
                setting:
                  - src_field: env_info.videocard
                    dst_field: product_name

          - field: accounts
            handler:
              - method: drop
                setting:
                  - field: gid
                  - field: sudo

              - method: add
                setting:
                  - field: full_name
                    value: ${user}

              - method: rename
                setting:
                   - src_field: user
                     dst_field: name

                   - src_field: is_root
                     dst_field: root

                   - src_field: last_login_time
                     dst_field: last_login_time

                   - src_field: group
                     dst_field: groups

                   - src_field: password_expires
                     dst_field: password_expire_time

              - method: convert
                setting:
                  - field: password_expire_time
                    type: datetime
                    setting:
                      datetime_format: "%Y-%m-%d %H:%M:%S"

          - field: softwares
            handler:
              - method: rename
                setting:
                  - src_field: install_path
                    dst_field: path

                  - src_field: publisher
                    dst_field: vendor

        - script:
            - content: |
                def get_capacity_size(capacity):
                    result = 0
                    if capacity.endswith("B"):
                        temp = capacity.replace("B","")
                        result = float(temp)
                    if capacity.endswith("K"):
                        temp = capacity.replace("K","")
                        result = float(temp) * 1024
                    elif capacity.endswith("M"):
                        temp = capacity.replace("M","")
                        result = float(temp) * 1024 * 1024
                    elif capacity.endswith("G"):
                        temp = capacity.replace("G","")
                        result = float(temp) * 1024 * 1024 * 1024
                    elif capacity.endswith("T"):
                        temp = capacity.replace("T","")
                        result = float(temp) * 1024 * 1024 * 1024 * 1024
                    elif capacity.endswith("P"):
                        temp = capacity.replace("P","")
                        result = float(temp) * 1024 * 1024 * 1024 * 1024 * 1024
                    return result
                data = event.get("hardware")
                if not data:
                    return
                disk_data = data.get("disks",[])
                for item in disk_data:
                    capacity = item.get("capacity","")
                    item["capacity"] = get_capacity_size(capacity)
                mem_data = data.get("mem",{})
                mem_capacity = mem_data.get("capacity","")
                data["mem"]["capacity"] = get_capacity_size(mem_capacity)
                















