import time
from enum import Enum
import logging

from das_edr.clients.asset import DasEdrAssetClient, DasEdrAssetCountClient
from das_edr.clients.asset_detail import (
    DasEdrMachineDetailClient,
    DasEdrCpuDetailClient,
    DasEdrMemDetailClient,
    DasEdrNetDetailClient,
    DasEdrDiskDetailClient,
    DasEdrPortDetailClient,
    DasEdrProcessDetailClient,
    DasEdrAccountDetailClient,
    DasEdrSoftwareDetailClient,
    DasEdrStartUpDetailClient,
    DasEdrAssetWinLoopholeClient,
    DasEdrAssetLinLoopholeClient,
)
from das_edr.clients.auth import DasCodeClient, DasEdrLoginClient, DasEdrLogoutClient
from das_edr.clients.loophole import DasEdrWinLoopholeClient, DasEdrLinLoopholeClient
from das_edr.clients.tenant import DasEdrTenantListClient, DasTenantCountClient, DasEdrTenantLoginClient

log = logging.getLogger()


class ClientName(object):
    CODE = "code"
    LOGIN = "login"
    LOGOUT = "Logout"
    TENANT_COUNT = "tenant_count"
    TENANT_LIST = "tenant_list"
    TENANT_LOGIN = "tenant_login"
    ASSET = "asset"
    ASSET_COUNT = "asset_count"
    MACHINE_DETAIL = "machine_detail"
    CPU_DETAIL = "cpu_detail"
    MEM_DETAIL = "mem_detail"
    NET_DETAIL = "net_detail"
    DISK_DETAIL = "disk_detail"
    PORT_DETAIL = "port_detail"
    PROCESS_DETAIL = "process_detail"
    ACCOUNT_DETAIL = "account_detail"
    SOFTWARE_DETAIL = "software_detail"
    STARTUP_DETAIL = "startup_detail"
    ASSET_WIN_LOOPHOLE = "asset_win_loophole"
    ASSET_LIN_LOOPHOLE = "asset_lin_loophole"
    ASSET_OTHER_LOOPHOLE = "asset_other_loophole"
    WIN_LOOPHOLE = "win_loophole"
    LIN_LOOPHOLE = "lin_loophole"
    OTHER_LOOPHOLE = "other_loophole"


client_mapper = {
    ClientName.CODE: DasCodeClient,
    ClientName.LOGIN: DasEdrLoginClient,
    ClientName.LOGOUT: DasEdrLogoutClient,
    ClientName.TENANT_COUNT: DasTenantCountClient,
    ClientName.TENANT_LIST: DasEdrTenantListClient,
    ClientName.TENANT_LOGIN: DasEdrTenantLoginClient,
    ClientName.ASSET: DasEdrAssetClient,
    ClientName.ASSET_COUNT: DasEdrAssetCountClient,
    ClientName.MACHINE_DETAIL: DasEdrMachineDetailClient,
    ClientName.CPU_DETAIL: DasEdrCpuDetailClient,
    ClientName.MEM_DETAIL: DasEdrMemDetailClient,
    ClientName.NET_DETAIL: DasEdrNetDetailClient,
    ClientName.DISK_DETAIL: DasEdrDiskDetailClient,
    ClientName.PORT_DETAIL: DasEdrPortDetailClient,
    ClientName.PROCESS_DETAIL: DasEdrProcessDetailClient,
    ClientName.ACCOUNT_DETAIL: DasEdrAccountDetailClient,
    ClientName.SOFTWARE_DETAIL: DasEdrSoftwareDetailClient,
    ClientName.STARTUP_DETAIL: DasEdrStartUpDetailClient,
    ClientName.ASSET_WIN_LOOPHOLE: DasEdrAssetWinLoopholeClient,
    ClientName.ASSET_LIN_LOOPHOLE: DasEdrAssetLinLoopholeClient,
    ClientName.WIN_LOOPHOLE: DasEdrWinLoopholeClient,
    ClientName.LIN_LOOPHOLE: DasEdrLinLoopholeClient,
}


class OS(Enum):
    windows = 0
    linux = 1


class LoopholeLevel(Enum):
    Critical = "critical"
    High = "high"
    Medium = "medium"
    Low = "low"


class DasEdrManager(object):
    ASSET_DETAIL = [
        ClientName.MACHINE_DETAIL,
        ClientName.CPU_DETAIL,
        ClientName.MEM_DETAIL,
        ClientName.NET_DETAIL,
        ClientName.DISK_DETAIL,
        ClientName.PORT_DETAIL,
        ClientName.PROCESS_DETAIL,
        ClientName.ACCOUNT_DETAIL,
        ClientName.SOFTWARE_DETAIL,
        ClientName.STARTUP_DETAIL,
    ]

    ASSET_TYPE_DETAIL = [ClientName.CPU_DETAIL, ClientName.MEM_DETAIL, ClientName.NET_DETAIL]

    ASSET_KEY_DETAIL = [
        ClientName.PORT_DETAIL,
        ClientName.PROCESS_DETAIL,
        ClientName.ACCOUNT_DETAIL,
        ClientName.SOFTWARE_DETAIL,
        ClientName.STARTUP_DETAIL,
    ]

    def __init__(self, connection, session=None):
        self._connection = connection
        self._session = session
        self._client_cache = {}
        self._token = None
        self._instance_mapper = {}
        self._asset_complex_props = []
        self.admin_login_status = False

    def find_asset(self, offset, limit):
        self.auth()
        ## 首先获取所有租户
        tenant_name_list = self.get_all_tenant(offset, limit)
        ## 安恒edr 每次跳转都需要重新登陆admin
        asset_result = []
        for tenant_name in tenant_name_list:
            log.debug(f"******************BEGIN TO FETCH ASSET {tenant_name} INFO *******************")
            self.auth()
            try:
                self._token = self._call(ClientName.TENANT_LOGIN, userName=tenant_name)
                asset_result.extend(self.get_talent_asset(offset=0, limit=20))
            except Exception as e:
                log.warning(f"get {tenant_name} asset error")
            self.logout_auth()

        return asset_result

    def auth(self):
        if not self.admin_login_status:
            captcha_result = self._call(ClientName.CODE)
            self._token = self._call(ClientName.LOGIN, **captcha_result)
            self.admin_login_status = True

    def logout_auth(self):
        self._call(ClientName.LOGOUT)
        self.admin_login_status = False

    def _call(self, client_type, *args, **kwargs):
        instance = client_mapper[client_type](self._connection, self._session, self._token)
        self._instance_mapper[client_type] = instance
        return self._instance_mapper[client_type].handle(*args, **kwargs)

    def get_talent_asset(self, offset: int, limit: int):
        all_asset = []
        asset_count = self._call(ClientName.ASSET_COUNT, offset=offset, limit=limit)
        _count = 0
        temp_offset = offset
        while _count < asset_count:
            asset_info = self._call(ClientName.ASSET, offset=temp_offset, limit=limit)
            if not asset_info:
                break
            offset += 1
            temp_offset = offset * limit
            for asset in asset_info:
                _count += 1
                asset_id = asset.get("id")
                os_type = asset.get("os_type", None)
                _time = str(int(time.time() * 1000))
                status = asset.get("status", 0)
                if not asset_id or not status:
                    continue
                try:
                    asset[ClientName.MACHINE_DETAIL] = self._call(ClientName.MACHINE_DETAIL, node_id=asset_id) or []
                    asset[ClientName.DISK_DETAIL] = (
                        self._call(ClientName.DISK_DETAIL, node_id=asset_id, _time=_time) or []
                    )
                except Exception as e:
                    log.error(f"get {ClientName.MACHINE_DETAIL} asset info error, error info : {e}")

                for _asset_key in self.ASSET_KEY_DETAIL:
                    try:
                        asset[_asset_key] = self._call(_asset_key, node_id=asset_id, key="") or []
                    except Exception as e:
                        log.error(f"get {_asset_key} asset info error, error info : {e}")

                if os_type is None:
                    continue

                # if os_type == OS.linux.value:
                #     loophole = []
                #     for loophole_level in LoopholeLevel:
                #         try:
                #             loophole.append(
                #                 self._call(ClientName.ASSET_LIN_LOOPHOLE, type="linux", vulType=loophole_level.value)
                #             )
                #         except Exception as e:
                #             log.error(f"get vul asset info error {e}")
                #         try:
                #             loophole.append(
                #                 self._call(ClientName.ASSET_LIN_LOOPHOLE, type="other", vulType=loophole_level.value)
                #             )
                #         except Exception as e:
                #             log.error(f"get vul asset info error {e}")
                #     asset[ClientName.ASSET_LIN_LOOPHOLE] = loophole
                #
                # if os_type == OS.windows.value:
                #     try:
                #         win_loophole = self._call(ClientName.ASSET_WIN_LOOPHOLE, node_id=asset_id) or []
                #         asset[ClientName.ASSET_WIN_LOOPHOLE] = win_loophole.get("hightList",[])
                #     except Exception as e:
                #         log.error(f"get vul asset info error {e}")
            all_asset.extend(asset_info)
        return all_asset

    def get_all_tenant(self, offset, limit):
        tenant_name_list = []
        tenant_list = self._call(ClientName.TENANT_LIST, offset=offset, limit=limit, order="", sort="")
        for tenant in tenant_list:
            if tenant.get("role") == 2 and tenant.get("roleName") == "租户管理员":
                tenant_name_list.append(tenant.get("userName"))
        return tenant_name_list

    def get_asset_count(self):
        self.auth()
        return self._call(ClientName.TENANT_COUNT, offset=1, limit=1, order="", sort="")
