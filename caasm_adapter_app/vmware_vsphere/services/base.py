from pyVmomi import vim, vmodl


class DataHandlerBaseService(object):
    @classmethod
    def serialize(cls, obj):
        """
        将 pyVmomi 对象转换为可序列化的字典。
        """
        if hasattr(obj, "DynamicType") and obj.DynamicType is not None:
            # 对具有 DynamicType 的对象进行特殊处理
            return str(obj)
        elif isinstance(obj, (list, tuple)):
            # 列表或元组
            return [cls.serialize(item) for item in obj]
        elif isinstance(obj, dict):
            # 字典
            return {k: cls.serialize(v) for k, v in obj.items()}
        elif isinstance(obj, vmodl.DynamicData):
            # vSphere 动态数据类型
            return {k: cls.serialize(v) for k, v in obj.__dict__.items()}
        else:
            # 基本类型
            return obj

    @classmethod
    def serialize_vmomi_object(cls, obj):
        raise NotImplementedError
