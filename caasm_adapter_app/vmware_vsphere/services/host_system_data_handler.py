import json
import logging
from pyVmomi import vim

from vmware_vsphere.services.base import DataHandlerBaseService

log = logging.getLogger()


class HostSystemDataHandler(DataHandlerBaseService):
    @classmethod
    def serialize_vmomi_object(cls, host):
        summary = host.summary
        hardware = host.hardware
        network = host.config.network
        storage = host.config.storageDevice

        details = {
            "name": summary.config.name,
            "model": hardware.systemInfo.model,
            "os_type": host.config.product.osType,
            "os_full_name": host.config.product.fullName,
            "cpu_model": hardware.cpuPkg[0].description,
            "num_cpu_cores": hardware.cpuInfo.numCpuCores,
            "num_nics": len(network.pnic),
            "num_vms": len(host.vm),
            "status": summary.overallStatus,
            "ip": host.summary.managementServerIp if host.summary.managementServerIp else None,
            "memory_size": host.hardware.memorySize / 1024**3,
            "disk": cls.get_disk_info(host),
            "mac_info": cls.get_mac_info(host),
        }
        return details

    @classmethod
    def get_disk_info(cls, host):
        disk_info = []
        try:
            storage_system = host.configManager.storageSystem
            if storage_system and storage_system.storageDeviceInfo:
                for disk in storage_system.storageDeviceInfo.scsiLun:
                    if isinstance(disk, vim.host.ScsiDisk):
                        disk_capacity = disk.capacity.block * disk.capacity.blockSize / 1024**3  # 磁盘容量 (GB)
                        disk_info.append({"deviceName": disk.deviceName, "disk_capacity": disk_capacity})
        except AttributeError as err:
            log.error(f"无法获取磁盘信息:{err}")
        except Exception as e:
            log.error(f"未知错误: {e}")
        return disk_info

    @classmethod
    def get_mac_info(cls, host):
        mac_info = []
        try:
            network_system = host.configManager.networkSystem
            if network_system and network_system.networkInfo and network_system.networkInfo.pnic:
                for pnic in network_system.networkInfo.pnic:
                    mac_info.append({"device": pnic.device, "mac": pnic.mac})
        except Exception as e:
            log.error(f"get_mac_info: get mac info err!name is :{host.name},err is :{e}")

        return mac_info
