import json
import logging
from vmware_vsphere.services.base import DataHandlerBaseService
from pyVmomi import vim

log = logging.getLogger()


class VirtualMachineDataHandler(DataHandlerBaseService):
    @classmethod
    def serialize_vmomi_object(cls, obj):
        data = {
            "name": obj.summary.config.name,
            "os": obj.config.guestId,
            "full_name": obj.config.guestFullName,
            "vm_path_name": cls.serialize(obj.summary.config.vmPathName),
            "cpu_count": obj.summary.config.numCpu,
            "memory_size_MB": cls.serialize(obj.summary.config.memorySizeMB),
            # "total_disk" : sum(disk.capacityInKB for disk in obj.summary.config.vmStorageArray) / 1024/1024,
            "power_state": cls.serialize(obj.runtime.powerState),
            "disk_info": cls.get_disk_info(obj),
            "network_data": cls.get_network_info(obj),
        }

        return data

    @classmethod
    def get_disk_info(cls, host):
        disk_info = []
        try:
            if host.config:
                for device in host.config.hardware.device:
                    if isinstance(device, vim.vm.device.VirtualDisk):
                        disk_size = device.capacityInKB / 1024 / 1024
                        disk_info.append({"disk_label": device.deviceInfo.label, "disk_capacity": disk_size})
        except AttributeError as err:
            log.error(f"{err} 无法获取磁盘信息")
        except Exception as e:
            log.error(f"未知错误: {e}")
        return disk_info

    @classmethod
    def get_network_info(cls, host):
        network_info = []

        for net in host.guest.net:
            try:
                ip_addresses = net.ipConfig.ipAddress if net.ipConfig else []
                ip_list = [ip.ipAddress for ip in ip_addresses]
                mac = net.macAddress
                network_info.append(
                    {
                        "mac": mac,
                    }
                )
                if len(ip_list) >= 1:
                    network_info[-1].update({"ip": ip_list[0]})
            except Exception as e:
                log.error(f"get_network_info failed! host_name is {host.name},err info : {e}")

        return network_info
