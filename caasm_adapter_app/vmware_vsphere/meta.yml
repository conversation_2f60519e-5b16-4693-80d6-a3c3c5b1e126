name: "vmware_vsphere"
display_name: "VMWare vSphere"
description: "VMware vSphere 是业界领先且最可靠的虚拟化平台。 vSphere将应用程序和操作系统从底层硬件分离出来，从而简化了IT操作。 您现有的应用程序可以看到专有资源，而您的服务器则可以作为资源池进行管理。 因此，您的业务将在简化但恢复能力极强的IT 环境中运行。"
type: "虚拟化平台"
company: "VMWare"
logo: "vmware_vsphere.png"
version: "v0.1"
priority: 1
properties:
  - "虚拟化平台"

connection:
  - name: address
    type: url
    required: true
    display_name: "地址"
    description: "地址信息"
    validate_rules:
      - name: length
        error_hint: "IP地址，不带https或者http"
        setting:
          min: 2
          max: 100
  - name: port
    type: integer
    required: true
    default: 443
    display_name: "默认端口"
    description: "默认端口"
    validate_rules:
      - name: number
        error_hint: "区域元素长度不合法。长度最小不得小于1，最大不得大于100"
        setting:
          type: string
          min: 1
          max: 65536

  - name: username
    type: string
    required: true
    display_name: "用户名"
    description: "用户名称"
    validate_rules:
      - name: length
        error_hint: "用户名长度必须大于等于2且小于等于100"
        setting:
          min: 2
          max: 100

  - name: password
    type: password
    required: true
    display_name: "密码"
    description: "密码信息"
    validate_rules:
      - name: length
        error_hint: "密码长度必须大于等于6且小于等于100"
        setting:
          min: 6
          max: 100

fetch_setting:
  type: disposable
  point: "vmware_vsphere.fetch:find_asset"
  condition_point: "vmware_vsphere.fetch:build_query_condition"
  size: 1000
  fetch_type_mapper:
    asset:
      - virtual_machine
      - physical_machine