from caasm_adapter.sdk.adapter_fetch import fetch_sdk
from vmware_vsphere.manage import VmwareVsphereManager


def find_asset(connection, fetch_type, page_index=0, page_size=1, session=None, condition=None, **kwargs):
    result = VmwareVsphereManager(connection, session, condition).find_asset(
        fetch_type=fetch_type, page_index=page_index, page_size=page_size
    )
    result = fetch_sdk.build_asset(result, fetch_type)
    return fetch_sdk.return_success(result)


def build_query_condition(connection, session=None, fetch_type=None):
    return {"physical_machine": {"fetch_count": 1}, "virtual_machine": {"fetch_count": 1}}
