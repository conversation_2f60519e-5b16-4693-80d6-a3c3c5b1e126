import logging
import ssl
from requests.exceptions import ConnectTimeout, ReadTimeout
from pyVim.connect import SmartConnect
from caasm_adapter.util.client import FetchJsonResultClient
from pyVmomi import vim

log = logging.getLogger()


class VmWareVspereBaseClient(FetchJsonResultClient):
    def __init__(self, connection, session=None):
        super(VmWareVspereBaseClient, self).__init__(connection=connection, session=session)
        self.content = None

    def handle(self, *args, **kwargs):
        try:
            self.build_client()
            result = self.get_current_result(*args, **kwargs)
        except (ConnectTimeout, ReadTimeout):
            return self.timeout_handle(*args, **kwargs)
        except Exception as e:
            return self.error_handle(e, *args, **kwargs)
        else:
            return self.parse_biz_result(result, *args, **kwargs)

    def get_current_result(self, *args, **kwargs):
        response = self.get_folder_data(*args, **kwargs)
        return self.parse_response(response, *args, **kwargs)

    def get_folder_data(self, *args, **kwargs):
        raise NotImplementedError

    def build_client(self):
        raise NotImplementedError

    @property
    def password(self):
        return self.connection.get("password", "")

    @property
    def username(self):
        return self.connection.get("username", "")

    @property
    def address(self):
        return self.connection.get("address", "")

    @property
    def port(self):
        return self.connection.get("port", 443)


class VmWareVsphereClient(VmWareVspereBaseClient):
    def build_client(self):
        try:
            context = ssl._create_unverified_context()
            _client = SmartConnect(
                host=self.address, user=self.username, pwd=self.password, port=self.port, sslContext=context
            )
            self.content = _client.RetrieveContent()
        except Exception as e:
            log.error(f"build client error! Detail is {e}")

    def get_folder_data(self, *args, **kwargs):
        container = self.content.viewManager.CreateContainerView(self.content.rootFolder, [self.virtual_type], True)
        vm_list = container.view
        return vm_list

    def parse_response(self, response, *args, **kwargs):
        data = []
        try:
            for item in response:
                result = getattr(self.handle_class, self.convert_func)(item)
                data.append(result)
        except Exception as e:
            log.warning(f"parse response error({e}),code is {response.status_code},content is {response.content}")
            return self.error_handle(e, *args, **kwargs)
        else:
            return data

    def parse_biz_result(self, result, *args, **kwargs):
        return result

    @property
    def virtual_type(self):
        raise NotImplementedError

    @property
    def handle_class(self):
        raise NotImplementedError

    @property
    def convert_func(self):
        return "serialize_vmomi_object"
