from caasm_adapter.sdk.adapter_fetch import fetch_sdk
from microsoft_ad.manage import MicrosoftADManager


def find_asset(connection, fetch_type, **kwargs):
    page_size = kwargs.get("page_size")
    condition = kwargs.get("condition")

    records = _build_manager(connection, condition).find(fetch_type, page_size)
    result = fetch_sdk.build_asset(records, fetch_type)
    return fetch_sdk.return_success(result)


def auth(connection, *args, **kwargs):
    return _build_manager(connection).auth()


def _build_manager(connection, condition=None):
    return MicrosoftADManager(connection, condition)
