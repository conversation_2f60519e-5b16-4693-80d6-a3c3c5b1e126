import urllib.parse as urllib2

from ldap3 import Server, Connection, ALL

from caasm_adapter.util.client import FetchJsonResultClient


class MicrosoftADBaseClient(FetchJsonResultClient):
    @property
    def suc_flag(self):
        return

    @property
    def flag_key_name(self):
        return

    @property
    def data_key_name(self):
        return

    def __init__(self, connection, condition):
        super(MicrosoftADBaseClient, self).__init__(connection)
        address_object = urllib2.urlparse(self.address)
        server = Server(address_object.hostname, port=address_object.port, get_info=ALL)
        self._conn = Connection(server, user=self.username, password=self.password, raise_exceptions=True)
        self._conn.bind()
        self._condition = condition

    def handle_common(self, *args, **kwargs):
        raise NotImplementedError

    @property
    def conn(self):
        return self._conn

    @property
    def password(self):
        return self._connection.get("password")

    @property
    def username(self):
        return self._connection.get("username")

    @property
    def condition(self):
        return self._condition
