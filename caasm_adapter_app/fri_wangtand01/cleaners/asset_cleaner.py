from caasm_adapter.fetcher.cleaners.base import FetchLinkBaseCleaner


class AssetCleaner(FetchLinkBaseCleaner):
    @property
    def main_data_type(self):
        return "asset_vul_instance"

    @property
    def link_data_type(self):
        return "computer"

    @property
    def main_field(self):
        return "ip"

    @property
    def dst_field(self):
        return "vuls"

    @property
    def indexes(self):
        return ("ip",)
