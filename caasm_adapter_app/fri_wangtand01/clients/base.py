from caasm_adapter.util.client import FetchJsonResultClient


class FRIWangTanBaseClient(FetchJsonResultClient):
    METHOD = "POST"

    def __init__(self, connection, session=None, token=None):
        super(FRIWangTanBaseClient, self).__init__(connection=connection, session=session)
        self.token = token

    @property
    def suc_flag(self):
        return "ok"

    @property
    def flag_key_name(self):
        return "status"

    @property
    def data_key_name(self):
        return "list"

    @property
    def email(self):
        return self.connection.get("email")

    @property
    def password(self):
        return self.connection.get("password")
