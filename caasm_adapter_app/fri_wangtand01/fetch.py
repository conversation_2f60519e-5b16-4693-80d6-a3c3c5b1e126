from caasm_adapter.sdk.adapter_fetch import fetch_sdk
from fri_wangtand01.manage import FRIWangTanManager


def find_asset(connection, fetch_type, page_index=0, page_size=1, condition=None, **kwargs):
    session = kwargs.get("session")

    data = FRIWangTanManager(connection, session=session).find_asset(fetch_type, page_index, page_size)
    result = fetch_sdk.build_asset(data, fetch_type)
    return fetch_sdk.return_success(result)


def get_auth_connection(connection, session=None):
    FRIWangTanManager(connection, session=session).auth()
