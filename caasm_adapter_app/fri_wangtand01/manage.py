from caasm_adapter.util.exception import AdapterFetchApiNotSupportException
from fri_wangtand01.clients.auth import FRIWangTanD01AuthClient
from fri_wangtand01.clients.host import FRIWangTanD01HostClient
from fri_wangtand01.clients.threats import FRIWangTanD01HostThreatClient


class FetchType(object):
    AUTH = "auth"
    HOST = "computer"
    THREAT_INFO = "asset_vul_instance"


class FRIWangTanManager(object):

    FETCH_TYPE_MAPPER = {
        FetchType.HOST: FRIWangTanD01HostClient,
        FetchType.AUTH: FRIWangTanD01AuthClient,
        FetchType.THREAT_INFO: FRIWangTanD01HostThreatClient,
    }

    def __init__(self, connection=None, session=None):
        self._connection = connection
        self._session = session
        self.token = None

    def find_asset(self, fetch_type=None, page_index=None, page_size=None):
        """
        查询不同类型的资产
        """
        self.auth() if not self.token else ...

        result = self._call(fetch_type, page_index=page_index, page_size=page_size)
        return result

    def auth(self):
        self.token = self._call(FetchType.AUTH)

    def _call(self, fetch_type, *args, **kwargs):
        if fetch_type not in self.FETCH_TYPE_MAPPER:
            raise AdapterFetchApiNotSupportException()
        clazz = self.FETCH_TYPE_MAPPER.get(fetch_type)
        return clazz(self._connection, self._session, self.token).handle(*args, **kwargs)
