name: fri_wangtand01
display_name: "网络资产测绘分析系统(网探D01)"
description: "公安部第一研究所通过收集互联网资产数据及指纹，实现网络空间资产检索、分析、监控，结合漏洞、厂商信息等威胁情报，开展漏洞统计分析工作，旨在为国家重点行业、部门提供全面的网络资产安全态势，使其更好地应对威胁关键信息基础设施的网络攻击事件。"
type: "内网资产测绘"
logo: "d01.png"
company: "公安部第一研究所"
version: "v0.1"
priority: 10
properties:
  - "内网测绘"

connection:

  - name: address
    type: url
    required: true
    display_name: "请求地址"
    description: "管理系统平台管理员请求地址"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'


  - name: email
    type: string
    required: true
    display_name: "邮箱"
    description: "用户的邮箱"
    validate_rules:
      - name: length
        error_hint: "用户密码格式无效。长度最小不得小于2，最大不得大于100"
        setting:
          min: 2
          max: 100

  - name: password
    type: password
    required: true
    display_name: "用户密码"
    description: "用户密码"
    validate_rules:
      - name: length
        error_hint: "用户密码格式无效。长度最小不得小于6，最大不得大于200"
        setting:
          min: 6
          max: 200

fetch_setting:
  point: "fri_wangtand01.fetch:find_asset"
  size: 100
  is_need_test_service: true
  test_auth_point: "fri_wangtand01.fetch:get_auth_connection"
  fetch_type_mapper:
    asset:
      - computer
      - asset_vul_instance
  cleaner_mapper:
    asset:
      computer:
        - "fri_wangtand01.cleaners.asset_cleaner:AssetCleaner"

merge_setting:
  size: 200
  setting: { }

convert_setting:
  size: 200
  before_executor_mapper: { }
  executor_mapper: { }