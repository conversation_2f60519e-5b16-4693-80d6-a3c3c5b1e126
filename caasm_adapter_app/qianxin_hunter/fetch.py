from caasm_adapter.sdk.adapter_fetch import fetch_sdk
from qianxin_hunter.manager import QAXHunterManager


def find_asset(connection, fetch_type, page_index=0, page_size=1, session=None, **kwargs):
    records = _build_manager(connection, session).find_asset(page_index + 1, page_size)
    result = fetch_sdk.build_asset(records, fetch_type)
    return fetch_sdk.return_success(result)


def auth(connection, session):
    _build_manager(connection, session).auth()


def _build_manager(connection, session=None):
    return QAXHunterManager(connection, session)
