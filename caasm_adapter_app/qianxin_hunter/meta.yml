name: "qianxin_hunter"
display_name: "奇安信hunter"
description: "互联网安全云监测系统是实战场景与产品设计的深度融合，便捷过滤污染数据，轻松搜集目标资产，快速锁定组件问题，完整掌握企业暴露面。"
type: "互联网测绘"
company: "奇安信"
logo: "qianxin_hunter.png"
version: "v0.1"
priority: 1
properties:
  - "互联网测绘"

connection:
  - name: address
    type: url
    required: true
    display_name: "地址"
    description: "地址信息"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'
  - name: api_key
    type: password
    required: true
    display_name: "密钥"
    description: "密码"
    validate_rules:
      - name: length
        error_hint: "密钥长度必须大于等于6且小于等于100"
        setting:
          min: 6
          max: 100
  - name: company
    type: string
    required: true
    display_name: "公司名称"
    description: "公司名称"
    validate_rules:
      - name: length
        error_hint: "公司名称必须大于等于2且小于等于100"
        setting:
          min: 2
          max: 100

fetch_setting:
  type: disposable
  point: "qianxin_hunter.fetch:find_asset"
  is_need_test_service: true
  test_auth_point: "qianxin_hunter.fetch:auth"
  size: 100
  fetch_type_mapper:
    asset:
      - port_server

fabric_setting:
  choose_point_mapper:
    asset: "qianxin_hunter.fabric:choose_new_record"

merge_setting:
  size: 200
  setting: { }

convert_setting:
  size: 200
  before_executor_mapper: { }
  executor_mapper: { }