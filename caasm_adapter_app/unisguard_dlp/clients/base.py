import base64
import hmac
import time
from caasm_adapter.util.client import FetchJsonResultClient


class UnisGuardDLPBaseClient(FetchJsonResultClient):
    def __init__(self, connection, session=None):
        super(UnisGuardDLPBaseClient, self).__init__(connection, session=session)
        self.timestamp = str(int(time.time() * 1000))

    @property
    def org_hash(self):
        return self.connection.get("org_hash")

    @property
    def app_key(self):
        return self.connection.get("app_key")

    @property
    def app_secret(self):
        return self.connection.get("app_secret")

    def make_sign(self, params: dict):
        sorted_params = dict(sorted(params.items(), key=lambda x: x[0]))
        row_str = "&".join(["{}={}".format(*i) for i in sorted_params.items()])
        h = hmac.new(self.app_secret.encode("UTF-8"), row_str.encode("UTF-8"), digestmod="SHA256")
        return base64.b64encode(h.digest()).decode()

    def build_request_header(self, *args, **kwargs):
        return {"X-ORGHASH": self.org_hash, "Content-Type": "application/json; charset=utf-8"}

    def build_params(self, *args, **kwargs):
        return {"appkey": self.app_key}

    @property
    def data_key_name(self):
        return "data.content"

    @property
    def suc_flag(self):
        return 0

    @property
    def flag_key_name(self):
        return "status"
