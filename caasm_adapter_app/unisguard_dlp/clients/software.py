import time

from unisguard_dlp.clients.base import UnisGuardDLPBaseClient


class SoftwareListClient(UnisGuardDLPBaseClient):

    URL = "/openapi/v1/software"
    METHOD = "GET"

    def build_params(self, *args, **kwargs):
        params = super(SoftwareListClient, self).build_params(*args, **kwargs)
        params["agentUuid"] = kwargs.get("agent_id")
        params["timestamp"] = self.timestamp
        return params

    def build_request_header(self, *args, **kwargs):
        header = super(SoftwareListClient, self).build_request_header(*args, **kwargs)
        header["X-OPENAPI-SIGN"] = self.make_sign(self.build_params(*args, **kwargs))
        return header

    def build_request_params(self, *args, **kwargs):
        return self.build_params(*args, **kwargs)
