import time

from unisguard_dlp.clients.base import UnisGuardDLPBaseClient


class AgentListClient(UnisGuardDLPBaseClient):
    URL = "/openapi/v1/nodeAgents"
    METHOD = "GET"

    def build_request_params(self, *args, **kwargs):
        return self.build_params(*args, **kwargs)

    def build_params(self, *args, **kwargs):
        params = super(AgentListClient, self).build_params(*args, **kwargs)
        params["page"] = kwargs.get("page_index")
        params["size"] = kwargs.get("page_size")
        params["timestamp"] = self.timestamp
        return params

    def build_request_header(self, *args, **kwargs):
        header = super(AgentListClient, self).build_request_header(*args, **kwargs)
        header["X-OPENAPI-SIGN"] = self.make_sign(self.build_params(*args, **kwargs))
        return header
