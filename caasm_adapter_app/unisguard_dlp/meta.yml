name: unisguard_dlp
display_name: "国舜数据防泄漏系统"
description: "国舜数据防泄漏系统专注于轻终端重后端，轻管控重监测数据泄漏预警解决方案，基于UEBA 技术对企业数据流转行为进行实时分析，在不影响企业办公效率的情况下实现终端数据泄露风险的实时监测与告警。"
type: "终端防护"
company: "北京国舜科技股份有限公司"
logo: "unisguard.png"
version: "v0.1"
priority: 10
properties:
  - 终端防护


connection:
  - name: address
    type: url
    required: true
    display_name: "请求地址"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'

  - name: org_hash
    type: string
    required: true
    display_name: "orgHash"
    validate_rules:
      - name: length
        error_hint: "密钥长度必须大于等于6且小于等于100"
        setting:
          min: 6
          max: 100

  - name: app_key
    type: string
    required: true
    display_name: "appkey"
    validate_rules:
      - name: length
        error_hint: "密钥长度必须大于等于6且小于等于100"
        setting:
          min: 6
          max: 100

  - name: app_secret
    type: string
    required: true
    display_name: "appSecret"
    validate_rules:
      - name: length
        error_hint: "密钥长度必须大于等于6且小于等于100"
        setting:
          min: 6
          max: 100


fetch_setting:
  point: "unisguard_dlp.fetch:find_asset"
  size: 20
  test_auth_point: "unisguard_dlp.fetch:get_auth_connection"
  is_need_test_service: true
  fetch_type_mapper:
    asset:
      - terminal

merge_setting: {}

convert_setting:
  size: 50
  before_executor_mapper: { }
  executor_mapper: { }

fabric_setting:
  choose_point_mapper:
    asset: "unisguard_dlp.fabric:choose_new_record"