import traceback

from caasm_adapter.sdk.adapter_fetch import fetch_sdk
from unisguard_dlp.manage import UnisGuardDLPManage
import logging

log = logging.getLogger()


def find_asset(connection, fetch_type, page_index, page_size, session=None, **kwargs):
    records = UnisGuardDLPManage(connection, session=session).find_asset(page_index, page_size)
    result = fetch_sdk.build_asset(records, fetch_type)
    return fetch_sdk.return_success(result)


def get_auth_connection(connection, session=None):
    UnisGuardDLPManage(connection, session).auth()
