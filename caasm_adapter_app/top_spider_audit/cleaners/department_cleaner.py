from caasm_adapter.fetcher.cleaners.base import FetchDepartmentCleaner


class TopSpiderAuditDepartmentCleaner(FetchDepartmentCleaner):
    def __init__(self, *args, **kwargs):
        super(TopSpiderAuditDepartmentCleaner, self).__init__(*args, **kwargs)
        self._tmp_storage = {}

    def clean(self):
        self._tmp_storage.clear()
        total_data = self.find_total_fetch_data()
        new_data = self._clean_core(total_data)
        self.delete_fetch_data(total_data)
        self.save_fetch_data(new_data)

    def _clean_core(self, data):
        result = []

        for child in data:
            child["fullTitle"] = child["title"]
            children = child["children"]
            self._parse_parent_title(child["fullTitle"], children)
            self._parse_children_title(child)
            self._parse_children_title_loop(children)

        for child in data:
            self._squish(child, result)
        return result

    @classmethod
    def _squish(cls, child, result):
        children = child.pop("children")
        result.append(child)

        if not children:
            return

        for tmp_child in children:
            cls._squish(tmp_child, result)

    @classmethod
    def _parse_parent_title(cls, parent_title, children):
        if not children:
            return
        for child in children:
            child["parentTitle"] = parent_title
            child["fullTitle"] = parent_title + child["title"]
            cls._parse_parent_title(child["fullTitle"], child["children"])

    @classmethod
    def _parse_children_title(cls, child):
        children_full_titles = []
        cls._find_children_title(child["children"], children_full_titles)
        child["childrenFullTitles"] = children_full_titles

    @classmethod
    def _parse_children_title_loop(cls, children):
        if not children:
            return []

        for child in children:
            cls._parse_children_title(child)
            cls._parse_children_title_loop(child["children"])

    @classmethod
    def _find_children_title(cls, children, result):
        if not children:
            return result

        for child in children:
            result.append(child["fullTitle"])
            cls._find_children_title(child["children"], result)
