import hashlib
import json
import logging
import time

from caasm_adapter.util.client import FetchJsonResultClient
from caasm_adapter.util.exception import AdapterFetchApiResponseException

log = logging.getLogger()


class TopAuditClient(FetchJsonResultClient):
    METHOD = "POST"

    def __init__(self, connection, session=None, token=None):
        super(TopAuditClient, self).__init__(connection, session)
        t = time.time()
        self._timestamp = str(int(t))
        self._token = token
        self._timestamp_mic = str(int(t * 1000)) + "888"
        self._nonce = "12345678"
        self._salt = "dO(QK*EX@cTG"

    def build_request_params(self, *args, **kwargs):
        result = {
            "stime": self._timestamp,
            "nonce": self._nonce,
            "sign": hashlib.md5((self._timestamp + self._nonce + self._salt).encode()).hexdigest(),
        }
        return result

    @property
    def salt(self):
        return self.connection.get("salt", self._salt)

    def build_request_header(self, *args, **kwargs):
        headers = {}
        if self._token:
            headers["Authorization"] = self._token
        return headers

    @property
    def data_key_name(self):
        return ""

    @property
    def suc_flag(self):
        return ""

    @property
    def flag_key_name(self):
        return ""


class TopAuditQueryPageClient(TopAuditClient):
    def build_request_json(self, page_index, page_size):
        return {"page": str(page_index), "pageSize": str(page_size)}

    def check_biz_result(self, result):
        return True

    def clean_result(self, result):
        try:
            result = json.loads(result).get("result") or []
        except Exception as e:
            log.warning(f"Clean error({e})")
            raise AdapterFetchApiResponseException
        else:
            return result

    @property
    def data_key_name(self):
        return "msg"
