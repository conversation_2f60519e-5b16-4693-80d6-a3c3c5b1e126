import base64
import json
import logging

from Crypto.Cipher import <PERSON><PERSON>
from pysmx.SM3 import SM3

from caasm_adapter.util.exception import AdapterFetchAuthFailedException
from top_spider_audit.clients.base import TopAuditClient

log = logging.getLogger()


class TopSpiderAuditAuthClient(TopAuditClient):
    URL = "/td/auth/token"

    def build_request_json(self):
        username = self.connection.get("username", "")
        password = self.connection.get("password", "")

        biz_data = json.dumps({"pwd": password, "factor": "false", "sm3": self._compute_sm3()})

        result = {
            "temp": self._encrypt(self._timestamp_mic, self.token_salt, self.token_salt),
            "factor": self._encrypt(biz_data, self._timestamp_mic, self._timestamp_mic),
            "username": username,
        }

        return result

    def check_biz_result(self, result):
        return bool(result.get("status"))

    @property
    def data_key_name(self):
        return "result.token"

    def clean_result(self, result):
        if not result:
            raise AdapterFetchAuthFailedException

        return result

    def parse_error_handle(self, result, *args, **kwargs):
        raise AdapterFetchAuthFailedException

    def error_handle(self, err, *args, **kwargs):
        raise AdapterFetchAuthFailedException

    def _compute_sm3(self):
        data = self.username + self.password + self._timestamp_mic
        return SM3(data.encode()).hexdigest()

    @property
    def username(self):
        return self.connection.get("username", "")

    @property
    def password(self):
        return self.connection.get("password", "")

    @property
    def token_salt(self):
        return self.connection.get("token_salt", "topsec0123456789")

    @classmethod
    def _encrypt(cls, text, key, iv):
        text = cls._pad(text).encode()
        cipher = AES.new(key=key.encode(), mode=AES.MODE_CBC, IV=iv.encode())
        encrypted_text = cipher.encrypt(text)
        return base64.b64encode(encrypted_text).decode("utf-8")

    @classmethod
    def _pad(cls, text):
        _size = AES.block_size
        return text + (_size - len(text.encode()) % _size) * chr(_size - len(text.encode()) % _size)


class TopSpiderAuditRefreshTokenClient(TopAuditClient):
    URL = "/td/updatetoken"
    _suc_flag = '"success"'

    def build_request_json(self):
        return {}

    def parse_response(self, response, *args, **kwargs):
        return response.content

    def check_biz_result(self, result):
        result = result.strip().decode()
        return result == self._suc_flag

    def error_handle(self, err, *args, **kwargs):
        raise AdapterFetchAuthFailedException

    def parse_error_handle(self, result, *args, **kwargs):
        raise AdapterFetchAuthFailedException
