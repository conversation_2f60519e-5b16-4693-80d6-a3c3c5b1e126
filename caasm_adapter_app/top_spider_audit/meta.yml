name: top_spider_audit
display_name: "天融信主机监控与审计系统"
description: "天融信主机监控与审计系统（TopDesk）是面向用户终端对象的安全防护产品，系统严格按照等保、分保、国军标关于终端安全管理的技术要求进行设计，服务器和客户端均支持国产软硬件平台。在具备补丁管理、准入控制、存储介质（U盘等）管理、非法外联管理等功能基础上，增加风险管理和主动防范机制，具备完善的终端安全风险监控和分析体系，实现有效防护和控制，降低风险，并指导持续改进和完善防护策略。既满足相关法律法规硬性要求，又满足管理者实际内网安全管理需求，是最佳的终端防护解决方案。"
type: "终端审计"
logo: "logo.png"
company: "天融信"
version: "v0.1"
priority: 10
properties:
  - 终端审计
  - Agent


connection:
  - name: username
    type: string
    required: true
    display_name: "用户名"
    description: "用户名"
    validate_rules:
      - name: length
        error_hint: "用户名格式无效。长度最小不得小于2，最大不得大于100"
        setting:
          min: 2
          max: 100

  - name: password
    type: password
    required: true
    display_name: "密码"
    description: "密码"
    validate_rules:
      - name: length
        error_hint: "密码格式无效。长度最小不得小于6，最大不得大于200"
        setting:
          min: 6
          max: 200

  - name: address
    type: url
    required: true
    display_name: "请求地址"
    description: "请求地址"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'


fetch_setting:
  point: "top_spider_audit.fetch:find_asset"
  is_need_test_service: true
  test_auth_point: "top_spider_audit.fetch:get_auth_connection"
  size: 10
  fetch_type_mapper:
    asset:
      - asset
    owner:
      - owner
    department:
      - department
  cleaner_mapper:
    department:
      default:
        - "top_spider_audit.cleaners.department_cleaner:TopSpiderAuditDepartmentCleaner"

merge_setting:
  size: 100
  setting:
    asset:
      asset:
        fields:
          - key
    owner:
      owner:
        fields:
          - personid
    department:
      department:
        fields:
          - key

convert_setting:
  size: 100
  before_executor_mapper:
    asset:
      - add:
          - field: asset_type
            value: "终端"
    department:
      - add:
          - field: asset_type
            value: "部门"
    owner:
      - add:
          - field: asset_type
            value: "责任人"

  executor_mapper:
    asset:
      default:
        - for_add:
            - size: 1
              field: ips
              setting:
                - field: addr
                  value: "${ip}"
                - field: mac
                  value: "${mac}"
            - size: 1
              field: agents
              setting:
                - field: name
                  value: "天融信审计"
                - field: version
                  value: "${agentversion}"
                - field: status
                  value: "${onlinestat}"
            - size: 1
              field: departments
              setting:
                - field: full_name
                  value: "${depart}"

        - add:
            - field: os.type
              value: "${systemtype}"
            - field: os.full
              value: "${operatingsystem}"
            - field: device.sid
              value:
                - "${key}"
            - field: host_name
              value: "${computername}"
            - field: device.name
              value: "${name}"

        - for:
            - field: agents
              handler:
                - method: translate
                  setting:
                    - field: status
                      default: 0
                      values:
                        - field: 在线
                          value: 1
                        - field: 离线
                          value: 2
            - field: departments
              handler:
                - method: replace
                  setting:
                    - field: full_name
                      src_value: "/"
                      dst_value: ""
    department:
      default:
        - rename:
            - src_field: title
              dst_field: name
            - src_field: fullTitle
              dst_field: full_name
            - src_field: parentTitle
              dst_field: parent_full_name
            - src_field: childrenFullTitles
              dst_field: children_full_names
            - src_field: key
              dst_field: source_id

    owner:
      default:
        - rename:
            - src_field: personname
              dst_field: username
            - src_field: personid
              dst_field: source_id

        - for:
            - field: "~td.relation.persongroup"
              get_direct: true
              handler:
                - method: replace
                  setting:
                    - field: "depart"
                      src_value: "/"
                      dst_value: ""
        - extract:
            - src_field: "~td.relation.persongroup"
              son_field: depart
              dst_field: departments
              get_direct: true
        - add:
            - field: emails
              value:
                - "${personemail}"
            - field: phones
              value:
                - "${personephone}"
        - filter:
            - field: emails
              condition: empty_check
            - field: phones
              condition: empty_check



fabric_setting:
  choose_point_mapper:
    asset: "top_spider_audit.fabric:choose_new_record"
