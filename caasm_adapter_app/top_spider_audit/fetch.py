from caasm_adapter.sdk.adapter_fetch import fetch_sdk
from top_spider_audit.manage import TopSpiderAuditManager


def find_asset(connection, page_index=0, page_size=10, session=None, **kwargs):
    fetch_type = kwargs.get("fetch_type")
    result = TopSpiderAuditManager(connection, session=session).find_asset(fetch_type, page_index + 1, page_size)
    fetch_sdk.build_asset(result, fetch_type)
    return fetch_sdk.return_success(result)


def get_auth_connection(connection, session=None):
    TopSpiderAuditManager(connection, session).auth()
