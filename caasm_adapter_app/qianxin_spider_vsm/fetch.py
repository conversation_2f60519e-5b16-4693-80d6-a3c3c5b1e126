from caasm_adapter.sdk.adapter_fetch import fetch_sdk
from qianxin_spider_vsm.manage import QianxinSpiderVsmManager


def find_asset(connection, fetch_type, page_index=0, page_size=1, **kwargs):
    session = kwargs.get("session")

    result = QianxinSpiderVsmManager(connection, session).find_asset(page_index + 1, page_size)
    fetch_sdk.build_asset(result, fetch_type)
    return fetch_sdk.return_success(result)


def get_auth_connection(connection, session):
    QianxinSpiderVsmManager(connection, session)._auth()
