name: "qianxin_spider_vsm"
display_name: "奇安信网神虚拟化安全管理系统(页面数据抓取)"
description: "奇安信网神虚拟化安全管理系统采用创新的无代理防护模式，即在宿主机的虚拟化层对文件、网络和系统数据进行检测，避免了安全软件在同一主机上的重复部署，显著的降低了安全系统对资源的占用。经测算，采用新的防护方式，可将虚拟机部署数量提升3倍以上，大幅度的降低云计算数据中心的建设成本。"
type: "主机防护"
company: ""
logo: "logo.png"
version: "v0.1"
priority: 1
properties:
  - 主机防护
  - 防病毒

connection:
  - name: username
    type: string
    required: true
    display_name: "用户名"
    description: "用户名称"
    validate_rules:
      - name: length
        error_hint: "用户名长度必须大于等于2且小于等于100"
        setting:
          min: 2
          max: 100

  - name: password
    type: password
    required: true
    display_name: "密码"
    description: "密码信息"
    validate_rules:
      - name: length
        error_hint: "密码长度必须大于等于6且小于等于100"
        setting:
          min: 6
          max: 100

  - name: address
    type: url
    required: true
    display_name: "地址"
    description: "地址信息"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'


fetch_setting:
  type: disposable
  point: "qianxin_spider_vsm.fetch:find_asset"
  is_need_test_service: true
  test_auth_point: "qianxin_spider_vsm.fetch:get_auth_connection"
  size: 100
  fetch_type_mapper:
    asset:
      - asset

merge_setting:
  size: 1000
  setting:
    asset:
      asset:
        fields:
          - mid


convert_setting:
  size: 1000
  before_executor_mapper:
    asset:
      - add:
          - field: asset_type
            value: "主机"
  executor_mapper:
    asset:
      主机:
        - drop:
            - field: os
        - add:
            - field: host_name
              value: "${name}"
            - field: os.full
              value: "${os_name}"
            - field: device.sid
              value: "${mid}"

        - convert:
            - field: device.sid
              type: list

        - for_add:
            - size: 1
              field: ips
              setting:
                - field: addr
                  value: "${ip}"
            - size: 1
              field: virus.libs
              setting:
                - field: name
                  value: "奇安信网神虚拟化安全管理系统毒库"
                - field: version
                  value: "${virus_lib_version}"
fabric_setting:
  choose_point_mapper:
    asset: "qianxin_spider_vsm.fabric:choose_new_record"