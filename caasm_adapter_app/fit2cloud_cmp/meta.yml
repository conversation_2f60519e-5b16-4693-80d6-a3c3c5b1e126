name: fit2cloud_cmp
display_name: "飞致云多云管理平台"
description: "飞致云多云管理平台旨在以统一的方式帮助企业用户在多云环境下实现自动化、自助式的服务交付，同时持续提升企业的运营效率及安全合规水平，支持企业从传统 IT 渐进、无缝地过渡到多云战略。"
type: "云管平台"
company: "飞致云"
logo: "fit2cloud_cmp.png"
version: "v0.1"
priority: 1
properties:
  - 云管

connection:
  - name: access_key
    type: string
    required: true
    display_name: "access_key"
    description: "access_key"
    validate_rules:
      - name: length
        error_hint: "access_key格式无效。长度最小不得小于2，最大不得大于200"
        setting:
          min: 2
          max: 100

  - name: secret_key
    type: password
    required: true
    display_name: "secret_key"
    description: "secret_key"
    validate_rules:
      - name: length
        error_hint: "secret_key格式无效。长度最小不得小于6，最大不得大于200"
        setting:
          min: 6
          max: 200

  - name: address
    type: url
    required: true
    display_name: "地址"
    description: "地址信息"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'


fetch_setting:
  size: 200
  type: disposable
  point: "fit2cloud_cmp.fetch:find_asset"
  is_need_test_service: true
  mode: "compute_page"
  count_point: "fit2cloud_cmp.fetch:get_asset_count"
  test_auth_point: "fit2cloud_cmp.fetch:get_auth_connection"
  fetch_type_mapper:
    asset:
      - virtual_machine
      - physical_machine
  cleaner_mapper:
    asset:
      virtual_machine:
        - "fit2cloud_cmp.cleaners.virtual_cleaner:VirtualCleaner"
      physical_machine:
        - "fit2cloud_cmp.cleaners.physical_cleaner:PhysicalCleaner"

fabric_setting:
  choose_point_mapper:
    asset: "fit2cloud_cmp.fabric:choose_new_record"


merge_setting:
  size: 200
  setting:
    asset:
      virtual_machine:
        fields:
          - instanceUuid
      physical_machine:
        fields:
          - instanceUuid

convert_setting:
  size: 200
  before_executor_mapper: { }
  executor_mapper: { }