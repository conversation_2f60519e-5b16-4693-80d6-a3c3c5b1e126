from caasm_adapter.util.exception import AdapterFetchApiNotSupportException
from fit2cloud_cmp.clients.physical_machine import Fit2CloudCMPPhysicalMachineListClient
from fit2cloud_cmp.clients.virtual_machine import Fit2CloudCMPVirtualMachineListClient


class ApiType(object):
    VIRTUAL_MACHINE = "virtual_machine"
    PHYSICAL_MACHINE = "physical_machine"


class Fit2CloudCMPManager(object):
    client_mapper = {
        ApiType.VIRTUAL_MACHINE: Fit2CloudCMPVirtualMachineListClient,
        ApiType.PHYSICAL_MACHINE: Fit2CloudCMPPhysicalMachineListClient,
    }

    def __init__(self, connection, session=None):
        self._connection = connection
        self._session = session

    def find(self, fetch_type, page_index, page_size):
        return self._instance(fetch_type).handle(page_index + 1, page_size)

    def get_asset_count(self, fetch_type):
        instance = self._instance(fetch_type)
        return instance.get_count(page_index=1, page_size=1)

    def _instance(self, api_type):
        clazz = self.client_mapper.get(api_type)
        if not clazz:
            raise AdapterFetchApiNotSupportException()
        return clazz(self._connection, self._session)
