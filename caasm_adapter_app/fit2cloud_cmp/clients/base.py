import base64
import logging
import time
import uuid

from Crypto.Cipher import <PERSON><PERSON>

from caasm_adapter.util.client import FetchJsonResultClient
from caasm_adapter.util.exception import AdapterFetchAuthFailedException

log = logging.getLogger()


class Fit2CloudCMPClient(FetchJsonResultClient):
    METHOD = "post"

    def build_request_header(self, *args, **kwargs):
        return {"accessKey": self.access_key, "signature": self._compute_sign()}

    @property
    def data_key_name(self):
        return "data.listObject"

    @property
    def flag_key_name(self):
        return "success"

    @property
    def suc_flag(self):
        return True

    def get_count(self, *args, **kwargs):
        result = self.handle_common(*args, **kwargs) or {}
        data = result.get("data") or {}
        count = data.get("pageCount") or 0
        return count

    def build_request_json(self, page_index, page_size):
        return {}

    @property
    def access_key(self):
        return self.connection.get("access_key")

    @property
    def secret_key(self):
        return self.connection.get("secret_key")

    def _compute_sign(self):
        timestamp = str(int(time.time()) * 1000)
        wait_sign_list = [self.access_key, str(uuid.uuid4()), timestamp]
        wait_sign_str = "|".join(wait_sign_list)
        log.debug(f"need sign str is {wait_sign_str}")
        return self._encrypt(self.secret_key, self.access_key, wait_sign_str)

    @classmethod
    def _encrypt(cls, key, iv, text):
        text = cls._pad(text).encode()
        cipher = AES.new(key=key.encode(), mode=AES.MODE_CBC, IV=iv.encode())
        encrypted_text = cipher.encrypt(text)
        return base64.b64encode(encrypted_text).decode("utf-8")

    @classmethod
    def _pad(cls, text):
        _size = AES.block_size
        return text + (_size - len(text.encode()) % _size) * chr(_size - len(text.encode()) % _size)

    def parse_response(self, response, *args, **kwargs):
        if response.status_code == 403:
            raise AdapterFetchAuthFailedException()
        elif response.status_code != 200:
            return self.parse_error_handle(None, *args, **kwargs)
        return super(Fit2CloudCMPClient, self).parse_response(response, *args, **kwargs)
