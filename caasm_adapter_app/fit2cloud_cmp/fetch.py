from caasm_adapter.sdk.adapter_fetch import fetch_sdk
from fit2cloud_cmp.manage import Fit2CloudCMPManager, ApiType


def find_asset(connection, fetch_type, page_index=0, page_size=1, session=None, **kwargs):
    manager = _build_manager(connection, session)
    records = manager.find(fetch_type, page_index, page_size)
    result = fetch_sdk.build_asset(records, fetch_type)
    return fetch_sdk.return_success(result)


def get_asset_count(connection, fetch_type, session=None, **kwargs):
    return _build_manager(connection, session).get_asset_count(fetch_type)


def get_auth_connection(connection, session=None):
    _build_manager(connection, session).get_asset_count(ApiType.VIRTUAL_MACHINE)


def _build_manager(connection, session):
    return Fit2CloudCMPManager(connection, session)
