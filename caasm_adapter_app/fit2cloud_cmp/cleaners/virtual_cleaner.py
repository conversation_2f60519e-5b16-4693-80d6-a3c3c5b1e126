import re

import yaml

from caasm_adapter.fetcher.cleaners.base import Fe<PERSON><PERSON><PERSON><PERSON>leaner
from caasm_tool.util import deduplicate


class VirtualCleaner(FetchBaseCleaner):
    _ip_re = re.compile(r"\d+(?:\.\d+){3}")

    def __init__(self, *args, **kwargs):
        super(VirtualCleaner, self).__init__(*args, **kwargs)
        self._clean_methods = [self._clean_ip, self._clean_owner]

    def clean_single(self, detail):
        result = {}
        for _clean_method in self._clean_methods:
            _clean_result = _clean_method(detail)
            if not _clean_result:
                continue
            result.update(_clean_result)
        return result

    @classmethod
    def _clean_ip(cls, detail):
        instance_name = detail.get("instanceName", "")
        if not instance_name:
            return False

        ip_array = detail.get("ipArray")
        if not ip_array:
            ip_array = []
        else:
            ip_array = yaml.safe_load(ip_array)

        ips = cls._ip_re.findall(instance_name)
        if not ips:
            return False

        new_ip = ips[0]

        if new_ip not in ip_array:
            ip_array.append(new_ip)

        ip_array = deduplicate(ip_array)

        return {"ips": [{"addr": ip} for ip in ip_array]}

    @classmethod
    def _clean_owner(cls, record):
        operator_users = record.get("operatorUser")
        if not operator_users:
            return
        operator_users = operator_users if isinstance(operator_users, list) else [operator_users]
        _owners = [{"username": operatorUser} for operatorUser in operator_users]
        return {"owners": _owners}
