from caasm_adapter.sdk.adapter_fetch import fetch_sdk
from qing_boss2_cloud.manage import QingBoss2CloudManager


def find_asset(connection, fetch_type, page_size=500, session=None, condition=None, **kwargs):
    result = _build_manager(connection, condition, session).find_asset(fetch_type, page_size)
    records = fetch_sdk.build_asset(result, fetch_type)
    return fetch_sdk.return_success(records)


def build_query_condition(connection, session, fetch_type):
    zones = connection.get("zones") or []
    current_zone = zones.pop() if zones else None
    return {"zones": zones, "current_page_index": 0, "current_zone": current_zone}


def auth(connection, session):
    return _build_manager(connection).auth()


def _build_manager(connection, condition=None, session=None):
    return QingBoss2CloudManager(connection, condition, session)
