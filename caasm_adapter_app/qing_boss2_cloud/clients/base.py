import abc
import json
import urllib.parse as urllib2
from functools import cached_property

from qingcloud.iaas import APIConnection

from caasm_adapter.util.client import FetchJsonResultClient
from caasm_tool.constants import PROTOCOL_DEFAULT_MAPPER


class QingBoss2CloudBaseClient(FetchJsonResultClient, metaclass=abc.ABCMeta):
    METHOD = "post"
    DEFAULT_URL = "/boss2/"

    def __init__(self, zone, connection, *args, **kwargs):
        self.zone = zone
        super(QingBoss2CloudBaseClient, self).__init__(connection, *args, **kwargs)

    def handle_common(self, *args, **kwargs):
        boss2 = APIConnection(self.access_key, self.secret_key, self.zone, self.host, self.port, self.protocol)
        body = {"params": json.dumps(self.build_request_json(*args, **kwargs))}
        return boss2.send_request(action=self.action, body=body, url=self.DEFAULT_URL, verb=self.METHOD.upper())

    def build_request_json(self, *args, **kwargs):
        return {"zone": self.zone}

    @property
    def action(self):
        raise NotImplementedError

    @property
    def access_key(self):
        return self.connection.get("access_key")

    @property
    def secret_key(self):
        return self.connection.get("secret_key")

    @property
    def protocol(self):
        return self.address[0]

    @property
    def host(self):
        return self.address[1]

    @property
    def port(self):
        return self.address[2]

    @cached_property
    def address(self):
        address = super(QingBoss2CloudBaseClient, self).address
        result = urllib2.urlparse(address)
        return result.scheme, result.netloc, result.port or PROTOCOL_DEFAULT_MAPPER.get(result.scheme)

    @property
    def flag_key_name(self):
        return "ret_code"

    @property
    def suc_flag(self):
        return 0


class QingBoss2CloudBasePageClient(QingBoss2CloudBaseClient, metaclass=abc.ABCMeta):
    def build_request_json(self, page_index, page_size):
        request_json = super(QingBoss2CloudBasePageClient, self).build_request_json(page_index, page_size)
        request_json["offset"] = page_index * page_size
        request_json["limit"] = page_size
        return request_json
