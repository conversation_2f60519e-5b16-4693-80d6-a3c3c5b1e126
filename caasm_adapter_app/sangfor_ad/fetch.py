from caasm_adapter.sdk.adapter_fetch import fetch_sdk
from sangfor_ad.manage import SangForADManager


def find_asset(connection, fetch_type, page_index=0, page_size=1, session=None, condition=None, **kwargs):
    token = condition.get("token")
    records = _manager(connection, session, token).find(fetch_type, page_index, page_size)
    result = fetch_sdk.build_asset(records, fetch_type)
    return fetch_sdk.return_success(result)


def get_auth_connection(connection, session):
    _manager(connection, session).auth()


def build_query_condition(connection, session, **kwargs):
    token = _manager(connection, session).auth()
    return {"token": token}


def _manager(connection, session, token=None):
    return SangForADManager(connection, session, token)
