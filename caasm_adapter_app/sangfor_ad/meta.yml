name: "sangfor_ad"
display_name: "深信服AD"
description: "应用交付 AD 能够为用户提供包括多数据中心负载均衡、多链路负载均衡、服务器负载均衡的全方位解决方案。不仅实现对各个数据中心、链路以及服务器状态的实时监控，同时根据预设规则，将用户的访问请求分配给相应的数据中心、 链路以及服务器，进而实现数据流的合理分配，使所有的数据中心、链路和服务器都得到充分利用。应用交付 AD 还支持与各个云平台对接，实现云场景下租户的自服务负载需求；IPv6改造方案，可有效攻克“天窗”问题。"
type: "网络配置"
company: "深信服"
logo: "sangfor_ad.png"
version: "v0.1"
priority: 10
properties:
  - "网络配置"

connection:
  - name: address
    type: url
    required: true
    display_name: "地址"
    description: "地址信息"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'

  - name: username
    type: string
    required: true
    display_name: "用户名"
    description: "用户名称"
    validate_rules:
      - name: length
        error_hint: "用户名长度必须大于等于2且小于等于100"
        setting:
          min: 2
          max: 100

  - name: password
    type: password
    required: true
    display_name: "密码"
    description: "密码信息"
    validate_rules:
      - name: length
        error_hint: "密码长度必须大于等于6且小于等于100"
        setting:
          min: 6
          max: 100


fetch_setting:
  type: disposable
  point: "sangfor_ad.fetch:find_asset"
  size: 100
  test_auth_point: "sangfor_ad.fetch:get_auth_connection"
  condition_point: "sangfor_ad.fetch:build_query_condition"
  is_need_test_service: true
  fetch_type_mapper:
    network:
      - network_mapping
      - pool
  cleaner_mapper:
    network:
      network_mapping:
        - "sangfor_ad.cleaners.network_mapping:NetworkMappingCleaner"