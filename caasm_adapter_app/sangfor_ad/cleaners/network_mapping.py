import logging
from collections import defaultdict

from caasm_adapter.fetcher.cleaners.base import FetchTotalBaseCleaner

log = logging.getLogger()


class NetworkMappingCleaner(FetchTotalBaseCleaner):
    _DEFAULT_DATA_TYPE = "network_mapping"
    _VALID_STATE = "ENABLE"

    def build_common(self, biz_records):
        record_mapper_by_fetch_type = self._group_record(biz_records)

        pool_by_name = {pool["name"]: pool for pool in record_mapper_by_fetch_type["pool"]}

        result = []

        for network_mapping in record_mapper_by_fetch_type["network_mapping"]:
            try:
                state = network_mapping.get("state")
                service_name = network_mapping.get("name")
                description = network_mapping.get("description")
                if state != self._VALID_STATE:
                    continue

                vips = network_mapping.get("vips") or []
                vip_ports = list(map(int, network_mapping.get("vports") or []))
                pool_name = network_mapping.get("pool")
                pool = pool_by_name.get(pool_name, {})
                nodes = [
                    {"ip": node["address"], "port": node["port"]}
                    for node in pool.get("nodes", [])
                    if node["state"] == self._VALID_STATE
                ]

                domain_name = ""
                try:
                    domain_name = service_name.split("服务")[0] + "." + description.split("-")[1] + ".com"
                except Exception as e:
                    log.error(f"handle domain_name error({e}) service: {service_name}, description:{description}")

                for vip in vips:
                    detail = {
                        "vip": vip,
                        "vip_ports": vip_ports,
                        "nodes": nodes,
                        "service_name": network_mapping.get("name"),
                        "description": description,
                        "pool_name": pool_name,
                        "domain_name": domain_name,
                    }
                    result.append(detail)
            except Exception as e:
                log.error(f"clean network mapping({network_mapping}) error({e})")

        return result

    @classmethod
    def _group_record(cls, biz_records):
        result = defaultdict(list)
        for biz_record in biz_records:
            result[biz_record["fetch_type"]].append(biz_record)
        return result
