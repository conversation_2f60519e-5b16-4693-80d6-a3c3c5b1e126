from caasm_adapter.util.client import FetchRestfulClient


class SangForADClient(FetchRestfulClient):
    METHOD = "post"

    def __init__(self, connection=None, session=None, token=None):
        super().__init__(connection, session)
        self._token = token

    def build_request_header(self, *args, **kwargs):
        return {"x-token-sangforad": self._token}

    @property
    def data_key_name(self):
        return ""

    @property
    def flag_key_name(self):
        return ""
