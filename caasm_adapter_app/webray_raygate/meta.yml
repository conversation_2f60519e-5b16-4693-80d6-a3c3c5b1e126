name: "webray_raygate"
display_name: "网络资产安全治理平台（RayGate）"
description: "网络资产安全治理平台（RayGate）是盛邦安全针对网站及网络安全管理、监测预警与应急处置等方面的要求而研发的一款网络资产治理产品，它通过主被动结合机制，发现门户网站、业务系统及联网设备安全问题。将资产上线安全检查与备案系统相结合，简化备案工作流程，并提供网站资产及物联网资产安全的日常监控，包含脆弱性管理、篡改监控、暗链、webshell等安全问题，提供专业的修补意见。帮助客户实现对自身资产安全状况做到“资产有数、风险可控、主动防御、威胁可感知、安全可量化”的高效安全管理，适用于高校网络、园区网、云计算中心、行业垂直网络及政府横向网络等场景。"
type: "内网资产测绘"
company: "盛邦"
logo: "webray_raygate.png"
version: "v0.1"
priority: 1
properties:
  - "内网资产测绘"

connection:
  - name: username
    type: string
    required: true
    display_name: "用户名"
    description: "用户名称"
    validate_rules:
      - name: length
        error_hint: "用户名长度必须大于等于2且小于等于100"
        setting:
          min: 2
          max: 100

  - name: password
    type: password
    required: true
    display_name: "密码"
    description: "密码信息"
    validate_rules:
      - name: length
        error_hint: "密码长度必须大于等于6且小于等于100"
        setting:
          min: 6
          max: 100

  - name: address
    type: url
    required: true
    display_name: "地址"
    description: "地址信息"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'


fetch_setting:
  type: disposable
  point: "webray_raygate.fetch:find_asset"
  size: 1000
  fetch_type_mapper:
    asset:
      - asset

fabric_setting:
  choose_point_mapper:
    asset: "webray_raygate.fabric:choose_new_record"

merge_setting:
  size: 1000
  setting: { }

convert_setting:
  size: 1000
  before_executor_mapper: { }
  executor_mapper: { }