from baidu_cloud.manage import BaiduCloudManager
from caasm_adapter.sdk.adapter_fetch import fetch_sdk


def find_asset(connection, fetch_type, condition, page_index=0, page_size=20, session=None, **kwargs):
    manager = BaiduCloudManager(connection, session, condition)
    records = manager.find_asset(fetch_type, page_size=page_size, page_index=page_index + 1)
    result = fetch_sdk.build_asset(records, fetch_type)
    return fetch_sdk.return_success(result)


def build_query_condition(connection, fetch_type, **kwargs):
    return {"marker_mapper": {"bbc": {}}, "count_limit": {"bos": 1}}


def get_auth_connection():
    pass
