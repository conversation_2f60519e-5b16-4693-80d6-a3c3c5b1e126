from baidu_cloud.clients.api.base import BaiduCloudListBaseClient, BaiduCloudBaseClient


class BaiDuCloudCCRQYInstanceClient(BaiduCloudBaseClient):

    METHOD = "GET"
    URL = "/v1/instances?pageNo={}&pageSize={}"
    CANONICAL_URL = "/v1/instances"
    QUERY_STRING = "pageNo={}&pageSize={}"
    DEFAULT_CATEGORY = ""

    def build_request_url(self, *args, **kwargs):
        page_index = kwargs.get("page_index")
        page_size = kwargs.get("page_size")
        self.query_string = self.QUERY_STRING.format(page_index, page_size)
        request_url = super(BaiDuCloudCCRQYInstanceClient, self).build_request_url()
        return request_url.format(page_index, page_size, *args, **kwargs)

    @property
    def data_key_name(self):
        return "instances"


class BaiDuCloudCCRQYNameSpaceClient(BaiduCloudBaseClient):

    METHOD = "GET"
    URL = "/v1/instances/{}/projects?pageNo={}&pageSize={}"
    CANONICAL_URL = "/v1/instances/{}/projects"
    QUERY_STRING = "pageNo={}&pageSize={}"
    DEFAULT_CATEGORY = ""

    def build_request_params(self, *args, **kwargs):
        instance_id = kwargs.get("instance_id")
        page_index = kwargs.get("page_index")
        page_size = kwargs.get("page_size")

        self.CANONICAL_URL = self.CANONICAL_URL.format(instance_id)
        self.query_string = self.QUERY_STRING.format(page_index, page_size)
        request_url = super(BaiDuCloudCCRQYNameSpaceClient, self).build_request_url()
        return request_url.format(instance_id, page_index, page_size, *args, **kwargs)

    @property
    def data_key_name(self):
        return "projects"


class BaiDuCloudCCRQYMirrorHouseClient(BaiduCloudBaseClient):

    METHOD = "GET"
    URL = "/v1/instances/{}/projects/{}/repositories?pageNo={}&pageSize={}"
    CANONICAL_URL = "/v1/instances/{}/projects/{}/repositories"
    QUERY_STRING = "pageNo={}&pageSize={}"
    DEFAULT_CATEGORY = ""

    def build_request_params(self, *args, **kwargs):
        instance_id = kwargs.get("instance_id")
        project_name = kwargs.get("project_name")
        page_index = kwargs.get("page_index")
        page_size = kwargs.get("page_size")
        self.CANONICAL_URL = self.CANONICAL_URL.format(instance_id, project_name)
        self.query_string = self.QUERY_STRING.format(page_index, page_size)
        request_url = super(BaiDuCloudCCRQYMirrorHouseClient, self).build_request_url()
        return request_url.format(instance_id, page_index, page_size, *args, **kwargs)


class BaiDuCloudCCRQYMirrorTagClient(BaiduCloudBaseClient):
    METHOD = "GET"
    URL = "/v1/instances/{}/projects/{}/repositories/{}/tags?pageNo={}&pageSize={}"
    CANONICAL_URL = "/v1/instances/{}/projects/{}/repositories/{}/tags"
    QUERY_STRING = "pageNo={}&pageSize={}"
    DEFAULT_CATEGORY = ""

    def build_request_params(self, *args, **kwargs):
        instance_id = kwargs.get("instance_id")
        project_name = kwargs.get("project_name")
        repositories = kwargs.get("repositories")
        page_index = kwargs.get("page_index")
        page_size = kwargs.get("page_size")

        self.CANONICAL_URL = self.CANONICAL_URL.format(instance_id, project_name, repositories)
        self.query_string = self.QUERY_STRING.format(page_index, page_size)
        request_url = super(BaiDuCloudCCRQYMirrorTagClient, self).build_request_url()
        return request_url.format(instance_id, page_index, page_size, *args, **kwargs)


class BaiDuCloudCCRQYMirrorTagInfoClient(BaiduCloudBaseClient):
    METHOD = "GET"
    URL = "/v1/instances/{}/projects/{}/repositories/{}/tags/{}"
    CANONICAL_URL = "/v1/instances/{}/projects/{}/repositories/{}/tags/{}"
    QUERY_STRING = "pageNo={}&pageSize={}"
    DEFAULT_CATEGORY = ""

    def build_request_params(self, *args, **kwargs):
        instance_id = kwargs.get("instance_id")
        project_name = kwargs.get("project_name")
        repositories = kwargs.get("repositories")
        tags = kwargs.get("tags")
        self.CANONICAL_URL = self.CANONICAL_URL.format(instance_id, project_name, repositories, tags)
        page_index = kwargs.get("page_index")
        page_size = kwargs.get("page_size")
        self.query_string = self.QUERY_STRING.format(page_index, page_size)

        request_url = super(BaiDuCloudCCRQYMirrorTagInfoClient, self).build_request_url()
        return request_url.format(instance_id, page_index, page_size, *args, **kwargs)
