import time
import logging

from requests import ConnectTimeout, ReadTimeout

from baidu_cloud.clients.api.auth import BaiduCloudAPITokenBuilder
from caasm_adapter.util.client import FetchJsonResultClient
from caasm_tool.util import extract

log = logging.getLogger()


class BaiduCloudBaseClient(FetchJsonResultClient):
    DEFAULT_HTTP = "http://"
    CANONICAL_URL = ""
    QUERY_STRING = ""
    DEFAULT_CATEGORY = ""

    # 字段
    NEXT_MARKER = "nextMarker"

    def __init__(self, connection, session=None, condition=None, address=None):
        super(BaiduCloudBaseClient, self).__init__(connection, session)
        self._address = address
        self.query_string = ""
        self.header = {}
        self.condition = condition
        self.x_bce_date = time.strftime("%Y-%m-%dT%H:%M:%SZ", time.gmtime())
        self.endpoints = []

    def auth_token(self):
        builder = BaiduCloudAPITokenBuilder(
            ak=self.ak,
            sk=self.sk,
            method=self.METHOD,
            header=self.header,
            x_bce_date=self.x_bce_date,
            query_string=self.query_string,
            canonical_url=self.CANONICAL_URL,
        )
        return builder.build_token()

    def build_request_header(self, *args, **kwargs):
        header = {
            "Host": self.endpoint,
            "content-type": "application/json;charset=utf-8",
            "x-bce-date": self.x_bce_date,
        }
        self.header = header
        self.build_request_url(*args, **kwargs)
        header["Authorization"] = self.auth_token()
        return header

    @property
    def endpoint(self):
        return f"{self.DEFAULT_CATEGORY}.{self._address}"

    @property
    def ak(self):
        return self.connection.get("access_key")

    @property
    def sk(self):
        return self.connection.get("secret_key")

    @property
    def default_port(self):
        return self.connection.get("port") or 80

    @property
    def address(self):
        return f"{self.DEFAULT_HTTP}{self.DEFAULT_CATEGORY}.{self._address}:{self.default_port}"

    @property
    def suc_flag(self):
        return ""

    @property
    def flag_key_name(self):
        return ""

    @property
    def data_key_name(self):
        return "instances"

    def build_request_url(self, *args, **kwargs):
        return self.build_url(self.address, self.URL)


class BaiduCloudListBaseClient(BaiduCloudBaseClient):
    SUB_CATEGORY = ""

    def parse_biz_result(self, result, *args, **kwargs):

        if not self.check_biz_result(result):
            log.warning(f"{self.name}Response error. detail is {result}")
            return self.parse_error_handle(result, *args, **kwargs)

        self.update_next_find_marker(result, self.endpoint)
        data_key_name = self.data_key_name

        if data_key_name:
            result = extract(result, data_key_name)
        try:
            result = self.clean_result(result)
        except Exception as e:
            log.warning(f"Clean result error({e})")
            return self.parse_error_handle(result, *args, **kwargs)
        else:
            return result

    def update_next_find_marker(self, result, endpoint):
        marker = result.get(self.NEXT_MARKER)
        if not marker:
            self.condition["marker_mapper"][self.DEFAULT_CATEGORY][self.SUB_CATEGORY][endpoint] = False
            return

        self.condition["marker_mapper"][self.DEFAULT_CATEGORY][self.SUB_CATEGORY][endpoint] = marker

    def init_find_marker(self, *args, **kwargs):
        if self.DEFAULT_CATEGORY not in self.condition.get("marker_mapper"):
            self.condition["marker_mapper"][self.DEFAULT_CATEGORY] = {}
        if self.SUB_CATEGORY not in self.condition["marker_mapper"][self.DEFAULT_CATEGORY]:
            self.condition["marker_mapper"][self.DEFAULT_CATEGORY][self.SUB_CATEGORY] = {}
        if self.endpoint not in self.condition["marker_mapper"][self.DEFAULT_CATEGORY][self.SUB_CATEGORY]:
            self.condition["marker_mapper"][self.DEFAULT_CATEGORY][self.SUB_CATEGORY][self.endpoint] = ""

    def handle(self, *args, **kwargs):
        self.init_find_marker(*args, **kwargs)
        if self.condition["marker_mapper"][self.DEFAULT_CATEGORY][self.SUB_CATEGORY][self.endpoint] is False:
            return []
        try:
            result = self.handle_common(*args, **kwargs)
        except (ConnectTimeout, ReadTimeout):
            return self.timeout_handle(*args, **kwargs)
        except Exception as e:
            return self.error_handle(e, *args, **kwargs)
        else:
            return self.parse_biz_result(result, *args, **kwargs)
