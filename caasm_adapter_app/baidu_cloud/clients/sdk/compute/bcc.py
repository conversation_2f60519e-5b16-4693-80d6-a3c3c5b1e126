from baidubce.auth.bce_credentials import BceCredentials
from baidubce.bce_client_configuration import BceClientConfiguration
from baidubce.services.bcc import bcc_client

from baidu_cloud.clients.sdk.base import BaiduCloudInfoListClient, BaiduCloudInfoBaseClient


class BaiduCloudBCCInstanceClient(BaiduCloudInfoListClient):

    DEFAULT_CATEGORY = "bcc"
    SUB_CATEGORY = "bcc"

    def create_client(self, config=None):
        return bcc_client.BccClient(config)

    def find_client_data(self, client=None, marker=None, max_keys=None):
        return client.list_instances(marker=marker, max_keys=max_keys)

    @property
    def data_key(self):
        return "instances"


class BaiduCloudBCCInstanceInfoClient(BaiduCloudInfoBaseClient):

    DEFAULT_CATEGORY = "bcc"

    def create_client(self, config=None):
        return bcc_client.BccClient(config)

    def find_client_data(self, client=None, *args, **kwargs):
        instance_id = kwargs.get("instance_id")
        return client.get_instance(instance_id, contains_failed=False)

    @property
    def data_key(self):
        return "instance"
