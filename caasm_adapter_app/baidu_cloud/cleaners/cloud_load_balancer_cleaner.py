from caasm_adapter.fetcher.cleaners.base import FetchBaseCleaner


class CloudLoadBalancerCleaner(FetchBaseCleaner):
    def clean_single(self, detail):
        backend_servers = detail.get("backend_servers") or []
        result = []
        for backend in backend_servers:
            port_list = backend.get("port_list") or []

            for port in port_list:
                port_info = {{"port_info": port}}
                backend.pop("port_list")
                port_info.update(backend)
                result.append(port_info)
        detail["backends_servers"] = result
        detail.pop("backend_servers")
        return detail
