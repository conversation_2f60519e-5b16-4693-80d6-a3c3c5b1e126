from caasm_adapter.sdk.adapter_fetch import fetch_sdk
from caasm_tool.util import extract
from qianxin_spider_tianqing_v10.manage import QiAnXinSpiderTianQingV10Manager


def find_asset(connection, fetch_type, page_index=0, page_size=1, condition=None, **kwargs):
    session = kwargs.get("session")
    records = QiAnXinSpiderTianQingV10Manager(
        connection,
        session,
        condition,
    ).find(fetch_type, page_index, page_size)
    result = fetch_sdk.build_asset(records, fetch_type)
    return fetch_sdk.return_success(result)


def get_auth_connection(connection, session):
    manager = QiAnXinSpiderTianQingV10Manager(connection, session=session)
    get_asset_org_info(manager=manager)


def build_query_condition(connection, session, fetch_type):
    manager = QiAnXinSpiderTianQingV10Manager(connection, session=session)
    asset_info = get_asset_org_info(manager=manager)
    group_info = get_group_info(manager=manager, asset_info=asset_info)
    mate_info = get_meta_info(manager=manager, group_info=group_info)
    terminal_ids_info = get_terminal_info(manager=manager, mate_info=mate_info)
    return {"terminal_ids_info": terminal_ids_info}


def get_asset_org_info(manager=None):
    orgs = manager.find_orgs()
    asset_info = []
    for org_id in orgs:
        _info = manager.find_asset_id(org_id=org_id)
        if not _info:
            continue
        asset_info.extend(_info)
    return asset_info


def get_group_info(manager=None, asset_info=None):
    page_size = 100
    result = []
    for _asset_info in asset_info:
        page_index = 0
        asset_id = _asset_info.get("id")
        asset_oid = _asset_info.get("oid")
        asset_group_info = manager._call(
            "asset_group", page_index=page_index, page_size=page_size, asset_id=asset_id, asset_oid=asset_oid
        )
        if not asset_group_info:
            continue
        result.extend(asset_group_info)
    return result


def get_meta_info(manager=None, group_info=None):
    result = []
    meta_key = []
    for _group_info in group_info:
        group_id = extract(_group_info, "id")
        tree_id = extract(_group_info, "tree_id.id")
        asset_id = extract(_group_info, "tree_id.asset_id.id")
        asset_oid = extract(_group_info, "tree_id.asset_id.oid")
        meta_info = manager._call(
            "asset_meta", group_id=group_id, group_tree_id=tree_id, asset_id=asset_id, asset_oid=asset_oid
        )
        if not meta_info:
            continue
        has_child = meta_info.get("has_child")
        name = meta_info.get("name")
        path = meta_info.get("path")
        if path not in meta_key:
            meta_key.append(path)
            result.append(
                {
                    "asset_id": asset_id,
                    "asset_oid": asset_oid,
                    "tree_id": tree_id,
                    "group_id": group_id,
                    "has_child": has_child,
                    "name": name,
                    "path": path,
                }
            )
    return result


def get_terminal_info(manager, mate_info):
    result = []
    for _meta_info in mate_info:
        string_value = _meta_info.get("path")
        page_index = 0
        page_size = 100
        while True:
            data = manager._call("terminal_id", string_value=string_value, page_index=page_index, page_size=page_size)
            if not data:
                break
            page_index += 1
            for item in data:
                terminal_id = extract(item, "key.id")
                asset_id = extract(item, "key.asset_id.id")
                asset_oid = extract(item, "key.asset_id.oid")
                result.append({"terminal_id": terminal_id, "asset_id": asset_id, "asset_oid": asset_oid})
    return result
