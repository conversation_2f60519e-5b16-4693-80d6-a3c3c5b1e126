import hmac
import hashlib
import random
import time

from caasm_adapter.util.client import FetchJsonResultClient


class QiAnXinTianQingBaseClient(FetchJsonResultClient):
    def build_request_header(self, *args, **kwargs):
        timestamp = int(time.time() * 1000)
        nonce = random.randint(1, 100)
        string_to_sign = f"appKey:{self.app_key}&nonce:{nonce}&timestamp:{timestamp}"
        # 使用 HMAC256 算法生成签名
        signature = hmac.new(self.app_secret.encode(), string_to_sign.encode(), hashlib.sha256).hexdigest()
        return {
            "Content-Type": "application/json",
            "Authorization": f"ZEUS-HMAC-SHA256 appKey={self.app_key},nonce={nonce},timestamp={timestamp},signature={signature}",
        }

    @property
    def address(self):
        return self.connection.get("address")

    @property
    def app_key(self):
        return self.connection.get("app_key")

    @property
    def app_secret(self):
        return self.connection.get("app_secret")
