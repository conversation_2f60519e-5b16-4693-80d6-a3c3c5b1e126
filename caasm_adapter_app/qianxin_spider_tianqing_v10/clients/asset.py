from qianxin_spider_tianqing_v10.clients.base import QiAnXinTianQingBaseClient


class QiAnXinTianQingAssetIDClient(QiAnXinTianQingBaseClient):
    URL = "/asset/v1/org/{}/assets"
    METHOD = "GET"

    def build_request_url(self, org_id=None, *args, **kwargs):
        url = super(QiAnXinTianQingAssetIDClient, self).build_request_url(*args, **kwargs)
        return url.format(org_id)

    @property
    def data_key_name(self):
        return "assets"

    @property
    def suc_flag(self):
        return ""

    @property
    def flag_key_name(self):
        return ""


class QiAnXinTianQingTreeClient(QiAnXinTianQingBaseClient):
    URL = "/Gql/S"
    METHOD = "POST"

    def build_request_json(self, page_index=None, page_size=None, asset_id=None, asset_oid=None, *args, **kwargs):
        query = f"""
        {{
            tree(key: {{ id: "" asset_id: {{ id: {asset_id} oid: {asset_oid} }} }}) {{
                nodes(node_meta_filter: {{ is_root: true }} limit: {page_size} offset: {page_index * page_size}) {{
                    collects {{
                        id
                        tree_id {{
                            id
                            asset_id {{
                                id
                                oid
                            }}
                        }}
                    }}
                }}
            }}
        }}
        """

        return {"project": "skylar.business.odp", "query": query}

    @property
    def suc_flag(self):
        return []

    @property
    def flag_key_name(self):
        return "errors"

    @property
    def data_key_name(self):
        return "data.tree.nodes.collects"


class QiAnXinTianQingMetaClient(QiAnXinTianQingBaseClient):
    URL = "/Gql/S"
    METHOD = "POST"

    def build_request_json(self, group_id=None, group_tree_id=None, asset_id=None, asset_oid=None, *args, **kwargs):
        query = f"""  
                query {{  
                    node(key: {{  
                        id: "{group_id}",  
                        tree_id: {{  
                            id: "{group_tree_id}",  
                            asset_id: {{  
                                id: "{asset_id}",  
                                oid: "{asset_oid}"  
                            }}  
                        }}  
                    }}) {{  
                        meta {{  
                            name  
                            path  
                            has_child  
                        }}  
                        }}  
                    }}  
                """
        return {"project": "skylar.business.odp", "query": query}

    @property
    def suc_flag(self):
        return []

    @property
    def flag_key_name(self):
        return "errors"

    @property
    def data_key_name(self):
        return "data.node.meta"


class QiAnXinTianQingTerminalClient(QiAnXinTianQingBaseClient):
    URL = "/Gql/S"
    METHOD = "POST"

    def build_request_json(self, string_value=None, page_index=None, page_size=None, *args, **kwargs):
        query = f"""
        {{
            free_clients(limit: {page_size}, offset: {page_index * page_size}, extra_filter: {{
                ast_node: {{ 
                    and: {{
                    children: [{{
                        leaf: {{
                            key: "node_path", 
                            operator: 6,
                            string_value: "{string_value}" 
                                }}
                                }}]
                                }}
                                }}
                                    }})
                {{
                    count
                    relation_entitys {{
                    key {{
                        id
                        asset_id {{
                            id
                            oid
                        }}                
                    }}
                }}
            }}
        }}   
        """
        return {"project": "skylar.business.odp", "query": query}

    @property
    def suc_flag(self):
        return []

    @property
    def flag_key_name(self):
        return "errors"

    @property
    def data_key_name(self):
        return "data.free_clients.relation_entitys"


class QiAnXinTianQingTerminalInfoClient(QiAnXinTianQingBaseClient):
    URL = "/Gql/S"
    METHOD = "POST"

    def build_request_json(self, asset_id=None, asset_oid=None, terminal_id=None, *args, **kwargs):
        query = f"""
        {{
            client(
                key: {{
                        id: "{terminal_id}" 
                        asset_id: 
                            {{ id: "{asset_id}", oid: "{asset_oid}" }}
                    }}) 
            {{
                meta {{
                    name
                    report_ip
                    ip
                    mac
                    tos {{
                        os
                        arch
                        version {{
                            kernel_version
                            version
                            }}
                        }}
                    client_type
                    system_language
                    activation
                    login_account
                    domain
                    computer_working_group
                }}
                online_info {{
                    last_time
                    is_online
                }}
            module_info {{
                main_program_version
                virus_version
                virus_bd_version
                patch_version
                }}
            info {{
                brand_model
                hardware_info {{
                    board {{
                        serial_number
                    }}
                }}
            }}
            user {{
                relation_entity {{
                    meta {{
                        real_name
                        user_name
                    }}
                }}
            }}
            login_user {{
                relation_entity {{
                    info {{
                        contact
                        employee_number
                        station
                    }}
                
                    meta {{
                        user_name
                        real_name
                        email
                        mobile_phone {{
                            number
                        }}
                    }}
                }}
            }}
            
            }}
        }}
        """
        return {"project": "skylar.business.odp", "query": query}

    @property
    def data_key_name(self):
        return "data.client"

    @property
    def suc_flag(self):
        return ""

    @property
    def flag_key_name(self):
        return ""
