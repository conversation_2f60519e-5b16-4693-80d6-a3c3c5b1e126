from caasm_adapter.fetcher.cleaners.base import FetchBaseCleaner
from caasm_tool.util import extract, restore


class DefaultCleaner(FetchBaseCleaner):
    LOGIN_USER_NAME = "login_user.relation_entity.meta.user_name"
    LOGIN_REAL_NAME = "login_user.relation_entity.meta.real_name"
    LOGIN_PHONE_NUMBER = "login_user.relation_entity.meta.mobile_phone.number"
    LOGIN_EMAIL = "login_user.relation_entity.meta.email"
    LOGIN_CONTACT = "login_user.relation_entity.info.contact"
    LOGIN_EMPLOYEE = "login_user.relation_entity.info.employee_number"

    ASSET_USER_NAME = "user.relation_entity.meta.user_name"
    ASSET_REAL_NAME = "user.relation_entity.meta.real_name"

    def parser_login_user(self, user_info):
        result = {}
        if not user_info:
            return result
        user_name = extract(user_info, self.LOGIN_USER_NAME)
        real_name = extract(user_info, self.LOGIN_REAL_NAME)
        phone_number = extract(user_info, self.LOGIN_PHONE_NUMBER)
        email = extract(user_info, self.LOGIN_EMAIL)
        contact = extract(user_info, self.LOGIN_CONTACT)
        employee = extract(user_info, self.LOGIN_EMPLOYEE)
        phones = []
        phones.append(phone_number) if phone_number else ...
        phones.append(contact) if contact else ...

        restore("role", "使用者", result)
        restore("username", user_name, result)
        restore("nickname", real_name, result)
        restore("phones", phones, result)
        restore("employee_id", employee, result)
        restore("emails", [email], result)
        return result

    def parser_asset_owner(self, owner_info):
        result = {}
        if not owner_info:
            return result
        user_name = extract(owner_info, self.ASSET_USER_NAME)
        real_name = extract(owner_info, self.ASSET_REAL_NAME)

        restore("role", "责任人", result)
        restore("username", user_name, result)
        restore("nickname", real_name, result)
        return result

    def clean_single(self, detail):
        users = []
        login_user = self.parser_login_user(detail.get("login_user"))
        owner = self.parser_asset_owner(detail.get("user"))
        users.append(login_user) if login_user else ...
        users.append(owner) if owner else ...
        return {"wlkj_owners": users}
