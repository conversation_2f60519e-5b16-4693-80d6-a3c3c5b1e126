{"canvas": {"nodes": [{"id": "root", "field": "根节点", "path": "root", "datatype": "object", "type": "asset", "level": 0, "sub_fields": [], "x": 0, "y": 0, "asset_type": "asset", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 100, "id": "root.info", "level": 1, "path": "info", "sub_fields": [{"field": "brand_model", "path": "info.brand_model", "type": "string"}, {"field": "hardware_info", "path": "info.hardware_info", "type": "object", "sub_fields": [{"field": "board", "path": "info.hardware_info.board", "type": "object", "sub_fields": [{"field": "serial_number", "path": "info.hardware_info.board.serial_number", "type": "string"}]}]}], "type": "asset", "datatype": "object", "asset_type": "asset", "field": "info", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 200, "id": "root.info.brand_model", "level": 2, "path": "info.brand_model", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "brand_model", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 300, "id": "root.info.hardware_info", "level": 2, "path": "info.hardware_info", "sub_fields": [{"field": "board", "path": "info.hardware_info.board", "type": "object", "sub_fields": [{"field": "serial_number", "path": "info.hardware_info.board.serial_number", "type": "string", "x": 800, "y": 500}], "x": 600, "y": 400}], "type": "asset", "datatype": "object", "asset_type": "asset", "field": "hardware_info", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 600, "y": 400, "id": "root.info.hardware_info.board", "level": 3, "path": "info.hardware_info.board", "sub_fields": [{"field": "serial_number", "path": "info.hardware_info.board.serial_number", "type": "string", "x": 800, "y": 500}], "type": "asset", "datatype": "object", "asset_type": "asset", "field": "board", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 800, "y": 500, "id": "root.info.hardware_info.board.serial_number", "level": 4, "path": "info.hardware_info.board.serial_number", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "serial_number", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 600, "id": "root.login_user", "level": 1, "path": "login_user", "sub_fields": [{"field": "relation_entity", "path": "login_user.relation_entity", "type": "object", "sub_fields": [{"field": "meta", "path": "login_user.relation_entity.meta", "type": "object", "sub_fields": [{"field": "user_name", "path": "login_user.relation_entity.meta.user_name", "type": "string"}, {"field": "real_name", "path": "login_user.relation_entity.meta.real_name", "type": "string"}, {"field": "mobile_phone", "path": "login_user.relation_entity.meta.mobile_phone", "type": "object", "sub_fields": [{"field": "number", "path": "login_user.relation_entity.meta.mobile_phone.number", "type": "string"}]}, {"field": "email", "path": "login_user.relation_entity.meta.email", "type": "string"}]}, {"field": "info", "path": "login_user.relation_entity.info", "type": "object", "sub_fields": [{"field": "contact", "path": "login_user.relation_entity.info.contact", "type": "string"}, {"field": "employee_number", "path": "login_user.relation_entity.info.employee_number", "type": "string"}]}]}], "type": "asset", "datatype": "object", "asset_type": "asset", "field": "login_user", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 700, "id": "root.login_user.relation_entity", "level": 2, "path": "login_user.relation_entity", "sub_fields": [{"field": "meta", "path": "login_user.relation_entity.meta", "type": "object", "sub_fields": [{"field": "user_name", "path": "login_user.relation_entity.meta.user_name", "type": "string", "x": 800, "y": 900}, {"field": "real_name", "path": "login_user.relation_entity.meta.real_name", "type": "string", "x": 800, "y": 1000}, {"field": "mobile_phone", "path": "login_user.relation_entity.meta.mobile_phone", "type": "object", "sub_fields": [{"field": "number", "path": "login_user.relation_entity.meta.mobile_phone.number", "type": "string", "x": 1000, "y": 1200}], "x": 800, "y": 1100}, {"field": "email", "path": "login_user.relation_entity.meta.email", "type": "string", "x": 800, "y": 1300}], "x": 600, "y": 800}, {"field": "info", "path": "login_user.relation_entity.info", "type": "object", "sub_fields": [{"field": "contact", "path": "login_user.relation_entity.info.contact", "type": "string", "x": 800, "y": 1500}, {"field": "employee_number", "path": "login_user.relation_entity.info.employee_number", "type": "string", "x": 800, "y": 1600}], "x": 600, "y": 1400}], "type": "asset", "datatype": "object", "asset_type": "asset", "field": "relation_entity", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 600, "y": 800, "id": "root.login_user.relation_entity.meta", "level": 3, "path": "login_user.relation_entity.meta", "sub_fields": [{"field": "user_name", "path": "login_user.relation_entity.meta.user_name", "type": "string", "x": 800, "y": 900}, {"field": "real_name", "path": "login_user.relation_entity.meta.real_name", "type": "string", "x": 800, "y": 1000}, {"field": "mobile_phone", "path": "login_user.relation_entity.meta.mobile_phone", "type": "object", "sub_fields": [{"field": "number", "path": "login_user.relation_entity.meta.mobile_phone.number", "type": "string", "x": 1000, "y": 1200}], "x": 800, "y": 1100}, {"field": "email", "path": "login_user.relation_entity.meta.email", "type": "string", "x": 800, "y": 1300}], "type": "asset", "datatype": "object", "asset_type": "asset", "field": "meta", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 800, "y": 900, "id": "root.login_user.relation_entity.meta.user_name", "level": 4, "path": "login_user.relation_entity.meta.user_name", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "user_name", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 800, "y": 1000, "id": "root.login_user.relation_entity.meta.real_name", "level": 4, "path": "login_user.relation_entity.meta.real_name", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "real_name", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 800, "y": 1100, "id": "root.login_user.relation_entity.meta.mobile_phone", "level": 4, "path": "login_user.relation_entity.meta.mobile_phone", "sub_fields": [{"field": "number", "path": "login_user.relation_entity.meta.mobile_phone.number", "type": "string", "x": 1000, "y": 1200}], "type": "asset", "datatype": "object", "asset_type": "asset", "field": "mobile_phone", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 1000, "y": 1200, "id": "root.login_user.relation_entity.meta.mobile_phone.number", "level": 5, "path": "login_user.relation_entity.meta.mobile_phone.number", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "number", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 800, "y": 1300, "id": "root.login_user.relation_entity.meta.email", "level": 4, "path": "login_user.relation_entity.meta.email", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "email", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 600, "y": 1400, "id": "root.login_user.relation_entity.info", "level": 3, "path": "login_user.relation_entity.info", "sub_fields": [{"field": "contact", "path": "login_user.relation_entity.info.contact", "type": "string", "x": 800, "y": 1500}, {"field": "employee_number", "path": "login_user.relation_entity.info.employee_number", "type": "string", "x": 800, "y": 1600}], "type": "asset", "datatype": "object", "asset_type": "asset", "field": "info", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 800, "y": 1500, "id": "root.login_user.relation_entity.info.contact", "level": 4, "path": "login_user.relation_entity.info.contact", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "contact", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 800, "y": 1600, "id": "root.login_user.relation_entity.info.employee_number", "level": 4, "path": "login_user.relation_entity.info.employee_number", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "employee_number", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 1700, "id": "root.meta", "level": 1, "path": "meta", "sub_fields": [{"field": "activation", "path": "meta.activation", "type": "integer"}, {"field": "client_type", "path": "meta.client_type", "type": "string"}, {"field": "computer_working_group", "path": "meta.computer_working_group", "type": "string"}, {"field": "domain", "path": "meta.domain", "type": "string"}, {"field": "ip", "path": "meta.ip", "type": "string"}, {"field": "login_account", "path": "meta.login_account", "type": "string"}, {"field": "mac", "path": "meta.mac", "type": "string"}, {"field": "name", "path": "meta.name", "type": "string"}, {"field": "report_ip", "path": "meta.report_ip", "type": "string"}, {"field": "system_language", "path": "meta.system_language", "type": "string"}, {"field": "tos", "path": "meta.tos", "type": "object", "sub_fields": [{"field": "arch", "path": "meta.tos.arch", "type": "integer"}, {"field": "os", "path": "meta.tos.os", "type": "integer"}, {"field": "version", "path": "meta.tos.version", "type": "object", "sub_fields": [{"field": "kernel_version", "path": "meta.tos.version.kernel_version", "type": "string"}, {"field": "version", "path": "meta.tos.version.version", "type": "string"}]}]}], "type": "asset", "datatype": "object", "asset_type": "asset", "field": "meta", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 1800, "id": "root.meta.activation", "level": 2, "path": "meta.activation", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "activation", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 1900, "id": "root.meta.client_type", "level": 2, "path": "meta.client_type", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "client_type", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 2000, "id": "root.meta.computer_working_group", "level": 2, "path": "meta.computer_working_group", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "computer_working_group", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 2100, "id": "root.meta.domain", "level": 2, "path": "meta.domain", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "domain", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 2200, "id": "root.meta.ip", "level": 2, "path": "meta.ip", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "ip", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 2300, "id": "root.meta.login_account", "level": 2, "path": "meta.login_account", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "login_account", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 2400, "id": "root.meta.mac", "level": 2, "path": "meta.mac", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "mac", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 2500, "id": "root.meta.name", "level": 2, "path": "meta.name", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "name", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 2600, "id": "root.meta.report_ip", "level": 2, "path": "meta.report_ip", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "report_ip", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 2700, "id": "root.meta.system_language", "level": 2, "path": "meta.system_language", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "system_language", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 2800, "id": "root.meta.tos", "level": 2, "path": "meta.tos", "sub_fields": [{"field": "arch", "path": "meta.tos.arch", "type": "integer", "x": 600, "y": 2900}, {"field": "os", "path": "meta.tos.os", "type": "integer", "x": 600, "y": 3000}, {"field": "version", "path": "meta.tos.version", "type": "object", "sub_fields": [{"field": "kernel_version", "path": "meta.tos.version.kernel_version", "type": "string", "x": 800, "y": 3200}, {"field": "version", "path": "meta.tos.version.version", "type": "string", "x": 800, "y": 3300}], "x": 600, "y": 3100}], "type": "asset", "datatype": "object", "asset_type": "asset", "field": "tos", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 600, "y": 2900, "id": "root.meta.tos.arch", "level": 3, "path": "meta.tos.arch", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "arch", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 600, "y": 3000, "id": "root.meta.tos.os", "level": 3, "path": "meta.tos.os", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "os", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 600, "y": 3100, "id": "root.meta.tos.version", "level": 3, "path": "meta.tos.version", "sub_fields": [{"field": "kernel_version", "path": "meta.tos.version.kernel_version", "type": "string", "x": 800, "y": 3200}, {"field": "version", "path": "meta.tos.version.version", "type": "string", "x": 800, "y": 3300}], "type": "asset", "datatype": "object", "asset_type": "asset", "field": "version", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 800, "y": 3200, "id": "root.meta.tos.version.kernel_version", "level": 4, "path": "meta.tos.version.kernel_version", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "kernel_version", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 800, "y": 3300, "id": "root.meta.tos.version.version", "level": 4, "path": "meta.tos.version.version", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "version", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 3400, "id": "root.module_info", "level": 1, "path": "module_info", "sub_fields": [{"field": "main_program_version", "path": "module_info.main_program_version", "type": "string"}, {"field": "patch_version", "path": "module_info.patch_version", "type": "string"}, {"field": "virus_bd_version", "path": "module_info.virus_bd_version", "type": "string"}, {"field": "virus_version", "path": "module_info.virus_version", "type": "string"}], "type": "asset", "datatype": "object", "asset_type": "asset", "field": "module_info", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 3500, "id": "root.module_info.main_program_version", "level": 2, "path": "module_info.main_program_version", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "main_program_version", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 3600, "id": "root.module_info.patch_version", "level": 2, "path": "module_info.patch_version", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "patch_version", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 3700, "id": "root.module_info.virus_bd_version", "level": 2, "path": "module_info.virus_bd_version", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "virus_bd_version", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 3800, "id": "root.module_info.virus_version", "level": 2, "path": "module_info.virus_version", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "virus_version", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 3900, "id": "root.online_info", "level": 1, "path": "online_info", "sub_fields": [{"field": "is_online", "path": "online_info.is_online", "type": "integer"}, {"field": "last_time", "path": "online_info.last_time", "type": "string"}], "type": "asset", "datatype": "object", "asset_type": "asset", "field": "online_info", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 4000, "id": "root.online_info.is_online", "level": 2, "path": "online_info.is_online", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "is_online", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 4100, "id": "root.online_info.last_time", "level": 2, "path": "online_info.last_time", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "last_time", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 4200, "id": "root.user", "level": 1, "path": "user", "sub_fields": [{"field": "relation_entity", "path": "user.relation_entity", "type": "object", "sub_fields": [{"field": "meta", "path": "user.relation_entity.meta", "type": "object", "sub_fields": [{"field": "real_name", "path": "user.relation_entity.meta.real_name", "type": "string"}, {"field": "user_name", "path": "user.relation_entity.meta.user_name", "type": "string"}]}]}], "type": "asset", "datatype": "object", "asset_type": "asset", "field": "user", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 4300, "id": "root.user.relation_entity", "level": 2, "path": "user.relation_entity", "sub_fields": [{"field": "meta", "path": "user.relation_entity.meta", "type": "object", "sub_fields": [{"field": "real_name", "path": "user.relation_entity.meta.real_name", "type": "string", "x": 800, "y": 4500}, {"field": "user_name", "path": "user.relation_entity.meta.user_name", "type": "string", "x": 800, "y": 4600}], "x": 600, "y": 4400}], "type": "asset", "datatype": "object", "asset_type": "asset", "field": "relation_entity", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 600, "y": 4400, "id": "root.user.relation_entity.meta", "level": 3, "path": "user.relation_entity.meta", "sub_fields": [{"field": "real_name", "path": "user.relation_entity.meta.real_name", "type": "string", "x": 800, "y": 4500}, {"field": "user_name", "path": "user.relation_entity.meta.user_name", "type": "string", "x": 800, "y": 4600}], "type": "asset", "datatype": "object", "asset_type": "asset", "field": "meta", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 800, "y": 4500, "id": "root.user.relation_entity.meta.real_name", "level": 4, "path": "user.relation_entity.meta.real_name", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "real_name", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 800, "y": 4600, "id": "root.user.relation_entity.meta.user_name", "level": 4, "path": "user.relation_entity.meta.user_name", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "user_name", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 4700, "id": "root.wlkj_owners", "level": 1, "path": "wlkj_owners", "sub_fields": [{"type": "object", "sub_fields": [{"field": "role", "path": "wlkj_owners.element.role", "type": "string"}, {"field": "username", "path": "wlkj_owners.element.username", "type": "string"}, {"field": "nickname", "path": "wlkj_owners.element.nickname", "type": "string"}, {"field": "phones", "path": "wlkj_owners.element.phones", "type": "list", "sub_fields": [{"type": "string", "field": "element", "path": "wlkj_owners.element.phones.element"}]}, {"field": "employee_id", "path": "wlkj_owners.element.employee_id", "type": "string"}, {"field": "emails", "path": "wlkj_owners.element.emails", "type": "list", "sub_fields": [{"type": "string", "field": "element", "path": "wlkj_owners.element.emails.element"}]}], "field": "element", "path": "wlkj_owners.element"}], "type": "asset", "datatype": "list", "asset_type": "asset", "field": "wlkj_owners", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.info.brand_model.RrZ7HYfVW", "x": 852.40625, "y": 196.49999999999997, "order": 1, "level": 3, "source_type": "string", "path": "root.info.brand_model.RrZ7HYfVW", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "asset_base.device.vendor"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "40_C1p4Ez", "display_name": "管理属性", "description": "asset_base.device.vendor", "x": 1052.40625, "y": 196.49999999999997, "label": "管理属性-设备-供应商", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.info.hardware_info.board.serial_number.kMZqAkdIF", "x": 1240.40625, "y": 496.5, "order": 2, "level": 5, "source_type": "string", "path": "root.info.hardware_info.board.serial_number.kMZqAkdIF", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "computer.hardware.board.sn"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "-UzIXGB7l", "display_name": "计算机", "description": "computer.hardware.board.sn", "x": 1440.40625, "y": 496.5, "label": "计算机-硬件-主板-序列号", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.wlkj_owners.ODySndxLG", "x": 1006.40625, "y": 4696.5, "order": 3, "level": 2, "source_type": "list", "path": "root.wlkj_owners.ODySndxLG", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "for循环", "description": "用于将数组进行循环操作", "field": "for循环", "input": {"asset_web_type": "for", "description": "用于将数组进行循环操作"}, "action_type": "for", "attrs": {"text": "for循环"}}, {"x": 1206.40625, "y": 4796.5, "id": "root.wlkj_owners.ODySndxLG.element", "level": 3, "path": "wlkj_owners.element", "sub_fields": [{"field": "role", "path": "wlkj_owners.element.role", "type": "string", "x": 1406.40625, "y": 4896.5}, {"field": "username", "path": "wlkj_owners.element.username", "type": "string", "x": 1406.40625, "y": 4996.5}, {"field": "nickname", "path": "wlkj_owners.element.nickname", "type": "string", "x": 1406.40625, "y": 5096.5}, {"field": "phones", "path": "wlkj_owners.element.phones", "type": "list", "sub_fields": [{"type": "string", "field": "element", "path": "wlkj_owners.element.phones.element"}], "x": 1406.40625, "y": 5196.5}, {"field": "employee_id", "path": "wlkj_owners.element.employee_id", "type": "string", "x": 1406.40625, "y": 5296.5}, {"field": "emails", "path": "wlkj_owners.element.emails", "type": "list", "sub_fields": [{"type": "string", "field": "element", "path": "wlkj_owners.element.emails.element"}], "x": 1406.40625, "y": 5396.5}], "type": "asset", "datatype": "object", "asset_type": "asset", "field": "element", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 1406.40625, "y": 4896.5, "id": "root.wlkj_owners.ODySndxLG.element.role", "level": 4, "path": "wlkj_owners.element.role", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "role", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 1406.40625, "y": 4996.5, "id": "root.wlkj_owners.ODySndxLG.element.username", "level": 4, "path": "wlkj_owners.element.username", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "username", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 1406.40625, "y": 5096.5, "id": "root.wlkj_owners.ODySndxLG.element.nickname", "level": 4, "path": "wlkj_owners.element.nickname", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "nickname", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 1406.40625, "y": 5196.5, "id": "root.wlkj_owners.ODySndxLG.element.phones", "level": 4, "path": "wlkj_owners.element.phones", "sub_fields": [{"type": "string", "field": "element", "path": "wlkj_owners.element.phones.element"}], "type": "asset", "datatype": "list", "asset_type": "asset", "field": "phones", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 1406.40625, "y": 5296.5, "id": "root.wlkj_owners.ODySndxLG.element.employee_id", "level": 4, "path": "wlkj_owners.element.employee_id", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "employee_id", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 1406.40625, "y": 5396.5, "id": "root.wlkj_owners.ODySndxLG.element.emails", "level": 4, "path": "wlkj_owners.element.emails", "sub_fields": [{"type": "string", "field": "element", "path": "wlkj_owners.element.emails.element"}], "type": "asset", "datatype": "list", "asset_type": "asset", "field": "emails", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.wlkj_owners.ODySndxLG.K_fUmWCqG", "x": 1543.40625, "y": 4695.5, "order": 4, "level": 3, "path": "root.wlkj_owners.ODySndxLG.K_fUmWCqG", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "asset_base.owners"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "TXqQuxdeC", "display_name": "管理属性", "description": "asset_base.owners", "x": 1743.40625, "y": 4695.5, "label": "管理属性-责任人", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.wlkj_owners.ODySndxLG.element.role.KtLbloaG2", "x": 1871.40625, "y": 4892.5, "order": 5, "level": 5, "source_type": "string", "path": "root.wlkj_owners.ODySndxLG.element.role.KtLbloaG2", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "asset_base.owners.role"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "u6tUF9dD2", "display_name": "管理属性", "description": "asset_base.owners.role", "x": 2071.40625, "y": 4892.5, "label": "管理属性-责任人-角色", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.wlkj_owners.ODySndxLG.element.username.t3T6NzZmN", "x": 1873.40625, "y": 5000.5, "order": 6, "level": 5, "source_type": "string", "path": "root.wlkj_owners.ODySndxLG.element.username.t3T6NzZmN", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "asset_base.owners.username"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "Hf3kAFLSp", "display_name": "管理属性", "description": "asset_base.owners.username", "x": 2073.40625, "y": 5000.5, "label": "管理属性-责任人-用户姓名", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.wlkj_owners.ODySndxLG.element.nickname.KMcbnBE6a", "x": 1876.40625, "y": 5096.5, "order": 7, "level": 5, "source_type": "string", "path": "root.wlkj_owners.ODySndxLG.element.nickname.KMcbnBE6a", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "asset_base.owners.nickname"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "542U07e_0", "display_name": "管理属性", "description": "asset_base.owners.nickname", "x": 2076.40625, "y": 5096.5, "label": "管理属性-责任人-姓名", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.wlkj_owners.ODySndxLG.element.phones.4Psmf6SYG", "x": 1879.40625, "y": 5196.5, "order": 8, "level": 5, "source_type": "list", "path": "root.wlkj_owners.ODySndxLG.element.phones.4Psmf6SYG", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "asset_base.owners.phones"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "pinUMGdBr", "display_name": "管理属性", "description": "asset_base.owners.phones", "x": 2079.40625, "y": 5196.5, "label": "管理属性-责任人-电话", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.wlkj_owners.ODySndxLG.element.employee_id.Xk6s5elJ8", "x": 1891.40625, "y": 5295.5, "order": 9, "level": 5, "source_type": "string", "path": "root.wlkj_owners.ODySndxLG.element.employee_id.Xk6s5elJ8", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "asset_base.owners.employee_id"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "_dey7dQjO", "display_name": "管理属性", "description": "asset_base.owners.employee_id", "x": 2091.40625, "y": 5295.5, "label": "管理属性-责任人-工号", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.wlkj_owners.ODySndxLG.element.emails.REj911E_1", "x": 1908.40625, "y": 5396.5, "order": 10, "level": 5, "source_type": "list", "path": "root.wlkj_owners.ODySndxLG.element.emails.REj911E_1", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "asset_base.owners.emails"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "inBWWDrCX", "display_name": "管理属性", "description": "asset_base.owners.emails", "x": 2108.40625, "y": 5396.5, "label": "管理属性-责任人-邮件地址", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.meta.activation.nCXUEh1md", "x": 927.40625, "y": 1794.4999999999993, "order": 11, "level": 3, "source_type": "integer", "path": "root.meta.activation.nCXUEh1md", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "翻译", "description": "用于字段值翻译，常见用于枚举字段", "field": "翻译", "input": {"asset_web_type": "translate", "description": "用于字段值翻译，常见用于枚举字段", "values": [{"name": "true", "value": "1"}, {"name": "false", "value": "2"}], "default": "0"}, "action_type": "translate", "attrs": {"text": "翻译"}}, {"type": "asset", "asset_type": "action", "id": "root.meta.activation.nCXUEh1md.Q-ZZhlRWl", "x": 1248.40625, "y": 1792.4999999999993, "order": 12, "level": 4, "path": "root.meta.activation.nCXUEh1md.Q-ZZhlRWl", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "asset_base.device.status"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "YRkhEMO1x", "display_name": "管理属性", "description": "asset_base.device.status", "x": 1448.40625, "y": 1792.4999999999993, "label": "管理属性-设备-状态", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.meta.computer_working_group.TlNxPB7a7", "x": 946.40625, "y": 1996.5000000000002, "order": 17, "level": 3, "source_type": "string", "path": "root.meta.computer_working_group.TlNxPB7a7", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "computer.domain.workgroup"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "LY7_SIgQF", "display_name": "计算机", "description": "computer.domain.workgroup", "x": 1146.40625, "y": 1996.5000000000002, "label": "计算机-域-名称", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.meta.name.c4In7JN--", "x": 790.40625, "y": 2470.5, "order": 20, "level": 3, "source_type": "string", "path": "root.meta.name.c4In7JN--", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "asset_base.name"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "NY9xOHTfv", "display_name": "管理属性", "description": "asset_base.name", "x": 990.40625, "y": 2470.5, "label": "管理属性-资产名称", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.meta.name.IMxpCXqur", "x": 790.40625, "y": 2533.5, "order": 21, "level": 3, "source_type": "string", "path": "root.meta.name.IMxpCXqur", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "computer.host_name"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "VFaZ7DLbp", "display_name": "计算机", "description": "computer.host_name", "x": 990.40625, "y": 2533.5, "label": "计算机-主机名称", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.0lPDEvsby", "x": 366.40624999999994, "y": -33.**************, "order": 22, "level": 1, "source_type": "object", "path": "root.0lPDEvsby", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "生成对象数组", "description": "生成对象数组", "field": "生成对象数组", "input": {"asset_web_type": "add_list_object", "description": "生成对象数组", "size": "1", "field": "_a", "mapping": [{"name": "name", "value": "${meta.login_account}"}]}, "action_type": "add_list_object", "attrs": {"text": "生成对象数组"}}, {"type": "asset", "asset_type": "action", "id": "root.0lPDEvsby.XLU2DF87p", "x": 529.40625, "y": -34.**************, "order": 23, "level": 2, "path": "root.0lPDEvsby.XLU2DF87p", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "computer.accounts"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "k9ntS4tRQ", "display_name": "计算机", "description": "computer.accounts", "x": 676.40625, "y": -36.**************, "label": "计算机-账号", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.meta.system_language.zi86MLYxP", "x": 893.40625, "y": 2699.5, "order": 24, "level": 3, "source_type": "string", "path": "root.meta.system_language.zi86MLYxP", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "computer.os.lang"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "CfrPfVg1Q", "display_name": "计算机", "description": "computer.os.lang", "x": 1093.40625, "y": 2699.5, "label": "计算机-操作系统-语言", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.meta.tos.os.1TjoOMn4N", "x": 1046.40625, "y": 2992.5000000000005, "order": 25, "level": 4, "source_type": "integer", "path": "root.meta.tos.os.1TjoOMn4N", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "翻译", "description": "用于字段值翻译，常见用于枚举字段", "field": "翻译", "input": {"asset_web_type": "translate", "description": "用于字段值翻译，常见用于枚举字段", "values": [{"name": "0", "value": "UNKNOWN_OS"}, {"name": "1", "value": "WINDOWS"}, {"name": "2", "value": "LINUX"}, {"name": "3", "value": "MACOS"}, {"name": "4", "value": "ANDROID"}, {"name": "5", "value": "IOS"}, {"name": "6", "value": "HARMONYOS"}]}, "action_type": "translate", "attrs": {"text": "翻译"}}, {"type": "asset", "asset_type": "action", "id": "root.meta.tos.os.1TjoOMn4N.ACfLU_6Lt", "x": 1315.40625, "y": 2992.5000000000005, "order": 26, "level": 5, "path": "root.meta.tos.os.1TjoOMn4N.ACfLU_6Lt", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "computer.os.type"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "x8Q1IJnPX", "display_name": "计算机", "description": "computer.os.type", "x": 1515.40625, "y": 2992.5000000000005, "label": "计算机-操作系统-类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.meta.tos.arch.LrmKvDkZu", "x": 1011.40625, "y": 2889.5000000000023, "order": 27, "level": 4, "source_type": "integer", "path": "root.meta.tos.arch.LrmKvDkZu", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "翻译", "description": "用于字段值翻译，常见用于枚举字段", "field": "翻译", "input": {"asset_web_type": "translate", "description": "用于字段值翻译，常见用于枚举字段", "values": [{"name": "1", "value": "86"}, {"name": "2", "value": "64"}, {"name": "4", "value": "64"}, {"name": "6", "value": "64"}]}, "action_type": "translate", "attrs": {"text": "翻译"}}, {"type": "asset", "asset_type": "action", "id": "root.meta.tos.arch.LrmKvDkZu.joTZlr73x", "x": 1318.40625, "y": 2892.5000000000023, "order": 28, "level": 5, "path": "root.meta.tos.arch.LrmKvDkZu.joTZlr73x", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "computer.os.bitness"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "jdv-vmqJT", "display_name": "计算机", "description": "computer.os.bitness", "x": 1518.40625, "y": 2892.5000000000023, "label": "计算机-操作系统-位数", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.meta.tos.version.kernel_version.aVSWYl7Y6", "x": 1172.40625, "y": 3197.499999999999, "order": 29, "level": 5, "source_type": "string", "path": "root.meta.tos.version.kernel_version.aVSWYl7Y6", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "computer.os.kernel_version"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "AE0CXLa8b", "display_name": "计算机", "description": "computer.os.kernel_version", "x": 1372.40625, "y": 3197.499999999999, "label": "计算机-操作系统-内核版本", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.meta.tos.version.version.oAKXloEe9", "x": 1171.40625, "y": 3299.4999999999995, "order": 30, "level": 5, "source_type": "string", "path": "root.meta.tos.version.version.oAKXloEe9", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "computer.os.version"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "HV3goihVq", "display_name": "计算机", "description": "computer.os.version", "x": 1371.40625, "y": 3299.4999999999995, "label": "计算机-操作系统-发行版本", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.online_info.last_time.onovyxboe", "x": 802.40625, "y": 4087.499999999998, "order": 31, "level": 3, "source_type": "string", "path": "root.online_info.last_time.onovyxboe", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "base.agents.last_online_time"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "87x15ROeG", "display_name": "基础", "description": "base.agents.last_online_time", "x": 1002.40625, "y": 4087.499999999998, "label": "基础-代理-最近一次在线时间", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.online_info.is_online.RyRZALfMS", "x": 732.40625, "y": 4000.499999999998, "order": 32, "level": 3, "source_type": "integer", "path": "root.online_info.is_online.RyRZALfMS", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "翻译", "description": "用于字段值翻译，常见用于枚举字段", "field": "翻译", "input": {"asset_web_type": "translate", "description": "用于字段值翻译，常见用于枚举字段", "values": [{"name": "true", "value": "1"}, {"name": "false", "value": "2"}], "default": "0"}, "action_type": "translate", "attrs": {"text": "翻译"}}, {"type": "asset", "asset_type": "action", "id": "root.online_info.is_online.RyRZALfMS._Pqk1rl6t", "x": 994.40625, "y": 4000.499999999998, "order": 33, "level": 4, "path": "root.online_info.is_online.RyRZALfMS._Pqk1rl6t", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "base.agents.status"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "LRh8I8khV", "display_name": "基础", "description": "base.agents.status", "x": 1194.40625, "y": 4000.499999999998, "label": "基础-代理-状态", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.module_info.virus_version.BraL2b6qX", "x": 760.40625, "y": 3794.5000000000005, "order": 1, "level": 3, "source_type": "string", "path": "root.module_info.virus_version.BraL2b6qX", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "vulnerability.virus.libs.version"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "WM-PBh_Zq", "display_name": "脆弱性", "description": "vulnerability.virus.libs.version", "x": 960.40625, "y": 3794.5000000000005, "label": "脆弱性-病毒-病毒库信息-版本", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.module_info.patch_version.WVWQBaajB", "x": 851.40625, "y": 3611.499999999999, "order": 2, "level": 3, "source_type": "string", "path": "root.module_info.patch_version.WVWQBaajB", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "computer.patchs.version"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "eUufAGq8t", "display_name": "计算机", "description": "computer.patchs.version", "x": 1051.40625, "y": 3611.499999999999, "label": "计算机-补丁-版本号", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.IJntoVqjA", "x": 349.0859375, "y": -440.9999999999998, "order": 17, "level": 1, "source_type": "object", "path": "root.IJntoVqjA", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "新增字段", "description": "新增字段，字段值必须是字符串类型", "field": "新增字段", "input": {"asset_web_type": "add", "description": "新增字段，字段值必须是字符串类型", "field": "_mac", "values": ["${meta.mac}"]}, "action_type": "add", "attrs": {"text": "新增字段"}}, {"type": "asset", "asset_type": "action", "id": "root.IJntoVqjA.uzQ9MD7Kp", "x": 534.0859375, "y": -444.9999999999998, "order": 18, "level": 2, "path": "root.IJntoVqjA.uzQ9MD7Kp", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "替换(精确)", "description": "替换指定内容", "field": "替换(精确)", "input": {"asset_web_type": "replace", "description": "替换指定内容", "src_value": "-", "dst_value": ":"}, "action_type": "replace", "attrs": {"text": "替换(精确)"}}, {"type": "asset", "asset_type": "action", "id": "root.aQMeUswTM", "x": 351.0859375, "y": -367.9999999999998, "order": 19, "level": 1, "source_type": "object", "path": "root.aQMeUswTM", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "生成对象数组", "description": "生成对象数组", "field": "生成对象数组", "input": {"asset_web_type": "add_list_object", "description": "生成对象数组", "size": "_1", "field": "_interfaces", "mapping": [{"name": "mac", "value": "${_mac}"}]}, "action_type": "add_list_object", "attrs": {"text": "生成对象数组"}}, {"type": "asset", "asset_type": "action", "id": "root.aQMeUswTM.7f7iPgUr5", "x": 530.0859375, "y": -368.9999999999998, "order": 20, "level": 2, "path": "root.aQMeUswTM.7f7iPgUr5", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "network.interfaces"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "s-2bi84RD", "display_name": "网络", "description": "network.interfaces", "x": 730.0859375, "y": -368.9999999999998, "label": "网络-网口", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.kNJV70UYu", "x": 351.0859375, "y": -302.9999999999998, "order": 21, "level": 1, "source_type": "object", "path": "root.kNJV70UYu", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "生成对象数组", "description": "生成对象数组", "field": "生成对象数组", "input": {"asset_web_type": "add_list_object", "description": "生成对象数组", "size": "1", "field": "_ip", "mapping": [{"name": "mac", "value": "${_mac}"}, {"name": "ip", "value": "${meta.ip}"}]}, "action_type": "add_list_object", "attrs": {"text": "生成对象数组"}}, {"type": "asset", "asset_type": "action", "id": "root.kNJV70UYu.HnUXLVNW-", "x": 535.0859375, "y": -304.9999999999998, "order": 22, "level": 2, "path": "root.kNJV70UYu.HnUXLVNW-", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "network.ips"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "zjySkVTvU", "display_name": "网络", "description": "network.ips", "x": 735.0859375, "y": -304.9999999999998, "label": "网络-IP地址", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 4800, "id": "root.is_itai", "level": 1, "path": "is_itai", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "is_itai", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.is_itai.SkPV_iWeh", "x": 465.52545343697597, "y": 4800, "order": 2, "level": 2, "source_type": "integer", "path": "root.is_itai.SkPV_iWeh", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "asset_base.itai"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "HledXl7u4", "display_name": "管理属性", "description": "asset_base.itai", "x": 665.525453436976, "y": 4800, "label": "管理属性-是否信创", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}], "edges": [{"source": "root", "target": "root.info", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.85587599470901021711337177479", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 100, "anchor_index": 0}}, {"source": "root.info", "target": "root.info.brand_model", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.65206006938202781711337177480", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 125.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 200, "anchor_index": 0}}, {"source": "root.info", "target": "root.info.hardware_info", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.53791832377264331711337177480", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 125.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 300, "anchor_index": 0}}, {"source": "root.info.hardware_info", "target": "root.info.hardware_info.board", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.69039459521775351711337177480", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 400, "y": 325.5, "anchor_index": 1}, "end_point": {"x": 549.5, "y": 400, "anchor_index": 0}}, {"source": "root.info.hardware_info.board", "target": "root.info.hardware_info.board.serial_number", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.8329687026375981711337177480", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 600, "y": 425.5, "anchor_index": 1}, "end_point": {"x": 749.5, "y": 500, "anchor_index": 0}}, {"source": "root", "target": "root.login_user", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.92921513709351441711337177480", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 600, "anchor_index": 0}}, {"source": "root.login_user", "target": "root.login_user.relation_entity", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.1569495280011831711337177480", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 625.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 700, "anchor_index": 0}}, {"source": "root.login_user.relation_entity", "target": "root.login_user.relation_entity.meta", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.84428740941719041711337177480", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 400, "y": 725.5, "anchor_index": 1}, "end_point": {"x": 549.5, "y": 800, "anchor_index": 0}}, {"source": "root.login_user.relation_entity.meta", "target": "root.login_user.relation_entity.meta.user_name", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.64183978189433691711337177480", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 600, "y": 825.5, "anchor_index": 1}, "end_point": {"x": 749.5, "y": 900, "anchor_index": 0}}, {"source": "root.login_user.relation_entity.meta", "target": "root.login_user.relation_entity.meta.real_name", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.75868651193353241711337177480", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 600, "y": 825.5, "anchor_index": 1}, "end_point": {"x": 749.5, "y": 1000, "anchor_index": 0}}, {"source": "root.login_user.relation_entity.meta", "target": "root.login_user.relation_entity.meta.mobile_phone", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.24043933860143631711337177480", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 600, "y": 825.5, "anchor_index": 1}, "end_point": {"x": 749.5, "y": 1100, "anchor_index": 0}}, {"source": "root.login_user.relation_entity.meta.mobile_phone", "target": "root.login_user.relation_entity.meta.mobile_phone.number", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.082449426261872681711337177480", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 800, "y": 1125.5, "anchor_index": 1}, "end_point": {"x": 949.5, "y": 1200, "anchor_index": 0}}, {"source": "root.login_user.relation_entity.meta", "target": "root.login_user.relation_entity.meta.email", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.55518038883440381711337177480", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 600, "y": 825.5, "anchor_index": 1}, "end_point": {"x": 749.5, "y": 1300, "anchor_index": 0}}, {"source": "root.login_user.relation_entity", "target": "root.login_user.relation_entity.info", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.78373376447713031711337177481", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 400, "y": 725.5, "anchor_index": 1}, "end_point": {"x": 549.5, "y": 1400, "anchor_index": 0}}, {"source": "root.login_user.relation_entity.info", "target": "root.login_user.relation_entity.info.contact", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.70447015150159211711337177481", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 600, "y": 1425.5, "anchor_index": 1}, "end_point": {"x": 749.5, "y": 1500, "anchor_index": 0}}, {"source": "root.login_user.relation_entity.info", "target": "root.login_user.relation_entity.info.employee_number", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.66778479052515191711337177481", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 600, "y": 1425.5, "anchor_index": 1}, "end_point": {"x": 749.5, "y": 1600, "anchor_index": 0}}, {"source": "root", "target": "root.meta", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.62805244251616641711337177481", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 1700, "anchor_index": 0}}, {"source": "root.meta", "target": "root.meta.activation", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.5862462055806991711337177481", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 1725.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 1800, "anchor_index": 0}}, {"source": "root.meta", "target": "root.meta.client_type", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.658000543099791711337177481", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 1725.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 1900, "anchor_index": 0}}, {"source": "root.meta", "target": "root.meta.computer_working_group", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.86724255997437781711337177481", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 1725.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 2000, "anchor_index": 0}}, {"source": "root.meta", "target": "root.meta.domain", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.090595409856825791711337177481", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 1725.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 2100, "anchor_index": 0}}, {"source": "root.meta", "target": "root.meta.ip", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.0281171565876691711337177481", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 1725.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 2200, "anchor_index": 0}}, {"source": "root.meta", "target": "root.meta.login_account", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.031977545550570641711337177481", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 1725.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 2300, "anchor_index": 0}}, {"source": "root.meta", "target": "root.meta.mac", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.31406609689955611711337177481", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 1725.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 2400, "anchor_index": 0}}, {"source": "root.meta", "target": "root.meta.name", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.06275871784933541711337177481", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 1725.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 2500, "anchor_index": 0}}, {"source": "root.meta", "target": "root.meta.report_ip", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.464406754161547531711337177481", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 1725.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 2600, "anchor_index": 0}}, {"source": "root.meta", "target": "root.meta.system_language", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.46938563829862991711337177481", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 1725.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 2700, "anchor_index": 0}}, {"source": "root.meta", "target": "root.meta.tos", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.53653461093317481711337177481", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 1725.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 2800, "anchor_index": 0}}, {"source": "root.meta.tos", "target": "root.meta.tos.arch", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.58359436881066081711337177481", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 400, "y": 2825.5, "anchor_index": 1}, "end_point": {"x": 549.5, "y": 2900, "anchor_index": 0}}, {"source": "root.meta.tos", "target": "root.meta.tos.os", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.76840649197835581711337177481", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 400, "y": 2825.5, "anchor_index": 1}, "end_point": {"x": 549.5, "y": 3000, "anchor_index": 0}}, {"source": "root.meta.tos", "target": "root.meta.tos.version", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.56148765908021471711337177481", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 400, "y": 2825.5, "anchor_index": 1}, "end_point": {"x": 549.5, "y": 3100, "anchor_index": 0}}, {"source": "root.meta.tos.version", "target": "root.meta.tos.version.kernel_version", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.70824825682946591711337177481", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 600, "y": 3125.5, "anchor_index": 1}, "end_point": {"x": 749.5, "y": 3200, "anchor_index": 0}}, {"source": "root.meta.tos.version", "target": "root.meta.tos.version.version", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.051791813079500091711337177481", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 600, "y": 3125.5, "anchor_index": 1}, "end_point": {"x": 749.5, "y": 3300, "anchor_index": 0}}, {"source": "root", "target": "root.module_info", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.55756082697537561711337177481", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 3400, "anchor_index": 0}}, {"source": "root.module_info", "target": "root.module_info.main_program_version", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.111513331274344371711337177481", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 3425.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 3500, "anchor_index": 0}}, {"source": "root.module_info", "target": "root.module_info.patch_version", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.61307216377901111711337177481", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 3425.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 3600, "anchor_index": 0}}, {"source": "root.module_info", "target": "root.module_info.virus_bd_version", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.9289182935385291711337177482", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 3425.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 3700, "anchor_index": 0}}, {"source": "root.module_info", "target": "root.module_info.virus_version", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.72279128814849921711337177482", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 3425.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 3800, "anchor_index": 0}}, {"source": "root", "target": "root.online_info", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.71913134160712991711337177482", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 3900, "anchor_index": 0}}, {"source": "root.online_info", "target": "root.online_info.is_online", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.77118513196961211711337177482", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 3925.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 4000, "anchor_index": 0}}, {"source": "root.online_info", "target": "root.online_info.last_time", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.74016603830231521711337177482", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 3925.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 4100, "anchor_index": 0}}, {"source": "root", "target": "root.user", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.6335031810351211711337177482", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 4200, "anchor_index": 0}}, {"source": "root.user", "target": "root.user.relation_entity", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.431502394902461451711337177482", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 4225.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 4300, "anchor_index": 0}}, {"source": "root.user.relation_entity", "target": "root.user.relation_entity.meta", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.11287234013234661711337177482", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 400, "y": 4325.5, "anchor_index": 1}, "end_point": {"x": 549.5, "y": 4400, "anchor_index": 0}}, {"source": "root.user.relation_entity.meta", "target": "root.user.relation_entity.meta.real_name", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.23138049694576491711337177482", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 600, "y": 4425.5, "anchor_index": 1}, "end_point": {"x": 749.5, "y": 4500, "anchor_index": 0}}, {"source": "root.user.relation_entity.meta", "target": "root.user.relation_entity.meta.user_name", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.103720693535369831711337177482", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 600, "y": 4425.5, "anchor_index": 1}, "end_point": {"x": 749.5, "y": 4600, "anchor_index": 0}}, {"source": "root", "target": "root.wlkj_owners", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.65360123240095351711337177482", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 4700, "anchor_index": 0}}, {"source": "root.info.brand_model", "target": "root.info.brand_model.RrZ7HYfVW", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.120330767274431151711337192509", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 450.5, "y": 200, "anchor_index": 1}, "end_point": {"x": 801.90625, "y": 196.49999999999997, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.info.brand_model.RrZ7HYfVW", "target": "40_C1p4Ez", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.0215070088370403761711337227069", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 902.90625, "y": 196.49999999999997, "anchor_index": 1}, "end_point": {"x": 1001.90625, "y": 196.49999999999997, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.info.hardware_info.board.serial_number", "target": "root.info.hardware_info.board.serial_number.kMZqAkdIF", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.31592941485936591711337232374", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 850.5, "y": 500, "anchor_index": 1}, "end_point": {"x": 1189.90625, "y": 496.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.info.hardware_info.board.serial_number.kMZqAkdIF", "target": "-UzIXGB7l", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.68667675771625161711337249679", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1290.90625, "y": 496.5, "anchor_index": 1}, "end_point": {"x": 1389.90625, "y": 496.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.wlkj_owners", "target": "root.wlkj_owners.ODySndxLG", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.58623478607207421711337262320", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 4700, "anchor_index": 1}, "end_point": {"x": 955.90625, "y": 4696.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.wlkj_owners.ODySndxLG", "target": "root.wlkj_owners.ODySndxLG.element", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.66288068184234141711337267485", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1056.90625, "y": 4696.5, "anchor_index": 1}, "end_point": {"x": 1155.90625, "y": 4796.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.wlkj_owners.ODySndxLG.element", "target": "root.wlkj_owners.ODySndxLG.element.role", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.9824780823202491711337267486", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1206.40625, "y": 4822, "anchor_index": 1}, "end_point": {"x": 1355.90625, "y": 4896.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.wlkj_owners.ODySndxLG.element", "target": "root.wlkj_owners.ODySndxLG.element.username", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.84636539714561331711337267486", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1206.40625, "y": 4822, "anchor_index": 1}, "end_point": {"x": 1355.90625, "y": 4996.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.wlkj_owners.ODySndxLG.element", "target": "root.wlkj_owners.ODySndxLG.element.nickname", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.348436531661684871711337267486", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1206.40625, "y": 4822, "anchor_index": 1}, "end_point": {"x": 1355.90625, "y": 5096.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.wlkj_owners.ODySndxLG.element", "target": "root.wlkj_owners.ODySndxLG.element.phones", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.320499041853031711337267486", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1206.40625, "y": 4822, "anchor_index": 1}, "end_point": {"x": 1355.90625, "y": 5196.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.wlkj_owners.ODySndxLG.element", "target": "root.wlkj_owners.ODySndxLG.element.employee_id", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.06599284515680571711337267486", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1206.40625, "y": 4822, "anchor_index": 1}, "end_point": {"x": 1355.90625, "y": 5296.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.wlkj_owners.ODySndxLG.element", "target": "root.wlkj_owners.ODySndxLG.element.emails", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.5772647639668041711337267486", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1206.40625, "y": 4822, "anchor_index": 1}, "end_point": {"x": 1355.90625, "y": 5396.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.wlkj_owners.ODySndxLG", "target": "root.wlkj_owners.ODySndxLG.K_fUmWCqG", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.68624236274378681711337270946", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1056.90625, "y": 4696.5, "anchor_index": 1}, "end_point": {"x": 1492.90625, "y": 4695.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.wlkj_owners.ODySndxLG.K_fUmWCqG", "target": "TXqQuxdeC", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.101689116020638791711337278886", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1593.90625, "y": 4695.5, "anchor_index": 1}, "end_point": {"x": 1692.90625, "y": 4695.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.wlkj_owners.ODySndxLG.element.role", "target": "root.wlkj_owners.ODySndxLG.element.role.KtLbloaG2", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.114256304289247931711337284832", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1456.90625, "y": 4896.5, "anchor_index": 1}, "end_point": {"x": 1820.90625, "y": 4892.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.wlkj_owners.ODySndxLG.element.role.KtLbloaG2", "target": "u6tUF9dD2", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.258915083669061241711337293196", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1921.90625, "y": 4892.5, "anchor_index": 1}, "end_point": {"x": 2020.90625, "y": 4892.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.wlkj_owners.ODySndxLG.element.username", "target": "root.wlkj_owners.ODySndxLG.element.username.t3T6NzZmN", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.56420246924945381711337296221", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1456.90625, "y": 4996.5, "anchor_index": 1}, "end_point": {"x": 1822.90625, "y": 5000.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.wlkj_owners.ODySndxLG.element.username.t3T6NzZmN", "target": "Hf3kAFLSp", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.87143151165524621711337314941", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1923.90625, "y": 5000.5, "anchor_index": 1}, "end_point": {"x": 2022.90625, "y": 5000.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.wlkj_owners.ODySndxLG.element.nickname", "target": "root.wlkj_owners.ODySndxLG.element.nickname.KMcbnBE6a", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.73914274239697961711337318986", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1456.90625, "y": 5096.5, "anchor_index": 1}, "end_point": {"x": 1825.90625, "y": 5096.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.wlkj_owners.ODySndxLG.element.nickname.KMcbnBE6a", "target": "542U07e_0", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.221255267635664721711337324995", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1926.90625, "y": 5096.5, "anchor_index": 1}, "end_point": {"x": 2025.90625, "y": 5096.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.wlkj_owners.ODySndxLG.element.phones", "target": "root.wlkj_owners.ODySndxLG.element.phones.4Psmf6SYG", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.79257918997810981711337328639", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1456.90625, "y": 5196.5, "anchor_index": 1}, "end_point": {"x": 1828.90625, "y": 5196.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.wlkj_owners.ODySndxLG.element.phones.4Psmf6SYG", "target": "pinUMGdBr", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.0496454049478418651711337337188", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1929.90625, "y": 5196.5, "anchor_index": 1}, "end_point": {"x": 2028.90625, "y": 5196.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.wlkj_owners.ODySndxLG.element.employee_id", "target": "root.wlkj_owners.ODySndxLG.element.employee_id.Xk6s5elJ8", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.76813691350388111711337339535", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1456.90625, "y": 5296.5, "anchor_index": 1}, "end_point": {"x": 1840.90625, "y": 5295.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.wlkj_owners.ODySndxLG.element.employee_id.Xk6s5elJ8", "target": "_dey7dQjO", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.175441228024410871711337348558", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1941.90625, "y": 5295.5, "anchor_index": 1}, "end_point": {"x": 2040.90625, "y": 5295.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.wlkj_owners.ODySndxLG.element.emails", "target": "root.wlkj_owners.ODySndxLG.element.emails.REj911E_1", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.62338071241793221711337350556", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1456.90625, "y": 5396.5, "anchor_index": 1}, "end_point": {"x": 1857.90625, "y": 5396.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.wlkj_owners.ODySndxLG.element.emails.REj911E_1", "target": "inBWWDrCX", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.21825879647744031711337361203", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1958.90625, "y": 5396.5, "anchor_index": 1}, "end_point": {"x": 2057.90625, "y": 5396.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.meta.activation", "target": "root.meta.activation.nCXUEh1md", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.062258702727025161711337408780", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 450.5, "y": 1800, "anchor_index": 1}, "end_point": {"x": 876.90625, "y": 1794.4999999999993, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.meta.activation.nCXUEh1md", "target": "root.meta.activation.nCXUEh1md.Q-ZZhlRWl", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.9279241944731661711337439031", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 977.90625, "y": 1794.4999999999993, "anchor_index": 1}, "end_point": {"x": 1197.90625, "y": 1792.4999999999993, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.meta.activation.nCXUEh1md.Q-ZZhlRWl", "target": "YRkhEMO1x", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.159564427473092521711337447416", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1298.90625, "y": 1792.4999999999993, "anchor_index": 1}, "end_point": {"x": 1397.90625, "y": 1792.4999999999993, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.meta.computer_working_group", "target": "root.meta.computer_working_group.TlNxPB7a7", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.16160588257542141711338206305", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 450.5, "y": 2000, "anchor_index": 1}, "end_point": {"x": 895.90625, "y": 1996.5000000000002, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.meta.computer_working_group.TlNxPB7a7", "target": "LY7_SIgQF", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.86543026276401111711338230640", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 996.90625, "y": 1996.5000000000002, "anchor_index": 1}, "end_point": {"x": 1095.90625, "y": 1996.5000000000002, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.meta.name", "target": "root.meta.name.c4In7JN--", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.373874997581456371711338437136", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 450.5, "y": 2500, "anchor_index": 1}, "end_point": {"x": 739.90625, "y": 2470.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.meta.name.c4In7JN--", "target": "NY9xOHTfv", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.450225718636270371711338448031", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 840.90625, "y": 2470.5, "anchor_index": 1}, "end_point": {"x": 939.90625, "y": 2470.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.meta.name", "target": "root.meta.name.IMxpCXqur", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.52312662167935021711338452972", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 450.5, "y": 2500, "anchor_index": 1}, "end_point": {"x": 739.90625, "y": 2533.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.meta.name.IMxpCXqur", "target": "VFaZ7DLbp", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.477213185406825341711338469554", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 840.90625, "y": 2533.5, "anchor_index": 1}, "end_point": {"x": 939.90625, "y": 2533.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root", "target": "root.0lPDEvsby", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.1336745701834091711338485872", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 50.5, "y": 0, "anchor_index": 1}, "end_point": {"x": 315.90624999999994, "y": -33.**************, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.0lPDEvsby", "target": "root.0lPDEvsby.XLU2DF87p", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.36126900259748651711338546864", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 416.90624999999994, "y": -33.**************, "anchor_index": 1}, "end_point": {"x": 478.90625, "y": -34.**************, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.0lPDEvsby.XLU2DF87p", "target": "k9ntS4tRQ", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.439867724106345341711338554625", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 579.90625, "y": -34.**************, "anchor_index": 1}, "end_point": {"x": 625.90625, "y": -36.**************, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.meta.system_language", "target": "root.meta.system_language.zi86MLYxP", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.175701538489284381711338565907", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 450.5, "y": 2700, "anchor_index": 1}, "end_point": {"x": 842.90625, "y": 2699.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.meta.system_language.zi86MLYxP", "target": "CfrPfVg1Q", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.93542983224768641711338575275", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 943.90625, "y": 2699.5, "anchor_index": 1}, "end_point": {"x": 1042.90625, "y": 2699.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.meta.tos.os", "target": "root.meta.tos.os.1TjoOMn4N", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.49209917483512181711338661489", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 650.5, "y": 3000, "anchor_index": 1}, "end_point": {"x": 995.90625, "y": 2992.5000000000005, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.meta.tos.os.1TjoOMn4N", "target": "root.meta.tos.os.1TjoOMn4N.ACfLU_6Lt", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.155409201443620581711338743683", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1096.90625, "y": 2992.5000000000005, "anchor_index": 1}, "end_point": {"x": 1264.90625, "y": 2992.5000000000005, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.meta.tos.os.1TjoOMn4N.ACfLU_6Lt", "target": "x8Q1IJnPX", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.6261148667884161711338751488", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1365.90625, "y": 2992.5000000000005, "anchor_index": 1}, "end_point": {"x": 1464.90625, "y": 2992.5000000000005, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.meta.tos.arch", "target": "root.meta.tos.arch.LrmKvDkZu", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.197608677192313121711338764007", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 650.5, "y": 2900, "anchor_index": 1}, "end_point": {"x": 960.90625, "y": 2889.5000000000023, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.meta.tos.arch.LrmKvDkZu", "target": "root.meta.tos.arch.LrmKvDkZu.joTZlr73x", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.72282352171018751711338882288", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1061.90625, "y": 2889.5000000000023, "anchor_index": 1}, "end_point": {"x": 1267.90625, "y": 2892.5000000000023, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.meta.tos.arch.LrmKvDkZu.joTZlr73x", "target": "jdv-vmqJT", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.18828020239651091711338980641", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1368.90625, "y": 2892.5000000000023, "anchor_index": 1}, "end_point": {"x": 1467.90625, "y": 2892.5000000000023, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.meta.tos.version.kernel_version", "target": "root.meta.tos.version.kernel_version.aVSWYl7Y6", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.107922816549263391711339004902", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 850.5, "y": 3200, "anchor_index": 1}, "end_point": {"x": 1121.90625, "y": 3197.499999999999, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.meta.tos.version.kernel_version.aVSWYl7Y6", "target": "AE0CXLa8b", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.158502731346734651711339015356", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1222.90625, "y": 3197.499999999999, "anchor_index": 1}, "end_point": {"x": 1321.90625, "y": 3197.499999999999, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.meta.tos.version.version", "target": "root.meta.tos.version.version.oAKXloEe9", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.45940548480360531711339037539", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 850.5, "y": 3300, "anchor_index": 1}, "end_point": {"x": 1120.90625, "y": 3299.4999999999995, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.meta.tos.version.version.oAKXloEe9", "target": "HV3goihVq", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.60554576424284751711339063820", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1221.90625, "y": 3299.4999999999995, "anchor_index": 1}, "end_point": {"x": 1320.90625, "y": 3299.4999999999995, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.online_info.last_time", "target": "root.online_info.last_time.onovyxboe", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.417918298419325261711339104432", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 450.5, "y": 4100, "anchor_index": 1}, "end_point": {"x": 751.90625, "y": 4087.499999999998, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.online_info.last_time.onovyxboe", "target": "87x15ROeG", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.70907911742305441711339131647", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 852.90625, "y": 4087.499999999998, "anchor_index": 1}, "end_point": {"x": 951.90625, "y": 4087.499999999998, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.online_info.is_online", "target": "root.online_info.is_online.RyRZALfMS", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.94946244936182181711339144875", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 450.5, "y": 4000, "anchor_index": 1}, "end_point": {"x": 681.90625, "y": 4000.499999999998, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.online_info.is_online.RyRZALfMS", "target": "root.online_info.is_online.RyRZALfMS._Pqk1rl6t", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.68683696684010951711339184033", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 782.90625, "y": 4000.499999999998, "anchor_index": 1}, "end_point": {"x": 943.90625, "y": 4000.499999999998, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.online_info.is_online.RyRZALfMS._Pqk1rl6t", "target": "LRh8I8khV", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.481162682994811461711339195071", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1044.90625, "y": 4000.499999999998, "anchor_index": 1}, "end_point": {"x": 1143.90625, "y": 4000.499999999998, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.module_info.virus_version", "target": "root.module_info.virus_version.BraL2b6qX", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.57072319275006361711346519530", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 450.5, "y": 3800, "anchor_index": 1}, "end_point": {"x": 709.90625, "y": 3794.5000000000005, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.module_info.virus_version.BraL2b6qX", "target": "WM-PBh_Zq", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.63875252957600081711346536505", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 810.90625, "y": 3794.5000000000005, "anchor_index": 1}, "end_point": {"x": 909.90625, "y": 3794.5000000000005, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.module_info.patch_version", "target": "root.module_info.patch_version.WVWQBaajB", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.088453917717987721711346564324", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 450.5, "y": 3600, "anchor_index": 1}, "end_point": {"x": 800.90625, "y": 3611.499999999999, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.module_info.patch_version.WVWQBaajB", "target": "eUufAGq8t", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.81531792860622491711346581455", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 901.90625, "y": 3611.499999999999, "anchor_index": 1}, "end_point": {"x": 1000.90625, "y": 3611.499999999999, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root", "target": "root.IJntoVqjA", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.92561672964963981714026130163", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 50.5, "y": 0, "anchor_index": 1}, "end_point": {"x": 298.5859375, "y": -440.9999999999998, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.IJntoVqjA", "target": "root.IJntoVqjA.uzQ9MD7Kp", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.54107885238171161714026155403", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 399.5859375, "y": -440.9999999999998, "anchor_index": 1}, "end_point": {"x": 483.5859375, "y": -444.9999999999998, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root", "target": "root.aQMeUswTM", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.0248153339520935661714026207608", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 50.5, "y": 0, "anchor_index": 1}, "end_point": {"x": 300.5859375, "y": -367.9999999999998, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.aQMeUswTM", "target": "root.aQMeUswTM.7f7iPgUr5", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.44980776929135491714026225210", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 401.5859375, "y": -367.9999999999998, "anchor_index": 1}, "end_point": {"x": 479.5859375, "y": -368.9999999999998, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.aQMeUswTM.7f7iPgUr5", "target": "s-2bi84RD", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.63342721516036661714026232018", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 580.5859375, "y": -368.9999999999998, "anchor_index": 1}, "end_point": {"x": 679.5859375, "y": -368.9999999999998, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root", "target": "root.kNJV70UYu", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.96715355307561431714026235658", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 50.5, "y": 0, "anchor_index": 1}, "end_point": {"x": 300.5859375, "y": -302.9999999999998, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.kNJV70UYu", "target": "root.kNJV70UYu.HnUXLVNW-", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.97261442224699261714026266386", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 401.5859375, "y": -302.9999999999998, "anchor_index": 1}, "end_point": {"x": 484.5859375, "y": -304.9999999999998, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.kNJV70UYu.HnUXLVNW-", "target": "zjySkVTvU", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.44686263118389081714026271829", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 585.5859375, "y": -304.9999999999998, "anchor_index": 1}, "end_point": {"x": 684.5859375, "y": -304.9999999999998, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root", "target": "root.is_itai", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.389924921481094261715828475522", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 4800, "anchor_index": 0}}, {"source": "root.is_itai", "target": "root.is_itai.SkPV_iWeh", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.067309357863684971715828495729", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 4800, "anchor_index": 1}, "end_point": {"x": 415.02545343697597, "y": 4800, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.is_itai.SkPV_iWeh", "target": "HledXl7u4", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.32644855920209961715828502843", "style": {}, "start_point": {"x": 516.************, "y": 4800, "anchor_index": 1}, "end_point": {"x": 615.************, "y": 4800, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}]}, "rules": [{"name": "add_list_object", "setting": {"size": 1, "field": "_a", "mapping": [{"name": "name", "value": "${meta.login_account}"}]}, "sub_rules": [], "_id": "root.0lPDEvsby"}, {"name": "enter", "setting": {"values": "computer.accounts", "field": "${_a}"}, "sub_rules": [], "_id": "root.0lPDEvsby.XLU2DF87p"}, {"name": "add", "setting": {"field": "_mac", "values": ["${meta.mac}"], "multi_flag": null, "is_output_root": null}, "sub_rules": [], "_id": "root.IJntoVqjA"}, {"name": "replace", "setting": {"src_value": "-", "dst_value": ":", "field": "_mac"}, "sub_rules": [], "_id": "root.IJntoVqjA.uzQ9MD7Kp"}, {"name": "add_list_object", "setting": {"size": "_1", "field": "_interfaces", "mapping": [{"name": "mac", "value": "${_mac}"}]}, "sub_rules": [], "_id": "root.aQMeUswTM"}, {"name": "enter", "setting": {"values": "network.interfaces", "field": "${_interfaces}"}, "sub_rules": [], "_id": "root.aQMeUswTM.7f7iPgUr5"}, {"name": "add_list_object", "setting": {"size": 1, "field": "_ip", "mapping": [{"name": "mac", "value": "${_mac}"}, {"name": "ip", "value": "${meta.ip}"}]}, "sub_rules": [], "_id": "root.kNJV70UYu"}, {"name": "enter", "setting": {"values": "network.ips", "field": "${_ip}"}, "sub_rules": [], "_id": "root.kNJV70UYu.HnUXLVNW-"}, {"name": "enter", "setting": {"values": "asset_base.device.vendor", "field": "${info.brand_model}"}, "sub_rules": [], "_id": "root.info.brand_model.RrZ7HYfVW"}, {"name": "enter", "setting": {"values": "computer.hardware.board.sn", "field": "${info.hardware_info.board.serial_number}"}, "sub_rules": [], "_id": "root.info.hardware_info.board.serial_number.kMZqAkdIF"}, {"name": "translate", "setting": {"values": [{"name": true, "value": 1}, {"name": false, "value": 2}], "default": 0, "field": "meta.activation"}, "sub_rules": [], "_id": "root.meta.activation.nCXUEh1md"}, {"name": "enter", "setting": {"values": "asset_base.device.status", "field": "${meta.activation}"}, "sub_rules": [], "_id": "root.meta.activation.nCXUEh1md.Q-ZZhlRWl"}, {"name": "enter", "setting": {"values": "computer.domain.workgroup", "field": "${meta.computer_working_group}"}, "sub_rules": [], "_id": "root.meta.computer_working_group.TlNxPB7a7"}, {"name": "enter", "setting": {"values": "asset_base.name", "field": "${meta.name}"}, "sub_rules": [], "_id": "root.meta.name.c4In7JN--"}, {"name": "enter", "setting": {"values": "computer.host_name", "field": "${meta.name}"}, "sub_rules": [], "_id": "root.meta.name.IMxpCXqur"}, {"name": "enter", "setting": {"values": "computer.os.lang", "field": "${meta.system_language}"}, "sub_rules": [], "_id": "root.meta.system_language.zi86MLYxP"}, {"name": "translate", "setting": {"values": [{"name": 1, "value": 86}, {"name": 2, "value": 64}, {"name": 4, "value": 64}, {"name": 6, "value": 64}], "default": null, "field": "meta.tos.arch"}, "sub_rules": [], "_id": "root.meta.tos.arch.LrmKvDkZu"}, {"name": "enter", "setting": {"values": "computer.os.bitness", "field": "${meta.tos.arch}"}, "sub_rules": [], "_id": "root.meta.tos.arch.LrmKvDkZu.joTZlr73x"}, {"name": "translate", "setting": {"values": [{"name": 0, "value": "UNKNOWN_OS"}, {"name": 1, "value": "WINDOWS"}, {"name": 2, "value": "LINUX"}, {"name": 3, "value": "MACOS"}, {"name": 4, "value": "ANDROID"}, {"name": 5, "value": "IOS"}, {"name": 6, "value": "HARMONYOS"}], "default": null, "field": "meta.tos.os"}, "sub_rules": [], "_id": "root.meta.tos.os.1TjoOMn4N"}, {"name": "enter", "setting": {"values": "computer.os.type", "field": "${meta.tos.os}"}, "sub_rules": [], "_id": "root.meta.tos.os.1TjoOMn4N.ACfLU_6Lt"}, {"name": "enter", "setting": {"values": "computer.os.kernel_version", "field": "${meta.tos.version.kernel_version}"}, "sub_rules": [], "_id": "root.meta.tos.version.kernel_version.aVSWYl7Y6"}, {"name": "enter", "setting": {"values": "computer.os.version", "field": "${meta.tos.version.version}"}, "sub_rules": [], "_id": "root.meta.tos.version.version.oAKXloEe9"}, {"name": "enter", "setting": {"values": "computer.patchs.version", "field": "${module_info.patch_version}"}, "sub_rules": [], "_id": "root.module_info.patch_version.WVWQBaajB"}, {"name": "enter", "setting": {"values": "vulnerability.virus.libs.version", "field": "${module_info.virus_version}"}, "sub_rules": [], "_id": "root.module_info.virus_version.BraL2b6qX"}, {"name": "translate", "setting": {"values": [{"name": true, "value": 1}, {"name": false, "value": 2}], "default": 0, "field": "online_info.is_online"}, "sub_rules": [], "_id": "root.online_info.is_online.RyRZALfMS"}, {"name": "enter", "setting": {"values": "base.agents.status", "field": "${online_info.is_online}"}, "sub_rules": [], "_id": "root.online_info.is_online.RyRZALfMS._Pqk1rl6t"}, {"name": "enter", "setting": {"values": "base.agents.last_online_time", "field": "${online_info.last_time}"}, "sub_rules": [], "_id": "root.online_info.last_time.onovyxboe"}, {"name": "for", "setting": {"field": "wlkj_owners"}, "sub_rules": [{"id": "root.wlkj_owners.ODySndxLG.element.role.KtLbloaG2", "name": "enter", "setting": {"values": "role", "field": "${role}"}, "sub_rules": []}, {"id": "root.wlkj_owners.ODySndxLG.element.username.t3T6NzZmN", "name": "enter", "setting": {"values": "username", "field": "${username}"}, "sub_rules": []}, {"id": "root.wlkj_owners.ODySndxLG.element.nickname.KMcbnBE6a", "name": "enter", "setting": {"values": "nickname", "field": "${nickname}"}, "sub_rules": []}, {"id": "root.wlkj_owners.ODySndxLG.element.phones.4Psmf6SYG", "name": "enter", "setting": {"values": "phones", "field": "${phones}"}, "sub_rules": []}, {"id": "root.wlkj_owners.ODySndxLG.element.employee_id.Xk6s5elJ8", "name": "enter", "setting": {"values": "employee_id", "field": "${employee_id}"}, "sub_rules": []}, {"id": "root.wlkj_owners.ODySndxLG.element.emails.REj911E_1", "name": "enter", "setting": {"values": "emails", "field": "${emails}"}, "sub_rules": []}], "_id": "root.wlkj_owners.ODySndxLG"}, {"name": "enter", "setting": {"values": "asset_base.owners", "field": "${wlkj_owners}"}, "sub_rules": [], "_id": "root.wlkj_owners.ODySndxLG.K_fUmWCqG"}, {"name": "enter", "setting": {"values": "asset_base.itai", "field": "${is_itai}"}, "sub_rules": [], "_id": "root.is_itai.SkPV_iWeh"}], "adapter_name": "qian<PERSON>_spider_tianqing_v10", "fetch_type": "terminal", "model_name": "computer", "asset_type": "terminal", "internal": true}