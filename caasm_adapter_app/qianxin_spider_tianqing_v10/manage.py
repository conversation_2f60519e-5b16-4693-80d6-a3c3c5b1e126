from qianxin_spider_tianqing_v10.clients.asset import (
    QiAnXinTianQingAssetIDClient,
    QiAnXinTianQingTreeClient,
    QiAnXinTianQingTerminalClient,
    QiAnXinTianQingTerminalInfoClient,
    QiAnXinTianQingMetaClient,
)
from qianxin_spider_tianqing_v10.clients.orgs import QiAnXinTianQingOrgsClient


class FetchType(object):
    ORGS = "orgs"
    ASSET_ID = "asset_id"
    ASSET_GROUP = "asset_group"
    ASSET_META = "asset_meta"
    TERMINAL_ID = "terminal_id"
    TERMINAL = "terminal"


class QiAnXinSpiderTianQingV10Manager(object):
    _CLIENT_MAPPER = {
        FetchType.ORGS: QiAnXinTianQingOrgsClient,
        FetchType.ASSET_ID: QiAnXinTianQingAssetIDClient,
        FetchType.ASSET_GROUP: QiAnXinTianQingTreeClient,
        FetchType.ASSET_META: QiAnXinTianQingMetaClient,
        FetchType.TERMINAL_ID: QiAnXinTianQingTerminalClient,
        FetchType.TERMINAL: QiAnXinTianQingTerminalInfoClient,
    }

    def __init__(self, connection=None, session=None, condition=None):
        self._connection = connection
        self._session = session
        self._condition = condition
        self._client_instance_mapper = {}

    def auth(self):
        pass

    def find(self, fetch_type, page_index, page_size):
        """
        查询终端信息
        """
        terminal_ids_info = self._condition.get("terminal_ids_info")
        if not terminal_ids_info:
            return []
        ids = terminal_ids_info[page_index * page_size : page_size * (page_index + 1)]
        if not ids:
            return []
        result = []
        for id_info in ids:
            asset_id = id_info.get("asset_id")
            asset_oid = id_info.get("asset_oid")
            terminal_id = id_info.get("terminal_id")
            data = self._call(FetchType.TERMINAL, asset_id=asset_id, asset_oid=asset_oid, terminal_id=terminal_id)
            if not data:
                continue
            data["is_itai"] = self._connection.get("is_itai") == "是"
            result.append(data)
        return result

    def find_orgs(self):
        return self._call(FetchType.ORGS)

    def find_asset_id(self, org_id=None):
        return self._call(FetchType.ASSET_ID, org_id=org_id)

    def _call(self, client_type, *args, **kwargs):
        if client_type not in self._client_instance_mapper:
            instance = self._CLIENT_MAPPER[client_type](self._connection, self._session)
            self._client_instance_mapper[client_type] = instance
        return self._client_instance_mapper[client_type].handle(*args, **kwargs)
