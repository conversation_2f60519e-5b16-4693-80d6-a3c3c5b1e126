from caasm_adapter.sdk.adapter_fetch import fetch_sdk
from volcengine_containersec.manage import Manager


def find_asset(connection, fetch_type, page_index=0, page_size=1, session=None, condition=None, **kwargs):
    page_index += 1
    _manager = _build_manager(connection, session)
    records = _manager.find_asset(fetch_type, page_index, page_size)
    result = fetch_sdk.build_asset(records, fetch_type)
    return fetch_sdk.return_success(result)


def auth(connection, session):
    _build_manager(connection, session)


def get_count(connection, fetch_type, session=None, **kwargs):
    return _build_manager(connection, session).get_count(fetch_type)


def _build_manager(connection, session):
    return Manager(connection, session)
