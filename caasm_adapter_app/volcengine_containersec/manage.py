import logging
import math

from caasm_adapter.util.exception import AdapterFetchApiNotSupportException
from volcengine_containersec.clients.auth import VCSAuthClient
from volcengine_containersec.clients.cluster import VCSClusterFetchClient
from volcengine_containersec.clients.container import (
    VCSContainerFetchClient,
    VCSContainerPortFetchClient,
    VCSContainerDetailFetchClient,
    VCSContainerProcessFetchClient,
    VCSContainerAppFetchClient,
)
from volcengine_containersec.clients.image import VCSImageFetchClient, VCSImageLayerFetchClient
from volcengine_containersec.clients.node import VCSNodeFetchClient
from volcengine_containersec.clients.pod import VCSPodFetchClient
from volcengine_containersec.clients.service import (
    VCSServiceFetchClient,
    VCSServiceDetailFetchClient,
    VCSServicePortFetchClient,
)

log = logging.getLogger()


class ClientType(object):
    KUBE_CLUSTER = "kube_cluster"
    COMPUTER = "computer"
    CONTAINER_IMAGE = "container_image"
    KUBE_POD = "kube_pod"

    CONTAINER = "container"
    CONTAINER_DETAIL = "container_detail"
    CONTAINER_PORT = "container_port"
    CONTAINER_PROCESS = "container_process"
    CONTAINER_APP = "container_app"

    KUBE_SERVICE = "kube_service"
    KUBE_SERVICE_DETAIL = "kube_service_detail"
    KUBE_SERVICE_PORT = "kube_service_port"

    CONTAINER_IMAGE_LAYER = "container_image_layer"
    AUTH = "auth"


class QueryType(object):
    SINGLE = "single"
    MULTI = "multi"


class Manager(object):
    _CLIENT_MAPPER = {
        ClientType.AUTH: VCSAuthClient,
        ClientType.KUBE_CLUSTER: VCSClusterFetchClient,
        ClientType.COMPUTER: VCSNodeFetchClient,
        ClientType.CONTAINER_IMAGE: VCSImageFetchClient,
        ClientType.CONTAINER: VCSContainerFetchClient,
        ClientType.CONTAINER_DETAIL: VCSContainerDetailFetchClient,
        ClientType.CONTAINER_PORT: VCSContainerPortFetchClient,
        ClientType.CONTAINER_PROCESS: VCSContainerProcessFetchClient,
        ClientType.CONTAINER_APP: VCSContainerAppFetchClient,
        ClientType.KUBE_POD: VCSPodFetchClient,
        ClientType.KUBE_SERVICE: VCSServiceFetchClient,
        ClientType.KUBE_SERVICE_DETAIL: VCSServiceDetailFetchClient,
        ClientType.KUBE_SERVICE_PORT: VCSServicePortFetchClient,
        ClientType.CONTAINER_IMAGE_LAYER: VCSImageLayerFetchClient,
    }

    _CLIENT_CHILD_PROPERTIES = {
        ClientType.CONTAINER: [
            (ClientType.CONTAINER_DETAIL, QueryType.SINGLE),
            (ClientType.CONTAINER_PROCESS, QueryType.MULTI),
            (ClientType.CONTAINER_PORT, QueryType.MULTI),
            (ClientType.CONTAINER_APP, QueryType.MULTI),
        ],
        ClientType.KUBE_SERVICE: [
            (ClientType.KUBE_SERVICE_DETAIL, QueryType.SINGLE),
            (ClientType.KUBE_SERVICE_PORT, QueryType.MULTI),
        ],
    }

    def __init__(self, connection, session=None):
        self._connection = connection
        self._session = session
        self._client_instance_mapper = {}
        self._token = None
        self.auth()

    def get_count(self, fetch_type, **kwargs):
        return self._instance(fetch_type).get_count(page_index=1, page_size=1, **kwargs)

    def find_asset(self, fetch_type, page_index, page_size):
        """
        查找资产
        """
        assets = self._call(fetch_type, page_index=page_index, page_size=page_size)
        for item in assets:
            self._build_child_values(fetch_type, item, page_size=page_size)
        return assets

    def auth(self):
        """
        认证方法
        """
        self._token = self._call(ClientType.AUTH)

    def _call(self, client_type, *args, **kwargs):
        _instance = self._instance(client_type)

        return _instance.handle(*args, **kwargs)

    def _instance(self, client_type):
        _instance = self._client_instance_mapper.get(client_type)
        if not _instance:
            _instance_class = self._CLIENT_MAPPER.get(client_type)
            if not _instance_class:
                raise AdapterFetchApiNotSupportException
            _instance = _instance_class(self._connection, session=self._session, token=self._token)
            self._client_instance_mapper[client_type] = _instance
        return _instance

    def _build_child_values(self, fetch_type, asset, page_size):
        child_types = self._CLIENT_CHILD_PROPERTIES.get(fetch_type)
        if not child_types:
            return

        for child_type_info in child_types:
            child_type, flag = child_type_info
            try:
                if flag == QueryType.MULTI:
                    values = self.__find_child(child_type, asset, page_size)
                else:
                    values = self.__get_child(child_type, asset)
            except Exception as e:
                log.warning(f"Build child property ({child_type}) error({e})")
            else:
                asset[child_type] = values

    def __find_child(self, client_type, asset, page_size):
        _count = self.get_count(client_type, parent=asset)
        _times = int(math.ceil(_count / page_size))
        result = []

        for i in range(_times):
            try:
                data = self._call(client_type, page_index=i + 1, page_size=page_size, parent=asset)
            except Exception as e:
                log.warning(f"Find child({client_type}) error({e})")
            else:
                result.extend(data)
        return result

    def __get_child(self, client_type, asset):
        return self._call(client_type, parent=asset)
