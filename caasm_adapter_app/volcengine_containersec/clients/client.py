from caasm_adapter.util.client import FetchJsonResultClient


class VCSFetchClient(FetchJsonResultClient):
    METHOD = "post"

    def __init__(self, connection, session, token=None):
        super(VCSFetchClient, self).__init__(connection, session)
        self._token = token

    @property
    def suc_flag(self):
        return True

    @property
    def flag_key_name(self):
        return "success"

    @property
    def data_key_name(self):
        return "data"

    def build_request_header(self, *args, **kwargs):
        return {"Authorization": self._token} if self._token else None


class VCSQueryClient(VCSFetchClient):
    def build_request_json(self, page_index, page_size):
        return {"page": {"currentPage": page_index, "pageSize": page_size}}

    @classmethod
    def extract_count(cls, data):
        return data["page"]["count"]
