import logging

from baidu_cloud_ak.clients.sdk.base_info.access_key import BaiduCloudAccessKeyClient
from baidu_cloud_ak.clients.sdk.base_info.policy import BaiduCloudPolicyClient
from baidu_cloud_ak.clients.sdk.base_info.user import BaiduCloudUserClient
from caasm_tool.util import extract, restore

log = logging.getLogger()


class FetchType(object):
    USER = "user"
    ACCESS_KEY = "access_key"
    POLICY = "policy"


class BaiduCloudAKManager(object):
    _CLIENT_MAPPING = {
        FetchType.ACCESS_KEY: BaiduCloudAccessKeyClient,
        FetchType.USER: BaiduCloudUserClient,
        FetchType.POLICY: BaiduCloudPolicyClient,
    }

    def __init__(self, connection=None, session=None, condition=None):
        self.connection = connection
        self.session = session
        self.condition = condition
        self.regions = None
        self.domains = None
        self.port = None
        self.address_list = []
        self.init()

    def init(self):
        self.get_all_address_list()

    def get_all_address_list(self):
        self.regions = self.connection.get("regions") or []
        self.domains = self.connection.get("domains") or []
        self.port = self.connection.get("port") or 80

        for region in self.regions:
            for domain in self.domains:
                self.address_list.append(f"{region}.{domain}:{self.port}")

    def get_sdk_fetch(self, fetch_type=None, address=None, page_size=None):
        result = []
        if fetch_type == FetchType.ACCESS_KEY:
            user_data = self.call(FetchType.USER, address=address)
            for user in user_data:
                user_name = user.get("name").encode("utf-8")
                ak_info = self.call(FetchType.ACCESS_KEY, address=address, user_name=user_name)
                policies = self.call(FetchType.POLICY, address=address, user_name=user_name)
                for ak in ak_info:
                    ak["user_data"] = [user]
                    ak["policies_info"] = policies
                    result.append(ak)
        return result

    def get_fetch_type_data(self, fetch_type, address=None, page_size=None, page_index=None):
        """
        获取采集类型数据
        """
        result = []
        if fetch_type in self._CLIENT_MAPPING:
            result = self.get_sdk_fetch(fetch_type=fetch_type, address=address, page_size=page_size)
        return result

    def find_asset(self, fetch_type, page_size=None, page_index=None):
        """
        云的 数据 是真的难接啊  即有sdk 又有api  两种对接方式
        """
        result = []
        if page_index > 1:
            return result

        for _address in self.address_list:
            data = self.get_fetch_type_data(fetch_type, address=_address, page_size=page_size, page_index=page_index)
            result.extend(data)
        result = self.clean_result(result)
        return result

    def clean_result(self, result):
        result = self.enrich_cloud_account(data=result)
        return result

    def enrich_cloud_account(self, data=None):
        if not self.cloud_account:
            return data
        for record in data:
            restore(field="cloud.account", value=self.cloud_account, result=record)
        return data

    def call(self, fetch_type, address=None, *args, **kwargs):
        _class = self._CLIENT_MAPPING.get(fetch_type)
        if not _class:
            return
        return _class(self.connection, self.session, self.condition, address).handle(*args, **kwargs)

    @property
    def cloud_account(self):
        return self.connection.get("cloud_account")
