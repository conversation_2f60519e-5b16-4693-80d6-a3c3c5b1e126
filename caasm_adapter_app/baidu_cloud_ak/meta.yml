name: "baidu_cloud_ak"
display_name: "百度云AK获取"
description: "百度智能云致力于为企业和开发者提供全球领先的人工智能、大数据和云计算服务，加速产业智能化转型升级。"
type: "云平台"
company: "百度"
logo: "baidu_cloud.png"
version: "v0.1"
priority: 1
properties:
  - "云平台"

connection:
  - name: access_key
    type: string
    required: true
    display_name: "access_key"
    description: "百度云访问Access key"
    validate_rules:
      - name: length
        error_hint: "access_key格式无效。长度最小不得小于2，最大不得大于100"
        setting:
          min: 2
          max: 100

  - name: secret_key
    type: password
    required: true
    display_name: "Secret Key"
    description: "百度云访问Secret Key"
    validate_rules:
      - name: length
        error_hint: "secret_key格式无效。长度最小不得小于6，最大不得大于200"
        setting:
          min: 6
          max: 200

  - name: regions
    type: list
    required: false
    display_name: "云服务器区域"
    description: "区域"
    validate_rules:
      - name: length
        error_hint: "区域最多只能设置100个"
        setting:
          min: 0
          max: 100
      - name: element_length
        error_hint: "区域元素长度不合法。长度最小不得小于1，最大不得大于100"
        setting:
          type: string
          min: 1
          max: 100

  - name: domains
    type: list
    required: false
    display_name: "专有云默认根域名"
    description: "专有云根域名，例如'bcc.region.baidubce.com' 中的 baidubce.com"
    default:
      - baidubce.com
    validate_rules:
      - name: length
        error_hint: "区域元素长度不合法。长度最小不得小于1，最大不得大于100"
        setting:
          type: string
          min: 1
          max: 100

  - name: port
    type: integer
    required: false
    display_name: "默认端口"
    description: "默认端口，可不填写，不填写则默认走 80端口"
    validate_rules:
      - name: number
        error_hint: "区域元素长度不合法。长度最小不得小于1，最大不得大于100"
        setting:
          type: string
          min: 1
          max: 65536

  - name: worker
    type: integer
    required: true
    display_name: "采集工作者数量"
    description: ""
    default: 8
    validate_rules:
      - name: number
        error_hint: "secret_key格式无效。长度最小不得小于6，最大不得大于200"
        setting:
          min: 2
          max: 64

  - name: cloud_account
    type: string
    required: false
    display_name: "云账户"
    description: "百度云账户"
    validate_rules:
      - name: length
        error_hint: "cloud_account格式无效。长度最小不得小于1，最大不得大于200"
        setting:
          min: 1
          max: 200


fetch_setting:
  type: disposable
  point: "baidu_cloud_ak.fetch:find_asset"
  condition_point: "baidu_cloud_ak.fetch:build_query_condition"
#  is_need_test_service: true
#  test_auth_point: "baidu_cloud.fetch:get_auth_connection"
#  test_connection_point: "baidu_cloud.fetch:check_connection"
  size: 50
  fetch_type_mapper:
    asset:
      - access_key


merge_setting:
  size: 50
  setting: { }

convert_setting:
  size: 50
  before_executor_mapper: { }
  executor_mapper: { }