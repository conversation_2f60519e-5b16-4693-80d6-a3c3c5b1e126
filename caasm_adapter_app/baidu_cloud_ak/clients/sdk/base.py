import json
import logging
import abc

from baidubce.auth.bce_credentials import BceCredentials
from baidubce.bce_client_configuration import BceClientConfiguration

from caasm_adapter.util.client import FetchClient


from caasm_tool.util import extract

log = logging.getLogger()


class BaiduCloudInfoBaseClient(FetchClient):

    DEFAULT_CATEGORY = ""
    NEXT_MARKER = "next_marker"

    def __init__(self, connection, session=None, condition=None, address=None):
        super(BaiduCloudInfoBaseClient, self).__init__(connection, session)
        self.condition = condition
        self.client = None
        self._address = address
        self.endpoints = []

    @property
    def ak(self):
        return self._connection.get("access_key")

    @property
    def sk(self):
        return self._connection.get("secret_key")

    @property
    def endpoint(self):
        return f"{self.DEFAULT_CATEGORY}.{self._address}"

    def create_client(self, config=None):
        raise NotImplementedError

    def find_client_data(self, client=None, *args, **kwargs):
        raise NotImplementedError

    def handle_common(self, *args, **kwargs):
        self.client = self._create_client()
        data = self.find_data(*args, **kwargs)
        return self.parse_response(data, *args, **kwargs)

    def parse_response(self, response, *args, **kwargs):
        try:
            result = self.object_to_dict(response)
        except Exception as e:
            log.warning(f"parse response error({e}),code is {response.status_code},content is {response.content}")
            return self.error_handle(e, *args, **kwargs)
        else:
            return result

    def find_data(self, *args, **kwargs):
        data = self.find_client_data(client=self.client, *args, **kwargs)
        return data

    def object_to_dict(self, obj):
        if hasattr(obj, "__dict__"):
            return {k: self.object_to_dict(v) for k, v in vars(obj).items()}
        elif isinstance(obj, list):
            return [self.object_to_dict(item) for item in obj]
        else:
            return obj

    def _create_client(self):
        config = BceClientConfiguration(credentials=BceCredentials(self.ak, self.sk), endpoint=self.endpoint)
        return self.create_client(config=config)

    @property
    def data_key(self):
        return ""

    def parse_biz_result(self, result, *args, **kwargs):
        try:
            result = self.parse_biz_core(result)
        except Exception as e:
            log.warning(f"Clean result error({e})")
            return self.parse_error_handle(result, *args, **kwargs)
        else:
            return self.extract_data(result)

    def parse_biz_core(self, result):
        return result

    def extract_data(self, result):
        if not self.data_key:
            return result
        return extract(result, self.data_key)

    @property
    def regions(self):
        return self._connection.get("regions")

    @property
    def port(self):
        return self._connection.get("port") or 80


class BaiduCloudInfoListClient(BaiduCloudInfoBaseClient):

    SUB_CATEGORY = ""

    def find_data(self, max_keys=None, *args, **kwargs):
        data = self.find_client_data(client=self.client, *args, **kwargs)
        return data

    def handle_common(self, *args, **kwargs):
        self.init_condition()
        self.client = self._create_client()
        data = self.find_data(*args, **kwargs)
        return self.parse_response(data, *args, **kwargs)

    def init_condition(self):
        if self.DEFAULT_CATEGORY not in self.condition.get("marker_mapper"):
            self.condition["marker_mapper"][self.DEFAULT_CATEGORY] = {}
        if self.SUB_CATEGORY not in self.condition.get("marker_mapper")[self.DEFAULT_CATEGORY]:
            self.condition["marker_mapper"][self.DEFAULT_CATEGORY][self.SUB_CATEGORY] = {}
        if self.endpoint not in self.condition["marker_mapper"][self.DEFAULT_CATEGORY][self.SUB_CATEGORY]:
            self.condition["marker_mapper"][self.DEFAULT_CATEGORY][self.SUB_CATEGORY][self.endpoint] = ""

    def update_next_find_marker(self, result, endpoint):
        """
        更新下一次查找的marker
        """
        marker = result.next_marker
        if not marker:
            self.condition["marker_mapper"][self.DEFAULT_CATEGORY][self.SUB_CATEGORY][endpoint] = False
            return

        self.condition["marker_mapper"][self.DEFAULT_CATEGORY][self.SUB_CATEGORY][endpoint] = marker
