from baidubce.services.iam.iam_client import IamClient
from baidu_cloud_ak.clients.sdk.base import BaiduCloudInfoListClient, BaiduCloudInfoBaseClient


class BaiduCloudAccessKeyClient(BaiduCloudInfoListClient):

    DEFAULT_CATEGORY = "iam"
    SUB_CATEGORY = "default_access_key"

    def create_client(self, config=None):
        return IamClient(config)

    def find_client_data(self, client=None, user_name=None, *args, **kwargs):
        return client.list_user_accesskey(user_name)

    @property
    def data_key(self):
        return "access_keys"
