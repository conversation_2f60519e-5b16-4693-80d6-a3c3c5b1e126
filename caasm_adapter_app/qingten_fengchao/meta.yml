name: "qingten_fengchao"
display_name: "青藤蜂巢·云原生安全平台"
description: "青藤蜂巢·云原生安全平台是青藤自主研发的云原生安全平台，能够很好集成到云原生复杂多变的环境中，如Kubernetes、PaaS云平台、OpenShift、Jenkins、Harbor、JFrog等等。通过提供覆盖容器全生命周期的一站式容器安全解决方案，青藤蜂巢可实现容器安全预测、防御、检测和响应的安全闭环。"
type: "容器安全"
company: "青藤"
logo: "qingten_fengchao.png"
version: "v0.1"
priority: 1
properties:
  - "容器安全"

connection:
  - name: username
    type: string
    required: true
    display_name: "用户名"
    description: "用户名称"
    validate_rules:
      - name: length
        error_hint: "用户名长度必须大于等于2且小于等于100"
        setting:
          min: 2
          max: 100

  - name: password
    type: password
    required: true
    display_name: "密码"
    description: "密码信息"
    validate_rules:
      - name: length
        error_hint: "密码长度必须大于等于6且小于等于100"
        setting:
          min: 6
          max: 100

  - name: address
    type: url
    required: true
    display_name: "地址"
    description: "地址信息"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'


fetch_setting:
  type: disposable
  point: "qingten_fengchao.fetch:find_asset"
  size: 1000
  fetch_type_mapper:
    asset:
      - asset

fabric_setting:
  choose_point_mapper:
    asset: "qingten_fengchao.fabric:choose_new_record"

merge_setting:
  size: 1000
  setting: { }

convert_setting:
  size: 1000
  before_executor_mapper: { }
  executor_mapper: { }