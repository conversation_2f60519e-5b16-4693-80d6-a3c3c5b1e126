import base64
import hashlib
import hmac
from datetime import datetime

from caasm_adapter.util.client import FetchJsonResultClient


class EagleCloudBaseClient(FetchJsonResultClient):
    METHOD = "POST"
    GMT_FORMAT = "%a, %d %b %Y %H:%M:%S GMT"

    def __init__(self, connection, session=None):
        super(EagleCloudBaseClient, self).__init__(connection=connection, session=session)
        self.date = datetime.utcnow().strftime(self.GMT_FORMAT)

    def build_request_header(self, *args, **kwargs):
        signature = self.build_header_signature(*args, **kwargs)
        return {
            "X-MANAGE_SCOPE_TYPE": "all",
            "X-HMAC-ALGORITHM": "hmac-sha256",
            "X-HMAC-ACCESS-KEY": self.access_key,
            "X-HMAC-SIGNATURE": base64.b64encode(signature.digest()),
            "Date": self.date,
        }

    def build_header_signature(self, *args, **kwargs):
        query = self.build_request_params(*args, **kwargs)
        query = self.build_query(query)
        sign_data = "{}\n{}\n{}\n{}\n{}\n".format(self.METHOD, self.URL, query, self.access_key, self.date)
        signature = hmac.new(self.secret_key, bytes(sign_data, encoding="utf8"), digestmod=hashlib.sha256)
        return signature

    def build_query(self, query):
        _str = ""
        count = len(query.keys())
        i = 0
        for key, value in query.items():
            i += 1
            _str = _str + f"{key}={value}"
            if i == count:
                continue
            else:
                _str = f"{_str}&"
        return _str

    @property
    def suc_flag(self):
        return "Success"

    @property
    def flag_key_name(self):
        return "message"

    @property
    def data_key_name(self):
        return "data.rows"

    @property
    def access_key(self):
        return self._connection.get("access_key")

    @property
    def secret_key(self):
        return self._connection.get("secret_key").encode()
