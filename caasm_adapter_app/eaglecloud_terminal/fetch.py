from caasm_adapter.sdk.adapter_fetch import fetch_sdk
from eaglecloud_terminal.manage import EagleCloudTerminal


def find_asset(connection, fetch_type, page_index=0, page_size=1, session=None, **kwargs):
    page_index = page_index + 1
    manager = EagleCloudTerminal(connection, session)
    records = manager.find(fetch_type, page_index, page_size)
    result = fetch_sdk.build_asset(records, fetch_type)
    return fetch_sdk.return_success(result)


def get_auth_connection(connection, session=None):
    EagleCloudTerminal(connection, session).find("terminal", page_index=1, page_size=1)
