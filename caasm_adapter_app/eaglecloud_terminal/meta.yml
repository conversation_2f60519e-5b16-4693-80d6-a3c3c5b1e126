name: eaglecloud_terminal
display_name: "亿格云枢终端管理"
description: "通过对移动端和PC端办公设备（Windows、macOS、Android、iOS）的统一管控，轻松满足合规要求，助力企业数字化转型。"
type: "终端防护"
company: "亿格云"
logo: "eaglecloud.png"
version: "v0.1"
priority: 10
properties:
  - 终端防护


connection:
  - name: address
    type: url
    required: true
    display_name: "请求地址"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'

  - name: access_key
    type: string
    required: true
    display_name: "Access Key"
    description: "Access Key"
    validate_rules:
      - name: length
        error_hint: "access_key格式无效。长度最小不得小于2，最大不得大于100"
        setting:
          min: 2
          max: 100

  - name: secret_key
    type: password
    required: true
    display_name: "Secret Key"
    description: "Secret Key"
    validate_rules:
      - name: length
        error_hint: "secret_key格式无效。长度最小不得小于6，最大不得大于200"
        setting:
          min: 6
          max: 200

fetch_setting:
  point: "eaglecloud_terminal.fetch:find_asset"
  size: 50
  test_auth_point: "eaglecloud_terminal.fetch:get_auth_connection"
  is_need_test_service: true
  fetch_type_mapper:
    asset:
      - terminal
      #- software
#  cleaner_mapper:
#    asset:
#      terminal:
#        - "eaglecloud_terminal.cleaners.terminal_cleaner:TerminalCleaner"
#


