from caasm_tool.reflect import parse_instance_callable
from qianxin_nids.clients.auth import QiAnXinNIDSCsrfTokenClient, QiAnXinNIDSAccessTokenClient
from qianxin_nids.clients.ip import QiAnXinNIDSIPClient
from qianxin_nids.clients.vul import QiAnXinNIDSAssetDetailVulClient, QiAnXinNIDSAssetVulClient


class ClientType(object):
    ACCESS_TOKEN = "access_token"
    CSRF_TOKEN = "csrf_token"
    IP = "ip"
    ASSET_VUL = "asset_vul"
    ASSET_VUL_DETAIL = "asset_vul_detail"


class FetchType(object):
    HOST = "host"


CLIENT_MAPPER = {
    ClientType.CSRF_TOKEN: QiAnXinNIDSCsrfTokenClient,
    ClientType.ACCESS_TOKEN: QiAnXinNIDSAccessTokenClient,
    ClientType.IP: QiAnXinNIDSIPClient,
    ClientType.ASSET_VUL: QiAnXinNIDSAssetVulClient,
    ClientType.ASSET_VUL_DETAIL: QiAnXinNIDSAssetDetailVulClient,
}


class QiAnXinNIDSManager(object):
    def __init__(self, connection=None, session=None, token=None):
        self._connection = connection
        self._session = session
        self._token = token
        self._find_method_mapper = parse_instance_callable(self, "_find_")

    def auth(self):
        access_token = self._call(ClientType.ACCESS_TOKEN)
        return self._call(ClientType.CSRF_TOKEN, access_token=access_token)

    def find(self, fetch_type, page_index, page_size):
        return self._find_method_mapper.get(fetch_type)(page_index, page_size)

    def _find_host(self, page_index, page_size):
        return self._call(ClientType.IP, page_index=page_index, page_size=page_size)

    def _call(self, client_type, *args, **kwargs):
        return CLIENT_MAPPER[client_type](self._connection, self._session, self._token).handle(*args, **kwargs)
