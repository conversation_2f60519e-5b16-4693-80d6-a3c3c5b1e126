name: qianxin_nids
display_name: "奇安信天眼"
description: "奇安信网神威胁监测与分析系统（天眼）是以攻防渗透和数据分析为核心竞争力，聚焦威胁检测和响应，为客户提供安全服务与产品解决方案。"
type: "流量检测"
company: "奇安信"
logo: "logo.png"
version: "v0.1"
priority: 10
properties:
  - 威胁检测
  - 流量检测

connection:
  - name: address
    type: url
    required: true
    display_name: "请求地址"
    description: "请求地址"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'

  - name: username
    type: string
    required: false
    display_name: "用户名称"
    description: "用户名称"
    default: "tapadmin"
    validate_rules:
      - name: length
        error_hint: "用户名称格式无效。长度最小不得小于2，最大不得大于200"
        setting:
          min: 2
          max: 100

  - name: login_key
    type: string
    required: true
    display_name: "login_key"
    description: "在系统管理-帐号管理-本地帐号管理页面获取“免密LOGIN密钥”"
    validate_rules:
      - name: length
        error_hint: "login_key格式无效。长度最小不得小于2，最大不得大于200"
        setting:
          min: 2
          max: 100


fetch_setting:
  point: "qianxin_nids.fetch:find_asset"
  is_need_test_service: true
  test_auth_point: "qianxin_nids.fetch:get_auth_connection"
  condition_point: "qianxin_nids.fetch:build_query_condition"
  size: 100
  fetch_type_mapper:
    asset:
      - host
