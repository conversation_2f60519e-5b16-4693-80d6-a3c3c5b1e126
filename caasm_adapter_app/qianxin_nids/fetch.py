from caasm_adapter.sdk.adapter_fetch import fetch_sdk
from qianxin_nids.manage import QiAnXinNIDSManager


def find_asset(connection, fetch_type, page_index=0, page_size=1, **kwargs):
    session = kwargs.get("session")
    condition = kwargs.get("condition")
    token = condition.get("token")

    records = _manager(connection, session, token).find(fetch_type, page_index, page_size)
    result = fetch_sdk.build_asset(records, fetch_type)
    return fetch_sdk.return_success(result)


def get_auth_connection(connection, session):
    _manager(connection, session).auth()


def build_query_condition(connection, session, fetch_type):
    token = _manager(connection, session).auth()
    return {"token": token}


def _manager(connection, session, token=""):
    return QiAnXinNIDSManager(connection, session, token=token)
