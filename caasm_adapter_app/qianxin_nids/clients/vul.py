from qianxin_nids.clients.base import QiAnXinNIDSPageClient, QiAnXinNIDSBaseClient


class QiAnXinNIDSAssetVulClient(QiAnXinNIDSPageClient):
    URL = "/skyeye/v1/asset/vul/asset-leaks/list"


class QiAnXinNIDSAssetDetailVulClient(QiAnXinNIDSBaseClient):
    URL = "/skyeye/v1/asset/vul/asset-leaks/info"
    METHOD = "get"

    def build_request_params(self, record, *args, **kwargs):
        params = super().build_request_params(*args, **kwargs)
        params["ids"] = record.get("id")
        return params

    @property
    def data_key_name(self):
        return "data"
