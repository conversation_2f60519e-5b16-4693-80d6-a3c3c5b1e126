from caasm_adapter.util.client import FetchJsonResultClient


class QiAnXinNIDSBaseClient(FetchJsonResultClient):
    METHOD = "POST"

    def __init__(self, connection, session=None, token=None):
        super().__init__(connection, session)
        self._token = token

    def build_request_params(self, *args, **kwargs):
        return {"csrf_token": self._token} if self._token else {}

    @property
    def suc_flag(self):
        return ""

    @property
    def flag_key_name(self):
        return ""

    @property
    def data_key_name(self):
        return ""


class QiAnXinNIDSPageClient(QiAnXinNIDSBaseClient):
    METHOD = "get"

    def build_request_params(self, page_index, page_size, *args, **kwargs):
        request_params = super().build_request_params(*args, **kwargs)
        request_params["limit"] = page_size
        request_params["offset"] = page_size * page_index
        return request_params

    @property
    def data_key_name(self):
        return "data.data"
