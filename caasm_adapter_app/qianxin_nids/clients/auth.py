import hashlib
import json
import re
import time

from qianxin_nids.clients.base import QiAnXinNIDSBaseClient


class QiAnXinNIDSCsrfTokenClient(QiAnXinNIDSBaseClient):
    METHOD = "GET"
    URL = "skyeye/v1/admin/auth?token={}"

    _CSRF_TEXT_RE = re.compile(r"\<meta name=\"csrf-token\" content=\"[0-9a-fA-F]*\"")
    _CSRF_TOKEN_RE = re.compile(r"[0-9a-fA-F]{16,32}")

    def build_request_url(self, access_token=None, *args, **kwargs):
        url = super().build_request_url(*args, **kwargs)
        return url.format(access_token)

    def parse_response(self, response, *args, **kwargs):
        return self._CSRF_TOKEN_RE.search(self._CSRF_TEXT_RE.search(response.text).group()).group()


class QiAnXinNIDSAccessTokenClient(QiAnXinNIDSBaseClient):
    URL = "/skyeye/v1/admin/auth"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._client_id = self._build_sha256_data(seed="mNSLP9UJCtBHtegjDPJnK3v")
        self._client_secret = self._build_sha256_data(seed="3460681205014671737")
        self._timestamp = str(int(time.time()))

    def build_request_header(self, *args, **kwargs):
        return {
            "X-Authorization": self._build_x_auth(),
            "X-Timestamp": self._timestamp,
            "Content-Type": "application/x-www-form-urlencoded",
        }

    def build_request_data(self, *args, **kwargs):
        return {"client_id": self._client_id, "username": self.username}

    def _build_x_auth(self):
        user_data = json.dumps({"client_id": self._client_id, "username": self.username}, separators=(",", ":"))
        raw = f"{user_data}{self._timestamp}{self._client_secret}"
        return hashlib.sha256(raw.encode()).hexdigest()

    def _build_sha256_data(self, seed=None):
        raw_string = f"{seed}|{self.login_key}"
        return hashlib.sha256(raw_string.encode()).hexdigest()

    @property
    def login_key(self):
        return self.connection.get("login_key")

    @property
    def username(self):
        return self.connection.get("username")

    @property
    def data_key_name(self):
        return "access_token"

    @property
    def flag_key_name(self):
        return "status"

    @property
    def suc_flag(self):
        return 200
