from imperva_waf.clients.auth import ImpervaWafAuthClient
from imperva_waf.clients.protected_ip import ImpervaWafProtectedIPsClient
from imperva_waf.clients.rule import ImpervaWafInRuleClient, ImpervaWafOutRuleClient
from imperva_waf.clients.server_group import ImpervaWafServerGroupClient, ImpervaWafServerGroupInfoClient
from imperva_waf.clients.service import ImpervaWafServiceClient, ImpervaWafServicePortClient
from imperva_waf.clients.site import ImpervaWafSitesClient


class ClientType(object):
    AUTH = "auth"
    SITE = "site"
    SERVER_GROUP = "server_group"
    SERVER_GROUP_DETAIL = "server_group_detail"
    PROTECTED_IP = "protected_ip"
    SERVICE = "web_service"
    SERVICE_PORT = "web_service_port"
    IN_RULE = "in_rule"
    OUT_RULE = "out_rule"


CLIENT_MAPPER = {
    ClientType.AUTH: ImpervaWafAuth<PERSON>lient,
    ClientType.SITE: ImpervaWafSitesClient,
    ClientType.SERVER_GROUP: ImpervaWafServerGroupClient,
    ClientType.SERVER_GROUP_DETAIL: ImpervaWafServerGroupInfoClient,
    ClientType.PROTECTED_IP: ImpervaWafProtectedIPsClient,
    ClientType.SERVICE: ImpervaWafServiceClient,
    ClientType.SERVICE_PORT: ImpervaWafServicePortClient,
    ClientType.IN_RULE: ImpervaWafInRuleClient,
    ClientType.OUT_RULE: ImpervaWafOutRuleClient,
}


class FetchType(object):
    WAF = "waf"
    SERVER_GROUP = "waf_server_group"


class ImpervaWafManager(object):
    def __init__(self, connection, session=None):
        self._connection = connection
        self._session = session
        self._fetch_method_mapper = {
            FetchType.WAF: self._find_waf,
            FetchType.SERVER_GROUP: self._find_server_group,
        }

    def auth(self):
        return self._call(ClientType.AUTH)

    def find_asset(self, fetch_type):
        fetch_method = self._fetch_method_mapper.get(fetch_type)
        if not fetch_method:
            return []
        return fetch_method()

    def _find_waf(self):
        return self._find_open_waf() if self._is_open_waf else self._find_proxy_waf()

    def _find_open_waf(self):
        result = []
        for info in self._find_server_group():
            ports = []
            for service in info["services"]:
                ports.extend(service["service_ports"]["ports"])

            for ip in info["project_ips"]:
                result.append(
                    {
                        "src_ip": ip["ip"],
                        "ports": ports,
                    }
                )
        return result

    def _find_proxy_waf(self):
        server_groups = self._find_server_group()

        result = []
        for server_group in server_groups:
            for rule in server_group["rules"]:
                entry_ip = rule["entry_ip"]
                gateway_ports = [rule["gateway_port"]]
                dst_info = {"ip": rule["internalIpHost"], "port": rule["serverPort"]}
                result.append({"src_ip": entry_ip, "ports": gateway_ports, "dst_ips": [dst_info]})
        return result

    def _find_server_group(self):
        result = []
        for site_name in self._call(ClientType.SITE):
            result.extend(self._get_server_group(site_name))
        return result

    def _get_server_group(self, site_name):
        result = []
        for server_group_name in self._call(ClientType.SERVER_GROUP, site_name=site_name):
            query = {"site_name": site_name, "server_group_name": server_group_name}
            server_group_detail = self._call(ClientType.SERVER_GROUP_DETAIL, **query)
            project_ips = self._call(ClientType.PROTECTED_IP, **query) if self._is_open_waf else []
            result.append(
                {
                    "site_name": site_name,
                    "server_group_name": server_group_name,
                    "server_group_detail": server_group_detail,
                    "services": self._find_service(**query),
                    "project_ips": project_ips,
                    "rules": self._find_rule(**query) if not self._is_open_waf else [],
                }
            )
        return result

    def _find_service(self, site_name, server_group_name):
        result = []
        query = {"site_name": site_name, "server_group_name": server_group_name}
        for service_name in self._call(ClientType.SERVICE, **query):
            service_ports = self._call(ClientType.SERVICE_PORT, service_name=service_name, **query)
            result.append({"service_name": service_name, "service_ports": service_ports})
        return result

    def _find_rule(self, site_name, server_group_name):
        result = []
        query = {"site_name": site_name, "server_group_name": server_group_name}
        for service_name in self._call(ClientType.SERVICE, **query):
            result.extend(self._get_rule(service_name, **query))
        return result

    def _get_rule(self, service_name, site_name, server_group_name):
        result = []
        query = {
            "site_name": site_name,
            "service_name": service_name,
            "server_group_name": server_group_name,
        }
        for rule in self._call(ClientType.IN_RULE, **query):
            for gateway_port in rule["gatewayPorts"]:
                query["alias_name"] = rule["aliasName"]
                query["gateway_port"] = gateway_port
                query["gateway_name"] = rule["gatewayGroupName"]
                tmp_out_rules = self._call(ClientType.OUT_RULE, **query)
                for tmp_out_rule in tmp_out_rules:
                    tmp_out_rule["alias_name"] = rule["aliasName"]
                    tmp_out_rule["gateway_port"] = gateway_port
                    tmp_out_rule["gateway_name"] = rule["gatewayGroupName"]
                result.extend(tmp_out_rules)

        for info in result:
            info.update(query)
            info["entry_ip"] = self._connection.get("entry_ip")
        return result

    def _call(self, client_type, *args, **kwargs):
        return CLIENT_MAPPER[client_type](self._connection, self._session).handle(*args, **kwargs)

    @property
    def _is_open_waf(self):
        return not bool(self._connection.get("entry_ip"))
