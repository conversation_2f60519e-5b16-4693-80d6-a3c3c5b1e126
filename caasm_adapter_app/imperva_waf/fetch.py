from caasm_adapter.sdk.adapter_fetch import fetch_sdk
from imperva_waf.manage import ImpervaWafManager


def find_asset(connection, fetch_type, page_index=0, session=None, page_size=10, **kwargs):
    manager = _manager(connection, session)
    if page_index != 0:
        return []

    result = fetch_sdk.build_asset(manager.find_asset(fetch_type), fetch_type)
    return fetch_sdk.return_success(result)


def get_auth_connection(connection, session=None):
    _manager(connection, session).auth()


def build_condition(connection, session, fetch_type):
    _manager(connection, session).auth()


def _manager(connection, session):
    return ImpervaWafManager(connection, session)
