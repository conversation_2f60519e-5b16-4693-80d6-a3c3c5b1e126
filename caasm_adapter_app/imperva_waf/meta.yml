name: "imperva_waf"
display_name: "ImpervaWaf"
description: "数据安全管控系统实现数据资产管理、敏感数据识别的统一管控能力;实现数据安全策略集中化管理、安全事件、安全风险统一管控、集中运维的能力;提供了敏感数据分布视图、敏感数据事件视图、敏感数据风险视图和敏感数据策略视图的分析展示能力"
type: "网络配置"
company: "impreva"
logo: "imperva_waf.png"
version: "v0.1"
priority: 1
properties:
  - WAF

connection:
  - name: address
    type: url
    required: true
    display_name: "地址"
    description: "地址信息"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'

  - name: username
    type: string
    required: true
    display_name: "用户名"
    description: "用户名称"
    validate_rules:
      - name: length
        error_hint: "用户名长度必须大于等于2且小于等于100"
        setting:
          min: 2
          max: 100

  - name: password
    type: password
    required: true
    display_name: "密码"
    description: "密码信息"
    validate_rules:
      - name: length
        error_hint: "密码长度必须大于等于1且小于等于100"
        setting:
          min: 1
          max: 100

  - name: entry_ip
    type: string
    required: false
    display_name: "入口IP"
    description: "反待模式下需要入口IP信息"
    validate_rules:
      - name: length
        error_hint: "入口IP长度必须大于等于1且小于等于100"
        setting:
          min: 5
          max: 100


fetch_setting:
  type: disposable
  point: "imperva_waf.fetch:find_asset"
  is_need_test_service: true
  condition_point: "imperva_waf.fetch:build_condition"
  test_auth_point: "imperva_waf.fetch:get_auth_connection"
  size: 100
  fetch_type_mapper:
    asset:
      - waf_server_group
    network:
      - waf


merge_setting:
  size: 1000
  setting: { }

convert_setting:
  size: 1000
  before_executor_mapper: { }
  executor_mapper: { }


fabric_setting:
  choose_point_mapper: { }