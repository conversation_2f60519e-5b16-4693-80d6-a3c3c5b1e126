from requests.auth import <PERSON><PERSON><PERSON><PERSON>asic<PERSON>uth

from caasm_adapter.util.client import FetchJsonResultClient
from caasm_adapter.util.exception import AdapterFetchRequestException


class ImpervaWafBaseClient(FetchJsonResultClient):
    def build_request_auth(self, *args, **kwargs):
        return HTTPBasicAuth(self.connection.get("username"), self.connection.get("password"))

    def parse_response(self, response, *args, **kwargs):
        return response.status_code, response.json()

    @property
    def data_key_name(self):
        return ""

    @property
    def flag_key_name(self):
        return ""

    def parse_biz_result(self, result, *args, **kwargs):
        if result[0] != 200:
            return self.error_handle(AdapterFetchRequestException("状态码异常"), *args, **kwargs)
        return super().parse_biz_result(result[1], *args, **kwargs)
