import datetime

from caasm_tool.reflect import parse_instance_callable
from quanzhi_api.clients.api import QZAPIList<PERSON>lient, QZAPIDetailClient


class ClientType(object):
    API = "api"
    API_DETAIL = "api_detail"


class QZAPIManager(object):
    _CLASS_MAPPER = {
        ClientType.API: QZAPIListClient,
        ClientType.API_DETAIL: QZAPIDetailClient,
    }
    _ENRICH_KEY = "_enrich_"
    _FIND_KEY = "_find_"

    def __init__(self, connection, session=None):
        self._connection = connection
        self._session = session
        self._enrich_method_mapper = parse_instance_callable(self, self._ENRICH_KEY)
        self._find_method_mapper = parse_instance_callable(self, self._FIND_KEY)

    def auth(self):
        self._call(ClientType.API, page_index=1, page_size=1)

    def find_asset(self, fetch_type, page_index=1, page_size=100):
        find_method = self._find_method_mapper.get(fetch_type)
        if not find_method:
            return []
        records = find_method(page_index, page_size)
        enrich_method = self._enrich_method_mapper.get(fetch_type)
        if not enrich_method:
            return records
        return [enrich_method(record) for record in records]

    def _find_api(self, page_index, page_size):
        return self._call(ClientType.API, page_index=page_index, page_size=page_size, **self.biz_query)

    def get_count(self, fetch_type, **kwargs):
        return self._instance(fetch_type).get_count(page_index=1, page_size=1, **self.biz_query)

    def _call(self, fetch_type, *args, **kwargs):
        return self._instance(fetch_type).handle(*args, **kwargs)

    def _instance(self, fetch_type):
        return self._CLASS_MAPPER[fetch_type](self._connection, self._session)

    @property
    def biz_query(self):
        query = {}
        if self.domains:
            query["visitDomains"] = self.domains
        if self.day:
            time_info = int((datetime.datetime.now() - datetime.timedelta(days=self.day)).timestamp() * 1000)
            query["discoverStartTime"] = time_info
        return query

    @property
    def day(self):
        return self._connection.get("query_day")

    @property
    def domains(self):
        return self._connection.get("v_domains") or []
