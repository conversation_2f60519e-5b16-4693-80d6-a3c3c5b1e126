from caasm_adapter.sdk.adapter_fetch import fetch_sdk
from quanzhi_api.manage import QZAPIManager


def find_asset(connection, fetch_type, page_index=0, page_size=1, session=None, **kwargs):
    result = _build_manager(connection, session).find_asset(fetch_type, page_index + 1, page_size)
    result = fetch_sdk.build_asset(result, fetch_type)
    return fetch_sdk.return_success(result)


def auth(connection, session=None):
    _build_manager(connection, session).auth()


def get_asset_count(connection, fetch_type, session=None, **kwargs):
    return _build_manager(connection, session).get_count(fetch_type)


def _build_manager(connection, session=None):
    return QZAPIManager(connection, session)
