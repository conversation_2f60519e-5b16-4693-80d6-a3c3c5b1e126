import abc
import datetime
import hashlib
import hmac
import json
import urllib.parse as urllib2

from caasm_adapter.util.client import FetchJsonResultClient


class QZAPIClient(FetchJsonResultClient, metaclass=abc.ABCMeta):
    METHOD = "post"
    TIMEOUT = 1200

    def __init__(self, *args, **kwargs):
        super(QZAPIClient, self).__init__(*args, **kwargs)
        self._payload = None
        self._now = datetime.datetime.now()
        self._now_utc = self._now.utcnow()

    def build_request_json(self, *args, **kwargs):
        return self.build_payload(*args, **kwargs)

    def build_request_header(self, *args, **kwargs):
        payload = self.build_payload(*args, **kwargs)
        amz_date = self._now_utc.strftime("%Y%m%dT%H%M%SZ")
        date_stamp = self._now_utc.strftime("%Y%m%d")
        timestamp = str(int(self._now.timestamp() * 1000))
        aws_header = {"host": "", "x-amz-date": amz_date}
        header = {
            "User-Agent": "QzOpenApiSDK",
            "Content-type": "application/json",
            "audit-open-api--key-type": "amz",
            "accessKey": self.access_key,
            "date": timestamp,
        }

        credential_scope = f"{date_stamp}/{self.region}/{self.service}/{self.aws4_request}"
        canonical_headers = self._generate_canonical_headers(aws_header)
        signed_headers = canonical_headers.get("signed_headers")
        payload_hash = hashlib.md5(json.dumps(payload).encode("utf-8")).hexdigest()
        payload_hash_map = {"md5": payload_hash}
        string_uri = "/"
        canonical_querystring = ""
        for k, v in payload_hash_map.items():
            canonical_querystring += k + "=" + str(v)
            canonical_querystring += "&"
        if len(canonical_querystring) > 0:
            canonical_querystring = canonical_querystring[:-1]
        canonical_headers_str = ""
        for k, v in aws_header.items():
            canonical_headers_str += k + ":" + v
            canonical_headers_str += "\n"
        fake_payload = ""
        payload_hex = hashlib.sha256(fake_payload.encode("utf-8")).hexdigest()
        canonical_request = (
            f"POST\n{string_uri}\n{canonical_querystring}\n{canonical_headers_str}\n{signed_headers}\n{payload_hex}"
        )
        canonical_request_hash = hashlib.sha256(canonical_request.encode("utf-8")).hexdigest()
        string_to_sign = f"""{self.algorithm}\n{amz_date}\n{credential_scope}\n{canonical_request_hash}"""
        signing_key = self._get_signature_key(self.secret_key, date_stamp, self.region, self.service)
        signature = hmac.new(signing_key, string_to_sign.encode("utf-8"), hashlib.sha256).hexdigest()
        aws_header[
            "Authorization"
        ] = f"{self.algorithm} Credential={self.access_key}/{credential_scope}, SignedHeaders={signed_headers}, Signature={signature}"
        header.update(aws_header)
        header["Host"] = urllib2.urlparse(self.address).hostname
        header["md5"] = payload_hash
        return header

    def build_payload(self, *args, **kwargs):
        if self._payload is None:
            self._payload = self.build_payload_core(*args, **kwargs)
        return self._payload

    def build_payload_core(self, *args, **kwargs):
        raise NotImplementedError

    @property
    def access_key(self):
        return self.connection.get("access_key", "")

    @property
    def secret_key(self):
        return self.connection.get("secret_key", "")

    @property
    def region(self):
        return self.connection.get("region", "china")

    @property
    def algorithm(self):
        return self.connection.get("algorithm", "AWS4-HMAC-SHA256")

    @property
    def service(self):
        return self.connection.get("service", "audit-open-core")

    @property
    def aws4_request(self):
        return self.connection.get("aws4_request", "aws4_request")

    @classmethod
    def _generate_canonical_headers(cls, headers):
        sorted_signed_headers = ""
        header_keys = headers.keys()
        lower_headers = {}
        for header in header_keys:
            lower_headers[header.lower()] = headers.get(header)
        sorted_lower_header_keys = sorted(lower_headers.keys())

        for lower_header in sorted_lower_header_keys:
            sorted_signed_headers += lower_header + ";"
        return {"signed_headers": sorted_signed_headers[:-1]}

    @classmethod
    def _get_signature_key(cls, key, date_stamp, region_name, service_name):
        k_date = cls._sign(("AWS4" + key).encode("utf-8"), date_stamp)
        k_region = cls._sign(k_date, region_name)
        k_service = cls._sign(k_region, service_name)
        k_signing = cls._sign(k_service, "aws4_request")
        return k_signing

    @classmethod
    def _sign(cls, key, msg):
        return hmac.new(key, msg.encode("utf-8"), hashlib.sha256).digest()

    @property
    def flag_key_name(self):
        return "code"

    @property
    def suc_flag(self):
        return "200"

    @property
    def data_key_name(self):
        return "data"

    def get_count(self, *args, **kwargs):
        result = self.handle_common(*args, **kwargs) or {}
        data = result.get("data") or {}
        count = data.get("totalCnt") or 0
        return count


class QZAPIQueryPageClient(QZAPIClient, metaclass=abc.ABCMeta):
    @property
    def data_key_name(self):
        return "data.rows"
