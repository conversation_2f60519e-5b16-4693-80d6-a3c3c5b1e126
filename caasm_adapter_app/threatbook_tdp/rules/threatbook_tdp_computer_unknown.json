{"canvas": {"nodes": [{"id": "root", "field": "根节点", "path": "root", "datatype": "object", "type": "asset", "level": 0, "sub_fields": [], "x": 0, "y": 0, "asset_type": "asset", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 100, "id": "root.id", "level": 1, "path": "id", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "id", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 200, "id": "root.machine", "level": 1, "path": "machine", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "machine", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 300, "id": "root.machine_name", "level": 1, "path": "machine_name", "sub_fields": [{"type": "string", "field": "element", "path": "machine_name.element"}], "type": "asset", "datatype": "list", "asset_type": "asset", "field": "machine_name", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 400, "id": "root.display_name", "level": 1, "path": "display_name", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "display_name", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 500, "id": "root.is_key_asset", "level": 1, "path": "is_key_asset", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "is_key_asset", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 600, "id": "root.machine_os", "level": 1, "path": "machine_os", "sub_fields": [{"type": "any", "field": "element", "path": "machine_os.element"}], "type": "asset", "datatype": "list", "asset_type": "asset", "field": "machine_os", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 700, "id": "root.assets", "level": 1, "path": "assets", "sub_fields": [{"field": "id", "path": "assets.id", "type": "integer", "x": 400, "y": 800}, {"field": "ip", "path": "assets.ip", "type": "string", "x": 400, "y": 900}, {"field": "name", "path": "assets.name", "type": "list", "sub_fields": [{"type": "string", "field": "element", "path": "assets.name.element"}], "x": 400, "y": 1000}, {"field": "source", "path": "assets.source", "type": "list", "sub_fields": [{"type": "string", "field": "element", "path": "assets.source.element"}], "x": 400, "y": 1100}, {"field": "section", "path": "assets.section", "type": "string", "x": 400, "y": 1200}, {"field": "location", "path": "assets.location", "type": "string", "x": 400, "y": 1300}, {"field": "level", "path": "assets.level", "type": "integer", "x": 400, "y": 1400}, {"field": "ext", "path": "assets.ext", "type": "string", "x": 400, "y": 1500}, {"field": "mac", "path": "assets.mac", "type": "string", "x": 400, "y": 1600}, {"field": "latestName", "path": "assets.latestName", "type": "string", "x": 400, "y": 1700}, {"field": "groupAndAssetName", "path": "assets.groupAndAssetName", "type": "string", "x": 400, "y": 1800}, {"field": "fullName", "path": "assets.fullName", "type": "string", "x": 400, "y": 1900}, {"field": "sub_type", "path": "assets.sub_type", "type": "string", "x": 400, "y": 2000}, {"field": "group_id", "path": "assets.group_id", "type": "integer", "x": 400, "y": 2100}, {"field": "group_name", "path": "assets.group_name", "type": "string", "x": 400, "y": 2200}], "type": "asset", "datatype": "object", "asset_type": "asset", "field": "assets", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 800, "id": "root.assets.id", "level": 2, "path": "assets.id", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "id", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 900, "id": "root.assets.ip", "level": 2, "path": "assets.ip", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "ip", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 1000, "id": "root.assets.name", "level": 2, "path": "assets.name", "sub_fields": [{"type": "string", "field": "element", "path": "assets.name.element"}], "type": "asset", "datatype": "list", "asset_type": "asset", "field": "name", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 1100, "id": "root.assets.source", "level": 2, "path": "assets.source", "sub_fields": [{"type": "string", "field": "element", "path": "assets.source.element"}], "type": "asset", "datatype": "list", "asset_type": "asset", "field": "source", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 1200, "id": "root.assets.section", "level": 2, "path": "assets.section", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "section", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 1300, "id": "root.assets.location", "level": 2, "path": "assets.location", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "location", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 1400, "id": "root.assets.level", "level": 2, "path": "assets.level", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "level", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 1500, "id": "root.assets.ext", "level": 2, "path": "assets.ext", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "ext", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 1600, "id": "root.assets.mac", "level": 2, "path": "assets.mac", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "mac", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 1700, "id": "root.assets.latestName", "level": 2, "path": "assets.latestName", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "latestName", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 1800, "id": "root.assets.groupAndAssetName", "level": 2, "path": "assets.groupAndAssetName", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "groupAndAssetName", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 1900, "id": "root.assets.fullName", "level": 2, "path": "assets.fullName", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "fullName", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 2000, "id": "root.assets.sub_type", "level": 2, "path": "assets.sub_type", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "sub_type", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 2100, "id": "root.assets.group_id", "level": 2, "path": "assets.group_id", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "group_id", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 400, "y": 2200, "id": "root.assets.group_name", "level": 2, "path": "assets.group_name", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "group_name", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 2300, "id": "root.web_frames", "level": 1, "path": "web_frames", "sub_fields": [{"type": "object", "sub_fields": [{"field": "name", "path": "web_frames.element.name", "type": "string", "x": 1408.0859375, "y": 2495.499999999999}, {"field": "time", "path": "web_frames.element.time", "type": "integer", "x": 1408.0859375, "y": 2595.499999999999}, {"field": "name_lower", "path": "web_frames.element.name_lower", "type": "string", "x": 1408.0859375, "y": 2695.499999999999}, {"field": "last_time", "path": "web_frames.element.last_time", "type": "integer", "x": 1408.0859375, "y": 2795.499999999999}, {"field": "sub_class", "path": "web_frames.element.sub_class", "type": "string", "x": 1408.0859375, "y": 2895.499999999999}, {"field": "class", "path": "web_frames.element.class", "type": "string", "x": 1408.0859375, "y": 2995.499999999999}, {"field": "is_new", "path": "web_frames.element.is_new", "type": "integer", "x": 1408.0859375, "y": 3095.499999999999}, {"field": "is_active", "path": "web_frames.element.is_active", "type": "integer", "x": 1408.0859375, "y": 3195.499999999999}], "field": "element", "path": "web_frames.element", "x": 1208.0859375, "y": 2395.499999999999}], "type": "asset", "datatype": "list", "asset_type": "asset", "field": "web_frames", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 2400, "id": "root.service_port", "level": 1, "path": "service_port", "sub_fields": [{"type": "object", "sub_fields": [{"field": "id", "path": "service_port.element.id", "type": "string", "x": 804.0859375, "y": 2598.5}, {"field": "service", "path": "service_port.element.service", "type": "string", "x": 804.0859375, "y": 2698.5}, {"field": "proto", "path": "service_port.element.proto", "type": "string", "x": 804.0859375, "y": 2798.5}, {"field": "port", "path": "service_port.element.port", "type": "integer", "x": 804.0859375, "y": 2898.5}, {"field": "frameworks", "path": "service_port.element.frameworks", "type": "list", "sub_fields": [{"type": "object", "sub_fields": [{"field": "name", "path": "service_port.element.frameworks.element.name", "type": "string"}, {"field": "time", "path": "service_port.element.frameworks.element.time", "type": "integer"}, {"field": "name_lower", "path": "service_port.element.frameworks.element.name_lower", "type": "string"}, {"field": "last_time", "path": "service_port.element.frameworks.element.last_time", "type": "integer"}, {"field": "sub_class", "path": "service_port.element.frameworks.element.sub_class", "type": "string"}, {"field": "class", "path": "service_port.element.frameworks.element.class", "type": "string"}, {"field": "is_new", "path": "service_port.element.frameworks.element.is_new", "type": "integer"}, {"field": "is_active", "path": "service_port.element.frameworks.element.is_active", "type": "integer"}], "field": "element", "path": "service_port.element.frameworks.element"}], "x": 804.0859375, "y": 2998.5}, {"field": "sortTime", "path": "service_port.element.sortTime", "type": "integer", "x": 804.0859375, "y": 3098.5}, {"field": "service_ip", "path": "service_port.element.service_ip", "type": "string", "x": 804.0859375, "y": 3198.5}, {"field": "service_class", "path": "service_port.element.service_class", "type": "string", "x": 804.0859375, "y": 3298.5}, {"field": "machine_name", "path": "service_port.element.machine_name", "type": "list", "sub_fields": [{"type": "string", "field": "element", "path": "service_port.element.machine_name.element"}], "x": 804.0859375, "y": 3398.5}, {"field": "service_name", "path": "service_port.element.service_name", "type": "string", "x": 804.0859375, "y": 3498.5}, {"field": "app_proto", "path": "service_port.element.app_proto", "type": "string", "x": 804.0859375, "y": 3598.5}, {"field": "is_public", "path": "service_port.element.is_public", "type": "integer", "x": 804.0859375, "y": 3698.5}, {"field": "first_time", "path": "service_port.element.first_time", "type": "integer", "x": 804.0859375, "y": 3798.5}, {"field": "last_time", "path": "service_port.element.last_time", "type": "integer", "x": 804.0859375, "y": 3898.5}, {"field": "public_last_time", "path": "service_port.element.public_last_time", "type": "integer", "x": 804.0859375, "y": 3998.5}, {"field": "is_new", "path": "service_port.element.is_new", "type": "integer", "x": 804.0859375, "y": 4098.5}, {"field": "is_active", "path": "service_port.element.is_active", "type": "integer", "x": 804.0859375, "y": 4198.5}], "field": "element", "path": "service_port.element", "x": 604.0859375, "y": 2498.5}], "type": "asset", "datatype": "list", "asset_type": "asset", "field": "service_port", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.assets.id.Fi8Zb11F8", "x": 576.0859375, "y": 799.5, "order": 2, "level": 3, "source_type": "integer", "path": "root.assets.id.Fi8Zb11F8", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "将数据转化为字符串", "description": "用于将将数据转化为字符串", "field": "将数据转化为字符串", "input": {"asset_web_type": "str", "description": "用于将将数据转化为字符串"}, "action_type": "str", "attrs": {"text": "将数据转化为字符串"}}, {"type": "asset", "asset_type": "action", "id": "root.assets.id.Fi8Zb11F8.D4beBQ7qG", "x": 737.0859375, "y": 797.5, "order": 3, "level": 4, "path": "root.assets.id.Fi8Zb11F8.D4beBQ7qG", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "将数据转化为列表类型", "description": "用于列表类型的转换，将非列表格式的数据转换为列表类型", "field": "将数据转化为列表类型", "input": {"asset_web_type": "list", "description": "用于列表类型的转换，将非列表格式的数据转换为列表类型"}, "action_type": "list", "attrs": {"text": "将数据转化为列表类型"}}, {"type": "asset", "asset_type": "action", "id": "root.assets.id.Fi8Zb11F8.D4beBQ7qG.sJLxgwAV-", "x": 914.0859375, "y": 798.5, "order": 4, "level": 5, "path": "root.assets.id.Fi8Zb11F8.D4beBQ7qG.sJLxgwAV-", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "asset_base.device.sid"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "M8fWYk5Dj", "display_name": "资产基础", "description": "asset_base.device.sid", "x": 1114.0859375, "y": 798.5, "label": "资产基础-设备-唯一标识", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.assets.latestName.RaeEjdaTm", "x": 609.0859375, "y": 1699.5, "order": 5, "level": 3, "source_type": "string", "path": "root.assets.latestName.RaeEjdaTm", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "computer.host_name"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "5UxM-RmSP", "display_name": "计算机", "description": "computer.host_name", "x": 809.0859375, "y": 1699.5, "label": "计算机-主机名称", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.assets.latestName.HpjRGPzu5", "x": 604.0859375, "y": 1771.5, "order": 6, "level": 3, "source_type": "string", "path": "root.assets.latestName.HpjRGPzu5", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "asset_base.device.name"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "AO2UdwAi9", "display_name": "资产基础", "description": "asset_base.device.name", "x": 804.0859375, "y": 1771.5, "label": "资产基础-设备-主机名", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.assets.latestName.L3m01_Xix", "x": 603.0859375, "y": 1621.5, "order": 7, "level": 3, "source_type": "string", "path": "root.assets.latestName.L3m01_Xix", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "asset_base.name"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "6AlwwHu5b", "display_name": "资产基础", "description": "asset_base.name", "x": 803.0859375, "y": 1621.5, "label": "资产基础-资产名称", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.SQfqUMTCl", "x": 286.0859375, "y": 4.5, "order": 8, "level": 1, "source_type": "object", "path": "root.SQfqUMTCl", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "生成对象数组", "description": "生成对象数组", "field": "生成对象数组", "input": {"asset_web_type": "add_list_object", "description": "生成对象数组", "size": "1", "field": "ips", "mapping": [{"name": "addr", "value": "${machine}"}]}, "action_type": "add_list_object", "attrs": {"text": "生成对象数组"}}, {"type": "asset", "asset_type": "action", "id": "root.SQfqUMTCl.RHx4KR0Hn", "x": 475.0859375, "y": 2.5, "order": 9, "level": 2, "path": "root.SQfqUMTCl.RHx4KR0Hn", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "network.ips"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "cLVt0V3qQ", "display_name": "网络", "description": "network.ips", "x": 675.0859375, "y": 2.5, "label": "网络-IP地址", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.service_port.XNsd46Y4x", "x": 404.08593750000006, "y": 2398.5, "order": 10, "level": 2, "source_type": "list", "path": "root.service_port.XNsd46Y4x", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "for循环", "description": "用于将数组进行循环操作", "field": "for循环", "input": {"asset_web_type": "for", "description": "用于将数组进行循环操作"}, "action_type": "for", "attrs": {"text": "for循环"}}, {"x": 604.0859375, "y": 2498.5, "id": "root.service_port.XNsd46Y4x.element", "level": 3, "path": "service_port.element", "sub_fields": [{"field": "id", "path": "service_port.element.id", "type": "string", "x": 804.0859375, "y": 2598.5}, {"field": "service", "path": "service_port.element.service", "type": "string", "x": 804.0859375, "y": 2698.5}, {"field": "proto", "path": "service_port.element.proto", "type": "string", "x": 804.0859375, "y": 2798.5}, {"field": "port", "path": "service_port.element.port", "type": "integer", "x": 804.0859375, "y": 2898.5}, {"field": "frameworks", "path": "service_port.element.frameworks", "type": "list", "sub_fields": [{"type": "object", "sub_fields": [{"field": "name", "path": "service_port.element.frameworks.element.name", "type": "string"}, {"field": "time", "path": "service_port.element.frameworks.element.time", "type": "integer"}, {"field": "name_lower", "path": "service_port.element.frameworks.element.name_lower", "type": "string"}, {"field": "last_time", "path": "service_port.element.frameworks.element.last_time", "type": "integer"}, {"field": "sub_class", "path": "service_port.element.frameworks.element.sub_class", "type": "string"}, {"field": "class", "path": "service_port.element.frameworks.element.class", "type": "string"}, {"field": "is_new", "path": "service_port.element.frameworks.element.is_new", "type": "integer"}, {"field": "is_active", "path": "service_port.element.frameworks.element.is_active", "type": "integer"}], "field": "element", "path": "service_port.element.frameworks.element"}], "x": 804.0859375, "y": 2998.5}, {"field": "sortTime", "path": "service_port.element.sortTime", "type": "integer", "x": 804.0859375, "y": 3098.5}, {"field": "service_ip", "path": "service_port.element.service_ip", "type": "string", "x": 804.0859375, "y": 3198.5}, {"field": "service_class", "path": "service_port.element.service_class", "type": "string", "x": 804.0859375, "y": 3298.5}, {"field": "machine_name", "path": "service_port.element.machine_name", "type": "list", "sub_fields": [{"type": "string", "field": "element", "path": "service_port.element.machine_name.element"}], "x": 804.0859375, "y": 3398.5}, {"field": "service_name", "path": "service_port.element.service_name", "type": "string", "x": 804.0859375, "y": 3498.5}, {"field": "app_proto", "path": "service_port.element.app_proto", "type": "string", "x": 804.0859375, "y": 3598.5}, {"field": "is_public", "path": "service_port.element.is_public", "type": "integer", "x": 804.0859375, "y": 3698.5}, {"field": "first_time", "path": "service_port.element.first_time", "type": "integer", "x": 804.0859375, "y": 3798.5}, {"field": "last_time", "path": "service_port.element.last_time", "type": "integer", "x": 804.0859375, "y": 3898.5}, {"field": "public_last_time", "path": "service_port.element.public_last_time", "type": "integer", "x": 804.0859375, "y": 3998.5}, {"field": "is_new", "path": "service_port.element.is_new", "type": "integer", "x": 804.0859375, "y": 4098.5}, {"field": "is_active", "path": "service_port.element.is_active", "type": "integer", "x": 804.0859375, "y": 4198.5}], "type": "asset", "datatype": "object", "asset_type": "asset", "field": "element", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 804.0859375, "y": 2598.5, "id": "root.service_port.XNsd46Y4x.element.id", "level": 4, "path": "service_port.element.id", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "id", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 804.0859375, "y": 2698.5, "id": "root.service_port.XNsd46Y4x.element.service", "level": 4, "path": "service_port.element.service", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "service", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 804.0859375, "y": 2798.5, "id": "root.service_port.XNsd46Y4x.element.proto", "level": 4, "path": "service_port.element.proto", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "proto", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 804.0859375, "y": 2898.5, "id": "root.service_port.XNsd46Y4x.element.port", "level": 4, "path": "service_port.element.port", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "port", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 804.0859375, "y": 2998.5, "id": "root.service_port.XNsd46Y4x.element.frameworks", "level": 4, "path": "service_port.element.frameworks", "sub_fields": [{"type": "object", "sub_fields": [{"field": "name", "path": "service_port.element.frameworks.element.name", "type": "string"}, {"field": "time", "path": "service_port.element.frameworks.element.time", "type": "integer"}, {"field": "name_lower", "path": "service_port.element.frameworks.element.name_lower", "type": "string"}, {"field": "last_time", "path": "service_port.element.frameworks.element.last_time", "type": "integer"}, {"field": "sub_class", "path": "service_port.element.frameworks.element.sub_class", "type": "string"}, {"field": "class", "path": "service_port.element.frameworks.element.class", "type": "string"}, {"field": "is_new", "path": "service_port.element.frameworks.element.is_new", "type": "integer"}, {"field": "is_active", "path": "service_port.element.frameworks.element.is_active", "type": "integer"}], "field": "element", "path": "service_port.element.frameworks.element"}], "type": "asset", "datatype": "list", "asset_type": "asset", "field": "frameworks", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 804.0859375, "y": 3098.5, "id": "root.service_port.XNsd46Y4x.element.sortTime", "level": 4, "path": "service_port.element.sortTime", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "sortTime", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 804.0859375, "y": 3198.5, "id": "root.service_port.XNsd46Y4x.element.service_ip", "level": 4, "path": "service_port.element.service_ip", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "service_ip", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 804.0859375, "y": 3298.5, "id": "root.service_port.XNsd46Y4x.element.service_class", "level": 4, "path": "service_port.element.service_class", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "service_class", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 804.0859375, "y": 3398.5, "id": "root.service_port.XNsd46Y4x.element.machine_name", "level": 4, "path": "service_port.element.machine_name", "sub_fields": [{"type": "string", "field": "element", "path": "service_port.element.machine_name.element"}], "type": "asset", "datatype": "list", "asset_type": "asset", "field": "machine_name", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 804.0859375, "y": 3498.5, "id": "root.service_port.XNsd46Y4x.element.service_name", "level": 4, "path": "service_port.element.service_name", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "service_name", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 804.0859375, "y": 3598.5, "id": "root.service_port.XNsd46Y4x.element.app_proto", "level": 4, "path": "service_port.element.app_proto", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "app_proto", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 804.0859375, "y": 3698.5, "id": "root.service_port.XNsd46Y4x.element.is_public", "level": 4, "path": "service_port.element.is_public", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "is_public", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 804.0859375, "y": 3798.5, "id": "root.service_port.XNsd46Y4x.element.first_time", "level": 4, "path": "service_port.element.first_time", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "first_time", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 804.0859375, "y": 3898.5, "id": "root.service_port.XNsd46Y4x.element.last_time", "level": 4, "path": "service_port.element.last_time", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "last_time", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 804.0859375, "y": 3998.5, "id": "root.service_port.XNsd46Y4x.element.public_last_time", "level": 4, "path": "service_port.element.public_last_time", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "public_last_time", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 804.0859375, "y": 4098.5, "id": "root.service_port.XNsd46Y4x.element.is_new", "level": 4, "path": "service_port.element.is_new", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "is_new", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 804.0859375, "y": 4198.5, "id": "root.service_port.XNsd46Y4x.element.is_active", "level": 4, "path": "service_port.element.is_active", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "is_active", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.service_port.XNsd46Y4x.hr5mgfN2k", "x": 619.0859375, "y": 2395.5, "order": 11, "level": 3, "path": "root.service_port.XNsd46Y4x.hr5mgfN2k", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "network.ports"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "SoDMlM2WL", "display_name": "网络", "description": "network.ports", "x": 819.0859375, "y": 2395.5, "label": "网络-端口", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.service_port.XNsd46Y4x.element.app_proto.pKz2ZpS4u", "x": 1002.1718750000001, "y": 3596.000000000001, "order": 12, "level": 5, "source_type": "string", "path": "root.service_port.XNsd46Y4x.element.app_proto.pKz2ZpS4u", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "network.ports.protocol"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "9N2uNCrpX", "display_name": "网络", "description": "network.ports.protocol", "x": 1202.171875, "y": 3596.000000000001, "label": "网络-端口-协议", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.service_port.XNsd46Y4x.element.service_ip.BPrfq3Z3S", "x": 993.1718750000001, "y": 3194.8400000005945, "order": 13, "level": 5, "source_type": "string", "path": "root.service_port.XNsd46Y4x.element.service_ip.BPrfq3Z3S", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "network.ports.ip"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "bUbmFw0Xv", "display_name": "网络", "description": "network.ports.ip", "x": 1193.171875, "y": 3194.8400000005945, "label": "网络-端口-关联IP", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.service_port.XNsd46Y4x.element.service_name.XkH_kJK6b", "x": 987.1718750000001, "y": 3494.9999999999995, "order": 14, "level": 5, "source_type": "string", "path": "root.service_port.XNsd46Y4x.element.service_name.XkH_kJK6b", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "network.ports.service_name"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "LxYOmM-La", "display_name": "网络", "description": "network.ports.service_name", "x": 1187.171875, "y": 3494.9999999999995, "label": "网络-端口-服务名称", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.web_frames.9LKl8fOmX", "x": 1008.0859375, "y": 2295.499999999999, "order": 16, "level": 2, "source_type": "list", "path": "root.web_frames.9LKl8fOmX", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "for循环", "description": "用于将数组进行循环操作", "field": "for循环", "input": {"asset_web_type": "for", "description": "用于将数组进行循环操作"}, "action_type": "for", "attrs": {"text": "for循环"}}, {"x": 1208.0859375, "y": 2395.499999999999, "id": "root.web_frames.9LKl8fOmX.element", "level": 3, "path": "web_frames.element", "sub_fields": [{"field": "name", "path": "web_frames.element.name", "type": "string", "x": 1408.0859375, "y": 2495.499999999999}, {"field": "time", "path": "web_frames.element.time", "type": "integer", "x": 1408.0859375, "y": 2595.499999999999}, {"field": "name_lower", "path": "web_frames.element.name_lower", "type": "string", "x": 1408.0859375, "y": 2695.499999999999}, {"field": "last_time", "path": "web_frames.element.last_time", "type": "integer", "x": 1408.0859375, "y": 2795.499999999999}, {"field": "sub_class", "path": "web_frames.element.sub_class", "type": "string", "x": 1408.0859375, "y": 2895.499999999999}, {"field": "class", "path": "web_frames.element.class", "type": "string", "x": 1408.0859375, "y": 2995.499999999999}, {"field": "is_new", "path": "web_frames.element.is_new", "type": "integer", "x": 1408.0859375, "y": 3095.499999999999}, {"field": "is_active", "path": "web_frames.element.is_active", "type": "integer", "x": 1408.0859375, "y": 3195.499999999999}], "type": "asset", "datatype": "object", "asset_type": "asset", "field": "element", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 1408.0859375, "y": 2495.499999999999, "id": "root.web_frames.9LKl8fOmX.element.name", "level": 4, "path": "web_frames.element.name", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "name", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 1408.0859375, "y": 2595.499999999999, "id": "root.web_frames.9LKl8fOmX.element.time", "level": 4, "path": "web_frames.element.time", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "time", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 1408.0859375, "y": 2695.499999999999, "id": "root.web_frames.9LKl8fOmX.element.name_lower", "level": 4, "path": "web_frames.element.name_lower", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "name_lower", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 1408.0859375, "y": 2795.499999999999, "id": "root.web_frames.9LKl8fOmX.element.last_time", "level": 4, "path": "web_frames.element.last_time", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "last_time", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 1408.0859375, "y": 2895.499999999999, "id": "root.web_frames.9LKl8fOmX.element.sub_class", "level": 4, "path": "web_frames.element.sub_class", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "sub_class", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 1408.0859375, "y": 2995.499999999999, "id": "root.web_frames.9LKl8fOmX.element.class", "level": 4, "path": "web_frames.element.class", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "class", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 1408.0859375, "y": 3095.499999999999, "id": "root.web_frames.9LKl8fOmX.element.is_new", "level": 4, "path": "web_frames.element.is_new", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "is_new", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 1408.0859375, "y": 3195.499999999999, "id": "root.web_frames.9LKl8fOmX.element.is_active", "level": 4, "path": "web_frames.element.is_active", "sub_fields": [], "type": "asset", "datatype": "integer", "asset_type": "asset", "field": "is_active", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.web_frames.9LKl8fOmX.Pv2aCva-Q", "x": 1330.0859375, "y": 2293.499999999999, "order": 17, "level": 3, "path": "root.web_frames.9LKl8fOmX.Pv2aCva-Q", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "computer.webframes"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "CL0wSDnsC", "display_name": "计算机", "description": "computer.webframes", "x": 1530.0859375, "y": 2293.499999999999, "label": "计算机-Web框架", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.service_port.XNsd46Y4x.element.id.Er0fEqJKI", "x": 974.0859375, "y": 2595.5, "order": 1, "level": 5, "source_type": "string", "path": "root.service_port.XNsd46Y4x.element.id.Er0fEqJKI", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "删除字段", "description": "用于删除字段", "field": "删除字段", "input": {"asset_web_type": "drop", "description": "用于删除字段"}, "action_type": "drop", "attrs": {"text": "删除字段"}}], "edges": [{"source": "root", "target": "root.id", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.86711029800796811675147990824", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 100, "anchor_index": 0}}, {"source": "root", "target": "root.machine", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.92899756335570041675147990828", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 200, "anchor_index": 0}}, {"source": "root", "target": "root.machine_name", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.88673672972263871675147990829", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 300, "anchor_index": 0}}, {"source": "root", "target": "root.display_name", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.55159479561316421675147990829", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 400, "anchor_index": 0}}, {"source": "root", "target": "root.is_key_asset", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.94530828287669971675147990829", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 500, "anchor_index": 0}}, {"source": "root", "target": "root.machine_os", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.226298841248414151675147990829", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 600, "anchor_index": 0}}, {"source": "root", "target": "root.assets", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.86627591672941831675147990830", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 700, "anchor_index": 0}}, {"source": "root.assets", "target": "root.assets.id", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.26707322916696621675147990830", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 725.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 800, "anchor_index": 0}}, {"source": "root.assets", "target": "root.assets.ip", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.121506753614019081675147990830", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 725.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 900, "anchor_index": 0}}, {"source": "root.assets", "target": "root.assets.name", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.107797295257678671675147990831", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 725.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 1000, "anchor_index": 0}}, {"source": "root.assets", "target": "root.assets.source", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.8264094400439631675147990832", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 725.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 1100, "anchor_index": 0}}, {"source": "root.assets", "target": "root.assets.section", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.75846565246616391675147990832", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 725.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 1200, "anchor_index": 0}}, {"source": "root.assets", "target": "root.assets.location", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.89076816554773111675147990832", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 725.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 1300, "anchor_index": 0}}, {"source": "root.assets", "target": "root.assets.level", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.46585003351048071675147990832", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 725.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 1400, "anchor_index": 0}}, {"source": "root.assets", "target": "root.assets.ext", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.64981272307882841675147990833", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 725.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 1500, "anchor_index": 0}}, {"source": "root.assets", "target": "root.assets.mac", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.0123530413005410461675147990833", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 725.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 1600, "anchor_index": 0}}, {"source": "root.assets", "target": "root.assets.latestName", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.26029001540472561675147990833", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 725.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 1700, "anchor_index": 0}}, {"source": "root.assets", "target": "root.assets.groupAndAssetName", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.6603182444840841675147990833", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 725.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 1800, "anchor_index": 0}}, {"source": "root.assets", "target": "root.assets.fullName", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.228459455925788651675147990833", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 725.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 1900, "anchor_index": 0}}, {"source": "root.assets", "target": "root.assets.sub_type", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.071921051072299851675147990834", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 725.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 2000, "anchor_index": 0}}, {"source": "root.assets", "target": "root.assets.group_id", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.498853843487808041675147990834", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 725.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 2100, "anchor_index": 0}}, {"source": "root.assets", "target": "root.assets.group_name", "source_anchor": 1, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.0082449241343276471675147990834", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 200, "y": 725.5, "anchor_index": 1}, "end_point": {"x": 349.5, "y": 2200, "anchor_index": 0}}, {"source": "root", "target": "root.web_frames", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.0140808216279306731675147990834", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 2300, "anchor_index": 0}}, {"source": "root", "target": "root.service_port", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.35206346011237821675147990834", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 2400, "anchor_index": 0}}, {"source": "root.assets.id", "target": "root.assets.id.Fi8Zb11F8", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.64970994126499161675148199035", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 450.5, "y": 800, "anchor_index": 1}, "end_point": {"x": 525.5859375, "y": 799.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.assets.id.Fi8Zb11F8", "target": "root.assets.id.Fi8Zb11F8.D4beBQ7qG", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.40814366080020961675148210835", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 626.5859375, "y": 799.5, "anchor_index": 1}, "end_point": {"x": 686.5859375, "y": 797.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.assets.id.Fi8Zb11F8.D4beBQ7qG", "target": "root.assets.id.Fi8Zb11F8.D4beBQ7qG.sJLxgwAV-", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.410236899529522471675148226919", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 787.5859375, "y": 797.5, "anchor_index": 1}, "end_point": {"x": 863.5859375, "y": 798.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.assets.id.Fi8Zb11F8.D4beBQ7qG.sJLxgwAV-", "target": "M8fWYk5Dj", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.7948571493361941675148235105", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 964.5859375, "y": 798.5, "anchor_index": 1}, "end_point": {"x": 1063.5859375, "y": 798.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.assets.latestName", "target": "root.assets.latestName.RaeEjdaTm", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.57693220043164711675148252436", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 450.5, "y": 1700, "anchor_index": 1}, "end_point": {"x": 558.5859375, "y": 1699.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.assets.latestName.RaeEjdaTm", "target": "5UxM-RmSP", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.209219901293754781675148257372", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 659.5859375, "y": 1699.5, "anchor_index": 1}, "end_point": {"x": 758.5859375, "y": 1699.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.assets.latestName", "target": "root.assets.latestName.HpjRGPzu5", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.491386523600153341675148260552", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 450.5, "y": 1700, "anchor_index": 1}, "end_point": {"x": 553.5859375, "y": 1771.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.assets.latestName.HpjRGPzu5", "target": "AO2UdwAi9", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.65710621039767371675148269035", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 654.5859375, "y": 1771.5, "anchor_index": 1}, "end_point": {"x": 753.5859375, "y": 1771.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.assets.latestName", "target": "root.assets.latestName.L3m01_Xix", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.346333860959547661675148271419", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 450.5, "y": 1700, "anchor_index": 1}, "end_point": {"x": 552.5859375, "y": 1621.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.assets.latestName.L3m01_Xix", "target": "6AlwwHu5b", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.5961058625700361675148278254", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 653.5859375, "y": 1621.5, "anchor_index": 1}, "end_point": {"x": 752.5859375, "y": 1621.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root", "target": "root.SQfqUMTCl", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.424831933809074471675148308835", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 50.5, "y": 0, "anchor_index": 1}, "end_point": {"x": 235.5859375, "y": 4.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.SQfqUMTCl", "target": "root.SQfqUMTCl.RHx4KR0Hn", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.80860437798772991675148336804", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 336.5859375, "y": 4.5, "anchor_index": 1}, "end_point": {"x": 424.5859375, "y": 2.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.SQfqUMTCl.RHx4KR0Hn", "target": "cLVt0V3qQ", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.55636629083279841675148345503", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 525.5859375, "y": 2.5, "anchor_index": 1}, "end_point": {"x": 624.5859375, "y": 2.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.service_port", "target": "root.service_port.XNsd46Y4x", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.26392824539369531675148375153", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 2400, "anchor_index": 1}, "end_point": {"x": 353.58593750000006, "y": 2398.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.service_port.XNsd46Y4x", "target": "root.service_port.XNsd46Y4x.element", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.59348656400657811675148385221", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 454.58593750000006, "y": 2398.5, "anchor_index": 1}, "end_point": {"x": 553.5859375, "y": 2498.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.service_port.XNsd46Y4x.element", "target": "root.service_port.XNsd46Y4x.element.id", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.182767204491167461675148385221", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 604.0859375, "y": 2524, "anchor_index": 1}, "end_point": {"x": 753.5859375, "y": 2598.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.service_port.XNsd46Y4x.element", "target": "root.service_port.XNsd46Y4x.element.service", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.63211215704574061675148385221", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 604.0859375, "y": 2524, "anchor_index": 1}, "end_point": {"x": 753.5859375, "y": 2698.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.service_port.XNsd46Y4x.element", "target": "root.service_port.XNsd46Y4x.element.proto", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.116548844987252711675148385221", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 604.0859375, "y": 2524, "anchor_index": 1}, "end_point": {"x": 753.5859375, "y": 2798.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.service_port.XNsd46Y4x.element", "target": "root.service_port.XNsd46Y4x.element.port", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.43661330070155691675148385222", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 604.0859375, "y": 2524, "anchor_index": 1}, "end_point": {"x": 753.5859375, "y": 2898.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.service_port.XNsd46Y4x.element", "target": "root.service_port.XNsd46Y4x.element.frameworks", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.92991693136894081675148385222", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 604.0859375, "y": 2524, "anchor_index": 1}, "end_point": {"x": 753.5859375, "y": 2998.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.service_port.XNsd46Y4x.element", "target": "root.service_port.XNsd46Y4x.element.sortTime", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.063269563937448761675148385222", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 604.0859375, "y": 2524, "anchor_index": 1}, "end_point": {"x": 753.5859375, "y": 3098.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.service_port.XNsd46Y4x.element", "target": "root.service_port.XNsd46Y4x.element.service_ip", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.253675907293643871675148385222", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 604.0859375, "y": 2524, "anchor_index": 1}, "end_point": {"x": 753.5859375, "y": 3198.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.service_port.XNsd46Y4x.element", "target": "root.service_port.XNsd46Y4x.element.service_class", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.372864492104186151675148385222", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 604.0859375, "y": 2524, "anchor_index": 1}, "end_point": {"x": 753.5859375, "y": 3298.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.service_port.XNsd46Y4x.element", "target": "root.service_port.XNsd46Y4x.element.machine_name", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.28413550147159651675148385222", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 604.0859375, "y": 2524, "anchor_index": 1}, "end_point": {"x": 753.5859375, "y": 3398.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.service_port.XNsd46Y4x.element", "target": "root.service_port.XNsd46Y4x.element.service_name", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.99110781142394841675148385222", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 604.0859375, "y": 2524, "anchor_index": 1}, "end_point": {"x": 753.5859375, "y": 3498.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.service_port.XNsd46Y4x.element", "target": "root.service_port.XNsd46Y4x.element.app_proto", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.214652361331962641675148385222", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 604.0859375, "y": 2524, "anchor_index": 1}, "end_point": {"x": 753.5859375, "y": 3598.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.service_port.XNsd46Y4x.element", "target": "root.service_port.XNsd46Y4x.element.is_public", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.483794532985201141675148385222", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 604.0859375, "y": 2524, "anchor_index": 1}, "end_point": {"x": 753.5859375, "y": 3698.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.service_port.XNsd46Y4x.element", "target": "root.service_port.XNsd46Y4x.element.first_time", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.67695163208933561675148385222", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 604.0859375, "y": 2524, "anchor_index": 1}, "end_point": {"x": 753.5859375, "y": 3798.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.service_port.XNsd46Y4x.element", "target": "root.service_port.XNsd46Y4x.element.last_time", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.58138313994398951675148385222", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 604.0859375, "y": 2524, "anchor_index": 1}, "end_point": {"x": 753.5859375, "y": 3898.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.service_port.XNsd46Y4x.element", "target": "root.service_port.XNsd46Y4x.element.public_last_time", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.055569436228138261675148385222", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 604.0859375, "y": 2524, "anchor_index": 1}, "end_point": {"x": 753.5859375, "y": 3998.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.service_port.XNsd46Y4x.element", "target": "root.service_port.XNsd46Y4x.element.is_new", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.100030364220519591675148385223", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 604.0859375, "y": 2524, "anchor_index": 1}, "end_point": {"x": 753.5859375, "y": 4098.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.service_port.XNsd46Y4x.element", "target": "root.service_port.XNsd46Y4x.element.is_active", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.28786043733163071675148385223", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 604.0859375, "y": 2524, "anchor_index": 1}, "end_point": {"x": 753.5859375, "y": 4198.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.service_port.XNsd46Y4x", "target": "root.service_port.XNsd46Y4x.hr5mgfN2k", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.95663018748599131675148389035", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 454.58593750000006, "y": 2398.5, "anchor_index": 1}, "end_point": {"x": 568.5859375, "y": 2395.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.service_port.XNsd46Y4x.hr5mgfN2k", "target": "SoDMlM2WL", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.63532913959569991675148399302", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 669.5859375, "y": 2395.5, "anchor_index": 1}, "end_point": {"x": 768.5859375, "y": 2395.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.service_port.XNsd46Y4x.element.app_proto", "target": "root.service_port.XNsd46Y4x.element.app_proto.pKz2ZpS4u", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.53160902440168961675148406752", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 854.5859375, "y": 3598.5, "anchor_index": 1}, "end_point": {"x": 951.6718750000001, "y": 3596.000000000001, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.service_port.XNsd46Y4x.element.app_proto.pKz2ZpS4u", "target": "9N2uNCrpX", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.97265650437464851675148412952", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1052.671875, "y": 3596.000000000001, "anchor_index": 1}, "end_point": {"x": 1151.671875, "y": 3596.000000000001, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.service_port.XNsd46Y4x.element.service_ip", "target": "root.service_port.XNsd46Y4x.element.service_ip.BPrfq3Z3S", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.0414578244689618461675148437585", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 854.5859375, "y": 3198.5, "anchor_index": 1}, "end_point": {"x": 942.6718750000001, "y": 3194.8400000005945, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.service_port.XNsd46Y4x.element.service_ip.BPrfq3Z3S", "target": "bUbmFw0Xv", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.57251759984094131675148447154", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1043.671875, "y": 3194.8400000005945, "anchor_index": 1}, "end_point": {"x": 1142.671875, "y": 3194.8400000005945, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.service_port.XNsd46Y4x.element.service_name", "target": "root.service_port.XNsd46Y4x.element.service_name.XkH_kJK6b", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.96553694073885631675148470867", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 854.5859375, "y": 3498.5, "anchor_index": 1}, "end_point": {"x": 936.6718750000001, "y": 3494.9999999999995, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.service_port.XNsd46Y4x.element.service_name.XkH_kJK6b", "target": "LxYOmM-La", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.342557617193468561675148476404", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1037.671875, "y": 3494.9999999999995, "anchor_index": 1}, "end_point": {"x": 1136.671875, "y": 3494.9999999999995, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.web_frames", "target": "root.web_frames.9LKl8fOmX", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.79437690392849941675148519135", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 2300, "anchor_index": 1}, "end_point": {"x": 957.5859375, "y": 2295.499999999999, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.web_frames.9LKl8fOmX", "target": "root.web_frames.9LKl8fOmX.element", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.468762110829566761675148522746", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1058.5859375, "y": 2295.499999999999, "anchor_index": 1}, "end_point": {"x": 1157.5859375, "y": 2395.499999999999, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.web_frames.9LKl8fOmX.element", "target": "root.web_frames.9LKl8fOmX.element.name", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.81122273840027131675148522747", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1208.0859375, "y": 2420.999999999999, "anchor_index": 1}, "end_point": {"x": 1357.5859375, "y": 2495.499999999999, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.web_frames.9LKl8fOmX.element", "target": "root.web_frames.9LKl8fOmX.element.time", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.90078166928557431675148522747", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1208.0859375, "y": 2420.999999999999, "anchor_index": 1}, "end_point": {"x": 1357.5859375, "y": 2595.499999999999, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.web_frames.9LKl8fOmX.element", "target": "root.web_frames.9LKl8fOmX.element.name_lower", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.90940138789932661675148522747", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1208.0859375, "y": 2420.999999999999, "anchor_index": 1}, "end_point": {"x": 1357.5859375, "y": 2695.499999999999, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.web_frames.9LKl8fOmX.element", "target": "root.web_frames.9LKl8fOmX.element.last_time", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.61270899444456761675148522747", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1208.0859375, "y": 2420.999999999999, "anchor_index": 1}, "end_point": {"x": 1357.5859375, "y": 2795.499999999999, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.web_frames.9LKl8fOmX.element", "target": "root.web_frames.9LKl8fOmX.element.sub_class", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.44732332536485231675148522748", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1208.0859375, "y": 2420.999999999999, "anchor_index": 1}, "end_point": {"x": 1357.5859375, "y": 2895.499999999999, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.web_frames.9LKl8fOmX.element", "target": "root.web_frames.9LKl8fOmX.element.class", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.64800897786729571675148522748", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1208.0859375, "y": 2420.999999999999, "anchor_index": 1}, "end_point": {"x": 1357.5859375, "y": 2995.499999999999, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.web_frames.9LKl8fOmX.element", "target": "root.web_frames.9LKl8fOmX.element.is_new", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.607812202058751675148522748", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1208.0859375, "y": 2420.999999999999, "anchor_index": 1}, "end_point": {"x": 1357.5859375, "y": 3095.499999999999, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.web_frames.9LKl8fOmX.element", "target": "root.web_frames.9LKl8fOmX.element.is_active", "source_anchor": 1, "target_anchor": 0, "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.86558734063374351675148522748", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1208.0859375, "y": 2420.999999999999, "anchor_index": 1}, "end_point": {"x": 1357.5859375, "y": 3195.499999999999, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.web_frames.9LKl8fOmX", "target": "root.web_frames.9LKl8fOmX.Pv2aCva-Q", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.037955559662598671675148526068", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1058.5859375, "y": 2295.499999999999, "anchor_index": 1}, "end_point": {"x": 1279.5859375, "y": 2293.499999999999, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.web_frames.9LKl8fOmX.Pv2aCva-Q", "target": "CL0wSDnsC", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.56870321982098471675148532168", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1380.5859375, "y": 2293.499999999999, "anchor_index": 1}, "end_point": {"x": 1479.5859375, "y": 2293.499999999999, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.service_port.XNsd46Y4x.element.id", "target": "root.service_port.XNsd46Y4x.element.id.Er0fEqJKI", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.223998226284684241675568439585", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 854.5859375, "y": 2598.5, "anchor_index": 1}, "end_point": {"x": 923.5859375, "y": 2595.5, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}]}, "rules": [{"name": "add_list_object", "setting": {"size": 1, "field": "ips", "mapping": [{"name": "addr", "value": "${machine}"}]}, "sub_rules": [], "_id": "root.SQfqUMTCl"}, {"name": "enter", "setting": {"values": "network.ips", "field": "${ips}"}, "sub_rules": [], "_id": "root.SQfqUMTCl.RHx4KR0Hn"}, {"name": "str", "setting": {"field": "assets.id"}, "sub_rules": [], "_id": "root.assets.id.Fi8Zb11F8"}, {"name": "list", "setting": {"field": "assets.id"}, "sub_rules": [], "_id": "root.assets.id.Fi8Zb11F8.D4beBQ7qG"}, {"name": "enter", "setting": {"values": "asset_base.device.sid", "field": "${assets.id}"}, "sub_rules": [], "_id": "root.assets.id.Fi8Zb11F8.D4beBQ7qG.sJLxgwAV-"}, {"name": "enter", "setting": {"values": "computer.host_name", "field": "${assets.latestName}"}, "sub_rules": [], "_id": "root.assets.latestName.RaeEjdaTm"}, {"name": "enter", "setting": {"values": "asset_base.device.name", "field": "${assets.latestName}"}, "sub_rules": [], "_id": "root.assets.latestName.HpjRGPzu5"}, {"name": "enter", "setting": {"values": "asset_base.name", "field": "${assets.latestName}"}, "sub_rules": [], "_id": "root.assets.latestName.L3m01_Xix"}, {"name": "for", "setting": {"field": "web_frames"}, "sub_rules": [], "_id": "root.web_frames.9LKl8fOmX"}, {"name": "enter", "setting": {"values": "computer.webframes", "field": "${web_frames}"}, "sub_rules": [], "_id": "root.web_frames.9LKl8fOmX.Pv2aCva-Q"}, {"name": "for", "setting": {"field": "service_port"}, "sub_rules": [{"id": "root.service_port.XNsd46Y4x.element.id.Er0fEqJKI", "name": "drop", "setting": {"field": "id"}, "sub_rules": []}, {"id": "root.service_port.XNsd46Y4x.element.service_ip.BPrfq3Z3S", "name": "enter", "setting": {"values": "ip", "field": "${service_ip}"}, "sub_rules": []}, {"id": "root.service_port.XNsd46Y4x.element.service_name.XkH_kJK6b", "name": "enter", "setting": {"values": "service_name", "field": "${service_name}"}, "sub_rules": []}, {"id": "root.service_port.XNsd46Y4x.element.app_proto.pKz2ZpS4u", "name": "enter", "setting": {"values": "protocol", "field": "${app_proto}"}, "sub_rules": []}], "_id": "root.service_port.XNsd46Y4x"}, {"name": "enter", "setting": {"values": "network.ports", "field": "${service_port}"}, "sub_rules": [], "_id": "root.service_port.XNsd46Y4x.hr5mgfN2k"}], "adapter_name": "threatbook_tdp", "fetch_type": "computer", "model_name": "computer", "asset_type": "unknown", "internal": true}