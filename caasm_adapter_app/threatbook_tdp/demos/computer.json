{"id": "*************", "machine": "*************", "machine_name": ["test.com.cn", "test.com"], "display_name": "", "is_key_asset": "", "machine_os": [], "assets": {"id": 28683, "ip": "**********/16", "name": ["SSLO地址"], "source": ["manual"], "section": "服务器", "location": "", "level": 0, "ext": "28683", "mac": "", "latestName": "SSLO地址", "groupAndAssetName": "未分组/[SSLO地址]", "fullName": "SSLO地址", "sub_type": "", "group_id": 0, "group_name": "未分组"}, "web_frames": [{"name": "Spring", "time": 1658206330, "name_lower": "spring", "last_time": 1663909250, "sub_class": "", "class": "web_framework", "is_new": false, "is_active": false}], "service_port": [{"id": "*************", "service": "nginx", "proto": "TCP", "port": 443, "frameworks": [{"name": "Spring", "time": 1658206330, "name_lower": "spring", "last_time": 1663909250, "sub_class": "", "class": "web_framework", "is_new": false, "is_active": false}], "sortTime": 0, "service_ip": "*************", "service_class": "web", "machine_name": ["wx3g.test.com.cn"], "service_name": "nginx", "app_proto": "http", "is_public": true, "first_time": 1657849262, "last_time": 1666678144, "public_last_time": 1666678144, "is_new": false, "is_active": true}, {"id": "*************", "service": "nginx", "proto": "TCP", "port": 80, "applications": [{"name": "<PERSON>", "time": 1658415637, "name_lower": "jenkins", "last_time": 1660502247, "sub_class": "管理工具", "class": "web_application", "is_new": false, "is_active": false}], "sortTime": 0, "service_ip": "*************", "service_class": "web", "machine_name": ["test.cn"], "service_name": "nginx", "app_proto": "http", "is_public": true, "first_time": 1657849264, "last_time": 1666678144, "public_last_time": 1666678144, "is_new": false, "is_active": true}]}