from caasm_adapter.sdk.adapter_fetch import fetch_sdk

from threatbook_tdp.manage import ThreatBookTDPManager


def find_asset(connection, fetch_type, page_index=0, page_size=1, session=None, condition=None, **kwargs):
    records = ThreatBookTDPManager(connection, session, condition).find_asset(fetch_type, page_index + 1, page_size)
    result = fetch_sdk.build_asset(records, fetch_type)
    return fetch_sdk.return_success(result)


def build_query_condition(connection, session=None, fetch_type=None):
    manager = ThreatBookTDPManager(connection, session)
    manager.auth()
    domain_names = ThreatBookTDPManager(connection, session)._call("api_host") or []
    return {"domain_names": domain_names}


def get_auth_connection(connection, session):
    ThreatBookTDPManager(connection, session).auth()
