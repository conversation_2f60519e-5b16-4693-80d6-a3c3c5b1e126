from caasm_adapter.util.client import FetchJsonResultClient


class ThreatBookTDPClient(FetchJsonResultClient):
    referer_url = "/login?callback=/investigation/logquery&params={}"

    @property
    def data_key_name(self):
        return "data"

    @property
    def flag_key_name(self):
        return "response_code"

    @property
    def suc_flag(self):
        return 0

    def build_request_header(self, *args, **kwargs):
        referer = self.build_url(url=self.referer_url)
        headers = {"referer": referer}
        return headers


class ThreatBookTDPPostClient(ThreatBookTDPClient):
    METHOD = "post"

    @property
    def data_key_name(self):
        return "data.items"
