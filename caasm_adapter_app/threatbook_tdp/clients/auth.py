import base64

import ddddocr

from caasm_adapter.util.exception import AdapterFetchAuthFailedException
from threatbook_tdp.clients.base import ThreatBookTDPPostClient


class ThreatBookTDPAuthClient(ThreatBookTDPPostClient):
    URL = "/api/web/auth"

    def build_request_json(self, captcha=None):
        if captcha:
            result = {"user": self.username, "password": self.password, "recaptcha": captcha}
        else:
            result = {"user": self.username, "password": self.password}
        return result

    def parse_biz_result(self, result, *args, **kwargs):
        result = super(ThreatBookTDPAuthClient, self).parse_biz_result(result)
        return result == 0

    @property
    def data_key_name(self):
        return self.flag_key_name

    @property
    def username(self):
        return self.connection.get("username", "")

    @property
    def password(self):
        return self.connection.get("password", "")

    def parse_error_handle(self, result, *args, **kwargs):
        raise AdapterFetchAuthFailedException

    def error_handle(self, err, *args, **kwargs):
        raise AdapterFetchAuthFailedException


class ThreatBookTDPCaptchaClient(ThreatBookTDPPostClient):
    URL = "/api/recaptcha"
    METHOD = "POST"
    OCR = ddddocr.DdddOcr(beta=True, show_ad=False)

    @property
    def data_key_name(self):
        return ""

    def clean_result(self, result):
        base = result.get("data")
        base = base.replace("data:image/jpeg;base64,", "")
        base = base64.b64decode(base.replace("\n", ""))
        return self.OCR.classification(base)

    def error_handle(self, err, *args, **kwargs):
        raise AdapterFetchAuthFailedException

    def parse_error_handle(self, result, *args, **kwargs):
        raise AdapterFetchAuthFailedException
