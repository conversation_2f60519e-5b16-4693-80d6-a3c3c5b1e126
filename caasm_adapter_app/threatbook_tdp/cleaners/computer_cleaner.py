from caasm_adapter.fetcher.cleaners.base import FetchBaseCleaner


class ThreatBookTDPCleaner(FetchBaseCleaner):
    def clean_single(self, detail):
        service_ports = detail.get("service_port")

        if not service_ports:
            return

        if not isinstance(service_ports, list):
            return

        web_frames = []
        for service_port in service_ports:
            _frameworks = service_port.get("frameworks")
            _applications = service_port.get("applications")
            web_frames.extend(_frameworks) if _frameworks else ...
            web_frames.extend(_applications) if _applications else ...

        return {"web_frames": web_frames}
