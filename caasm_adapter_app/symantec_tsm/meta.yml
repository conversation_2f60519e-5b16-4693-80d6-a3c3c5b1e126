name: "symantec_tsm"
display_name: "TSM终端安全管理系统"
description: "赛门铁克Secospace TSM系统通过在端点接入网络前主动进行安全状态评估、建立基于用户角色的网络访问机制、为不符合安全基线的终端提供系统漏洞修复等技术手段，不仅将外网的病毒等不安全因素彻底屏蔽屏，而且对内网用户的行为进行了严格的规范，为企业和组织构建了一个完整、简单和易于管理的终端安全环境。"
type: "终端防护"
company: "赛门铁克"
logo: "symantec_tsm.webp"
version: "v0.1"
priority: 1
properties:
  - "终端防护"

connection:
  - name: username
    type: string
    required: true
    display_name: "用户名"
    description: "用户名称"
    validate_rules:
      - name: length
        error_hint: "用户名长度必须大于等于2且小于等于100"
        setting:
          min: 2
          max: 100

  - name: password
    type: password
    required: true
    display_name: "密码"
    description: "密码信息"
    validate_rules:
      - name: length
        error_hint: "密码长度必须大于等于6且小于等于100"
        setting:
          min: 6
          max: 100

  - name: address
    type: url
    required: true
    display_name: "地址"
    description: "地址信息"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'


fetch_setting:
  type: disposable
  point: "symantec_tsm.fetch:find_asset"
  size: 1000
  fetch_type_mapper:
    asset:
      - asset

fabric_setting:
  choose_point_mapper:
    asset: "symantec_tsm.fabric:choose_new_record"

merge_setting:
  size: 1000
  setting: { }

convert_setting:
  size: 1000
  before_executor_mapper: { }
  executor_mapper: { }