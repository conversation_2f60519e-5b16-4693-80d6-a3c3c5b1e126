name: "rising_esm"
display_name: "企业终端安全管理系统ESM3.0"
description: "瑞星ESM3.0实质上不只是一个管理平台，企业用户可以根据自身的需求在其上布置具有不同管理功能的子产品。本软件可以满足各类企业的不同需求，有针对性的解决企业遇到的各种安全风险，彻底改变了以往安全类软件功能过于笼统、不够灵活的缺点。"
type: "终端防护"
company: "瑞星"
logo: "rising_esm.jpeg"
version: "v0.1"
priority: 1
properties:
  - "终端防护"

connection:
  - name: username
    type: string
    required: true
    display_name: "用户名"
    description: "用户名称"
    validate_rules:
      - name: length
        error_hint: "用户名长度必须大于等于2且小于等于100"
        setting:
          min: 2
          max: 100

  - name: password
    type: password
    required: true
    display_name: "密码"
    description: "密码信息"
    validate_rules:
      - name: length
        error_hint: "密码长度必须大于等于6且小于等于100"
        setting:
          min: 6
          max: 100

  - name: address
    type: url
    required: true
    display_name: "地址"
    description: "地址信息"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'


fetch_setting:
  type: disposable
  point: "rising_esm.fetch:find_asset"
  size: 1000
  fetch_type_mapper:
    asset:
      - asset

fabric_setting:
  choose_point_mapper:
    asset: "rising_esm.fabric:choose_new_record"

merge_setting:
  size: 1000
  setting: { }

convert_setting:
  size: 1000
  before_executor_mapper: { }
  executor_mapper: { }