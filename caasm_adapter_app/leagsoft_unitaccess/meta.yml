name: "leagsoft_unitaccess"
display_name: "联软UniAccess终端安全管理系统"
description: "联软UniAccess 终端安全管理系统帮助企业搭建集中管控平台，提供资产管理、补丁管理、企业软件管理、用户操作权限管控、安全状态评估、主机监控审计、行为管理、移动存储介质管理、桌面安全管控等主要功能模块，解决终端的各种安全管理问题和满足各种合规需求，是强大的终端安全管理软件。"
type: "终端防护"
company: "联软"
logo: "leagsoft_unitaccess.png"
version: "v0.1"
priority: 1
properties:
  - "终端防护"

connection:
  - name: username
    type: string
    required: true
    display_name: "用户名"
    description: "用户名称"
    validate_rules:
      - name: length
        error_hint: "用户名长度必须大于等于2且小于等于100"
        setting:
          min: 2
          max: 100

  - name: password
    type: password
    required: true
    display_name: "密码"
    description: "密码信息"
    validate_rules:
      - name: length
        error_hint: "密码长度必须大于等于6且小于等于100"
        setting:
          min: 6
          max: 100

  - name: address
    type: url
    required: true
    display_name: "地址"
    description: "地址信息"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'


fetch_setting:
  type: disposable
  point: "leagsoft_unitaccess.fetch:find_asset"
  size: 1000
  fetch_type_mapper:
    asset:
      - asset

fabric_setting:
  choose_point_mapper:
    asset: "leagsoft_unitaccess.fabric:choose_new_record"

merge_setting:
  size: 1000
  setting: { }

convert_setting:
  size: 1000
  before_executor_mapper: { }
  executor_mapper: { }