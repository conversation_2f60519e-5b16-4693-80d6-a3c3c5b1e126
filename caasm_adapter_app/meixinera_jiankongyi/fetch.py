from caasm_adapter.sdk.adapter_fetch import fetch_sdk
from meixinera_jiankongyi.manage import MeiXinEraManager


def find_asset(connection, fetch_type, page_index=0, page_size=1, session=None, condition=None, **kwargs):
    if page_index > 0:
        return None
    result = []
    jiankongyi = MeiXinEraManager(connection, None)
    result = jiankongyi.find_asset(connection, **kwargs)
    result = fetch_sdk.build_asset(result, fetch_type)
    return fetch_sdk.return_success(result)
