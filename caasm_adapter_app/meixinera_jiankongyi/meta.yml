name: "meixinera_jiankongyi"
display_name: "监控易"
description: "监控易智慧管理监控系统 智能运维管理软件平台-全面信创体系建设及解决方案，国产化适配"
type: "主机防护"
company: "美信时代"
logo: "jiankongyi.png"
version: "v0.1"
priority: 1
properties:
  - "主机防护"

connection:
  - name: username
    type: string
    required: true
    display_name: "用户名"
    description: "用户名称"
    validate_rules:
      - name: length
        error_hint: "用户名长度必须大于等于2且小于等于100"
        setting:
          min: 2
          max: 100

  - name: userp
    type: password
    required: true
    display_name: "密码"
    description: "密码信息"
    validate_rules:
      - name: length
        error_hint: "密码长度必须大于等于6且小于等于100"
        setting:
          min: 6
          max: 100

  - name: address
    type: url
    required: true
    display_name: "地址"
    description: "地址信息"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'


fetch_setting:
  type: disposable
  point: "meixinera_jiankongyi.fetch:find_asset"
  size: 1000
  is_need_test_service: true
  fetch_type_mapper:
    asset:
      - host

fabric_setting:
  choose_point_mapper:
    asset: "meixinera_jiankongyi.fabric:choose_new_record"

merge_setting:
  size: 50
  setting: { }

convert_setting:
  size: 50
  before_executor_mapper: { }
  executor_mapper: { }