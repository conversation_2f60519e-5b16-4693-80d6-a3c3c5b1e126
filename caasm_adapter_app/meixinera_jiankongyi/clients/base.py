import time

from caasm_adapter.util.client import FetchJsonResultClient


class MeiXinEraBase(FetchJsonResultClient):
    METHOD = "GET"

    def build_request_header(self, *args, **kwargs):
        return {
            "Cookie": f'timettttt={int(time.time() * 1000)};{self._connection.get("session_id","")};right=0;{self._connection.get("token","")};',
            "Referer": self._connection.get("address", "") + "groupmanage.html",
            "Origin": self._connection.get("address"),
            "Host": self._connection.get("address")[7:-1],
        }

    @property
    def data_key_name(self):
        return "data"

    @property
    def suc_flag(self):
        return None

    @property
    def flag_key_name(self):
        return "errmsg"
