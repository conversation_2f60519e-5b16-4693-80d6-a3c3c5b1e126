import logging
import math
import random

from meixinera_jiankongyi.clients.base import MeiXinEraBase

log = logging.getLogger()


class JanKongYiSesssion(MeiXinEraBase):
    URL = "mxlogin.bsi"
    METHOD = "GET"

    def build_request_url(self, *args, **kwargs):
        url = super(JanKongYiSesssion, self).build_request_url(self, *args, **kwargs)
        return url + self.url_contact()

    def parse_response(self, response, *args, **kwargs):
        return response.headers.get("Set-Cookie", "").split(";")[0]

    def random_Str(self, min, max=None):
        _range = round(random.random() * (max - min)) if max else min
        arr = [
            "a",
            "b",
            "c",
            "d",
            "e",
            "f",
            "g",
            "h",
            "i",
            "j",
            "k",
            "l",
            "m",
            "n",
            "o",
            "p",
            "q",
            "r",
            "s",
            "t",
            "u",
            "v",
            "w",
            "x",
            "y",
            "z",
        ]
        code_str = ""
        for item in range(0, _range):
            index = round(random.random() * (len(arr) - 1))
            code_str += arr[index]

        return code_str

    def encyp(self, password, _code):
        lLV3 = ""
        for item in _code:
            lLV3 += str(ord(item))
        drDJ5 = int(len(lLV3) / 5)
        ZssJ6 = int(lLV3[drDJ5] + lLV3[drDJ5 * 2] + lLV3[drDJ5 * 3] + lLV3[drDJ5 * 4] + lLV3[drDJ5 * 5])
        RyF7 = math.ceil(len(_code) / 2)
        PPiZmF8 = math.pow(2, 31) - 1
        sXHJlrE9 = round(random.random() * 1000000000) % 100000000
        lLV3 += str(sXHJlrE9)

        while len(lLV3) > 10:
            pss10 = math.ceil(len(lLV3) / 10)
            Xk_lKMm11 = 0
            for i in range(0, pss10):
                # 目测下面没什么用 还存在问题
                # if i== len(pss10)-1:
                #     Xk_lKMm11 = int(Xk_lKMm11)+int(lLV3[10*i:len(lLV3)])
                # else:
                Xk_lKMm11 = int(Xk_lKMm11) + int(lLV3[10 * i : 10 * (i + 1)])
            lLV3 = str(Xk_lKMm11)

        lLV3 = (ZssJ6 * int(lLV3) + RyF7) % PPiZmF8
        SZhNBYzij13 = ""
        jspD14 = ""
        for i in range(0, len(password)):
            SZhNBYzij13 = int(ord(password[i]) ^ math.floor((lLV3 / PPiZmF8) * 255))
            if SZhNBYzij13 < 16:
                jspD14 += ("0" + hex(SZhNBYzij13))[2:]
            else:
                jspD14 += hex(SZhNBYzij13)[2:]
        sXHJlrE9 = hex(sXHJlrE9)[2:]
        while len(sXHJlrE9) < 8:
            sXHJlrE9 = "0" + sXHJlrE9
            jspD14 += sXHJlrE9
        return jspD14

    @property
    def data_key_name(self):
        return None

    def url_contact(
        self,
    ):
        return f'?username={self._connection.get("username")}&userp={self.connection.get("userp","")}&encodeing=utf-8'
