from caasm_adapter.sdk.adapter_fetch import fetch_sdk
from mokahr_foeye.manage import MokaHrFoEyeManager, FetchType


def find_asset(connection, fetch_type, page_index=0, page_size=1, session=None, condition=None, **kwargs):
    auth_data = condition.get("auth")
    records = _manager(connection, session, auth_data).find(fetch_type, page_index + 1, page_size)
    return fetch_sdk.return_success(fetch_sdk.build_asset(records, fetch_type))


def build_query_condition(connection, session, fetch_type):
    key = connection.get("key")
    email = connection.get("email")
    auth_data = {"key": key, "email": email} if _is_email_auth(connection) else auth(connection, session)

    return {"auth": auth_data}


def auth(connection, session):
    manager = _manager(connection, session)
    return manager.auth() if _is_email_auth(connection) else manager.find(FetchType.COMPUTER, 1, 1)


def _manager(connection, session, auth_data=None):
    return MokaHrFoEyeManager(connection, session, auth_data)


def _is_email_auth(connection):
    return bool(connection.get("email"))
