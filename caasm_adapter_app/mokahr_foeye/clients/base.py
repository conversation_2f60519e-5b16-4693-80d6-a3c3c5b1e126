from caasm_adapter.util.client import FetchRestfulClient


class MokaHrFoEyeBaseClient(FetchRestfulClient):
    TIMEOUT = 180

    def __init__(self, auth_data=None, *args, **kwargs):
        super(MokaHrFoEyeBaseClient, self).__init__(*args, **kwargs)
        self._auth_data = auth_data

    def build_request_params(self, *args, **kwargs):
        if self._auth_data is None:
            return {}
        return {"email": self._auth_data.get("email"), "key": self._auth_data.get("key")}

    @property
    def data_key_name(self):
        return ""

    @property
    def suc_flag(self):
        return "ok"

    @property
    def flag_key_name(self):
        return "status"
