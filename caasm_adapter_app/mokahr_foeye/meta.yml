name: "mokahr_foeye"
display_name: "foeye网络空间资产测绘系统"
description: "FOEYE 2.0 网络资产测绘及风险分析系统，是华顺信安依靠多年在网络资产测绘以及漏洞挖掘验证方面的积累，自主研发的一款智 能资产识别及风险发现系统，从攻击者视角帮助企业发现静默资产及识别资产运行状况、端口开放情况和资产指纹信息，同时具备快速 扫描漏洞的检测功能，帮助企业高效地应对最新安全风险，实时监测企业风险并通过邮件方式及时通报相关人员，从风险的发现—通报 —核查形成闭环，从而实现资产透明化管理及安全风险监测。"
type: "内网资产测绘"
company: "华顺信安"
logo: "mokahr_foeye.jpeg"
version: "v0.1"
priority: 1
properties:
  - "内网资产测绘"

connection:
  - name: address
    type: url
    required: true
    display_name: "地址"
    description: "地址信息"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'

  - name: username
    type: string
    required: false
    display_name: "用户名"
    description: "用户名称"
    validate_rules:
      - name: length
        error_hint: "用户名长度必须大于等于2且小于等于100"
        setting:
          min: 2
          max: 100

  - name: password
    type: password
    required: false
    display_name: "密码"
    description: "密码信息"
    validate_rules:
      - name: length
        error_hint: "密码长度必须大于等于6且小于等于100"
        setting:
          min: 6
          max: 100

  - name: email
    type: string
    required: false
    display_name: "邮箱"
    description: "邮箱"
    validate_rules:
      - name: length
        error_hint: "邮箱长度必须大于等于2且小于等于100"
        setting:
          min: 2
          max: 100

  - name: key
    type: password
    required: false
    display_name: "key"
    description: "key"
    validate_rules:
      - name: length
        error_hint: "key长度必须大于等于2且小于等于100"
        setting:
          min: 2
          max: 100


fetch_setting:
  type: disposable
  point: "mokahr_foeye.fetch:find_asset"
  is_need_test_service: true
  condition_point: "mokahr_foeye.fetch:build_query_condition"
  test_auth_point: "mokahr_foeye.fetch:auth"
  size: 100
  fetch_type_mapper:
    asset:
      - computer

fabric_setting:
  choose_point_mapper: { }

merge_setting:
  size: 100
  setting: { }

convert_setting:
  size: 100
  before_executor_mapper: { }
  executor_mapper: { }