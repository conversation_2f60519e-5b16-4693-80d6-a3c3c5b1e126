name: qax_yunsuo
display_name: "奇安信云锁"
description: "云锁服务器安全管理系统依据CWPP（云工作负载保护平台）框架打造，基于服务器轻量级Agent，能有效保护服务器操作系统及应用，通过资产清点、基线检查、漏洞检测、虚拟补丁、应用防护（RASP、IN-APP waf）、系统加固、webshell&病毒查杀、攻击溯源等功能，有效抵御黑客攻击和恶意代码，实现资产发现-漏洞检测-漏洞利用防护的闭环管理，打造服务器事前防御、事中对抗、事后溯源的内生安全能力"
type: "主机防护"
company: "奇安信"
logo: "logo.png"
version: "v0.1"
priority: 10
properties:
  - Agent
  - 主机防护
  - HIDS
  - 漏洞扫描

connection:
  - name: address
    type: url
    required: true
    display_name: "请求地址"
    description: "请求地址"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'

  - name: token
    type: password
    required: true
    display_name: "token"
    description: "token"
    validate_rules:
      - name: length
        error_hint: "token格式无效。长度最小不得小于6，最大不得大于200"
        setting:
          min: 6
          max: 200

fetch_setting:
  point: "qax_yunsuo.fetch:find_asset"
  test_auth_point: "qax_yunsuo.fetch:auth"
  is_need_test_service: true
  size: 100
  fetch_type_mapper:
    asset:
      - host

merge_setting:
  size: 100
  setting: { }

convert_setting:
  size: 100

fabric_setting:
  choose_point_mapper: { }