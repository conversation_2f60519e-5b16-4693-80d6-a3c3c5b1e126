from caasm_adapter.sdk.adapter_fetch import fetch_sdk
from qax_yunsuo.manage import QaxManager


def find_asset(connection, fetch_type, session=None, page_index=0, page_size=1, **kwargs):
    records = _manager(connection, session).find_asset(fetch_type, page_index + 1, page_size)
    result = fetch_sdk.build_asset(records, fetch_type)
    return fetch_sdk.return_success(result)


def auth(connection, session=None):
    return _manager(connection, session).auth()


def _manager(connection, session=None):
    return QaxManager(connection, session)
