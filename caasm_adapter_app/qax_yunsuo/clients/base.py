from caasm_adapter.util.client import FetchJsonResultClient


class QaxYunsuoBaseClient(FetchJsonResultClient):
    @property
    def suc_flag(self):
        return "1"

    @property
    def flag_key_name(self):
        return "code"

    @property
    def data_key_name(self):
        return "data"

    @property
    def token(self):
        return self.connection.get("token")

    def build_request_json(self, *args, **kwargs):
        return {"token": self.token}


class QaxYunsuoPageClient(QaxYunsuoBaseClient):
    METHOD = "post"

    @property
    def data_key_name(self):
        return "data.list"
