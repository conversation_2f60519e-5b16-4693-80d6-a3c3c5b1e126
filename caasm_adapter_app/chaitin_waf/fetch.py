from caasm_adapter.sdk.adapter_fetch import fetch_sdk
from chaitin_waf.manage import <PERSON><PERSON>nWafManager, FetchType


def find_asset(connection, fetch_type, page_index=0, page_size=None, session=None, **kwargs):
    records = _manager(connection, session).find_asset(fetch_type, page_index, page_size)
    result = fetch_sdk.build_asset(records, fetch_type)
    return fetch_sdk.return_success(result)


def get_auth_connection(connection, session):
    _manager(connection, session).find_asset(FetchType.NETWORK_MAPPING, page_index=0, page_size=1)


def _manager(connection, session):
    return ChaitinWafManager(connection, session)
