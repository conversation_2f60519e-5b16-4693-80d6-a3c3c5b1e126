import copy

from caasm_adapter.fetcher.cleaners.base import FetchTotalBaseCleaner


class NetworkMappingCleaner(FetchTotalBaseCleaner):
    def build_common(self, biz_records):
        result = []

        for biz_record in biz_records:
            result.extend(self._parse_biz_record(biz_record))
        return result

    @classmethod
    def _parse_ip_v4(cls, info):
        ip = port = None
        address = info.split(":")
        if len(address) >= 1:
            ip = address[0]
        if len(address) >= 2:
            port = int(address[1])
        return ip, port

    @classmethod
    def _parse_ip_v6(cls, info):
        ip = port = None
        address = info.split("]:")
        if len(address) >= 1:
            ip = address[0].replace("[", "")
        if len(address) >= 2:
            port = int(address[1])
        return ip, port

    def _parse_address(self, address):
        return self._parse_ip_v6(address) if "[" in address else self._parse_ip_v4(address)

    def _parse_biz_record(self, biz_record):
        records = []

        ports = biz_record.get("ports")
        if not ports:
            return records

        for port_info in ports:
            address = port_info.get("port", "")
            if not address:
                continue
            ssl = port_info.get("ssl")
            ip = port = self._parse_address(address)

            data = {"ip": ip, "port": port, "class": "WAF", "protocol": "https" if ssl else "http"}
            records.append(data)

        server_names = biz_record.get("server_names", [])

        if not server_names:
            return records

        result = []
        for server_name in server_names:
            for record in records:
                tmp_record = copy.deepcopy(record)
                tmp_record["server_name"] = server_name
                tmp_record["is_enabled"] = biz_record.get("is_enabled")
                result.append(tmp_record)

        return result
