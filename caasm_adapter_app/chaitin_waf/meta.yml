name: chaitin_waf
display_name: "长亭雷池"
description: "雷池（SafeLine）是业内首款基于人工智能语义分析的下一代Web应用防火墙（NGWAF，Next Generation Web Application Firewall），能够基于上下文逻辑实现"
type: "防火墙"
company: "北京长亭未来科技有限公司"
version: "v0.1"
logo: "chaitin_waf.png"
priority: 1
properties:
  - "防火墙"

connection:
  - name: address
    type: string
    required: true
    display_name: "地址信息"
    description: "请求地址信息"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以https开头的地址信息"
        setting:
          reg: '^((https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'

  - name: api_token
    type: string
    required: true
    display_name: "api_token"
    description: "api_token"
    validate_rules:
      - name: length
        error_hint: "api_token格式无效。长度最小不得小于1，最大不得大于100"
        setting:
          min: 1
          max: 100

fetch_setting:
  fetch_type_mapper:
    # 资产类型
    network:
      # 资产名称
      - network_mapping
  point: "chaitin_waf.fetch:find_asset"
  is_need_test_service: true
  test_auth_point: "chaitin_waf.fetch:get_auth_connection"
  size: 200
  cleaner_mapper:
    network:
      network_mapping:
        - "chaitin_waf.cleaners.network_mapping_cleaner:NetworkMappingCleaner"


merge_setting:
  size: 200
  setting: { }

convert_setting:
  size: 200
  before_executor_mapper: { }
  executor_mapper: { }

fabric_setting:
  choose_point_mapper: { }