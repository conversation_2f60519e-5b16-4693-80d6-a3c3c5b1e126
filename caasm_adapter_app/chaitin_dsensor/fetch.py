from caasm_adapter.sdk.adapter_fetch import fetch_sdk
from chaitin_dsensor.manage import DSENSORManage


def find_asset(connection, fetch_type, page_index=0, page_size=None, session=None, **kwargs):
    records = _build_manager(connection, session).find(fetch_type, page_index, page_size)
    result = fetch_sdk.build_asset(records, fetch_type)
    return fetch_sdk.return_success(result)


def get_auth_connection(connection, session):
    manager = _build_manager(connection, session)
    return manager.auth()


def _build_manager(connection, session):
    return DSENSORManage(connection, session)
