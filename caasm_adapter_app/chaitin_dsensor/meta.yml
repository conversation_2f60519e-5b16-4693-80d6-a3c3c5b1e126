name: chaitin_dsensor
display_name: "长亭谛听伪装欺骗系统"
description: "长亭谛听伪装欺骗系统利用欺骗伪装技术，通过在关键入侵路径上部署诱饵和陷阱，诱导攻击者攻击伪装目标，远离真实资产，并且对攻击者做取证和追踪溯源，为企业提供先人一步的主动防御手段，保护真实资产，提升主动防御能力。"
type: "蜜罐"
company: "北京长亭未来科技有限公司"
version: "v0.1"
logo: "chaitin_dsensor.png"
priority: 1
properties:
  - "蜜罐"

connection:
  - name: address
    type: string
    required: true
    display_name: "地址信息"
    description: "请求地址信息"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以https开头的地址信息"
        setting:
          reg: '^((https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'

  - name: api_token
    type: string
    required: true
    display_name: "api_token"
    description: "api_token"
    validate_rules:
      - name: length
        error_hint: "api_token格式无效。长度最小不得小于1，最大不得大于100"
        setting:
          min: 1
          max: 100

fetch_setting:
  fetch_type_mapper:
    asset:
      - computer
  point: "chaitin_dsensor.fetch:find_asset"
  is_need_test_service: true
  test_auth_point: "chaitin_dsensor.fetch:get_auth_connection"
  size: 200
  cleaner_mapper:
    asset:
      computer:
        - "chaitin_dsensor.cleaner.port_maps:PortMapsCleaner"


merge_setting:
  size: 200
  setting:
    asset:
      computer:
        fields:
          - id

convert_setting:
  size: 200
  before_executor_mapper: { }
  executor_mapper: { }

fabric_setting:
  choose_point_mapper:
    asset: "chaitin_dsensor.fabric:choose_new_record"