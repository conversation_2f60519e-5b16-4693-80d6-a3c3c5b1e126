from caasm_adapter.util.exception import AdapterFetchApiNotSupportException
from chaitin_dsensor.clients.agent import DSENSORAgentListClient


class ClientType(object):
    ASSET = "computer"


class DSENSORManage:
    _CLASS_DEFINE = {ClientType.ASSET: DSENSORAgentListClient}

    def __init__(self, connection, session=None):
        self._connection = connection
        self._session = session

    def find(self, fetch_type, page_index, page_size):
        offset = page_index * page_size
        limit = page_size
        return self._instance(fetch_type).handle(limit=limit, offset=offset)

    def auth(self):
        self.find(ClientType.ASSET, 0, 1)
        return True

    def _instance(self, api_type):
        clazz = self._CLASS_DEFINE.get(api_type)
        if not clazz:
            raise AdapterFetchApiNotSupportException()
        return clazz(self._connection, self._session)
