import abc

from caasm_adapter.util.client import FetchJsonResultClient


class DSENSORBASEClient(FetchJsonResultClient, abc.ABC):
    @property
    def suc_flag(self):
        return ""

    @property
    def flag_key_name(self):
        return ""

    def check_biz_result(self, result):
        return True

    def build_request_header(self, *args, **kwargs):
        return {"API-Token": self.api_token}

    @property
    def api_token(self):
        return self.connection.get("api_token", "")
