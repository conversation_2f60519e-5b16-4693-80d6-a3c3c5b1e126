name: "e_cloud"
display_name: "移动云"
description: "移动云为客户提供云网一体、安全可控的专业云服务。 依托移动云计算能力，建设N+31+X资源布局，基于北京、广州、哈尔滨、呼和浩特、长沙构建大规模资源池，辅以全国各省的省级数据中心，通过专线互联，实现业务跨区域的高速、低延迟、大带宽、高QoS互通。"
type: "云平台"
company: "中国移动"
logo: "e_cloud.png"
version: "v0.1"
priority: 1
properties:
  - "云平台"

connection:
  - name: username
    type: string
    required: true
    display_name: "用户名"
    description: "用户名称"
    validate_rules:
      - name: length
        error_hint: "用户名长度必须大于等于2且小于等于100"
        setting:
          min: 2
          max: 100

  - name: password
    type: password
    required: true
    display_name: "密码"
    description: "密码信息"
    validate_rules:
      - name: length
        error_hint: "密码长度必须大于等于6且小于等于100"
        setting:
          min: 6
          max: 100

  - name: address
    type: url
    required: true
    display_name: "地址"
    description: "地址信息"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'


fetch_setting:
  type: disposable
  point: "e_cloud.fetch:find_asset"
  is_need_test_service: true
  size: 1000
  fetch_type_mapper:
    asset:
      - asset

fabric_setting:
  choose_point_mapper:
    asset: "e_cloud.fabric:choose_new_record"

merge_setting:
  size: 1000
  setting: { }

convert_setting:
  size: 1000
  before_executor_mapper: { }
  executor_mapper: { }