from caasm_adapter.util.client import FetchJsonResultClient


class AsiainfoScanBaseClient(FetchJsonResultClient):
    def __init__(self, connection, session=None, cookie=None):
        super(AsiainfoScanBaseClient, self).__init__(connection, session)
        self.cookie = cookie

    def build_request_header(self, *args, **kwargs):
        origin = self.address[0:-1] if self.address.endswith("/") else self.address
        header = {
            "Origin": origin,
            "Cookie": self.cookie,
        }
        return header

    @property
    def data_key_name(self):
        return ""

    @property
    def suc_flag(self):
        return ""

    @property
    def flag_key_name(self):
        return ""
