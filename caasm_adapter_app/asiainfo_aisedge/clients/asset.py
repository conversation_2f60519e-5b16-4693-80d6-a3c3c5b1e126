from asiainfo_aisedge.clients.base import AsiainfoScanBaseClient
from asiainfo_aisedge.utils import build_url


class AsiainfoScanAssetClient(AsiainfoScanBaseClient):
    URL = "/officescan/console/html/cgi/cgiShowClientAdm.exe"
    METHOD = "post"

    def build_request_header(self, *args, **kwargs):
        header = super(AsiainfoScanAssetClient, self).build_request_header()
        referer_url = (
            "/officescan/console/html/cgi/cgishowclientadm.exe?id=1000&FRAME_STATUS=Widget_Query&QUERY_TYPE=ALL"
        )
        header["Referer"] = build_url(self.address, referer_url)
        header["Content-Type"] = "application/x-www-form-urlencoded"
        return header

    def build_request_data(self, top=None, bottom=None):
        return f"id:10002&TOP:{top}&BOTTOM:{bottom}&SORT_ORDER:1&TICKET:1&SORT_COLUMN:NAME&domain:00000000-0000-0000-0000-000000000000&VIEW:view_all"

    @property
    def data_key_name(self):
        return "RESPONSE.NODES"

    @property
    def suc_flag(self):
        return 0

    @property
    def flag_key_name(self):
        return "ERROR.ERROR_CODE"
