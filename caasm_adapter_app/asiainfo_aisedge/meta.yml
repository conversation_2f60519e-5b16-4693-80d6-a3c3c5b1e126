name: "asiainfo_aisedge"
display_name: "亚信防毒墙"
description: ""
type: "终端防护"
company: "亚信"
logo: "asiainfo_aisedge.png"
version: "v0.1"
priority: 1
properties:
  - "终端防护"
  - "防病毒"

connection:
  - name: username
    type: string
    required: true
    display_name: "用户名"
    description: "用户名称"
    validate_rules:
      - name: length
        error_hint: "用户名长度必须大于等于2且小于等于100"
        setting:
          min: 2
          max: 100

  - name: password
    type: password
    required: true
    display_name: "密码"
    description: "密码信息"
    validate_rules:
      - name: length
        error_hint: "密码长度必须大于等于6且小于等于100"
        setting:
          min: 6
          max: 100

  - name: address
    type: url
    required: true
    display_name: "地址"
    description: "地址信息"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'


fetch_setting:
  type: disposable
  point: "asiainfo_aisedge.fetch:find_asset"
  condition_point: "asiainfo_aisedge.fetch:build_query_condition"
  test_auth_point: "asiainfo_aisedge.fetch:get_auth_connection"
  finish_point: "asiainfo_aisedge.fetch:free_condition"
  is_need_test_service: true
  size: 1000
  fetch_type_mapper:
    asset:
      - terminal

fabric_setting:
  choose_point_mapper:
    asset: "asiainfo_aisedge.fabric:choose_new_record"

merge_setting:
  size: 1000
  setting: { }

convert_setting:
  size: 1000
  before_executor_mapper: { }
  executor_mapper: { }