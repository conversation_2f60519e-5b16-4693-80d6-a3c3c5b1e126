name: zero0sec_easm
display_name: "零零信安外网攻击面管理平台"
description: "零零信安0.zone外部攻击面管理平台，是国内首个外部攻击面管理SaaS平台。其可以通过“企业名称”，查询相关企业的信息系统暴露面、移动端应用、邮箱地址和凭据泄露、代码和文档泄露等。为甲方和乙方的信息安全工程师进行暴露面收敛工作提供技术支持。"
type: "互联网测绘"
logo: "zero0sec_easm.png"
company: "北京零零信安科技有限公司"
version: "v0.1"
priority: 10
properties:
  - "互联网测绘"

connection:
  - name: address
    type: url
    required: true
    display_name: "请求地址"
    description: "零零信安官方网址"
    default: "https://0.zone"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'

  - name: company
    type: string
    required: true
    display_name: "公司名称"
    description: "需要查询的公司名称"
    validate_rules:
      - name: length
        error_hint: "用户密码格式无效。长度最小不得小于2，最大不得大于100"
        setting:
          min: 2
          max: 100
  - name: api_key
    type: string
    required: true
    display_name: "API KEY"
    description: "与该公司匹配的API KEY"
    validate_rules:
      - name: length
        error_hint: "API KEY格式无效。长度最小不得小于2，最大不得大于100"
        setting:
          min: 2
          max: 100




fetch_setting:
  point: "zero0sec_easm.fetch:find_asset"
  size: 40
  is_need_test_service: true
  test_connection_point: "zero0sec_easm.fetch:check_connection"
  test_auth_point: "zero0sec_easm.fetch:get_auth_connection"
  fetch_type_mapper:
    asset:
      - port_server
      - domain_name
      - app
  cleaner_mapper:
    asset:
      domain_name:
        - "zero0sec_easm.cleaner.domain_name:DomainNameCleaner"

fabric_setting:
  choose_point_mapper:
    asset: "zero0sec_easm.fabric:choose_new_record"

merge_setting:
  size: 50
  setting: { }



convert_setting:
  size: 50
  before_executor_mapper: { }
  executor_mapper: { }
