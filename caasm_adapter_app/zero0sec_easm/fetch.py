import time

from caasm_adapter.sdk.adapter_connection import connection_sdk
from caasm_adapter.sdk.adapter_fetch import fetch_sdk
from zero0sec_easm.manage import Zero0SecManager, FetchTypeEnum


def find_asset(connection, fetch_type, page_index=1, page_size=1, session=None, **kwargs):
    time.sleep(3)
    page_index += 1
    records = Zero0SecManager(connection=connection, session=session).find_asset(fetch_type, page_index, page_size)
    result = fetch_sdk.build_asset(records, fetch_type)
    return fetch_sdk.return_success(result)


def get_auth_connection(connection, session=None):
    Zero0SecManager(connection=connection, session=session).find_asset(FetchTypeEnum.DOMAIN_NAME, 1, 1)


def check_connection(connection, session=None):
    address = connection.get("address")
    return connection_sdk("http", address=address)
