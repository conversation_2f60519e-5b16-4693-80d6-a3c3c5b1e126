from caasm_adapter.util.client import FetchJsonResultClient


class Zero0SecBaseClient(FetchJsonResultClient):
    def __init__(self, connection, session=None):
        super(Zero0SecBaseClient, self).__init__(connection, session)

    @property
    def api_key(self):
        return self.connection.get("api_key")

    @property
    def company(self):
        return self.connection.get("company")

    @property
    def flag_key_name(self):
        return "code"

    @property
    def suc_flag(self):
        return 0

    @property
    def data_key_name(self):
        return "data"
