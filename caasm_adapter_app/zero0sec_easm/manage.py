from caasm_adapter.util.exception import AdapterFetchApiNotSupportException
from caasm_tool.constants import StrEnum
from zero0sec_easm.clients.zero0sec_data_client import Zero0SecDataClient


class FetchTypeEnum(StrEnum):
    PORT_SERVER = "port_server"
    DOMAIN_NAME = "domain_name"
    APP = "app"


Zero0ClassMap = {
    FetchTypeEnum.PORT_SERVER: Zero0SecDataClient,
    FetchTypeEnum.DOMAIN_NAME: Zero0SecDataClient,
    FetchTypeEnum.APP: Zero0SecDataClient,
}

ZeroQueryMap = {FetchTypeEnum.PORT_SERVER: "site", FetchTypeEnum.DOMAIN_NAME: "domain", FetchTypeEnum.APP: "apk"}


class Zero0SecManager(object):
    def __init__(self, connection=None, session=None):
        self._connection = connection
        self._session = session

    def find_asset(self, fetch_type, page_index, page_size):
        limit = page_size
        offset = page_index
        query_type = ZeroQueryMap.get(fetch_type)
        return self._instance(fetch_type).handle(query_type=query_type, limit=limit, offset=offset)

    def _instance(self, fetch_type):
        clazz = Zero0ClassMap.get(fetch_type)
        if not clazz:
            raise AdapterFetchApiNotSupportException()
        return clazz(self._connection, self._session)
