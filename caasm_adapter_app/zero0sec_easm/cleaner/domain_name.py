import IPy

from caasm_adapter.fetcher.cleaners.base import FetchBaseCleaner


class DomainNameCleaner(FetchBaseCleaner):
    @classmethod
    def is_ip(cls, address):
        try:
            IPy.IP(address)
            return True
        except Exception as e:
            return False

    def clean_single(self, detail):
        result = {}
        toplv_domain = detail.get("toplv_domain")
        result["toplv_domain"] = toplv_domain
        result["domain"] = detail.get("domain")
        msg = detail.get("msg")
        if msg and isinstance(msg, dict):
            ip = msg.get("ip")
            if ip and self.is_ip(ip):
                result["ip"] = ip
        result["detail"] = detail
        return result
