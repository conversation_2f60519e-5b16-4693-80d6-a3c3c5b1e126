from caasm_tool import log
from caasm_file.parsers.xlsx_parse import XlsxFileParserV1


class BusinessProtectionInfoImporter(XlsxFileParserV1):

    INVALID_SHEET_INDEXES = [1]
    INVALID_ROW_INDEXES = [0]
    BUSINESS_NAME_FIELD = "business.name"
    BUSINESS_FULLNAME_FIELD = "business.full_name"
    BUSINESS_PROTECTION_LEVEL_FIELD = "business.grade_protection_level"
    BUSINESS_CODE = "business.code"
    BUSINESS_CLASS = "business.class"
    BUSINESS_SYSTEM_PROTECTION_CODE = "business.system_protection_code"
    BUSINESS_IS_PASS_ASSESSMENT = "business.is_pass_assessment"

    def _parse_name(self, row, result):
        if len(row) < 2:
            return
        self.restore(self.BUSINESS_NAME_FIELD, row[1], result)

    def _parse_full_name(self, row, result):
        if len(row) < 2:
            return
        self.restore(self.BUSINESS_FULLNAME_FIELD, row[1], result)

    def _parse_grade_protection_level(self, row, result):
        if len(row) < 4:
            return
        try:
            level_str = row[3]
            if level_str:
                return
            level = int(level_str)
        except Exception as e:
            log.info(f"business protection level parse error: {str(e)}")
            return
        self.restore(self.BUSINESS_PROTECTION_LEVEL_FIELD, level, result)

    def _parse_system_protection_code(self, row, result):
        if len(row) < 3:
            return
        self.restore(self.BUSINESS_SYSTEM_PROTECTION_CODE, row[2], result)

    def _parse_is_pass_assessment(self, row, result):
        if len(row) < 6:
            return
        row_value = row[5]
        if row_value == "是":
            value = True
        else:
            value = False
        self.restore(self.BUSINESS_IS_PASS_ASSESSMENT, value, result)

    @staticmethod
    def get_parser_name():
        return "导入系统备案信息"

    @staticmethod
    def get_category():
        return "business"

    @staticmethod
    def get_model_name():
        return "business"

    @staticmethod
    def get_data_type():
        return "business"

    @classmethod
    def get_asset_type(cls, record):
        return "business"


CLASS = BusinessProtectionInfoImporter
