import logging
import re
import zipfile
from collections import OrderedDict

from bs4 import BeautifulSoup
from caasm_file.parsers.zip_parse import ZipFileParser

log = logging.getLogger()


class NsRsasHtmlParser(ZipFileParser):
    IP_RE = re.compile(r"(?s)IP地址</th>(?:.*?)<td>(.*?)</td>")
    OS_RE = re.compile(r"(?s)操作系统</th>(?:.*?)<td>(.*?)</td>")

    _IP_FIELD = "network.ips"
    _OS_FIELD = "computer.os.type"
    _VUL_FIELD = "vulnerability.vulners"
    _PORT_FIELD = "network.ports"
    _HOST_NAME_FIELD = "computer.host_name"
    _NAME_FIELD = "computer.name"
    _ASSET_NAME_FIELD = "asset_base.name"

    def parse(self, file_content):
        z_file = zipfile.ZipFile(file_content, "r")

        for z_name in z_file.namelist():
            if not self.judging_name_legitimacy(z_name):
                continue
            try:
                f_info = z_file.read(z_name)
                yield self.convert_info(f_info, z_name)
            except Exception as e:
                log.error(f"read {z_name}  or convert {z_name} fail! please check it!")
        z_file.close()

    def judging_name_legitimacy(self, name):
        if name in self.INVALID_NAMES or not name.startswith("host/") or not name.endswith(".html"):
            return False
        return True

    def convert_info(self, row, name):
        result = {}
        html_content = row.decode()
        self.html_parser(html_content, name, result)
        return result

    @staticmethod
    def get_parser_name():
        return "绿盟远程评估html版zip文件导入"

    @staticmethod
    def get_category():
        return "asset"

    @staticmethod
    def get_model_name():
        return "computer"

    @staticmethod
    def get_data_type():
        return "asset"

    @classmethod
    def get_asset_type(cls, record):
        return "host"

    def html_parser(self, html_content, name, result):
        if not html_content:
            return
        soup = BeautifulSoup(html_content, "html.parser")

        ip_info = self.IP_RE.search(html_content)
        ip = None
        if ip_info:
            ip = ip_info.group(1)
        else:
            ip_pattern = r"\b(?:\d{1,3}\.){3}\d{1,3}\b"
            ips = re.findall(ip_pattern, name)
            if ips:
                ip = ips[0]

        if ip:
            addr = [{"addr": ip}]
            self.restore(self._IP_FIELD, addr, result)
            self.restore(self._NAME_FIELD, ip, result)
            self.restore(self._ASSET_NAME_FIELD, ip, result)
            self.restore(self._HOST_NAME_FIELD, ip, result)

        os_info = self.OS_RE.search(html_content)
        if os_info:
            os = os_info.group(1)
            self.restore(self._OS_FIELD, os, result)

        self.parse_vul_info(soup, result)

        self.parse_port_info(soup, ip, result)

        return result

    def scan_vulns(self, vuln_list, vuln_detail, result):
        columns_tail = ("详细描述", "解决办法", "威胁分值", "危险插件", "发现日期", "CVE编号", "CNNVD编号", "CNCVE编号", "CVSS评分")
        columns_tail_default_list = tuple(zip(columns_tail, [""] * len(columns_tail)))
        clear_re = re.compile(r"\S+")  # 匹配非空白行
        vulns_detail_map = dict()
        for tr in vuln_detail.findAll("tr", {"class": "solution"}):
            table_id = tr.get("id")
            infos = OrderedDict(columns_tail_default_list)
            for item in tr.findAll("tr"):
                key = item.th.get_text()
                value = item.td.get_text().strip().replace(",", "，")  # 去除首尾空格，英文','改中文'，'
                if key in infos:
                    if key in columns_tail:
                        value = "".join(clear_re.findall(value))  # 删除首尾空格
                    infos[key] = value
            vulns_detail_map[table_id] = infos
        vulners = []
        for tr in vuln_list.findAll("tr"):
            items = tr.contents
            port = items[1].get_text()
            protocol = items[3].get_text()
            server = items[5].get_text()
            for span in items[7].findAll("span"):
                vuln_name = span.get_text().strip().replace(",", "，")
                level = span.get("class")[0].split("_")[-1]
                table_id = span.get("onclick").split("'")[-2]
                record = (level, port, protocol, server, vuln_name, vulns_detail_map[table_id])
                vulners.append(
                    {
                        "name": vuln_name,
                        "type": 1,
                        "cve_id": vulns_detail_map[table_id].get("CVE编号", ""),
                        "cnnvd_id": vulns_detail_map[table_id].get("CNNVD编号", ""),
                        "cnvd_id": vulns_detail_map[table_id].get("CNCVE编号", ""),
                        "cvss_score": float(vulns_detail_map[table_id].get("CVSS评分", 0))
                        if vulns_detail_map[table_id].get("CVSS评分")
                        else 0.0,
                        "description": vulns_detail_map[table_id].get("详细描述", ""),
                        "solution": vulns_detail_map[table_id].get("解决办法", ""),
                        "service": {
                            "protocol": protocol,
                            "port": int(port) if port != "--" and port else 0,
                            "name": server,
                        },
                    }
                )
            self.restore(self._VUL_FIELD, vulners, result)

    def parse_vul_info(self, soup, result):
        vuln_list = soup.find("table", {"id": "vuln_list", "class": "report_table"})
        if not vuln_list:
            return
        vuln_list = vuln_list.tbody
        vuln_detail = soup.find("div", {"id": "vul_detail"})

        self.scan_vulns(vuln_list, vuln_detail, result)

    def parse_port_info(self, soup, ip, result):

        # 查找所有的div元素
        div_elements = soup.find_all("div", class_="report_h")

        # 初始化变量用于标记是否找到"远程端口信息"的关键字
        found_remote_port_info = False

        # 遍历所有的div元素
        div_element = None
        for div_element in div_elements:
            if "远程端口信息" in div_element.text.strip():
                found_remote_port_info = True
                break

        # 如果找到了"远程端口信息"的关键字，则继续提取表格数据
        if found_remote_port_info:
            # 找到包含"远程端口信息"的div元素后，再找到其后的table元素
            table = div_element.find_next("table", class_="report_table")
            table_rows = table.find_all("tr")

            ports = []
            # 跳过第一行，因为它包含表头
            for row in table_rows[1:]:
                cols = row.find_all("td")
                port = cols[0].text.strip()
                protocol = cols[1].text.strip()
                service = cols[2].text.strip()
                statuse = cols[3].text.strip()

                ports.append(
                    {
                        "number": int(port) if port and port != "--" else 0,
                        "ip": ip or "",
                        "protocol": protocol,
                        "service_name": service,
                        "block": statuse,
                    }
                )
            self.restore(self._PORT_FIELD, ports, result)

        else:
            return


CLASS = NsRsasHtmlParser
