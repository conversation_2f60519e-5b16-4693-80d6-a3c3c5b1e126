class Table(object):
    account = "account"
    asset = "asset"
    vulnerability_instance = "vulnerability"
    asset_vul_instance = "asset_vul_instance"

    retrieve_scenes = "retrieve_scenes"
    retrieve_statement = "retrieve_statement"

    operation_log = "operation_log"
    operation_log_config = "operation_log.config"

    convert_func = "convert_func"
    convert_func_type = "convert_func_type"

    fetch_type_config = "fetch_type_config"

    manufacturer = "manufacturer"
    adapter = "adapter"
    adapter_instance = "adapter_instance"
    adapter_instance_fetch_poc_log = "adapter_instance_fetch_poc_log"
    fetch_record = "fetch.record"
    merge_record = "merge.record"
    convert_record = "convert.record"
    convert_visualization = "convert.visualization"
    convert_visualization_relation = "convert.visualization_relation"
    fabric_meta_model_config = "fabric.meta_model_config"

    sequence = "sequence"

    setting = "setting"

    department = "department"
    owner = "owner"
    business = "business"

    user = "user.info"
    user_asset_aql_history = "user.user_asset_aql_history"
    user_asset_aql_collect = "user.user_asset_aql_collect"
    user_default_entity_fields = "user.user_default_entity_fields"
    menu = "menu"
    role = "role"

    variable = "variable"

    job = "job"
    # 元数据
    meta_field = "meta.field"
    meta_model = "meta.model"
    meta_view = "meta.view"
    meta_asset_type_view = "meta.asset_type_view"
    meta_entity_fields = "meta.entity_fields"
    meta_entity_type_view = "meta.entity_type_view"

    asset_type = "asset_type"

    field_fabric_policy = "field_fabric_policy"

    realms = "operator.realm"

    business_portraiter = "business.portraits"
    business_portraiter_update = "business.portraits_updates"

    vulnerability = "vulnerability"

    ## 大屏和 仪表盘
    overview_space = "overview.spaces"
    overview_chart = "overview.charts"
    overview_user_setting = "overview.user_setting"
    overview_chart_category = "overview.chart_category"

    ## 文件管理
    file_manage = "file_manage"

    internet_relation = "internet.relation"

    # 单点登录
    sso_config = "sso_config"
    temp_login_key = "temp_login_key"

    # 白名单
    whitelist = "whitelist"
    api_call_record = "api_call_record"

    # 快照
    snapshot_record = "snapshot.record"
    meta_model_snapshot_record = "snapshot.meta_model"
    meta_field_snapshot_record = "snapshot.meta_field"

    # 基础数据变化，由变化引擎检测得来
    change_details = "change.details"
    change_temp = "change.tmp"
    change_records = "change.records"

    # 唯一实体
    unique = "uniques"
    unique_indices = "unique.indices"

    # 实体变化，由后处理，APP等得来
    entity_change_details = "entity.change.details"
    entity_change_records = "entity.change.records"

    # 优先级
    vul_priority = "vul.priority"
    vul_priority_settings = "vul.priority.settings"

    # 漏洞文件
    vul_files = "vul.files"

    # 漏洞时间线
    vul_timeline = "vul.timeline"

    # 漏洞导出配置字段集
    join_export_fields = "join_export_fields"

    quick_search = "quick_search"

    # 实体图
    entity_graph = "link_graph.entities"
    link_graph = "link_graph.graphs"

    # 网络映射
    network_mapping_clusters = "network_mapping.clusters"

    # 告警
    alarm_rule_template = "alarm_rule_template"
    alarm_rule_instance = "alarm_rule_instance"
    alarm_rule_run_record = "alarm_rule_run_record"
    alarm_record = "alarm_record"

    # 系统运行状态仪表盘
    system_status = "platform.statuses"

    # 工作流相关
    workflow_record = "workflow.record"
    workflow_playbook = "workflow.playbook"
    workflow_task = "workflow.task"
    category = "category"

    # 实体类型血缘（类似表血缘）
    entity_type_stage_lineage = "lineage.entity_type_stages"

    # 实体血缘
    entity_fetch_stage_lineage = "lineage.entity_stages.fetch"
    entity_merge_stage_lineage = "lineage.entity_stages.merge"
    entity_convert_stage_lineage = "lineage.entity_stages.convert"
    entity_fabric_stage_lineage = "lineage.entity_stages.fabric"
    entity_enforcement_stage_lineage = "lineage.entity_stages.enforcement"

    # 值血缘
    value_fetch_stage_lineage = "lineage.value_stages.fetch"
    value_merge_stage_lineage = "lineage.value_stages.merge"
    value_convert_stage_lineage = "lineage.value_stages.convert"
    value_fabric_stage_lineage = "lineage.value_stages.fabric"
    value_enforcement_stage_lineage = "lineage.value_stages.enforcement"

    # 数据维护
    form = "maintenance.forms"
    maintenance = "maintenance.maintenances"

    # 数据校验
    entity_validation = "validation.entity_validations"

    # IP富化记录
    ip_segment_enrichment = "enrichment.ip_segment"

    # 导出
    export_record = "export.records"

    # 360
    data_stream_360_record = "data_stream_360.records"
    data_stream_360 = "data_stream_360.streams"
    data_stream_360_realm = "data_stream_360.realms"
    # 告警拦截记录
    intercept_alarm = "intercept_alarm"
    vuln_lifecycle = "vuln.lifecycle"
    order = "order"
