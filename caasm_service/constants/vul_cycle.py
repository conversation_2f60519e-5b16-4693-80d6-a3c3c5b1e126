from enum import Enum


class VulnStatusEnum(int, Enum):
    """漏洞状态枚举"""

    REPRODUCED = 0
    UNFIXED = 1
    FALSE_POSITIVE = 2
    FIXED = 3


VulnStatusMapper = {
    VulnStatusEnum.REPRODUCED: "复现",
    VulnStatusEnum.UNFIXED: "待修复",
    VulnStatusEnum.FALSE_POSITIVE: "忽略",
    VulnStatusEnum.FIXED: "已修复",
}

VulnStatusToEnumMapper = {
    "待修复": VulnStatusEnum.UNFIXED,
    "已修复": VulnStatusEnum.FIXED,
    "复现": VulnStatusEnum.REPRODUCED,
    "忽略": VulnStatusEnum.FALSE_POSITIVE,
}


class VulnOriginEnum(int, Enum):
    VULNSCAN = 0
    NORMAL = 1


VulnOriginMapper = {
    VulnOriginEnum.VULNSCAN: "绿盟漏扫",
    VulnOriginEnum.NORMAL: "人工渗透",
}


class VulnLevelEnum(int, Enum):
    """漏洞等级枚举"""

    HIGH = 0
    MEDIUM = 1
    LOW = 2


VULNLEVELMAPPING = {
    VulnLevelEnum.HIGH: 0,
    VulnLevelEnum.MEDIUM: 1,
    VulnLevelEnum.LOW: 2,
}

VulnLevelMapper = {
    VulnLevelEnum.HIGH: "高危",
    VulnLevelEnum.MEDIUM: "中危",
    VulnLevelEnum.LOW: "低危",
}

VulnLevelToEnumMapper = {
    "高危": VulnLevelEnum.HIGH,
    "中危": VulnLevelEnum.MEDIUM,
    "低危": VulnLevelEnum.LOW,
}
