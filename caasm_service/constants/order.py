from caasm_tool.constants import StrEnum


class OrderStatusEnum(StrEnum):
    """工单状态枚举"""

    PENDING = "pending"  # 待处理
    PROCESSING = "processing"  # 处理中
    COMPLETED = "completed"  # 已完成
    CLOSED = "closed"  # 已关闭


class OrderPriorityEnum(StrEnum):
    """工单优先级枚举"""

    LOW = "low"  # 低
    MEDIUM = "medium"  # 中
    HIGH = "high"  # 高
    CRITICAL = "critical"  # 紧急


class SupportImportVulnFile(StrEnum):
    """漏洞导入的文件格式"""

    XLSX = ".xlsx"
    XLS = ".xls"
    ZIP = ".zip"
