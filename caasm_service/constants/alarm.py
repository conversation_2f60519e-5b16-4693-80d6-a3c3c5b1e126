from enum import Enum

from caasm_tool.constants import StrEnum


class AlarmSeverityLevelEnum(StrEnum):
    CRITICAL = "critical"
    HIGH = "high"
    MIDDLE = "middle"
    LOW = "low"
    UNKNOWN = "unknown"


class AlarmRuleTypeEnum(StrEnum):
    INTERNAL = "internal"
    CUSTOMIZE = "customize"


class AlarmRuleResultTypeEnum(StrEnum):
    MULTIPLE_INSTANCES = "multiple_instances"
    SINGLE_INSTANCE = "single_instance"


class AlarmRuleStatementModelEnum(StrEnum):
    ASQL = "asql"
    TEMPLATE = "template"


class AlarmTriggerTypeEnum(StrEnum):
    DEFAULT = "default"
    CUSTOMIZE = "customize"
    AFTER_DATA_COLLECT = "after_data_collect"


class AlarmRuleInstanceRunStatus(StrEnum):
    INIT = "init"
    WAIT = "wait"
    DOING = "doing"
    FAILED = "failed"
    SUCCESS = "success"


class AlarmDisposalStatus(StrEnum):
    UNDISPOSED = "undisposed"
    DISPOSED = "disposed"


ALARM_DISPOSED_STATUS_MAPPER = {AlarmDisposalStatus.UNDISPOSED: "未处置", AlarmDisposalStatus.DISPOSED: "已处置"}


class ALARMStatus(Enum):
    NEW = 1
    ACTIVE = 2
    OFFLINE = 3
    RECURRENT = 4


ALARM_STATUS_MAPPING = {
    ALARMStatus.NEW: "新增",
    ALARMStatus.ACTIVE: "活动",
    ALARMStatus.OFFLINE: "下线",
    ALARMStatus.RECURRENT: "复现",
}


ALARM_RULE_LEVEL_MAPPER = {
    AlarmSeverityLevelEnum.CRITICAL: "严重",
    AlarmSeverityLevelEnum.HIGH: "高",
    AlarmSeverityLevelEnum.MIDDLE: "中",
    AlarmSeverityLevelEnum.LOW: "低",
}

ALARM_INSTANCE_RUN_STATUS_MAPPER = {
    AlarmRuleInstanceRunStatus.INIT: "待运行",
    AlarmRuleInstanceRunStatus.WAIT: "待运行",
    AlarmRuleInstanceRunStatus.DOING: "运行中",
    AlarmRuleInstanceRunStatus.SUCCESS: "运行成功",
    AlarmRuleInstanceRunStatus.FAILED: "运行失败",
}


ALARM_INSTANCE_RESULT_TYPE_MAPPER = {
    AlarmRuleResultTypeEnum.SINGLE_INSTANCE: "所有涉及实体包含在一个结果中",  # 告警聚合: 单告警
    AlarmRuleResultTypeEnum.MULTIPLE_INSTANCES: "每个实体一个结果",  # 告警分散: 多告警
}


ALARM_RULE_TYPE_MAPPER = {AlarmRuleTypeEnum.INTERNAL: "内置", AlarmRuleTypeEnum.CUSTOMIZE: "自定义"}

ALARM_TRIGGER_TYPE = {
    AlarmTriggerTypeEnum.DEFAULT: "系统默认",
    AlarmTriggerTypeEnum.CUSTOMIZE: "自定义",
    AlarmTriggerTypeEnum.AFTER_DATA_COLLECT: "数据治理之后",
}


ALARM_RESULT_STATEMENT_MODEL_MAPPER = {
    AlarmRuleStatementModelEnum.ASQL: "ASQL",
    AlarmRuleStatementModelEnum.TEMPLATE: "模版",
}
