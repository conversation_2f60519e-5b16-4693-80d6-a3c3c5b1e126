from caasm_tool.constants import StrEnum


class AdapterInnerType(StrEnum):
    NORMAL = "normal"
    VIRTUAL = "virtual"
    PROXY = "proxy"


class AdapterFetchType(StrEnum):
    # 同步采集任务，直接拿到结果
    DISPOSABLE = "disposable"
    # 发起采集任务，异步查询采集结果
    POLLED = "polled"
    # 常驻型，比如http、tcp等待数据传输
    PERMANENT = "permanent"


class AdapterFetchMode(StrEnum):
    DEFAULT = "default"
    COMPUTE_PAGE = "compute_page"


class AdapterFetchStatus(StrEnum):
    INIT = "init"
    FETCHING = "fetching"
    SUCCESS = "success"
    FAILED = "failed"
    CANCEL = "cancel"


ADAPTER_FETCH_STATUS_MAPPER = {
    AdapterFetchStatus.INIT: "待采集",
    AdapterFetchStatus.FETCHING: "采集中",
    AdapterFetchStatus.SUCCESS: "采集成功",
    AdapterFetchStatus.FAILED: "采集失败",
    AdapterFetchStatus.CANCEL: "取消",
}


class AdapterInstanceRunStatus(StrEnum):
    INIT = "init"
    WAIT = "wait"
    DOING = "doing"
    FAILED = "failed"
    SUCCESS = "success"


class AdapterInstanceConnectionStatus(StrEnum):
    UNKNOWN = "unknown"
    SUCCESS = "success"
    FAILED = "failed"


class AdapterMergeStatus(StrEnum):
    WAIT = "init"
    MERGING = "merging"
    SUCCESS = "success"
    FAILED = "failed"
    CANCEL = "cancel"


ADAPTER_MERGE_STATUS_MAPPER = {
    AdapterMergeStatus.WAIT: "待合并",
    AdapterMergeStatus.MERGING: "合并中",
    AdapterMergeStatus.SUCCESS: "合并成功",
    AdapterMergeStatus.FAILED: "合并失败",
    AdapterMergeStatus.CANCEL: "取消",
}


class AdapterConvertStatus(StrEnum):
    WAIT = "init"
    CONVERTING = "converting"
    SUCCESS = "success"
    FAILED = "failed"
    CANCEL = "cancel"


ADAPTER_CONVERT_STATUS_MAPPER = {
    AdapterConvertStatus.WAIT: "待转换",
    AdapterConvertStatus.CONVERTING: "转换中",
    AdapterConvertStatus.SUCCESS: "转换成功",
    AdapterConvertStatus.FAILED: "转换失败",
    AdapterConvertStatus.CANCEL: "取消",
}


class AdapterFabricStatus(StrEnum):
    WAIT = "init"
    FABRIC_ING = "fabric_ing"
    SUCCESS = "success"
    FAILED = "failed"


ADAPTER_FABRIC_STATUS_MAPPER = {
    AdapterFabricStatus.WAIT: "未融合",
    AdapterFabricStatus.FABRIC_ING: "融合中",
    AdapterFabricStatus.SUCCESS: "融合成功",
    AdapterFabricStatus.FAILED: "融合失败",
}
