from caasm_tool.constants import StrEnum


class ImportOption(StrEnum):
    #   完全刷新覆盖，删除原有数据
    REFRESH = "refresh"
    #   已有记录更新，新记录插入
    UPSERT = "upsert"


class FormComponentType(StrEnum):
    ITEM = "item"
    GROUP = "group"


class FormItemUIType(StrEnum):
    #   str, ip, ipv4, ipv6
    INPUT = "input"
    #   int, float
    INPUT_NUMBER = "input_number"
    #   enum
    SELECT = "select"
    #   long str
    TEXT = "text"
    #   boolean
    CHECKBOX = "checkbox"
    #   datetime
    DATETIME = "datetime"
    #   date
    DATE = "date"
    #   time
    TIME = "time"
    # address
    ADDRESS = "address"
    # LIST
    LIST = "list"
