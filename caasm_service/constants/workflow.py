from caasm_tool.constants import StrEnum


class NodeType(StrEnum):
    START = "start"
    ACTION = "action"
    PLAYBOOK = "playbook"
    END = "end"
    DUMMY = "dummy"


class CheckType(StrEnum):
    ALL_SUCCESS = "all_success"
    SINGLE_SUCCESS = "single_success"


class WorkflowStatus(StrEnum):
    INIT = "init"
    DOING = "doing"
    SUCCESS = "success"
    SLEEP = "sleep"
    FAILED = "failed"
    WAIT_CANCEL = "wait_cancel"
    CANCEL = "cancel"


WORKFLOW_STATUS_MAPPER = {
    WorkflowStatus.INIT: "初始化",
    WorkflowStatus.DOING: "处理中",
    WorkflowStatus.SUCCESS: "成功",
    WorkflowStatus.SLEEP: "等待中",
    WorkflowStatus.FAILED: "失败",
    WorkflowStatus.WAIT_CANCEL: "取消中",
    WorkflowStatus.CANCEL: "已取消",
}
