import re
from caasm_service.schema.runtime import alarm_record_schema
from caasm_persistence.handler.storage.mongo import <PERSON>go<PERSON><PERSON><PERSON>
from caasm_service.constant import Table


class AlarmRecordService(MongoHandler):
    DEFAULT_TABLE = Table.alarm_record
    DEFAULT_SCHEMA = alarm_record_schema

    def get_alarm_record_info(self, record_id=None, unique_key=None, alarm_name=None):
        """
        查询告警的展示信息
        """
        condition = self.build_alarm_record_condition(record_id=record_id, unique_key=unique_key, alarm_name=alarm_name)
        return self.get(condition=condition)

    def get_alarm_record_count(
        self,
        record_id=None,
        record_ids=None,
        rule_instance_id=None,
        alarm_category=None,
        alarm_level=None,
        alarm_status_list=None,
        unique_key=None,
        alarm_name=None,
        disposal_status=None,
        keyword=None,
    ):
        condition = self.build_alarm_record_condition(
            record_id=record_id,
            rule_instance_id=rule_instance_id,
            unique_key=unique_key,
            alarm_status_list=alarm_status_list,
            alarm_category=alarm_category,
            record_ids=record_ids,
            alarm_name=alarm_name,
            disposal_status=disposal_status,
            keyword=keyword,
        )
        return self.count(condition)

    def find_alarm_rule_instance_info(
        self,
        alarm_name=None,
        record_id=None,
        record_ids=None,
        rule_instance_id=None,
        alarm_status_list=None,
        alarm_category=None,
        alarm_level=None,
        disposal_status=None,
        page_index=None,
        page_size=None,
        sort_fields=None,
        keyword=None,
    ):
        """
        根据图表分类查询图表
        """
        condition = self.build_alarm_record_condition(
            alarm_name=alarm_name,
            rule_instance_id=rule_instance_id,
            record_id=record_id,
            alarm_level=alarm_level,
            alarm_status_list=alarm_status_list,
            alarm_category=alarm_category,
            record_ids=record_ids,
            disposal_status=disposal_status,
            keyword=keyword,
        )
        if not page_size:
            return self.find(condition=condition)
        return self.find(condition=condition, limit=page_size, offset=page_index * page_size, sort_fields=sort_fields)

    def delete_alarm_rule_record_info(self, record_id=None, record_ids=None):
        """
        删除图表
        """
        condition = self.build_alarm_record_condition(record_id=record_id, record_ids=record_ids)
        return self.delete_multi(condition=condition)

    def update_alarm_rule_record_by_id(self, record_id=None, unique_key=None, value=None):
        """
        更新图表数据
        """
        condition = self.build_alarm_record_condition(record_id=record_id, unique_key=unique_key)
        return self.update_direct(condition=condition, values=value)

    def add_alarm_rule_record(self, values):
        """新增图表"""
        return self.save(values).inserted_id

    @classmethod
    def build_alarm_record_condition(
        cls,
        record_id=None,
        record_ids=None,
        rule_instance_id=None,
        unique_key=None,
        alarm_name=None,
        rule_id=None,
        alarm_status_list=None,
        disposal_status=None,
        alarm_category=None,
        alarm_level=None,
        check_cout=None,
        hit_count=None,
        start_time=None,
        finish_time=None,
        keyword=None,
    ):
        query = {}

        if record_id:
            query["_id"] = cls._build_id(record_id)

        if rule_instance_id:
            query["rule_instance_id"] = cls._build_id(rule_instance_id)

        if record_ids:
            query["_id"] = {"$in": cls._build_ids(record_ids)}

        if rule_id:
            query["rule_id"] = cls._build_id(rule_id)

        if alarm_status_list:
            query["alarm_status"] = {"$in": alarm_status_list}

        if unique_key:
            query["unique_key"] = unique_key

        if alarm_name:
            query["alarm_name"] = alarm_name

        if disposal_status:
            query["disposal_status"] = disposal_status

        if alarm_category:
            query["alarm_category"] = alarm_category

        if alarm_level:
            query["alarm_level"] = alarm_level

        if check_cout:
            query["check_cout"] = check_cout

        if hit_count:
            query["hit_count"] = hit_count

        if start_time:
            query["start_time"] = start_time

        if finish_time:
            query["finish_time"] = finish_time

        if keyword:
            keyword_re = re.compile(keyword)
            query["$or"] = [{"alarm_name": {"$regex": keyword_re}}]

        return query
