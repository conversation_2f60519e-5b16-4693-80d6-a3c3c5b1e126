import re

from caasm_persistence.handler.storage.mongo import <PERSON><PERSON><PERSON><PERSON><PERSON>
from caasm_service.constant import Table
from caasm_service.constants.overview import DashboardSpaceDisplayEnum
from caasm_service.schema.runtime import overview_schema


class OverviewService(MongoHandler):
    DEFAULT_TABLE = Table.overview_space
    DEFAULT_SCHEMA = overview_schema

    def add_new_space(self, data):
        return self.save(data).inserted_id

    def find_owner_space(
        self,
        space_id=None,
        space_ids=None,
        user_id=None,
        role_codes=None,
        ownership=None,
        category=None,
        is_default=None,
        is_release=None,
        space_name=None,
        page_index=None,
        page_size=None,
        keyword=None,
        fields=None,
        sort_field=None,
    ):
        """
        获取当前自己所属空间
        """
        condition = self.build_space_condition(
            space_id=space_id,
            space_ids=space_ids,
            is_default=is_default,
            category=category,
            user_id=user_id,
            role_codes=role_codes,
            space_name=space_name,
            ownership=ownership,
            keyword=keyword,
        )
        return self.find(condition=condition, fields=fields, sort_fields=sort_field, offset=page_index, limit=page_size)

    def get_owner_space(
        self, space_id=None, role_codes=None, space_name=None, category=None, frontend_validate_name=None
    ):
        """
        获取自己的一个空间
        """
        condition = self.build_space_condition(
            space_name=space_name,
            space_id=space_id,
            role_codes=role_codes,
            category=category,
            frontend_validate_name=frontend_validate_name,
        )
        return self.get(condition=condition)

    def get_space_count(self, space_name=None, user_id=None, category=None, role_codes=None, keyword=None):
        """
        获取空间数量
        """
        condition = self.build_space_condition(
            space_name=space_name, user_id=user_id, role_codes=role_codes, keyword=keyword, category=category
        )
        return self.count(condition=condition)

    def update_space(self, space_id=None, values=None):
        """
        空间更新
        """
        condition = self.build_space_condition(space_id=space_id)
        return self.update_multi_direct(condition=condition, values=values)

    def delete_space(self, space_id=None, space_ids=None, is_default=None, space_name=None):
        """
        删除空间
        """
        condition = self.build_space_condition(
            space_id=space_id, space_ids=space_ids, is_default=is_default, space_name=space_name
        )
        return self.delete_multi(condition=condition)

    @classmethod
    def build_space_condition(
        cls,
        space_id=None,
        space_ids=None,
        user_id=None,
        category=None,
        role_codes=None,
        space_name=None,
        ownership=None,
        is_default=None,
        frontend_validate_name=None,
        keyword=None,
    ):
        query = {}

        if space_id:
            query["_id"] = cls._build_id(space_id)

        if frontend_validate_name:
            query["frontend_validate_name"] = frontend_validate_name

        if space_ids:
            query["_id"] = {"$in": cls._build_ids(space_ids)}

        if category:
            query["category"] = category

        if is_default:
            query["is_default"] = is_default

        if category:
            query["category"] = category

        if role_codes:
            query["$or"] = [
                {"role_codes": {"$in": role_codes}},
                {"is_default": True},
                {"user_id": user_id},
                {"ownership": DashboardSpaceDisplayEnum.PUBLIC.value},
            ]

        if space_name:
            query["name"] = space_name

        if keyword:
            keyword_re = re.compile(keyword)
            if "$or" not in query.keys():
                query["$or"] = []
            query["$or"].extend([{"display_model": {"$regex": keyword_re}}, {"name": {"$regex": keyword_re}}])

        return query
