from datetime import datetime
from typing import List, Optional, Dict

from caasm_persistence.handler.storage.mongo import MongoHandler
from caasm_service.constant import Table
from caasm_service.entity.order import OrderEntity
from caasm_service.schema.runtime import order_schema
from caasm_service.constants.order import OrderStatusEnum
from caasm_tool.constants import DATETIME_FORMAT
from caasm_tool.util import get_now


class OrderService(MongoHandler):
    """工单服务类"""

    DEFAULT_TABLE = Table.order
    DEFAULT_SCHEMA = order_schema

    def save_order(self, order: OrderEntity):
        """保存工单信息"""
        return self.save(order)

    def get_order(self, order_id: str = None, fields: List[str] = None):
        """获取单个工单信息"""
        condition = self.build_order_condition(order_id=order_id)
        return self.get(condition=condition, fields=fields)

    def find_orders(
        self,
        order_id: str = None,
        title: str = None,
        status: str = None,
        priority: str = None,
        creator: str = None,
        assignee: str = None,
        business_system: str = None,
        vul_id: str = None,
        create_time_start: datetime = None,
        create_time_end: datetime = None,
        creator_mobile: str = None,
        creator_phone: str = None,
        creator_email: str = None,
        assignee_mobile: str = None,
        assignee_phone: str = None,
        assignee_email: str = None,
        offset: int = None,
        limit: int = None,
        fields: List[str] = None,
        sort_fields: List[tuple] = None,
    ):
        """查询工单列表"""
        condition = self.build_order_condition(
            order_id=order_id,
            title=title,
            status=status,
            priority=priority,
            creator=creator,
            assignee=assignee,
            business_system=business_system,
            vul_id=vul_id,
            create_time_start=create_time_start,
            create_time_end=create_time_end,
            creator_mobile=creator_mobile,
            creator_phone=creator_phone,
            creator_email=creator_email,
            assignee_mobile=assignee_mobile,
            assignee_phone=assignee_phone,
            assignee_email=assignee_email,
        )
        return self.find(
            condition=condition,
            offset=offset,
            limit=limit,
            fields=fields,
            sort_fields=sort_fields,
        )

    def get_order_count(
        self,
        order_id: str = None,
        title: str = None,
        status: str = None,
        priority: str = None,
        creator: str = None,
        assignee: str = None,
        business_system: str = None,
        vul_id: str = None,
        create_time_start: datetime = None,
        create_time_end: datetime = None,
        creator_mobile: str = None,
        creator_phone: str = None,
        creator_email: str = None,
        assignee_mobile: str = None,
        assignee_phone: str = None,
        assignee_email: str = None,
    ):
        """获取工单数量"""
        condition = self.build_order_condition(
            order_id=order_id,
            title=title,
            status=status,
            priority=priority,
            creator=creator,
            assignee=assignee,
            business_system=business_system,
            vul_id=vul_id,
            create_time_start=create_time_start,
            create_time_end=create_time_end,
            creator_mobile=creator_mobile,
            creator_phone=creator_phone,
            creator_email=creator_email,
            assignee_mobile=assignee_mobile,
            assignee_phone=assignee_phone,
            assignee_email=assignee_email,
        )
        return self.count(condition=condition)

    def update_order_status(self, order_id: str, status=None, business_system=None):
        """更新工单状态"""
        condition = self.build_order_condition(order_id=order_id)
        update_data = {}
        if status is not None:
            update_data["status"] = status
        if business_system is not None:
            update_data["business_system"] = business_system
        return self.update_direct(condition=condition, values=update_data)

    def add_vul_to_order(self, order_id: str, vul_id: str):
        """向工单添加漏洞ID"""
        order = self.get_order(order_id=order_id)
        if not order:
            return False

        # 检查漏洞ID是否已存在
        if vul_id in order.vul_ids:
            return True

        # 添加漏洞ID
        condition = self.build_order_condition(order_id=order_id)
        update_data = {"vul_ids": order.vul_ids + [vul_id], "update_time": datetime.now()}
        return self.update_direct(condition=condition, values=update_data)

    def remove_vul_from_order(self, order_id: str, vul_id: str):
        """从工单移除漏洞ID"""
        order = self.get_order(order_id=order_id)
        if not order:
            return False

        # 检查漏洞ID是否存在
        if vul_id not in order.vul_ids:
            return True

        # 移除漏洞ID
        vul_ids = order.vul_ids.copy()
        vul_ids.remove(vul_id)

        condition = self.build_order_condition(order_id=order_id)
        update_data = {"vul_ids": vul_ids, "update_time": datetime.now()}
        return self.update_direct(condition=condition, values=update_data)

    def sync_operation_logs(self, order_id: str, logs: List[Dict] = None, creator_info: Dict = None):
        """从其他系统同步操作日志

        Args:
            order_id: 工单ID
            logs: 操作日志列表，每个日志应包含以下字段：
                - operation_type: 操作类型
                - operator: 操作人
                - operation_time: 操作时间
                - content: 操作内容
                - details: 操作详情（可选）
                  - conn: 联系信息，格式如 "手机号：13312341234，办公电话：010-11111111，邮箱：<EMAIL>"

        Returns:
            bool: 同步是否成功
        """
        # 获取工单信息
        order: OrderEntity = self.get_order(order_id=order_id)
        if not order:
            return False

        # 更新工单的操作日志
        condition = self.build_order_condition(order_id=order_id)
        update_data = {"operation_logs": logs}

        # 提取创建人和负责人的联系信息

        # 如果提供了创建人信息
        if creator_info:
            update_data["creator"] = creator_info.get("operator", None)
            mobile = creator_info.get("mobile", None)
            if creator_info.get("creator_id", None):
                update_data["creator_id"] = str(creator_info.get("creator_id", None))
            if mobile:
                update_data["creator_mobile"] = mobile
            phone = creator_info.get("phone", None)
            if phone:
                update_data["creator_phone"] = phone
            email = creator_info.get("email", None)
            if email:
                update_data["creator_email"] = email
            if creator_info.get("order_status", None) == OrderStatusEnum.CLOSED.value:
                update_data["status"] = OrderStatusEnum.CLOSED.value
            closed_time = creator_info.get("order_closed_time", None)
            if closed_time:
                update_data["finish_time"] = closed_time
            if creator_info.get("create_time", None):
                update_data["create_time"] = creator_info.get("create_time", "")
            if creator_info.get("update_time", None):
                update_data["update_time"] = creator_info.get("update_time", "")

        return self.update_direct(condition=condition, values=update_data)

    def append_operation_logs(self, order_id: str, logs: List[Dict]):
        """添加操作日志到工单中

        Args:
            order_id: 工单ID
            logs: 要添加的操作日志列表

        Returns:
            bool: 添加是否成功
        """
        # 获取工单信息
        order = self.get_order(order_id=order_id)
        if not order:
            return False

        # 添加日志到工单的操作日志中
        condition = self.build_order_condition(order_id=order_id)
        update_data = {"operation_logs": order.operation_logs + logs, "update_time": datetime.now()}

        # 提取联系信息
        for log_item in logs:
            # 如果是创建人信息
            if log_item.get("is_creator", False) and "details" in log_item and "conn" in log_item["details"]:
                conn_info = log_item["details"]["conn"]
                mobile, phone, email = self._extract_contact_info(conn_info)

                if mobile and not order.creator_mobile:
                    update_data["creator_mobile"] = mobile
                if phone and not order.creator_phone:
                    update_data["creator_phone"] = phone
                if email and not order.creator_email:
                    update_data["creator_email"] = email

            # 如果是负责人信息（根据操作类型判断）
            if log_item.get("operation_type") == "分派工单" and "details" in log_item and "conn" in log_item["details"]:
                conn_info = log_item["details"]["conn"]
                mobile, phone, email = self._extract_contact_info(conn_info)

                if mobile:
                    update_data["assignee_mobile"] = mobile
                if phone:
                    update_data["assignee_phone"] = phone
                if email:
                    update_data["assignee_email"] = email

        return self.update_direct(condition=condition, values=update_data)

    def get_operation_logs(self, order_id: str, limit: int = None):
        """获取工单的操作日志

        Args:
            order_id: 工单ID
            limit: 返回的日志数量限制，如果为 None 则返回所有日志

        Returns:
            List[Dict]: 操作日志列表，按操作时间倒序排序
        """
        order = self.get_order(order_id=order_id)
        if not order or not order.operation_logs:
            return []

        # 按操作时间倒序排序
        logs = sorted(order.operation_logs, key=lambda x: x.get("operation_time", ""), reverse=True)

        # 如果指定了限制数量，则只返回指定数量的日志
        if limit and limit > 0:
            logs = logs[:limit]

        return logs

    def build_order_condition(
        self,
        order_id: str = None,
        title: str = None,
        status: str = None,
        priority: str = None,
        creator: str = None,
        assignee: str = None,
        business_system: str = None,
        vul_id: str = None,
        create_time_start: datetime = None,
        create_time_end: datetime = None,
        creator_mobile: str = None,
        creator_phone: str = None,
        creator_email: str = None,
        assignee_mobile: str = None,
        assignee_phone: str = None,
        assignee_email: str = None,
    ):
        """构建查询条件"""
        query = {}
        if order_id:
            query["order_id"] = order_id
        if title:
            query["title"] = {"$regex": title}
        if status:
            query["status"] = status
        if priority:
            query["priority"] = priority
        if creator:
            query["creator"] = creator
        if assignee:
            query["assignee"] = assignee
        if business_system:
            query["business_system"] = business_system
        if vul_id:
            query["vul_ids"] = vul_id

        # 联系信息查询
        if creator_mobile:
            query["creator_mobile"] = creator_mobile
        if creator_phone:
            query["creator_phone"] = creator_phone
        if creator_email:
            query["creator_email"] = creator_email
        if assignee_mobile:
            query["assignee_mobile"] = assignee_mobile
        if assignee_phone:
            query["assignee_phone"] = assignee_phone
        if assignee_email:
            query["assignee_email"] = assignee_email

        # 创建时间范围查询
        if create_time_start or create_time_end:
            create_time_condition = {}
            if create_time_start:
                create_time_condition["$gte"] = create_time_start
            if create_time_end:
                create_time_condition["$lte"] = create_time_end
            if create_time_condition:
                query["create_time"] = create_time_condition

        return query
