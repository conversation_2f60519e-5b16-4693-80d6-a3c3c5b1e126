from typing import List

from caasm_persistence.entity.base import BaseEntity
from caasm_persistence.handler.storage.mongo import <PERSON><PERSON><PERSON>and<PERSON>
from caasm_persistence.schema.base import BaseSchema
from caasm_service.constant import Table
from caasm_service.schema.runtime import (
    entity_lineage_stage_schema,
    entity_type_lineage_stage_schema,
    value_lineage_stage_schema,
)


class EntityTypeLineageStageService(MongoHandler):
    DEFAULT_TABLE = Table.entity_type_stage_lineage
    DEFAULT_SCHEMA = entity_type_lineage_stage_schema

    def get_lineage_by_upstream_table(self, table):
        condition = {"table": table}
        founds = list(self.find(condition, sort_fields=[("create_time", -1)], limit=1))
        if founds:
            return founds[0]
        else:
            return None


class EntityLineageStageService(MongoHandler):
    DEFAULT_SCHEMA = entity_lineage_stage_schema

    def _get_table(self, dst_table):
        return f"{self.DEFAULT_TABLE}.{dst_table}"

    def save_stage(self, entity: BaseEntity, dst_table, schema: BaseSchema = None, **kwargs):
        return self.save(entity, self._get_table(dst_table), schema, **kwargs)

    def save_stages(self, entities: List[BaseEntity], dst_table, schema=None):
        return self.save_multi(entities, self._get_table(dst_table), schema)


class EntityLineageFetchStageService(EntityLineageStageService):
    DEFAULT_TABLE = Table.entity_fetch_stage_lineage


class EntityLineageMergeStageService(EntityLineageStageService):
    DEFAULT_TABLE = Table.entity_merge_stage_lineage


class EntityLineageConvertStageService(EntityLineageStageService):
    DEFAULT_TABLE = Table.entity_convert_stage_lineage


class EntityLineageFabricStageService(EntityLineageStageService):
    DEFAULT_TABLE = Table.entity_fabric_stage_lineage

    def get_table(self, category, date):
        return f"{self.DEFAULT_TABLE}.{date}.{category}"


class ValueLineageStageService(EntityLineageStageService):
    DEFAULT_SCHEMA = value_lineage_stage_schema


class ValueLineageFetchStageService(ValueLineageStageService):
    DEFAULT_TABLE = Table.value_fetch_stage_lineage


class ValueLineageMergeStageService(ValueLineageStageService):
    DEFAULT_TABLE = Table.value_merge_stage_lineage


class ValueLineageConvertStageService(ValueLineageStageService):
    DEFAULT_TABLE = Table.value_convert_stage_lineage


class ValueLineageFabricStageService(ValueLineageStageService):
    DEFAULT_TABLE = Table.value_fabric_stage_lineage

    def get_table(self, category, date):
        return f"{self.DEFAULT_TABLE}.{date}.{category}"
