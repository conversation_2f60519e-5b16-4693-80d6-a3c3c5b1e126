from bson import ObjectId

from caasm_persistence.handler.storage.mongo import <PERSON><PERSON><PERSON><PERSON><PERSON>
from caasm_service.constant import Table
from caasm_service.entity.link_graph import LinkGraph, EntityLinkGraph
from caasm_service.schema.runtime import entity_link_graph_schema, link_graph_schema


class EntityLinkGraphService(MongoHandler):
    DEFAULT_SCHEMA = entity_link_graph_schema
    DEFAULT_TABLE = Table.entity_graph

    def save_graphs(self, graphs):
        if not isinstance(graphs, list):
            graphs = [graphs]
        self.save_multi(graphs)

    def begin(self):
        self.delete_multi({"finished": False})

    def finish(self):
        self.delete_multi({"finished": True})
        self.update_multi_direct(
            {
                "finished": False,
            },
            {"finished": True},
        )

    def get_graph(self, category, entity_id):
        return self.get(
            {
                "category": category,
                "entity_id": entity_id,
            }
        )

    def delete_graphs(self, graph_ids):
        self.delete_multi({"graph_id": {"$in": graph_ids}})

    def get_entity_graph(self, category, entity_id):
        entity_link_graph: EntityLinkGraph = self.get_graph(category, entity_id)
        if entity_link_graph:
            from caasm_service.runtime import link_graph_service

            return link_graph_service.get_graph(entity_link_graph.graph_id)

    def find_entity_link_graph(self, category, entity_id, asset_type=None, *args, **kwargs):
        condition = {"category": category, "entity_id": entity_id}
        if asset_type:
            condition["asset_type"] = asset_type
        return self.find(condition, *args, **kwargs)


class LinkGraphService(MongoHandler):
    DEFAULT_SCHEMA = link_graph_schema
    DEFAULT_TABLE = Table.link_graph

    def save_graphs(self, graphs):
        if not isinstance(graphs, list):
            graphs = [graphs]
        for graph in graphs:
            graph: LinkGraph = graph
            graph.finished = False
        self.save_multi(graphs)

    def get_graph(self, graph_id):
        if isinstance(graph_id, str):
            graph_id = ObjectId(graph_id)
        return self.get({"_id": graph_id, "finished": True})

    def find_graph_ids_of_domain(self, domain):
        return self.find({"domains": domain}, fields=["_id"])

    def begin(self):
        from caasm_service.runtime import entity_link_graph_service

        entity_link_graph_service.begin()
        self.delete_multi({"finished": False})

    def finish(self):
        from caasm_service.runtime import entity_link_graph_service

        entity_link_graph_service.finish()
        self.delete_multi({"finished": True})
        self.update_multi_direct(
            {
                "finished": False,
            },
            {"finished": True},
        )
