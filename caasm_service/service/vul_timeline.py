from bson import ObjectId

from caasm_persistence.handler.storage.mongo import <PERSON><PERSON><PERSON><PERSON><PERSON>
from caasm_service.constant import Table
from caasm_service.entity.vul_timeline import VulTimeLine
from caasm_service.schema.runtime import vul_timeline_schema


class VulTimelineService(MongoHandler):
    DEFAULT_SCHEMA = vul_timeline_schema
    DEFAULT_TABLE = Table.vul_timeline

    def save_vul_timeline(self, vul_timeline: VulTimeLine):
        return self.save(vul_timeline)

    def find_vul_timeline(self, vul_id=None, offset=None, limit=None, sort_fields=None):
        condition = self.build_search_condition(vul_id)
        return self.find(condition, sort_fields=sort_fields, limit=limit, offset=offset)

    def build_search_condition(self, vul_id=None):
        query = {}
        query["vul_id"] = vul_id

        return query
