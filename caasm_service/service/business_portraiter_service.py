import re

from caasm_persistence.handler.storage.mongo import <PERSON>goHand<PERSON>
from caasm_service.constant import Table
from caasm_service.schema.runtime import business_portrait_schema, business_portrait_update_stream


class BusinessPortraiterService(MongoHandler):
    DEFAULT_TABLE = Table.business_portraiter
    DEFAULT_SCHEMA = business_portrait_schema

    def get_table(self):
        return self.DEFAULT_TABLE

    def find_business_portraiter(
        self,
        keyword=None,
        owner_keyword=None,
        magnitude=None,
        critical_infrastructure=None,
        grade_protection_level=None,
        data_center=None,
        data_centers=None,
        realm=None,
        offset=None,
        limit=None,
        fields=None,
    ):
        query = self.build_business_portraiter_query(
            keyword,
            owner_keyword,
            magnitude,
            critical_infrastructure,
            grade_protection_level,
            data_center=data_center,
            data_centers=data_centers,
            realm=realm,
        )
        return self.find(condition=query, offset=offset, limit=limit, fields=fields, table=self.get_table())

    def get_business_portraiter_count(
        self,
        keyword=None,
        owner_keyword=None,
        magnitude=None,
        critical_infrastructure=None,
        grade_protection_level=None,
        data_center=None,
        data_centers=None,
        realm=None,
    ):
        query = self.build_business_portraiter_query(
            keyword,
            owner_keyword,
            magnitude,
            critical_infrastructure,
            grade_protection_level,
            data_center=data_center,
            data_centers=data_centers,
            realm=realm,
        )
        return self.count(query, table=self.get_table())

    def get_business_portraiter(self, business_portraiter_id=None):
        query = self.build_business_portraiter_query(business_portraiter_id=business_portraiter_id)
        return self.get(query, table=self.get_table())

    @classmethod
    def build_business_portraiter_query(
        cls,
        keyword=None,
        owner_keyword=None,
        magnitude=None,
        critical_infrastructure=None,
        grade_protection_level=None,
        business_portraiter_id=None,
        data_center=None,
        data_centers=None,
        realm=None,
    ):
        query = {}

        if keyword:
            query["name"] = {"$regex": re.compile(keyword)}
        if owner_keyword:
            query["owner"] = {"$regex": re.compile(owner_keyword)}

        if magnitude is not None:
            query["magnitude"] = magnitude

        if critical_infrastructure is not None:
            query["critical_infrastructure"] = critical_infrastructure

        if grade_protection_level is not None:
            query["grade_protection_level"] = grade_protection_level

        if business_portraiter_id:
            query["_id"] = cls._build_id(business_portraiter_id)

        # 处理数据中心过滤条件
        if data_centers and isinstance(data_centers, list) and len(data_centers) > 0:
            query["data_centers"] = {"$in": data_centers}
        elif data_center:
            # 如果数据中心是"所有分公司"，则匹配所有后缀为"分公司"的数据中心
            if data_center == "所有分公司":
                query["data_centers"] = {"$regex": "分公司$"}
            else:
                query["data_centers"] = data_center

        if realm:
            query["realms"] = realm

        return query


class BusinessPortraitUpdateService(MongoHandler):
    DEFAULT_SCHEMA = business_portrait_update_stream
    DEFAULT_TABLE = Table.business_portraiter_update

    def save_update_direct(self, update):
        entity_id = update.get("entity_id")
        if not entity_id:
            raise ValueError("未指定entity_id值")
        existing_update_ids = []
        for existing_update in self.find_direct({"entity_id": entity_id}):
            existing_update_ids.append(existing_update.get("_id"))
        self.save_direct(update)
        if existing_update_ids:
            self.delete_multi({"_id": {"$in": existing_update_ids}})
