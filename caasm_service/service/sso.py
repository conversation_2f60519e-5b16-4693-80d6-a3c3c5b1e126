from caasm_persistence.handler.storage.mongo import <PERSON>go<PERSON><PERSON><PERSON>
from caasm_service.constant import Table
from caasm_service.schema.runtime import sso_config_schema, temp_login_key, sso_config_schema


class TempLoginKeyService(MongoHandler):
    DEFAULT_SCHEMA = temp_login_key
    DEFAULT_TABLE = Table.temp_login_key

    def save_temp_login_key(self, data):
        self.save(data)

    def get_temp_login_key(self, sso_key=None, login_key=None):
        condition = self.build_temp_login_key_condition(sso_key=sso_key, login_key=login_key)
        return self.get_direct(condition=condition)

    def delete_temp_login_key(self, login_key=None, sso_key=None):
        condition = self.build_temp_login_key_condition(login_key=login_key, sso_key=sso_key)
        return self.delete_one(condition=condition)

    def build_temp_login_key_condition(self, sso_key=None, login_key=None):

        query = {}

        if sso_key:
            query["sso_key"] = sso_key

        if login_key:
            query["login_key"] = login_key

        return query


class SSOConfigService(MongoHandler):
    DEFAULT_SCHEMA = sso_config_schema
    DEFAULT_TABLE = Table.sso_config

    def get_sso_config(self, authentication_type=None):
        condition = self.build_sso_config_condition(authentication_type=authentication_type)
        return self.get(condition=condition)

    def save_sso_config(self, data):
        self.save(data)

    def delete_other_config(self):
        self.delete_multi(condition={})

    def update_sso_config(self, authentication_type=None, config=None):
        condition = self.build_sso_config_condition(authentication_type=authentication_type)
        self.update_direct(condition=condition, values={"config": config})

    def build_sso_config_condition(self, authentication_type=None):

        query = {}
        if authentication_type:
            query["authentication_type"] = authentication_type

        return query
