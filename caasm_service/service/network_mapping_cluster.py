from caasm_persistence.handler.storage.mongo import <PERSON><PERSON><PERSON><PERSON><PERSON>
from caasm_service.constant import Table
from caasm_service.entity.network_mapping import NetworkMappingClusterEntity
from caasm_service.schema.runtime import network_mapping_cluster_schema


class NetworkMappingClusterService(MongoHandler):
    DEFAULT_TABLE = Table.network_mapping_clusters
    DEFAULT_SCHEMA = network_mapping_cluster_schema

    def save_cluster(self, clusters):
        if isinstance(clusters, NetworkMappingClusterEntity):
            clusters = [clusters]
        self.save(clusters)

    def find_clusters(self):
        return self.find()
