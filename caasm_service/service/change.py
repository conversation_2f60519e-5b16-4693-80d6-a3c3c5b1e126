from datetime import date

from bson import ObjectId

from caasm_persistence.handler.storage.mongo import MongoHandler
from caasm_service.constant import Table
from caasm_service.entity.change import ChangeRecord
from caasm_service.schema.runtime import change_detail_schema, change_record_schema


class ChangeService(MongoHandler):
    DEFAULT_SCHEMA = change_detail_schema
    DEFAULT_TABLE = Table.change_details

    def save_change_details(self, records):
        return self.save_multi(records, table=Table.change_temp)

    def change_details_count(
        self,
        category=None,
        asset_type=None,
        entity_id=None,
        field_name=None,
        original_date_from=None,
        original_date_to=None,
        new_date_from=None,
        new_date_to=None,
        *args,
        **kwargs
    ):
        condition = self._build_condition(
            category,
            asset_type,
            entity_id,
            field_name,
            original_date_from,
            original_date_to,
            new_date_from,
            new_date_to,
        )
        return self.count(condition, *args, **kwargs)

    def find_change_details(
        self,
        category=None,
        asset_type=None,
        entity_id=None,
        field_name=None,
        original_date_from=None,
        original_date_to=None,
        new_date_from=None,
        new_date_to=None,
        *args,
        **kwargs
    ):
        condition = self._build_condition(
            category,
            asset_type,
            entity_id,
            field_name,
            original_date_from,
            original_date_to,
            new_date_from,
            new_date_to,
        )
        return self.find(condition, sort_fields=[("new_date", 1)], *args, **kwargs)

    def _build_condition(
        self,
        category=None,
        asset_type=None,
        entity_id=None,
        field_name=None,
        original_date_from=None,
        original_date_to=None,
        new_date_from=None,
        new_date_to=None,
    ):
        condition = {}
        if category:
            condition = {"category": category}
        if asset_type:
            condition = {"asset_type": asset_type}
        if entity_id:
            condition = {"entity_id": entity_id}
        if field_name:
            condition = {"field_name": field_name}
        if original_date_from:
            if isinstance(original_date_from, date):
                original_date_from = str(original_date_from)
            condition["original_date"] = {"$gte": original_date_from}
        if original_date_to:
            if isinstance(original_date_to, date):
                original_date_to = str(original_date_to)
            condition["original_date"] = {"$lte": original_date_to}
        if new_date_from:
            if isinstance(new_date_from, date):
                new_date_from = str(new_date_from)
            condition["new_date"] = {"$gte": new_date_from}
        if new_date_from:
            if isinstance(new_date_to, date):
                new_date_to = str(new_date_to)
            condition["new_date"] = {"$lte": new_date_to}
        return condition

    def get_change_record(self, category: str, original_date, new_date, finished=None):
        condition = {"category": category, "original_date": original_date, "new_date": new_date}
        if finished is not None:
            condition["finished"] = finished
        return self.get(condition=condition, schema=change_record_schema, table=Table.change_records)

    def begin_change_record(self, category: str, original_date=None, new_date=None, force=False):
        self.drop(Table.change_temp)
        change_record: ChangeRecord = self.get_change_record(category, original_date, new_date)
        if change_record:
            if change_record.finished:
                if force:
                    self.delete_one({"_id": change_record.id})
                    change_record = None
                else:
                    return None
            else:
                return change_record.id
        if change_record is None:
            change_record = change_record_schema.load(
                {"category": category, "original_date": original_date, "new_date": new_date, "finished": False}
            )
            return self.save(change_record, table=Table.change_records, schema=change_record_schema).inserted_id

    def finish_change_record(self, record_id):
        if isinstance(record_id, str):
            record_id = ObjectId(record_id)
        change_record: ChangeRecord = self.get(
            {"_id": record_id}, table=Table.change_records, schema=change_record_schema
        )
        if not change_record:
            return
        #   删除已有的
        self.delete_multi(
            {"original_date": change_record.original_date.isoformat(), "new_date": change_record.new_date.isoformat()}
        )
        offset = 0
        limit = 1000
        while True:
            details = list(self.find(table=Table.change_temp, offset=offset, limit=limit))
            if not details:
                break
            self.save_multi(details)
            offset += 1000
        self.drop(Table.change_temp)
        change_record.finished = True
        self.update(change_record, table=Table.change_records, schema=change_record_schema)

    def is_change_record_finished(self, category: str, original_date, new_date):
        change_record: ChangeRecord = self.get_change_record(category, original_date, new_date)
        return change_record and change_record.finished

    def find_change_records(self, category: str, finished=None):
        condition = {"category": category}
        if finished is not None:
            condition["finished"] = finished
        return self.find(condition, schema=change_record_schema, table=Table.change_records)

    def exist_change_record(self, category, original_date, new_date):
        if isinstance(original_date, date):
            original_date = str(original_date)
        if isinstance(new_date, date):
            new_date = str(new_date)
        record = self.get({"category": category, "original_date": original_date, "new_date": new_date})
        return record is not None

    @staticmethod
    def _find_undetected_dates(available_dates, detected_dates, current_date):
        detected_dates_ = []
        for f, t in detected_dates:
            if isinstance(f, str):
                f = date.fromisoformat(f)
            if isinstance(t, str):
                t = date.fromisoformat(t)
            detected_dates_.append((f, t))

        undetected_dates = []
        if available_dates:
            latest_date = available_dates[0]
            if isinstance(latest_date, str):
                latest_date = date.fromisoformat(latest_date)
            if current_date:
                if isinstance(current_date, str):
                    current_date = date.fromisoformat(current_date)
            if (
                latest_date
                and current_date
                and current_date > latest_date
                and (latest_date, current_date) not in detected_dates_
            ):
                undetected_dates.append((latest_date.isoformat(), current_date.isoformat()))
        return undetected_dates

    def find_undetected_dates(self, category: str, current_date=None):
        from caasm_service.runtime import snapshot_record_service

        dates = snapshot_record_service.get_dates(asc=False)
        available_dates = []
        for date_ in dates:
            from caasm_service.runtime import entity_service

            if entity_service.exists(entity_service.get_table(category, date_)):
                available_dates.append(date_)
        detected_dates = []
        for record in self.find_change_records(category, True):
            record: ChangeRecord = record
            detected_dates.append((record.original_date, record.new_date))
        return self._find_undetected_dates(available_dates, detected_dates, current_date)

    def delete_change_details(self, category, field_name: str = None, entity_id=None):
        condition = {"category": category}
        if field_name is not None:
            condition["field_name"] = field_name
        if entity_id:
            condition["entity_id"] = entity_id
        self.delete_multi(
            condition,
        )

    def delete_change_record(self, category, original_date, new_date):
        self.delete_multi(
            {"category": category, "original_date": original_date, "new_date": new_date}, table=Table.change_records
        )
