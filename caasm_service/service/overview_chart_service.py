import re

from caasm_persistence.handler.storage.mongo import <PERSON>goHand<PERSON>
from caasm_service.constant import Table
from caasm_service.schema.runtime import overview_chart_schema


class OverviewChartService(MongoHandler):
    DEFAULT_TABLE = Table.overview_chart
    DEFAULT_SCHEMA = overview_chart_schema

    def get_chart_info(self, chart_id=None, chart_name=None):
        """
        查询单个图表分类信息
        """
        condition = self.build_chart_condition(chart_id=chart_id, chart_name=chart_name)
        return self.get(condition=condition)

    def get_chart_count(self, category_tree_id=None, category_tree_ids=None, keyword=None):
        condition = self.build_chart_condition(
            category_tree_id=category_tree_id, category_tree_ids=category_tree_ids, keyword=keyword
        )
        return self.count(condition)

    def find_chart_info(
        self,
        chart_name=None,
        category_tree_id=None,
        category_tree_ids=None,
        page_index=None,
        page_size=None,
        chart_ids=None,
        sort_fields=None,
        keyword=None,
        fields=None,
    ):
        """
        根据图表分类查询图表
        """
        condition = self.build_chart_condition(
            chart_name=chart_name,
            category_tree_id=category_tree_id,
            category_tree_ids=category_tree_ids,
            chart_ids=chart_ids,
            keyword=keyword,
        )
        if not page_size:
            return self.find(condition=condition, fields=fields)
        return self.find(
            condition=condition, limit=page_size, offset=page_index * page_size, sort_fields=sort_fields, fields=fields
        )

    def delete_chart_info(self, chart_ids=None, internal=None):
        """
        删除图表
        """
        condition = self.build_chart_condition(chart_ids=chart_ids, internal=internal)
        return self.delete_multi(condition=condition)

    def update_chart_info_by_id(self, chart_id=None, category_tree_id=None, value=None):
        """
        更新图表数据
        """
        condition = self.build_chart_condition(chart_id=chart_id, category_tree_id=category_tree_id)
        return self.update_direct(condition=condition, values=value)

    def update_charts_info_by_id(self, chart_ids=None, category_tree_ids=None, value=None):
        """
        更新图表数据
        """
        condition = self.build_chart_condition(chart_ids=chart_ids, category_tree_ids=category_tree_ids)
        return self.update_multi_direct(condition=condition, values=value)

    def add_chart_info(self, values):
        """新增图表"""
        return self.save(values).inserted_id

    def get_chart_info_count(self, category_tree_id=None, category_tree_ids=None, keyword=None):
        """
        获取图表数量
        """
        condition = self.build_chart_condition(
            category_tree_id=category_tree_id, category_tree_ids=category_tree_ids, keyword=keyword
        )
        return self.count(condition=condition)

    def modify_same_chart_info_by_category_ids(self, category_tree_ids=None, values=None):
        """
        修改相同的chart信息通过chartids
        """
        condition = self.build_chart_condition(category_tree_ids=category_tree_ids)
        return self.update_direct(condition=condition, values=values)

    @classmethod
    def build_chart_condition(
        cls,
        chart_id=None,
        category_tree_id=None,
        category_tree_ids=None,
        chart_name=None,
        keyword=None,
        chart_ids=None,
        internal=None,
        description=None,
    ):
        query = {}

        if chart_id:
            query["_id"] = cls._build_id(chart_id)

        if chart_ids:
            query["_id"] = {"$in": cls._build_ids(chart_ids)}

        if category_tree_id:
            query["category_tree_id"] = category_tree_id

        if category_tree_ids:
            query["category_tree_id"] = {"$in": category_tree_ids}

        if chart_name:
            query["chart_name"] = chart_name

        if internal:
            query["internal"] = internal

        if description:
            query["description"] = description

        if keyword:
            keyword_re = re.compile(keyword)
            query["$or"] = [{"chart_name": {"$regex": keyword_re}}, {"description": {"$regex": keyword_re}}]

        return query
