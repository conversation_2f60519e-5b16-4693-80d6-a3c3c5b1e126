from bson import ObjectId

from caasm_persistence.handler.storage.mongo import <PERSON>go<PERSON><PERSON><PERSON>
from caasm_service.constant import Table
from caasm_service.entity.vul_priority import VulPrioritySetting, VulPriority
from caasm_service.schema.runtime import vul_priority_setting_schema, vul_priority_schema


class VulPriorityService(MongoHandler):
    DEFAULT_SCHEMA = vul_priority_schema
    DEFAULT_TABLE = Table.vul_priority

    def drop_priorities(self):
        self.drop()
        self.drop(Table.vul_priority_settings)

    @staticmethod
    def _get_id(id_):
        if isinstance(id_, str):
            id_ = ObjectId(id_)
        return id_

    def exist_priority(self, name: str):
        return self.get({"name": name}) is not None

    def create_priority(self, name: str, priority: int):
        if self.exist_priority(name):
            return
        vul_priority = vul_priority_schema.load({"name": name, "priority": priority})
        return self.save(vul_priority)

    def delete_priority(self, id_):
        id_ = self._get_id(id_)
        self.delete_multi({"priority": id_}, table=Table.vul_priority_settings)
        self.delete_one({"_id": id_})

    def update_priority(self, id_, name: str, priority: int):
        id_ = self._get_id(id_)
        vul_priority: VulPriority = self.get({"_id": id_})
        if vul_priority is None:
            return False
        vul_priority.priority = priority
        vul_priority.name = name
        self.update(vul_priority)
        return True

    def list_priorities(self):
        return self.find(sort_fields=[("priority", 1)])

    def get_priority(self, id_):
        id_ = self._get_id(id_)
        return self.get({"_id": id_})

    def create_setting(self, id_, setting: VulPrioritySetting):
        id_ = self._get_id(id_)
        vul_priority = self.get_priority(id_)
        if vul_priority is None:
            return False
        setting.priority = id_
        self.save(setting, table=Table.vul_priority_settings, schema=vul_priority_setting_schema)
        return True

    def delete_setting(self, id_, setting_id):
        id_ = self._get_id(id_)
        vul_priority = self.get_priority(id_)
        if vul_priority is None:
            return False
        self.delete_one({"priority": id_, "_id": self._get_id(setting_id)}, table=Table.vul_priority_settings)
        return True

    def update_setting(self, id_, setting):
        id_ = self._get_id(id_)
        vul_priority = self.get_priority(id_)
        if vul_priority is None:
            return False
        vul_setting = self.get(
            {"priority": id_, "_id": setting.id}, table=Table.vul_priority_settings, schema=vul_priority_setting_schema
        )
        if not vul_setting:
            return False
        self.update(setting, table=Table.vul_priority_settings, schema=vul_priority_setting_schema)
        return True

    def list_settings(self, id_):
        id_ = self._get_id(id_)
        return self.find({"priority": id_}, table=Table.vul_priority_settings, schema=vul_priority_setting_schema)

    def count_of_settings(self, id_):
        return self.count({"priority": self._get_id(id_)}, table=Table.vul_priority_settings)

    def list_settings_by_priority_name(self, name):
        priority = self.get({"name": name})
        if priority:
            return self.list_settings(priority.id)
        else:
            return None
