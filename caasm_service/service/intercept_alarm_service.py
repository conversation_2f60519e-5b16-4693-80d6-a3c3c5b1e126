import re
from caasm_service.schema.runtime import intercept_alarm_schema
from caasm_persistence.handler.storage.mongo import <PERSON>go<PERSON><PERSON><PERSON>
from caasm_service.constant import Table


class InterceptAlarmService(MongoHandler):
    """拦截告警服务类"""

    DEFAULT_TABLE = Table.intercept_alarm
    DEFAULT_SCHEMA = intercept_alarm_schema

    def get_intercept_alarm_info(self, record_id=None, src_ip=None, dst_ip=None):
        """
        查询拦截告警的展示信息
        """
        condition = self.build_intercept_alarm_condition(record_id=record_id, src_ip=src_ip, dst_ip=dst_ip)
        return self.get(condition=condition)

    def get_intercept_alarm_count(
        self,
        record_id=None,
        record_ids=None,
        src_ip=None,
        src_group_relationship_name=None,
        dst_ip=None,
        dst_port=None,
        dst_group_relationship_name=None,
        src_data_center=None,
        src_realm=None,
        dst_data_center=None,
        dst_realm=None,
        src_province=None,
        dst_province=None,
        keyword=None,
    ):
        """
        获取拦截告警数量
        """
        condition = self.build_intercept_alarm_condition(
            record_id=record_id,
            record_ids=record_ids,
            src_ip=src_ip,
            src_group_relationship_name=src_group_relationship_name,
            dst_ip=dst_ip,
            dst_port=dst_port,
            dst_group_relationship_name=dst_group_relationship_name,
            src_data_center=src_data_center,
            src_realm=src_realm,
            dst_data_center=dst_data_center,
            dst_realm=dst_realm,
            src_province=src_province,
            dst_province=dst_province,
            keyword=keyword,
        )
        return self.count(condition)

    def find_intercept_alarm_records(
        self,
        record_id=None,
        record_ids=None,
        src_ip=None,
        src_group_relationship_name=None,
        dst_ip=None,
        dst_port=None,
        dst_group_relationship_name=None,
        src_data_center=None,
        src_realm=None,
        dst_data_center=None,
        dst_realm=None,
        page_index=None,
        page_size=None,
        sort_fields=None,
        keyword=None,
        province=None,
        src_province=None,
        dst_province=None,
    ):
        """
        查询拦截告警记录列表
        """
        condition = self.build_intercept_alarm_condition(
            record_id=record_id,
            record_ids=record_ids,
            src_ip=src_ip,
            src_group_relationship_name=src_group_relationship_name,
            dst_ip=dst_ip,
            dst_port=dst_port,
            dst_group_relationship_name=dst_group_relationship_name,
            src_data_center=src_data_center,
            src_realm=src_realm,
            dst_data_center=dst_data_center,
            dst_realm=dst_realm,
            keyword=keyword,
            province=province,
            src_province=src_province,
            dst_province=dst_province,
        )
        if not page_size:
            return self.find(condition=condition)
        return self.find(condition=condition, limit=page_size, offset=page_index * page_size, sort_fields=sort_fields)

    def delete_intercept_alarm_records(self, record_id=None, record_ids=None):
        """
        删除拦截告警记录
        """
        condition = self.build_intercept_alarm_condition(record_id=record_id, record_ids=record_ids)
        return self.delete_multi(condition=condition)

    def update_intercept_alarm_by_id(self, record_id=None, value=None):
        """
        更新拦截告警数据
        """
        condition = self.build_intercept_alarm_condition(record_id=record_id)
        return self.update_direct(condition=condition, values=value)

    def add_intercept_alarm_record(self, values):
        """新增拦截告警记录"""
        return self.save(values).inserted_id

    @classmethod
    def build_intercept_alarm_condition(
        cls,
        record_id=None,
        record_ids=None,
        src_ip=None,
        src_group_relationship_name=None,
        dst_ip=None,
        dst_port=None,
        dst_group_relationship_name=None,
        src_data_center=None,
        src_realm=None,
        dst_data_center=None,
        dst_realm=None,
        count=None,
        keyword=None,
        province=None,
        src_province=None,
        dst_province=None,
    ):
        """
        构建查询条件
        """
        query = {}

        if record_id:
            query["_id"] = cls._build_id(record_id)

        if record_ids:
            query["_id"] = {"$in": cls._build_ids(record_ids)}

        if src_ip:
            query["src_ip"] = src_ip

        if src_group_relationship_name:
            query["src_group_relationship_name"] = src_group_relationship_name

        if dst_ip:
            query["dst_ip"] = dst_ip

        if dst_port:
            query["dst_port"] = dst_port

        if dst_group_relationship_name:
            query["dst_group_relationship_name"] = dst_group_relationship_name

        if count:
            query["count"] = count

        if src_data_center:
            query["src_data_center"] = src_data_center

        if src_realm:
            query["src_realm"] = src_realm

        if dst_data_center:
            query["dst_data_center"] = dst_data_center

        if dst_realm:
            query["dst_realm"] = dst_realm
        if src_province:
            query["src_province"] = src_province
        if dst_province:
            query["dst_province"] = dst_province

        if keyword:
            keyword_re = re.compile(keyword)
            query["$or"] = [
                {"src_ip": {"$regex": keyword_re}},
                {"src_group_relationship_name": {"$regex": keyword_re}},
                {"dst_ip": {"$regex": keyword_re}},
                {"dst_group_relationship_name": {"$regex": keyword_re}},
                {"reason": {"$regex": keyword_re}},
                {"src_data_center": {"$regex": keyword_re}},
                {"src_realm": {"$regex": keyword_re}},
                {"dst_data_center": {"$regex": keyword_re}},
                {"dst_realm": {"$regex": keyword_re}},
            ]

        return query
