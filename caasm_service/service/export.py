from datetime import datetime
from typing import List, Union

from bson import ObjectId

from caasm_persistence.handler.storage.mongo import <PERSON>goHand<PERSON>
from caasm_service.constant import Table
from caasm_service.constants.export import ExportRecordStatus
from caasm_service.entity.export import ExportRecordEntity
from caasm_service.schema.runtime import export_record_schema


class ExportRecordService(MongoHandler):
    DEFAULT_SCHEMA = export_record_schema
    DEFAULT_TABLE = Table.export_record

    @classmethod
    def _get_id(cls, id_):
        if isinstance(id_, str):
            id_ = ObjectId(id_)
        return id_

    def save_record(
        self, name, create_user, category, expire_datetime: datetime, field_list: List[str], asql="", date=""
    ):
        return self.save(
            self.DEFAULT_SCHEMA.load(
                {
                    "name": name,
                    "create_user": create_user,
                    "category": category,
                    "expire_datetime": expire_datetime.isoformat(),
                    "asql": asql,
                    "field_list": field_list,
                    "status": ExportRecordStatus.INIT,
                    "date": date,
                }
            )
        ).inserted_id

    def get_record(self, record_id):
        return self.get({"_id": self._get_id(record_id)})

    def update_workflow(self, record_id, workflow_id):
        record = self.get_record(record_id)
        if not record:
            return
        record.workflow_id = workflow_id
        self.update(record)

    def update_progress(
        self,
        record_id: Union[str, ObjectId],
        status: ExportRecordStatus = None,
        completed: int = None,
        total: int = None,
    ):
        record: ExportRecordEntity = self.get_record(record_id)
        if not record:
            return
        updated = False
        if status is not None:
            record.status = status
            updated = True
        if completed is not None:
            record.completed = completed
            updated = True
        if total is not None:
            record.total = total
            updated = True
        if updated:
            self.update(record)

    def complete_record(self, record_id, file_content, ext):
        record: ExportRecordEntity = self.get_record(record_id)
        if not record:
            return
        if record.status != ExportRecordStatus.PROCESSING:
            return
        file_id = self.save_file(file_content)
        record.file_id = file_id
        record.ext = ext
        record.status = ExportRecordStatus.COMPLETE
        self.update(record)

    def find_unexpired_records(self):
        return self.find({"status": {"$ne": ExportRecordStatus.EXPIRED.value}})
