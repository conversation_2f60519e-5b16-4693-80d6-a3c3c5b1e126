import re

from caasm_persistence.handler.storage.mongo import <PERSON><PERSON><PERSON><PERSON><PERSON>
from caasm_service.constant import Table
from caasm_service.schema.runtime import user_asset_aql_history_schema


class UserAssetAqlHistoryService(MongoHandler):
    DEFAULT_TABLE = Table.user_asset_aql_history
    DEFAULT_SCHEMA = user_asset_aql_history_schema

    def save_user_asset_aql_history(self, user_id, digest, aql=None, adapter_names=None, aql_type=None):
        entity = self.load_entity(
            user_id=user_id, digest=digest, aql=aql, adapter_names=adapter_names, aql_type=aql_type
        )
        return self.save(entity)

    def get_user_aql_history_count(self, user_id=None, keyword=None, digest=None):
        query = self.build_user_aql_history_query(user_id=user_id, keyword=keyword, digest=digest)
        return self.count(query)

    def find_user_aql_history(self, user_id=None, keyword=None, offset=None, limit=None, sort_fields=None, fields=None):
        query = self.build_user_aql_history_query(user_id=user_id, keyword=keyword)
        return self.find(condition=query, offset=offset, limit=limit, sort_fields=sort_fields, fields=fields)

    def get_user_aql_history(self, user_id=None, digest=None):
        query = self.build_user_aql_history_query(user_id=user_id, digest=digest)
        return self.get(condition=query)

    def delete_user_aql_history(self, user_id=None, history_ids=None):
        query = self.build_user_aql_history_query(user_id=user_id, history_ids=history_ids)
        return self.delete_multi(condition=query)

    @classmethod
    def build_user_aql_history_query(cls, user_id=None, history_ids=None, keyword=None, digest=None):
        query = {}
        if user_id:
            query["user_id"] = cls._build_id(user_id)

        if history_ids:
            query["_id"] = {"$in": cls._build_ids(history_ids)}

        if keyword:
            query["$or"] = [{"aql": {"$regex": re.compile(keyword)}}]
        if digest:
            query["digest"] = digest
        return query
