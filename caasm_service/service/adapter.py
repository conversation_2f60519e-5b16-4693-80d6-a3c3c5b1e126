import re

from caasm_persistence.handler.storage.mongo import <PERSON>go<PERSON>and<PERSON>
from caasm_service.constant import Table
from caasm_service.schema.runtime import adapter_schema


class AdapterService(MongoHandler):
    DEFAULT_TABLE = Table.adapter
    DEFAULT_SCHEMA = adapter_schema

    def modify_adapter(self, values, name=None):
        condition = self.build_adapter_condition(name=name)
        return self.update_direct(condition=condition, values=values)

    def delete_adapter(self, name=None, names=None):
        query = self.build_adapter_condition(name=name, names=names)
        return self.delete_multi(query)

    def get_adapter(self, name=None, fields=None, **kwargs):
        query = self.build_adapter_condition(name=name)
        return self.get(query, adapter_schema, fields=fields, **kwargs)

    def get_adapter_count(self, name=None, adapter_instance_exists=None):
        query = self.build_adapter_condition(name=name, adapter_instance_exists=adapter_instance_exists)
        return self.count(query)

    def find_adapter(
        self,
        name=None,
        display_name=None,
        names=None,
        ne_names=None,
        display_names=None,
        keyword=None,
        type=None,
        adapter_inner_type=None,
        adapter_inner_type_ne=None,
        is_biz_useful=None,
        fields=None,
        sort_fields=None,
        adapter_instance_exists=None,
        category=None,
        **kwargs,
    ):
        condition = self.build_adapter_condition(
            names=names,
            display_name=display_name,
            display_names=display_names,
            keyword=keyword,
            type=type,
            adapter_inner_type=adapter_inner_type,
            adapter_inner_type_ne=adapter_inner_type_ne,
            name=name,
            is_biz_useful=is_biz_useful,
            adapter_instance_exists=adapter_instance_exists,
            category=category,
            ne_names=ne_names,
        )
        return self.find_list(condition=condition, fields=fields, sort_fields=sort_fields, **kwargs)

    def find_adapter_to_mapper(self, names=None, fields=None):
        adapter_cursor = list(self.find_adapter(names=names, fields=fields))
        return {adapter.name: adapter for adapter in adapter_cursor}

    @classmethod
    def build_adapter_condition(
        cls,
        name=None,
        adapter_id=None,
        names=None,
        display_name=None,
        display_names=None,
        keyword=None,
        type=None,
        adapter_inner_type=None,
        adapter_inner_type_ne=None,
        is_biz_useful=None,
        category=None,
        adapter_instance_exists=None,
        ne_names=None,
    ):
        condition = {}
        _name_condition = {}

        if adapter_instance_exists:
            from caasm_service.runtime import adapter_instance_service

            tmp_names = adapter_instance_service.find_distinct("adapter_name")
            if names:
                names.extend(tmp_names)
            else:
                names = tmp_names

        if name:
            _name_condition["$eq"] = name
        if ne_names:
            _name_condition["$nin"] = ne_names

        if display_name:
            condition["display_name"] = display_name

        if adapter_id:
            condition["_id"] = cls._build_id(adapter_id)

        if names is not None:
            _name_condition["$in"] = names

        if display_names:
            condition["display_name"] = {"$in": display_names}

        if keyword:
            keyword_re = re.compile(keyword)
            condition["$or"] = [
                {
                    "display_name": {"$regex": keyword_re},
                },
                {"description": {"$regex": keyword_re}},
            ]

        if type:
            condition["type"] = type

        _a = {}
        if adapter_inner_type:
            _a["$eq"] = adapter_inner_type
        if adapter_inner_type_ne:
            _a["$ne"] = adapter_inner_type_ne
        if _a:
            condition["adapter_inner_type"] = _a

        if is_biz_useful is not None:
            condition["is_biz_useful"] = is_biz_useful

        if _name_condition:
            condition["name"] = _name_condition

        if category:
            condition[f"fetch_setting.fetch_type_mapper.{category}"] = {"$exists": True}

        return condition
