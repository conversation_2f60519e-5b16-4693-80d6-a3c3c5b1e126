from typing import List

from caasm_persistence.handler.storage.mongo import <PERSON><PERSON><PERSON><PERSON><PERSON>
from caasm_service.constant import Table
from caasm_service.entity.data_stream_360 import DataStream360RecordEntity
from caasm_service.schema.runtime import (
    data_stream_360_schema,
    data_stream_360_record_schema,
    data_stream_360_realm_schema,
)


class DataStream360RecordService(MongoHandler):
    DEFAULT_SCHEMA = data_stream_360_record_schema
    DEFAULT_TABLE = Table.data_stream_360_record

    def get_latest_successful_record(self):
        data_stream_360_entities: List[DataStream360RecordEntity] = list(
            self.find({"successful": True}, sort_fields=[("create_time", -1)], limit=1)
        )
        if data_stream_360_entities:
            return data_stream_360_entities[0]
        return None


class DataStream360Service(MongoHandler):
    DEFAULT_SCHEMA = data_stream_360_schema
    DEFAULT_TABLE = Table.data_stream_360

    def begin(self):
        self.delete_multi({"finished": False})

    def finish(self):
        self.delete_multi({"finished": True})
        self.update_multi_direct({"finished": False}, {"finished": True})

    def find_streams(self, province=None, data_center=None):
        condition = self._build_condition(province, data_center)
        return self.find(condition, sort_fields=[("count", -1)])

    @staticmethod
    def _build_condition(province=None, data_center=None):
        condition = {"$and": []}
        if province:
            condition["$and"].append({"$or": [{"src_province": province}, {"dst_province": province}]})
        if data_center:
            condition["$and"].append({"$and": [{"src_data_center": data_center}, {"dst_data_center": data_center}]})
        if condition["$and"]:
            return condition
        return None


class DataStream360RealmService(MongoHandler):
    DEFAULT_TABLE = Table.data_stream_360_realm
    DEFAULT_SCHEMA = data_stream_360_realm_schema

    def find_realms(self, data_center=None, realm=None):
        condition = {}
        if data_center:
            condition["data_center"] = data_center
        if realm:
            condition["realm"] = realm
        return self.find(condition)
