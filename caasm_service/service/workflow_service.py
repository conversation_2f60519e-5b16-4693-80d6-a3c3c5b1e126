from caasm_persistence.handler.storage.mongo import <PERSON><PERSON><PERSON><PERSON><PERSON>
from caasm_service.constant import Table
from caasm_service.constants.workflow import WorkflowStatus
from caasm_service.schema.workflow import PlaybookSchema, WorkflowSchema, TaskSchema


class PlaybookService(MongoHandler):
    DEFAULT_SCHEMA = PlaybookSchema()
    DEFAULT_TABLE = Table.workflow_playbook

    def get_playbook(self, name=None, enabled=None, fields=None):
        query = self.build_query_condition(name=name, enabled=enabled)
        return self.get(condition=query, fields=fields)

    @classmethod
    def build_query_condition(cls, name=None, enabled=None, sign=None):
        query = {}
        if name:
            query["name"] = name
        if enabled is not None:
            query["enabled"] = enabled
        if sign:
            query["sign"] = sign

        return query


class CommonService(MongoHandler):
    FINISH_STATUS_LIST = [WorkflowStatus.CANCEL, WorkflowStatus.SUCCESS, WorkflowStatus.FAILED]

    def check_status_finish(self, status):
        return status in self.FINISH_STATUS_LIST

    def build_values(
        self,
        status=None,
        runtime_id=None,
        result=None,
        error=None,
        params=None,
        display_name=None,
        next_nodes=None,
        node_type=None,
        top_workflow_id=None,
    ):
        values = {}
        if status:
            values["status"] = status
            if status in self.FINISH_STATUS_LIST:
                values["finish_time"] = self.now
                values["finished"] = True
            if status == WorkflowStatus.DOING:
                values["start_time"] = self.now
        if runtime_id:
            values["runtime_id"] = runtime_id
        if result:
            values["result"] = result
        if error is not None:
            values["error"] = error
        if params is not None:
            values["params"] = params
        if display_name:
            values["display_name"] = display_name
        if next_nodes:
            values["next_nodes"] = next_nodes
        if node_type:
            values["type"] = node_type
        if top_workflow_id:
            values["top_workflow_id"] = top_workflow_id

        return values


class WorkflowService(CommonService):
    DEFAULT_SCHEMA = WorkflowSchema()
    DEFAULT_TABLE = Table.workflow_record

    def get_count(self, status=None):
        query = self.build_query_condition(status=status)
        return self.count(query)

    def get_workflow(self, name=None, workflow_id=None, superior_task_id=None, fields=None):
        query = self.build_query_condition(workflow_id=workflow_id, superior_task_id=superior_task_id, name=name)
        return self.get(query, fields=fields)

    def get_workflow_count(self, workflow_id=None, name=None, superior_task_id=None, finished=None):
        query = self.build_query_condition(
            workflow_id=workflow_id,
            name=name,
            superior_task_id=superior_task_id,
            finished=finished,
        )
        return self.count(query)

    def find_workflow(
        self,
        status=None,
        superior_task_id=None,
        superior_task_ids=None,
        name=None,
        finished=None,
        fields=None,
        offset=None,
        limit=None,
        sort_fields=None,
    ):
        query = self.build_query_condition(
            status=status,
            superior_task_id=superior_task_id,
            name=name,
            superior_task_ids=superior_task_ids,
            finished=finished,
        )
        return self.find_list(query, fields=fields, limit=limit, sort_fields=sort_fields, offset=offset)

    def start_workflow(self, workflow_id):
        query = self.build_query_condition(workflow_id=workflow_id)
        values = self.build_values(status=WorkflowStatus.DOING)
        return self.update_direct(query, values=values)

    def finish_workflow(self, workflow_id, status=None, value_status=None, value_error=None, value_result=None):
        values = self.build_values(status=value_status, error=value_error, result=value_result)
        condition = self.build_query_condition(workflow_id=workflow_id, status=status, finished=False)
        return self.update_direct(condition, values)

    def update_single_workflow(
        self,
        workflow_id=None,
        finished=None,
        value_runtime_id=None,
        value_result=None,
        value_status=None,
        value_top_workflow_id=None,
    ):
        query = self.build_query_condition(workflow_id=workflow_id, finished=finished)
        values = self.build_values(
            runtime_id=value_runtime_id,
            result=value_result,
            status=value_status,
            top_workflow_id=value_top_workflow_id,
        )
        return self.update_direct(query, values) if values else None

    def update_more_workflow(self, status=None, workflow_ids=None, value_status=None):
        query = self.build_query_condition(workflow_ids=workflow_ids, status=status)
        values = self.build_values(status=value_status)
        return self.update_multi_direct(query, values) if values else None

    @classmethod
    def build_query_condition(
        cls,
        workflow_id=None,
        status=None,
        workflow_ids=None,
        name=None,
        superior_task_id=None,
        superior_task_ids=None,
        finished=None,
    ):
        query = {}
        _id_query = {}
        _superior_task_id_query = {}
        if status is not None:
            query["status"] = status

        if workflow_id:
            _id_query["$eq"] = cls._build_id(workflow_id)
        if workflow_ids:
            _id_query["$in"] = cls._build_ids(workflow_ids)
        if _id_query:
            query["_id"] = _id_query

        if name:
            query["name"] = name

        if superior_task_id:
            _superior_task_id_query["$eq"] = cls._build_id(superior_task_id)
        if superior_task_ids:
            _superior_task_id_query["$in"] = cls._build_ids(superior_task_ids)

        if _superior_task_id_query:
            query["superior_task_id"] = _superior_task_id_query
        if finished is not None:
            query["finished"] = finished
        return query

    def find_stuck_workflows(self):
        return self.find(
            {
                "name": "data_collect",
                "status": {
                    "$in": [
                        WorkflowStatus.INIT.value,
                        WorkflowStatus.DOING.value,
                        WorkflowStatus.SLEEP.value,
                        WorkflowStatus.WAIT_CANCEL.value,
                    ]
                },
            }
        )


class TaskService(CommonService):
    DEFAULT_SCHEMA = TaskSchema()
    DEFAULT_TABLE = Table.workflow_task

    def get_task(self, task_id=None, workflow_id=None, name=None, node_type=None, fields=None):
        condition = self.build_query_condition(task_id=task_id, node_type=node_type, workflow_id=workflow_id, name=name)
        return self.get(condition, fields=fields)

    def get_task_count(self, workflow_id=None):
        condition = self.build_query_condition(workflow_id=workflow_id)
        return self.get_count(condition)

    def update_more_task(
        self, status=None, finished=None, workflow_id=None, task_ids=None, value_status=None, value_error=None
    ):
        query = self.build_query_condition(status=status, task_ids=task_ids, finished=finished, workflow_id=workflow_id)
        values = self.build_values(status=value_status, error=value_error)
        return self.update_multi_direct(query, values)

    def delete_task(self, task_id=None):
        query = self.build_query_condition(task_id=task_id)
        return self.delete_one(query)

    def start_task(self, task_id):
        return self.update_single_task(status=WorkflowStatus.INIT, task_id=task_id, value_status=WorkflowStatus.DOING)

    def finish_task(self, task_id, status, result=None, error=""):
        return self.update_single_task(
            finished=False, task_id=task_id, value_status=status, value_result=result, value_error=error
        )

    def update_single_task(
        self,
        status=None,
        task_id=None,
        workflow_id=None,
        name=None,
        node_type=None,
        names=None,
        finished=None,
        value_status=None,
        value_result=None,
        value_error=None,
        value_runtime_id=None,
        value_params=None,
        value_display_name=None,
        value_next_nodes=None,
        value_node_type=None,
    ):
        condition = self.build_query_condition(
            status=status,
            task_id=task_id,
            finished=finished,
            workflow_id=workflow_id,
            name=name,
            node_type=node_type,
            names=names,
        )
        v = self.build_values(
            status=value_status,
            result=value_result,
            error=value_error,
            runtime_id=value_runtime_id,
            params=value_params,
            display_name=value_display_name,
            next_nodes=value_next_nodes,
            node_type=value_node_type,
        )
        return self.update_direct(condition, v) if v else None

    def heartbeat(self, task_id=None, workflow_id=None, name=None, node_type=None, value_runtime_id=None):
        condition = self.build_query_condition(task_id=task_id, workflow_id=workflow_id, name=name, node_type=node_type)
        values = {"heartbeat_time": self.now}
        if value_runtime_id:
            values["runtime_id"] = value_runtime_id
        return self.update_direct(condition, values)

    def find_task(
        self,
        task_ids=None,
        workflow_ids=None,
        workflow_id=None,
        next_task_name=None,
        names=None,
        status=None,
        finished=None,
        fields=None,
        node_type=None,
        limit=None,
        create_time_lte=None,
        heartbeat_time_lte=None,
    ):
        condition = self.build_query_condition(
            task_ids=task_ids,
            status=status,
            finished=finished,
            workflow_id=workflow_id,
            workflow_ids=workflow_ids,
            names=names,
            node_type=node_type,
            next_task_name=next_task_name,
            create_time_lte=create_time_lte,
            heartbeat_time_lte=heartbeat_time_lte,
        )
        return self.find_list(condition=condition, fields=fields, limit=limit)

    def get_count(self, task_ids=None, status=None):
        condition = self.build_query_condition(task_ids=task_ids, status=status)
        return self.count(condition)

    @classmethod
    def build_query_condition(
        cls,
        task_id=None,
        finished=None,
        task_ids=None,
        status=None,
        workflow_ids=None,
        workflow_id=None,
        names=None,
        name=None,
        next_task_name=None,
        node_type=None,
        create_time_lte=None,
        heartbeat_time_lte=None,
    ):
        query = {}

        _id_condition = {}
        if task_id:
            _id_condition["$eq"] = cls._build_id(task_id)
        if task_ids:
            _id_condition["$in"] = cls._build_ids(task_ids)

        _workflow_id_condition = {}
        if workflow_ids:
            _workflow_id_condition["$in"] = cls._build_ids(workflow_ids)
        if workflow_id:
            _workflow_id_condition["$eq"] = cls._build_id(workflow_id)

        _name_condition = {}
        if names:
            _name_condition["$in"] = names
        if name:
            _name_condition["$eq"] = name

        _next_task_name_condition = {}
        if next_task_name:
            _next_task_name_condition["$eq"] = next_task_name

        if _id_condition:
            query["_id"] = _id_condition

        if finished is not None:
            query["finished"] = finished

        if status is not None:
            query["status"] = status

        if _workflow_id_condition:
            query["workflow_id"] = _workflow_id_condition

        if _name_condition:
            query["name"] = _name_condition

        if _next_task_name_condition:
            query["next_nodes"] = _next_task_name_condition

        if node_type:
            query["type"] = node_type

        create_time_query = {}
        if create_time_lte:
            create_time_query["$lte"] = create_time_lte

        if create_time_query:
            query["create_time"] = create_time_query

        heartbeat_time_query = {}
        if heartbeat_time_lte:
            heartbeat_time_query["$lte"] = heartbeat_time_lte

        if heartbeat_time_query:
            query["heartbeat_time"] = heartbeat_time_query

        return query
