import re
from typing import List
from datetime import datetime, timedelta
from collections import defaultdict
from caasm_persistence.handler.storage.mongo import <PERSON>goHand<PERSON>
from caasm_service.constant import Table
from caasm_service.schema.runtime import vul_lifecycle_schema
from caasm_service.constants.vul_cycle import Vuln<PERSON><PERSON>inEnum, VulnStatusEnum, VULNLEVELMAPPING, VulnLevelEnum
from caasm_tool.constants import DATETIME_FORMAT
from caasm_service.entity.vul_lifecycle import VulLifecycleEntity
from caasm_tool.util import get_now


class VulLifecycleService(MongoHandler):
    DEFAULT_TABLE = Table.vuln_lifecycle
    DEFAULT_SCHEMA = vul_lifecycle_schema

    def find_vul_lifecycle(
        self,
        name=None,
        level=None,
        score=None,
        host=None,
        description=None,
        solution=None,
        cve_id=None,
        seen_time=None,
        status=None,
        cnnvd_id=None,
        cnvd_id=None,
        order_id=None,
        business_name=None,
        origin=None,
        create_time_start=None,
        create_time_end=None,
        port=None,
        protocal=None,
        keyword=None,
        level_group=None,
        status_group=None,
        page_index=None,
        page_size=None,
        condition=None,
        sort_fields=None,
        not_in_ids=None,
    ):
        """
        查询漏洞生命周期列表
        """
        if condition is None:
            condition = self.build_vul_lifecycle_condition(
                name=name,
                level=level,
                score=score,
                host=host,
                description=description,
                solution=solution,
                cve_id=cve_id,
                seen_time=seen_time,
                status=status,
                cnnvd_id=cnnvd_id,
                cnvd_id=cnvd_id,
                order_id=order_id,
                business_name=business_name,
                origin=origin,
                create_time_start=create_time_start,
                create_time_end=create_time_end,
                keyword=keyword,
                level_group=level_group,
                status_group=status_group,
                port=port,
                protocal=protocal,
                not_in_ids=not_in_ids,
            )
        return self.find(condition=condition, offset=page_index, limit=page_size, sort_fields=sort_fields)

    def get_vul_lifecycle_count(
        self,
        name=None,
        level=None,
        score=None,
        host=None,
        description=None,
        solution=None,
        cve_id=None,
        seen_time=None,
        status=None,
        cnnvd_id=None,
        cnvd_id=None,
        order_id=None,
        business_name=None,
        create_time_start=None,
        create_time_end=None,
        keyword=None,
        level_group=None,
        status_group=None,
        *args,
        **kwargs,
    ):
        """
        获取漏洞生命周期数量
        """
        condition = self.build_vul_lifecycle_condition(
            name=name,
            level=level,
            score=score,
            host=host,
            description=description,
            solution=solution,
            cve_id=cve_id,
            seen_time=seen_time,
            status=status,
            cnnvd_id=cnnvd_id,
            cnvd_id=cnvd_id,
            order_id=order_id,
            business_name=business_name,
            create_time_start=create_time_start,
            create_time_end=create_time_end,
            keyword=keyword,
            level_group=level_group,
            status_group=status_group,
        )
        return self.count(condition=condition)

    def get_monthly_vul_stats(self, months=6, create_time_start=None, create_time_end=None):
        """
        获取近N个月的漏洞数量统计，包括当前月在内
        按照每个月的1号到月末作为查询条件，返回N个查询结果

        Args:
            months: 要统计的月份数量，默认为6个月
            create_time_start: 创建时间起始（datetime对象）
            create_time_end: 创建时间结束（datetime对象）

        Returns:
            list: [{"month": "YYYY-MM", "count": n}, ...] 格式的月度统计数据
        """
        # 获取当前日期
        now = datetime.now()

        # 初始化结果列表
        stats = []

        # 计算当前月的第一天
        current_month_first_day = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)

        # 遍历N个月
        for i in range(months):
            # 计算当前迭代月份的第一天
            if i == 0:
                # 当前月
                month_first_day = current_month_first_day
            else:
                # 计算前i个月的第一天
                year = current_month_first_day.year
                month = current_month_first_day.month - i

                # 处理跨年的情况
                while month <= 0:
                    year -= 1
                    month += 12

                month_first_day = datetime(year, month, 1, 0, 0, 0)

            # 计算月末（下个月的第一天减去1天）
            if month_first_day.month == 12:
                next_month_first_day = datetime(month_first_day.year + 1, 1, 1, 0, 0, 0)
            else:
                next_month_first_day = datetime(month_first_day.year, month_first_day.month + 1, 1, 0, 0, 0)

            month_last_day = next_month_first_day - timedelta(days=1)

            # 格式化日期为字符串
            month_first_day_str = month_first_day.strftime("%Y-%m-%d")
            month_last_day_str = month_last_day.strftime("%Y-%m-%d")

            # 构建该月的查询条件
            month_condition = {"update_time": {"$gte": month_first_day_str, "$lte": month_last_day_str}}

            # 添加创建时间范围过滤
            if create_time_start or create_time_end:
                create_time_condition = {}
                if create_time_start:
                    create_time_condition["$gte"] = create_time_start.strftime(DATETIME_FORMAT)
                if create_time_end:
                    create_time_condition["$lte"] = create_time_end.strftime(DATETIME_FORMAT)
                if create_time_condition:
                    month_condition["create_time"] = create_time_condition

            # 查询该月的漏洞数量
            month_count = self.count(condition=month_condition)

            # 添加到结果列表
            month_key = month_first_day.strftime("%Y-%m")
            stats.append({"month": month_key, "count": month_count})

        # 按月份排序（从旧到新）
        stats.reverse()

        return stats

    def get_vul_type_stats(self, create_time_start=None, create_time_end=None):
        """
        获取各个漏洞类型的聚合数量统计，并按照统计值从大到小排序

        Args:
            create_time_start: 创建时间起始（datetime对象）
            create_time_end: 创建时间结束（datetime对象）

        Returns:
            list: [{"type": "漏洞类型名称", "count": n}, ...] 格式的漏洞类型统计数据
        """
        # 查询所有漏洞数据，带创建时间过滤
        all_vuls = self.find_vul_lifecycle(create_time_start=create_time_start, create_time_end=create_time_end)

        # 按漏洞名称统计
        type_counts = {}
        for vul in all_vuls:
            vul_type = vul.name
            if vul_type in type_counts:
                type_counts[vul_type] += 1
            else:
                type_counts[vul_type] = 1

        # 格式化结果并按数量从大到小排序
        stats = []
        for vul_type, count in type_counts.items():
            stats.append({"type": vul_type, "count": count})

        # 按count字段降序排序
        stats.sort(key=lambda x: x["count"], reverse=True)

        return stats

    def get_vul_level_stats(self, create_time_start=None, create_time_end=None):
        """
        获取各个漏洞威胁等级的聚合数量统计，并按照统计值从大到小排序

        Args:
            create_time_start: 创建时间起始（datetime对象）
            create_time_end: 创建时间结束（datetime对象）

        Returns:
            list: [{"level": "漏洞威胁等级", "count": n}, ...] 格式的漏洞威胁等级统计数据
        """
        # 查询所有漏洞数据，带创建时间过滤
        all_vuls: List[VulLifecycleEntity] = self.find_vul_lifecycle(
            create_time_start=create_time_start, create_time_end=create_time_end
        )

        # 按漏洞威胁等级统计
        level_counts = {}
        for vul in all_vuls:
            level: VulnLevelEnum = vul.level
            if level in level_counts:
                level_counts[level] += 1
            else:
                level_counts[level] = 1

        # 格式化结果并按数量从大到小排序
        stats = []
        for level, count in level_counts.items():
            stats.append({"name": level.value, "count": count, "level": VULNLEVELMAPPING[level]})

        # 按count字段降序排序
        stats.sort(key=lambda x: x["count"], reverse=True)

        return stats

    def get_vul_status_summary(self, create_time_start=None, create_time_end=None):
        """
        获取漏洞状态汇总统计，包括总数、未修复总数、已修复总数和复现漏洞总数

        Args:
            create_time_start: 创建时间起始（datetime对象）
            create_time_end: 创建时间结束（datetime对象）

        Returns:
            dict: {
                "total": 漏洞总数,
                "unfixed": 未修复总数,
                "fixed": 已修复总数,
                "reproduced": 复现漏洞总数
            }
        """
        # 初始化基础查询条件（创建时间过滤）
        base_condition = self.build_vul_lifecycle_condition(
            create_time_start=create_time_start, create_time_end=create_time_end
        )

        # 查询漏洞总数
        total_count = self.count(condition=base_condition)

        # 查询未修复漏洞总数
        unfixed_condition = base_condition.copy()
        unfixed_condition["status"] = VulnStatusEnum.UNFIXED.value
        unfixed_count = self.count(condition=unfixed_condition)

        # 查询已修复漏洞总数
        fixed_condition = base_condition.copy()
        fixed_condition["status"] = VulnStatusEnum.FIXED.value
        fixed_count = self.count(condition=fixed_condition)

        # 查询复现漏洞总数
        reproduced_condition = base_condition.copy()
        reproduced_condition["status"] = VulnStatusEnum.REPRODUCED.value
        reproduced_count = self.count(condition=reproduced_condition)

        # 返回统计结果
        return {"total": total_count, "unfixed": unfixed_count, "fixed": fixed_count, "reproduced": reproduced_count}

    def get_vul_network_level_stat(self, create_time_start=None, create_time_end=None):
        """
        统计已修复和未修复漏洞，按照内外网和威胁等级（高危/中危/低危）进行分类

        Args:
            create_time_start: 创建时间起始（datetime对象）
            create_time_end: 创建时间结束（datetime对象）

        Returns:
            dict: {
                "external_high_fixed": 外网高危已修复漏洞数量,
                "external_high_unfixed": 外网高危未修复漏洞数量,
                "external_medium_fixed": 外网中危已修复漏洞数量,
                "external_medium_unfixed": 外网中危未修复漏洞数量,
                "external_low_fixed": 外网低危已修复漏洞数量,
                "external_low_unfixed": 外网低危未修复漏洞数量,
                "internal_high_fixed": 内网高危已修复漏洞数量,
                "internal_high_unfixed": 内网高危未修复漏洞数量,
                "internal_medium_fixed": 内网中危已修复漏洞数量,
                "internal_medium_unfixed": 内网中危未修复漏洞数量
            }
        """
        # 初始化基础查询条件（创建时间过滤）
        base_condition = self.build_vul_lifecycle_condition(
            create_time_start=create_time_start, create_time_end=create_time_end
        )

        # 初始化结果字典
        result = {
            "external_high_fixed": 0,
            "external_high_unfixed": 0,
            "external_medium_fixed": 0,
            "external_medium_unfixed": 0,
            "external_low_fixed": 0,
            "external_low_unfixed": 0,
            "internal_high_fixed": 0,
            "internal_high_unfixed": 0,
            "internal_medium_fixed": 0,
            "internal_medium_unfixed": 0,
        }

        fixed_condition = base_condition.copy()
        fixed_condition["status"] = VulnStatusEnum.FIXED.value

        unfixed_condition = base_condition.copy()
        # 未修复状态包括待修复和复现
        unfixed_condition["status"] = {"$in": [VulnStatusEnum.UNFIXED.value, VulnStatusEnum.REPRODUCED.value]}

        # 查询外网高危已修复漏洞数量
        external_high_fixed_condition = fixed_condition.copy()
        external_high_fixed_condition["internal"] = False
        external_high_fixed_condition["level"] = VulnLevelEnum.HIGH.value
        result["external_high_fixed"] = self.count(condition=external_high_fixed_condition)

        # 查询外网高危未修复漏洞数量
        external_high_unfixed_condition = unfixed_condition.copy()
        external_high_unfixed_condition["internal"] = False
        external_high_unfixed_condition["level"] = VulnLevelEnum.HIGH.value
        result["external_high_unfixed"] = self.count(condition=external_high_unfixed_condition)

        # 查询外网中危已修复漏洞数量
        external_medium_fixed_condition = fixed_condition.copy()
        external_medium_fixed_condition["internal"] = False
        external_medium_fixed_condition["level"] = VulnLevelEnum.MEDIUM.value
        result["external_medium_fixed"] = self.count(condition=external_medium_fixed_condition)

        # 查询外网中危未修复漏洞数量
        external_medium_unfixed_condition = unfixed_condition.copy()
        external_medium_unfixed_condition["internal"] = False
        external_medium_unfixed_condition["level"] = VulnLevelEnum.MEDIUM.value
        result["external_medium_unfixed"] = self.count(condition=external_medium_unfixed_condition)

        # 查询外网低危已修复漏洞数量
        external_low_fixed_condition = fixed_condition.copy()
        external_low_fixed_condition["internal"] = False
        external_low_fixed_condition["level"] = VulnLevelEnum.LOW.value
        result["external_low_fixed"] = self.count(condition=external_low_fixed_condition)

        # 查询外网低危未修复漏洞数量
        external_low_unfixed_condition = unfixed_condition.copy()
        external_low_unfixed_condition["internal"] = False
        external_low_unfixed_condition["level"] = VulnLevelEnum.LOW.value
        result["external_low_unfixed"] = self.count(condition=external_low_unfixed_condition)

        # 查询内网高危已修复漏洞数量
        internal_high_fixed_condition = fixed_condition.copy()
        internal_high_fixed_condition["internal"] = True
        internal_high_fixed_condition["level"] = VulnLevelEnum.HIGH.value
        result["internal_high_fixed"] = self.count(condition=internal_high_fixed_condition)

        # 查询内网高危未修复漏洞数量
        internal_high_unfixed_condition = unfixed_condition.copy()
        internal_high_unfixed_condition["internal"] = True
        internal_high_unfixed_condition["level"] = VulnLevelEnum.HIGH.value
        result["internal_high_unfixed"] = self.count(condition=internal_high_unfixed_condition)

        # 查询内网中危已修复漏洞数量
        internal_medium_fixed_condition = fixed_condition.copy()
        internal_medium_fixed_condition["internal"] = True
        internal_medium_fixed_condition["level"] = VulnLevelEnum.MEDIUM.value
        result["internal_medium_fixed"] = self.count(condition=internal_medium_fixed_condition)

        # 查询内网中危未修复漏洞数量
        internal_medium_unfixed_condition = unfixed_condition.copy()
        internal_medium_unfixed_condition["internal"] = True
        internal_medium_unfixed_condition["level"] = VulnLevelEnum.MEDIUM.value
        result["internal_medium_unfixed"] = self.count(condition=internal_medium_unfixed_condition)

        return result

    def get_vul_business_summary(self, keyword=None, create_time_start=None, create_time_end=None, *args, **kwargs):
        """
        按照业务系统聚合，统计每个业务系统下的漏洞总数、未修复总数、已修复总数和复现总数，以及高危、中危、低危漏洞数量和工单ID数组

        Args:
            create_time_start: 创建时间起始（datetime对象）
            create_time_end: 创建时间结束（datetime对象）

        Returns:
            list: [
                {
                    "business_name": 业务系统名称,
                    "total": 漏洞总数,
                    "unfixed": 未修复总数,
                    "fixed": 已修复总数,
                    "reproduced": 复现总数,
                    "high_risk": 高危漏洞总数,
                    "medium_risk": 中危漏洞总数,
                    "low_risk": 低危漏洞总数,
                    "order_ids": [工单ID数组]
                },
                ...
            ]
        """
        # 初始化基础查询条件（创建时间过滤）
        match_condition = self.build_vul_lifecycle_condition(
            create_time_start=create_time_start,
            create_time_end=create_time_end,
            keyword=keyword,
        )

        # 使用MongoDB的聚合操作
        pipeline = [
            # 筛选条件
            {"$match": match_condition},
            # 处理空的business_name
            {
                "$addFields": {
                    "business_name": {
                        "$cond": [
                            {"$or": [{"$eq": ["$business_name", ""]}, {"$eq": ["$business_name", None]}]},
                            "未分类",
                            "$business_name",
                        ]
                    },
                }
            },
            # 展开order_ids数组
            {"$unwind": {"path": "$order_ids", "preserveNullAndEmptyArrays": True}},
            # 按业务系统分组，收集工单ID和状态信息
            {
                "$group": {
                    "_id": "$business_name",
                    "total": {"$sum": 1},
                    "order_ids": {"$addToSet": "$order_ids"},
                    "unfixed": {"$sum": {"$cond": [{"$eq": ["$status", VulnStatusEnum.UNFIXED.value]}, 1, 0]}},
                    "fixed": {"$sum": {"$cond": [{"$eq": ["$status", VulnStatusEnum.FIXED.value]}, 1, 0]}},
                    "reproduced": {"$sum": {"$cond": [{"$eq": ["$status", VulnStatusEnum.REPRODUCED.value]}, 1, 0]}},
                    "high_risk": {"$sum": {"$cond": [{"$eq": ["$level", VulnLevelEnum.HIGH.value]}, 1, 0]}},
                    "medium_risk": {"$sum": {"$cond": [{"$eq": ["$level", VulnLevelEnum.MEDIUM.value]}, 1, 0]}},
                    "low_risk": {"$sum": {"$cond": [{"$eq": ["$level", VulnLevelEnum.LOW.value]}, 1, 0]}},
                }
            },
            # 去重工单ID
            {"$addFields": {"order_ids": {"$setUnion": ["$order_ids", []]}}},
            # 格式化输出
            {
                "$project": {
                    "_id": 0,
                    "business_name": "$_id",
                    "total": 1,
                    "unfixed": 1,
                    "fixed": 1,
                    "reproduced": 1,
                    "high_risk": 1,
                    "medium_risk": 1,
                    "low_risk": 1,
                    "order_ids": 1,
                }
            },
            # 按总数降序排序
            {"$sort": {"total": -1}},
        ]

        # 执行聚合查询
        result = list(self.aggregate(pipeline))

        return result

    def update_vuln_multi(
        self,
        condition=None,
        status=None,
        file_ids=None,
        **kwargs,
    ):
        values: dict = {"update_time": get_now()}
        if status is not None:
            values["status"] = status
        if file_ids is not None:
            values["file_ids"] = file_ids
        return self.update_multi_direct(condition=condition, values=values)

    @classmethod
    def build_vul_lifecycle_condition(
        cls,
        ids=None,
        name=None,
        level=None,
        score=None,
        host=None,
        description=None,
        solution=None,
        cve_id=None,
        seen_time=None,
        status=None,
        cnnvd_id=None,
        cnvd_id=None,
        order_id=None,
        business_name=None,
        origin=None,
        create_time_start=None,
        create_time_end=None,
        keyword=None,
        level_group=None,
        status_group=None,
        port=None,
        protocal=None,
        not_in_ids=None,
    ):
        """构建查询条件"""
        query = {}

        # 处理关键字模糊查询
        if keyword:
            keyword_re = re.compile(keyword, re.IGNORECASE)
            query["$or"] = [
                {"name": {"$regex": keyword_re}},
                {"host": {"$regex": keyword_re}},
                {"order_ids": {"$regex": keyword_re}},
                {"business_name": {"$regex": keyword_re}},
                {"cve_id": {"$regex": keyword_re}},
                {"cnnvd_id": {"$regex": keyword_re}},
                {"cnvd_id": {"$regex": keyword_re}},
            ]
        if ids is not None:
            query["_id"] = {"$in": cls._build_ids(ids)}
        if not_in_ids is not None:
            query["_id"] = {"$nin": cls._build_ids(not_in_ids)}
        if name is not None:
            query["name"] = name
        if level is not None:
            query["level"] = level
        if score is not None:
            query["score"] = score
        if host is not None:
            query["host"] = host
        if description is not None:
            query["description"] = description
        if solution is not None:
            query["solution"] = solution
        if cve_id is not None:
            query["cve_id"] = cve_id
        if seen_time is not None:
            query["seen_time"] = seen_time
        if status is not None:
            query["status"] = status
        if cnnvd_id is not None:
            query["cnnvd_id"] = cnnvd_id
        if cnvd_id is not None:
            query["cnvd_id"] = cnvd_id
        if order_id is not None:
            query["order_ids"] = order_id
        if business_name is not None:
            query["business_name"] = business_name
        if port is not None:
            query["port"] = port
        if protocal is not None:
            query["protocal"] = protocal
        if origin is not None:
            query["origin"] = origin

        # 处理level_group参数（威胁等级组）
        if level_group and isinstance(level_group, list) and len(level_group) > 0:
            query["level"] = {"$in": level_group}

        # 处理status_group参数（状态组）
        if status_group and isinstance(status_group, list) and len(status_group) > 0:
            query["status"] = {"$in": status_group}

        # 添加创建时间范围过滤
        if create_time_start or create_time_end:
            create_time_condition = {}
            if create_time_start:
                # 如果是datetime对象，直接使用
                create_time_condition["$gte"] = create_time_start.strftime(DATETIME_FORMAT)
            if create_time_end:
                # 如果是datetime对象，直接使用
                create_time_condition["$lte"] = create_time_end.strftime(DATETIME_FORMAT)
            if create_time_condition:
                query["create_time"] = create_time_condition

        return query
