from caasm_persistence.handler.storage.model.response import UpdateResponse
from caasm_persistence.handler.storage.mongo import <PERSON><PERSON><PERSON><PERSON><PERSON>
from caasm_service.constant import Table
from caasm_service.schema.runtime import fabric_model_config_schema


class FabricModelConfigService(MongoHandler):
    DEFAULT_SCHEMA = fabric_model_config_schema
    DEFAULT_TABLE = Table.fabric_meta_model_config

    def save_fabric_model_config_service(
        self, asset_type_id, fabric_policy=None, adapter_confidence=None, field_global_policy=None
    ):
        mapper = {
            "asset_type_id": asset_type_id,
            "fabric_policy": fabric_policy,
            "adapter_confidence": adapter_confidence,
            "field_global_policy": field_global_policy,
        }
        return self.save_direct(mapper)

    def update_fabric_model_config_service(self, asset_type_id, fabric_policy=None):
        condition = self.build_fabric_meta_model_config_condition(asset_type_id=asset_type_id)
        values = {}
        values.update({"fabric_policy": fabric_policy}) if fabric_policy else ...
        if not values:
            return UpdateResponse(flag=True)
        return self.update_direct(condition, values=values)

    def find_fabric_meta_model_config(self, asset_type_ids=None, fields=None, sort_fields=None):
        query = self.build_fabric_meta_model_config_condition(asset_type_ids=asset_type_ids)
        return self.find(condition=query, fields=fields, sort_fields=sort_fields)

    def get_fabric_meta_model_config(
        self, fabric_config_id=None, asset_type_id=None, asset_type_ids=None, fields=None, need_dict=False
    ):
        query = self.build_fabric_meta_model_config_condition(
            fabric_config_id=fabric_config_id, asset_type_ids=asset_type_ids, asset_type_id=asset_type_id
        )
        _get_method = self.get_direct if need_dict else self.get
        return _get_method(condition=query, fields=fields)

    def get_fabric_meta_model_config_count(self, fabric_config_id=None, asset_type_id=None, asset_type_ids=None):
        query = self.build_fabric_meta_model_config_condition(
            fabric_config_id=fabric_config_id, asset_type_ids=asset_type_ids, asset_type_id=asset_type_id
        )
        return self.count(condition=query)

    def upsert_model_fabric_config(self, values, asset_type_id=None):
        condition = self.build_fabric_meta_model_config_condition(asset_type_id=asset_type_id)
        values = {"$set": values}
        return self.find_and_modify(condition, values, simple_values=False, upsert=True)

    def delete_model_fabric(self, fabric_config_id=None, asset_type_id=None):
        condition = self.build_fabric_meta_model_config_condition(
            fabric_config_id=fabric_config_id, asset_type_id=asset_type_id
        )
        return self.delete_one(condition=condition)

    @classmethod
    def build_fabric_meta_model_config_condition(
        cls, fabric_config_id=None, fabric_config_ids=None, asset_type_id=None, asset_type_ids=None
    ):
        query = {}

        _id_condition = cls.build_id_ids_condition(data_id=fabric_config_id, data_ids=fabric_config_ids)
        _asset_type_condition = cls.build_id_ids_condition(data_id=asset_type_id, data_ids=asset_type_ids)

        if _id_condition:
            query["_id"] = _id_condition

        if _asset_type_condition:
            query["asset_type_id"] = _asset_type_condition

        return query
