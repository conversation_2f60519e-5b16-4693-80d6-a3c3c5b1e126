import hashlib
import re

from caasm_persistence.handler.storage.mongo import <PERSON><PERSON><PERSON><PERSON><PERSON>
from caasm_service.constant import Table
from caasm_service.entity.user import User
from caasm_service.schema.runtime import user_schema


class UserService(MongoHandler):
    DEFAULT_TABLE = Table.user
    DEFAULT_SCHEMA = user_schema

    def save_user(self, user: User):
        return self.save(user)

    def update_user(self, user: User):
        return self.update(user)

    def update_user_info_by_user_id_or_name(self, user_id=None, username=None, data=None):
        query = self.build_user_query(user_id=user_id, username=username)
        return self.update_multi_direct(condition=query, values=data)

    def find_user(
        self,
        keyword=None,
        is_super=None,
        user_ids=None,
        bind_role_flag=None,
        role_codes=None,
        offset=None,
        limit=None,
        sort_fields=None,
        fields=None,
    ):
        query = self.build_user_query(
            keyword=keyword, is_super=is_super, user_ids=user_ids, bind_role_flag=bind_role_flag, role_codes=role_codes
        )
        return self.find(
            schema=user_schema,
            condition=query,
            sort_fields=sort_fields,
            offset=offset,
            limit=limit,
            fields=fields,
        )

    def delete_user(self, user_id=None, is_super=None, user_ids=None):
        query = self.build_user_query(user_id=user_id, is_super=is_super, user_ids=user_ids)
        return self.delete_multi(query)

    def get_user_count(
        self,
        username=None,
        mobile=None,
        email=None,
        keyword=None,
        bind_role_flag=None,
        is_super=None,
        role_codes=None,
        user_id=None,
    ):
        query = self.build_user_query(
            username=username,
            mobile=mobile,
            keyword=keyword,
            bind_role_flag=bind_role_flag,
            is_super=is_super,
            email=email,
            role_codes=role_codes,
            user_id=user_id,
        )
        return self.count(query)

    def get_user(self, username=None, user_id=None, is_super=None, fields=None, sso_keys=None):
        query = self.build_user_query(user_id=user_id, username=username, is_super=is_super, sso_keys=sso_keys)
        return self.get(condition=query, fields=fields)

    @classmethod
    def get_enc_password(cls, password: str):
        result = hashlib.sha256(password.encode()).hexdigest()
        return result

    @classmethod
    def build_user_query(
        cls,
        user_id=None,
        mobile="",
        email="",
        keyword="",
        is_super=None,
        username="",
        user_ids=None,
        bind_role_flag=None,
        role_codes=None,
        sso_keys=None,
    ):
        query = {}
        if user_ids:
            query["_id"] = {"$in": cls._build_ids(user_ids)}

        if user_id:
            query["_id"] = cls._build_id(user_id)

        if mobile:
            query["mobile"] = mobile

        if email:
            query["email"] = email

        if is_super is not None:
            query["is_super"] = is_super

        if username:
            query["username"] = username

        if keyword:
            keyword_re = re.compile(keyword)
            from caasm_service.runtime import role_service

            role_codes = [i.code for i in role_service.find_role(keyword=keyword, fields=["code"])]

            query["$or"] = [
                {"username": {"$regex": keyword_re}},
                {"mobile": {"$regex": keyword_re}},
                {"email": {"$regex": keyword_re}},
                {"role_codes": {"$in": role_codes}},
            ]

        if bind_role_flag:
            query["role_codes"] = {"$exists": True, "$ne": []}
        if role_codes:
            query["role_codes"] = {"$in": role_codes}

        if sso_keys:
            query["sso_keys"] = {"$in": sso_keys}

        return query
