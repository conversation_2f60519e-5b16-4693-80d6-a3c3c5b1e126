from caasm_service.service.adapter import AdapterService
from caasm_service.service.adapter_instance_fetch_poc_log_service import AdapterInstanceFetchPocLogService
from caasm_service.service.adapter_instance_service import AdapterInstanceService
from caasm_service.service.alarm_record import AlarmRecordService
from caasm_service.service.alarm_rule_instance import AlarmRuleInstanceService
from caasm_service.service.alarm_rule_run_record import AlarmRuleRunRecordService
from caasm_service.service.alarm_rule_template import AlarmRuleTemplateService
from caasm_service.service.api_call_record_service import ApiCallRecordService
from caasm_service.service.asset_type_service import AssetTypeService
from caasm_service.service.business_portraiter_service import BusinessPortraiterService, BusinessPortraitUpdateService
from caasm_service.service.category_service import CategoryService
from caasm_service.service.change import ChangeService
from caasm_service.service.convert_func import ConvertFuncService
from caasm_service.service.convert_func_type import ConvertFuncTypeService
from caasm_service.service.convert_service import ConvertService
from caasm_service.service.convert_visualization_relation_service import ConvertVisualizationRelationService
from caasm_service.service.convert_visualization_service import ConvertVisualizationService
from caasm_service.service.dashboard_user_setting_service import DashBoardUserSettingService
from caasm_service.service.data_stream_360 import (
    DataStream360RecordService,
    DataStream360Service,
    DataStream360RealmService,
)
from caasm_service.service.entity_change import EntityChangeService, EntityChangeRecordService
from caasm_service.service.entity_service import EntityService
from caasm_service.service.export import ExportRecordService
from caasm_service.service.fabric_meta_model_config_service import FabricModelConfigService
from caasm_service.service.fetch_service import FetchService
from caasm_service.service.field_fabric_policy import FieldFabricPolicyService
from caasm_service.service.file_manage_service import FileManageService
from caasm_service.service.intercept_alarm_service import InterceptAlarmService
from caasm_service.service.internet_relation_service import InternetRelationService
from caasm_service.service.ip_segment_enrichment import IPSegmentEnrichmentService
from caasm_service.service.job_service import JobService
from caasm_service.service.join_export_fields import JoinExportFieldsService
from caasm_service.service.lineage import (
    EntityTypeLineageStageService,
    EntityLineageFetchStageService,
    EntityLineageMergeStageService,
    EntityLineageConvertStageService,
    EntityLineageFabricStageService,
    ValueLineageFabricStageService,
)
from caasm_service.service.link_graph import EntityLinkGraphService, LinkGraphService
from caasm_service.service.maintenance import FormService, MaintenanceService
from caasm_service.service.manufacturer_service import ManufacturerService
from caasm_service.service.menu_service import MenuService
from caasm_service.service.merge_service import MergeService
from caasm_service.service.meta_asset_type_view_service import MetaAssetTypeViewService
from caasm_service.service.meta_entity_fields import EntityFieldsService
from caasm_service.service.meta_entity_type_view import MetaEntityTypeViewService
from caasm_service.service.meta_model_service import MetaFieldService, MetaModelService
from caasm_service.service.meta_view_service import MetaViewService
from caasm_service.service.network_mapping_cluster import NetworkMappingClusterService
from caasm_service.service.operation_log import OperationLogService, OperationLogConfigService
from caasm_service.service.overview_chart_category_service import OverviewChartCategoryService
from caasm_service.service.overview_chart_service import OverviewChartService
from caasm_service.service.overview_spaces_service import OverviewService
from caasm_service.service.quick_search_service import QuickSearchService
from caasm_service.service.retrieve_scenes_service import RetrieveScenesService
from caasm_service.service.retrieve_statement_service import RetrieveStatementService
from caasm_service.service.role_service import RolService
from caasm_service.service.sequence_service import SequenceService
from caasm_service.service.setting_service import SettingService
from caasm_service.service.snapshot_service import (
    SnapshotRecordService,
    MetaModelSnapshotRecordService,
    MetaFieldSnapshotRecordService,
)
from caasm_service.service.sso import TempLoginKeyService, SSOConfigService
from caasm_service.service.stream_data_service import StreamDataService
from caasm_service.service.system_status import SystemStatusService
from caasm_service.service.unique import UniqueService, UniqueIndexService
from caasm_service.service.user_asset_aql_collect_service import UserAssetAqlCollectService
from caasm_service.service.user_asset_aql_history_service import UserAssetAqlHistoryService
from caasm_service.service.user_default_entity_fields_service import UserDefaultEntityFieldsService
from caasm_service.service.user_service import UserService
from caasm_service.service.validation import EntityValidationService
from caasm_service.service.variable_service import VariableService
from caasm_service.service.vul_file import VulFileService
from caasm_service.service.vul_priority import VulPriorityService
from caasm_service.service.vul_timeline import VulTimelineService
from caasm_service.service.vulnerability_service import VulnerabilityService
from caasm_service.service.whitelist_service import WhitelistService
from caasm_service.service.workflow_service import (
    PlaybookService as WorkflowPlaybookService,
    TaskService as WorkflowTaskService,
    WorkflowService,
)
from caasm_service.service.vul_lifecycle_service import VulLifecycleService
from caasm_service.service.order_service import OrderService

entity_service = EntityService()

retrieve_scenes_service = RetrieveScenesService()
retrieve_statement_service = RetrieveStatementService()

manufacturer_service = ManufacturerService()
adapter_service = AdapterService()
adapter_instance_service = AdapterInstanceService()
adapter_instance_fetch_poc_log_service = AdapterInstanceFetchPocLogService()
fetch_service = FetchService()
merge_service = MergeService()
stream_data_service = StreamDataService()
operation_log_service = OperationLogService()
operation_log_config_service = OperationLogConfigService()
convert_service = ConvertService()
convert_func_service = ConvertFuncService()
convert_func_type_service = ConvertFuncTypeService()

sequence_service = SequenceService()
setting_service = SettingService()

user_service = UserService()
user_asset_aql_history_service = UserAssetAqlHistoryService()
user_asset_aql_collect_service = UserAssetAqlCollectService()
user_default_entity_fields_service = UserDefaultEntityFieldsService()
menu_service = MenuService()
role_service = RolService()

variable_service = VariableService()

dashboard_user_setting_service = DashBoardUserSettingService()
overview_service = OverviewService()
overview_chart_category_service = OverviewChartCategoryService()
overview_chart_service = OverviewChartService()

meta_field_service = MetaFieldService()
meta_model_service = MetaModelService()
meta_view_service = MetaViewService()
meta_asset_type_view_service = MetaAssetTypeViewService()
meta_entity_fields_service = EntityFieldsService()
meta_entity_type_service = MetaEntityTypeViewService()

convert_visualization_service = ConvertVisualizationService()
convert_visualization_relation_service = ConvertVisualizationRelationService()
asset_type_service = AssetTypeService()
fabric_model_config_service = FabricModelConfigService()
field_fabric_policy_service = FieldFabricPolicyService()

business_portraiter_service = BusinessPortraiterService()
business_portrait_update_service = BusinessPortraitUpdateService()

vulnerability_service = VulnerabilityService()

job_service = JobService()

file_manage_service = FileManageService()

internet_relation_service = InternetRelationService()

sso_config_service = SSOConfigService()
temp_login_key_service = TempLoginKeyService()

whitelist_service = WhitelistService()
api_call_record_service = ApiCallRecordService()

snapshot_record_service = SnapshotRecordService()
meta_model_snapshot_record_service = MetaModelSnapshotRecordService()
meta_field_snapshot_record_service = MetaFieldSnapshotRecordService()

change_service = ChangeService()
entity_change_service = EntityChangeService()
entity_change_record_service = EntityChangeRecordService()

unique_service = UniqueService()
unique_index_service = UniqueIndexService()

vul_priority_service = VulPriorityService()
vul_file_service = VulFileService()
join_export_fields_service = JoinExportFieldsService()

quick_search_service = QuickSearchService()

entity_link_graph_service = EntityLinkGraphService()
link_graph_service = LinkGraphService()

network_mapping_cluster_service = NetworkMappingClusterService()

alarm_rule_template_service = AlarmRuleTemplateService()
alarm_rule_instance_service = AlarmRuleInstanceService()
alarm_rule_run_record_service = AlarmRuleRunRecordService()
alarm_record_service = AlarmRecordService()

system_status_service = SystemStatusService()

entity_type_lineage_stage_service = EntityTypeLineageStageService()
entity_lineage_fetch_stage_service = EntityLineageFetchStageService()
entity_lineage_merge_stage_service = EntityLineageMergeStageService()
entity_lineage_convert_stage_service = EntityLineageConvertStageService()
entity_lineage_fabric_stage_service = EntityLineageFabricStageService()

value_lineage_fabric_stage_service = ValueLineageFabricStageService()

workflow_playbook_service = WorkflowPlaybookService()
workflow_task_service = WorkflowTaskService()
workflow_record_service = WorkflowService()

category_service = CategoryService()

form_service = FormService()
maintenance_service = MaintenanceService()

entity_validation_service = EntityValidationService()

ip_segment_enrichment_service = IPSegmentEnrichmentService()

export_record_service = ExportRecordService()

vul_time_line_service = VulTimelineService()

data_stream_360_record_service = DataStream360RecordService()
data_stream_360_service = DataStream360Service()
data_stream_360_realm_service = DataStream360RealmService()
intercept_alarm_service = InterceptAlarmService()

vul_lifecycle_service = VulLifecycleService()
order_service = OrderService()
