from dataclasses import dataclass
from datetime import datetime
from enum import Enum

from bson import ObjectId

from caasm_persistence.entity.base import DocumentEntity
from caasm_service.entity.change import ChangeType


@dataclass
class EntityChangeDetail(DocumentEntity):
    category: str
    entity_id: str
    field_name: str
    change_type: ChangeType
    child_change_type: ChangeType
    new_value: object
    new_display: str
    original_value: object
    original_display: str
    changed_datetime: datetime
    record_id: str
    finished: bool
    app_name: str
    user_id: ObjectId
    change_id: ObjectId


class SourceType(Enum):
    #   变化检测引擎
    DETECTION_ENGINE = 1
    #   用户修改
    USER = 2
    #   API修改
    API = 3
    #   应用检测
    APP = 4


SOURCE_TYPE_MAPPING = {
    SourceType.DETECTION_ENGINE: "变化检测引擎",
    SourceType.USER: "用户",
    SourceType.API: "API接口",
    SourceType.APP: "平台应用",
}


@dataclass
class EntityChangeRecord(DocumentEntity):
    category: str
    finished: bool
