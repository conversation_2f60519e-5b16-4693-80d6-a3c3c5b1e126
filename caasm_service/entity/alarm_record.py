from dataclasses import dataclass

from bson import ObjectId

from caasm_persistence.entity.base import DocumentEntity
from caasm_service.constants.alarm import AlarmSeverityLevelEnum, AlarmDisposalStatus, ALARMStatus


@dataclass
class AlarmRecordEntity(DocumentEntity):

    alarm_name: str
    rule_instance_id: ObjectId
    alarm_level: AlarmSeverityLevelEnum
    alarm_category: str
    check_count: str
    hit_count: str
    alarm_timeline: list
    disposal_status: AlarmDisposalStatus
    asql: str
    unique_key: str
    alarm_description: str
    snapshot_date: str
    alarm_status: ALARMStatus
