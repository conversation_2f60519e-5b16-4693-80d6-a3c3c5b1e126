from dataclasses import dataclass
from typing import List, Dict

from caasm_persistence.entity.base import BaseEntity, DocumentEntity


@dataclass
class MetaAssetMenuTreeEntity(BaseEntity):
    name: str
    display_name: str
    children: List["MetaAssetMenuTreeEntity"]


@dataclass
class MetaCardConfig(BaseEntity):
    card_type: str
    card_name: str
    display_name: str
    config: Dict


@dataclass
class MetaAssetTypeViewEntity(DocumentEntity):
    category: str
    asset_type: str
    description: str
    menu_tree: List[MetaAssetMenuTreeEntity]
    multi_title_expr: List[str]
    support_cards: List[MetaCardConfig]
