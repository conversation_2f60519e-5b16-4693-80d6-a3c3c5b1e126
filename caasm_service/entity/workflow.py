import datetime
from typing import List, Dict

from bson import ObjectId

from caasm_persistence.entity.base import DocumentEntity, dataclass, BaseEntity
from caasm_service.constants.workflow import NodeType, WorkflowStatus, CheckType


@dataclass
class Param(BaseEntity):
    name: str
    type: str
    value: any
    required: bool


@dataclass
class Node(BaseEntity):
    name: str
    entry: str
    callback_entry: str
    type: NodeType
    check_type: CheckType
    param_template: Dict[str, Param]
    output_template: Dict[str, Param]
    display_name: str
    next_nodes: List[str]


@dataclass
class Playbook(DocumentEntity):
    name: str
    enabled: bool
    description: str
    display_name: str
    callback_entry: str
    param_template: Dict[str, Param]
    output_template: Dict[str, Param]
    node_mapper: Dict[str, Node]
    sign: str


@dataclass
class Workflow(DocumentEntity):
    # 模版信息
    playbook_id: ObjectId
    param_template: Dict[str, Param]
    output_template: Dict[str, Param]
    callback_entry: str

    # 运行态信息
    display_name: str
    name: str
    status: WorkflowStatus
    runtime_id: str
    finished: bool
    start_time: datetime.datetime
    finish_time: datetime.datetime
    result: any
    params: Dict
    superior_task_id: ObjectId
    top_workflow_id: ObjectId


@dataclass
class Task(DocumentEntity):
    entry: str
    type: NodeType
    check_type: CheckType
    param_template: Dict[str, Param]
    output_template: Dict[str, Param]
    next_nodes: List[str]
    callback_entry: str

    # 运行状态
    display_name: str
    name: str
    workflow_id: ObjectId
    top_workflow_id: ObjectId
    runtime_id: str
    params: any
    result: any
    error: str
    status: WorkflowStatus
    finished: bool
    heartbeat_time: datetime.datetime
    start_time: datetime.datetime
    finish_time: datetime.datetime
