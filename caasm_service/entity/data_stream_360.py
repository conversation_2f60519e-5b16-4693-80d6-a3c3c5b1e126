from dataclasses import dataclass
from typing import List

from caasm_persistence.entity.base import DocumentEntity, BaseEntity


@dataclass
class DataStream360RecordEntity(DocumentEntity):
    successful: bool
    result: str
    error: str


@dataclass
class DataStream360Entity(DocumentEntity):
    src_data_center: str
    src_realm: str
    src_province: str
    dst_data_center: str
    dst_realm: str
    dst_province: str
    count: int
    finished: bool


@dataclass
class DataStream360IpSegmentEntity(BaseEntity):
    start: str
    end: str
    cidr: str


@dataclass
class DataStream360RealmEntity(DocumentEntity):
    data_center: str
    realm: str
    longitude: float
    latitude: float
    segments: List[DataStream360IpSegmentEntity]
    province: str
