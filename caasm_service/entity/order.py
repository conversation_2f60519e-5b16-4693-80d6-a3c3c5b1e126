from dataclasses import dataclass, field
from typing import List, Optional, Dict
from datetime import datetime

from caasm_persistence.entity.base import DocumentEntity
from caasm_service.constants.order import OrderStatusEnum, OrderPriorityEnum


@dataclass
class OrderEntity(DocumentEntity):
    """工单实体类"""

    order_id: str  # 工单ID
    title: str  # 工单标题
    description: str  # 工单描述
    status: OrderStatusEnum  # 工单状态
    priority: OrderPriorityEnum  # 优先级
    creator: str  # 创建人
    assignee: str  # 负责人
    creator_id: str
    business_system: str  # 所属业务系统
    vul_ids: List[str]  # 关联的漏洞ID列表
    finish_time: Optional[datetime] = None  # 完成时间
    operation_logs: List[Dict] = field(default_factory=list)  # 操作日志列表
    creator_mobile: str = ""  # 创建人手机号
    creator_phone: str = ""  # 创建人电话号
    creator_email: str = ""  # 创建人邮箱
    assignee_mobile: str = ""  # 负责人手机号
    assignee_phone: str = ""  # 负责人电话号
    assignee_email: str = ""  # 负责人邮箱
