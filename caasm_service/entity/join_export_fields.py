import datetime
from dataclasses import dataclass
from typing import List

from bson import ObjectId

from caasm_persistence.entity.base import DocumentEntity, BaseEntity


class ExportFields(BaseEntity):
    field_names: List[str]


class ExportCategoryFields(ExportFields):
    enabled: bool


@dataclass
class ExportFieldsConfig(DocumentEntity):
    vul_unique_field_names: ExportCategoryFields
    asset_field_names: ExportCategoryFields
    vul_field_names: ExportCategoryFields
    main_category: str
    user_id: ObjectId
