from dataclasses import dataclass, field
from typing import Dict

from bson import ObjectId

from caasm_persistence.entity.base import DocumentEntity, BaseEntity
from caasm_service.constants.alarm import (
    AlarmSeverityLevelEnum,
    AlarmRuleTypeEnum,
    AlarmRuleResultTypeEnum,
    AlarmRuleStatementModelEnum,
    AlarmRuleInstanceRunStatus,
)
from caasm_service.constants.trigger import TriggerType


@dataclass
class AlarmRuleStatementModel(BaseEntity):

    model: AlarmRuleStatementModelEnum
    statement: str
    template_name: str
    template_id: ObjectId


@dataclass
class Trigger(BaseEntity):

    type: TriggerType
    value: Dict = field(default_factory=dict)


@dataclass
class AlarmRuleInstanceEntity(DocumentEntity):

    rule_name: str  # 规则名称
    alarm_name: str
    enabled: bool  # 是否启用
    rule_category: str  # 规则分类
    rule_level: AlarmSeverityLevelEnum  # 规则等级
    rule_type: AlarmRuleTypeEnum  # 规则类型
    result_type: AlarmRuleResultTypeEnum  # 结果分类
    rule_scope_statement_model: AlarmRuleStatementModel  # 规则范围
    rule_condition_statement_model: AlarmRuleStatementModel  # 规则条件
    run_status: AlarmRuleInstanceRunStatus  # 规则运行状态

    trigger_type: str
    trigger: Trigger

    rule_description: str
