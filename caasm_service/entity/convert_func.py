import datetime
from dataclasses import dataclass
from typing import List

from bson import ObjectId

from caasm_persistence.entity.base import BaseEntity


@dataclass
class BaseData(BaseEntity):
    name: str
    en_display_name: str
    display_name: str
    description: str
    type: str
    headers: list
    src: list
    show: bool
    must: bool
    default: any
    direct: bool


@dataclass
class Input(BaseEntity):
    count: int
    complex_type: dict
    data: List["BaseData"]


@dataclass
class Output(BaseEntity):
    count: int
    data: List["BaseData"]


@dataclass
class ConvertFunc(BaseEntity):
    name: str
    en_display_name: str
    display_name: str
    classify: list
    description: str
    input: "Input"
    output: "Output"
    id: ObjectId
    create_time: datetime.datetime
    update_time: datetime.datetime
