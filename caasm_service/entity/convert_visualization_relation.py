from dataclasses import dataclass
from typing import Dict, List

from bson import ObjectId

from caasm_persistence.entity.base import DocumentEntity, BaseEntity


@dataclass
class ConvertRule(BaseEntity):
    id: str
    name: str
    setting: Dict
    sub_rules: List["ConvertRule"]


@dataclass
class ConvertVisualizationRelation(DocumentEntity):
    adapter_name: str
    fetch_type: str
    init: bool
    model_id: ObjectId
    internal: bool
    asset_type_id: ObjectId
    canvas: Dict
    rules: List[ConvertRule]
    modify_flag: bool
