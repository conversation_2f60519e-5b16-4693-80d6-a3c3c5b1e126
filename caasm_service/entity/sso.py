from dataclasses import dataclass
from typing import Dict

from caasm_persistence.entity.base import DocumentEntity
from caasm_service.constants.auth import AuthenticationType, AuthenticationStatus


@dataclass
class SSOConfig(DocumentEntity):

    authentication_type: AuthenticationType
    status: AuthenticationStatus
    config: Dict


class TempLoginKey(DocumentEntity):

    login_key: str
    sso_key: str
    status: AuthenticationStatus
    authentication_type: AuthenticationType
