from dataclasses import dataclass
from enum import IntEnum
from typing import List, Dict, Any

from bson import ObjectId

from caasm_persistence.entity.base import BaseEntity, DocumentEntity


class IsInternetAccessible(IntEnum):
    UNKNOWN = 0
    YES = 1
    NO = 2


class Status(IntEnum):
    UNKNOWN = 0
    USING = 1
    BUILDING = 2
    OFFLINE = 3
    DELETED = 4


class Magnitude(IntEnum):
    CRITICAL = 0
    IMPORTANT = 1
    NORMAL = 2
    UNKNOWN = 3


class GradeProtectionLevel(IntEnum):
    UNKNOWN = 0
    LEVEL1 = 1
    LEVEL2 = 2
    LEVEL3 = 3
    LEVEL4 = 4
    LEVEL5 = 5
    UNDEFINED = 999


class CriticalInfrastructure(IntEnum):
    NO = 0
    YES = 1
    UNKNOWN = 2


@dataclass
class BusinessPortraitNetwork(BaseEntity):
    ips: List[str]
    ports: List[int]


@dataclass
class BusinessPortrait(DocumentEntity):
    business_id: ObjectId
    name: str
    owner_names: List[str]
    operator_names: List[str]
    magnitude: Magnitude
    grade_protection_level: GradeProtectionLevel
    critical_infrastructure: bool
    adapters: List[str]
    is_internet_accessible: IsInternetAccessible
    status: Status
    host_count: int
    itai_host_count: int
    db_count: int
    middleware_count: int
    asset_group: Dict
    asset_count: int

    admin_accounts: List[str]
    data: List[str]
    software: List[str]
    network: BusinessPortraitNetwork
    system: List[str]
    physical: List[str]

    external_domain_count: int
    external_ip_count: int
    external_url_count: int
    external_port_count: int

    internal_ip_count: int
    vulnerability_count: int
    mid_vulnerability_count: int
    low_vulnerability_count: int
    high_vulnerability_count: int
    software_count: int
    website_count: int
    internal_port_count: int
    account_count: int
    jar_count: int
    web_frame_count: int

    without_cmdb_count: int
    without_hids_count: int
    without_osm_count: int
    without_va_count: int
    without_av_count: int

    coverage_charts: List[Any]

    data_centers: List[str]
    realms: List[str]


@dataclass
class BusinessPortraitUpdateItemEntity(BaseEntity):
    field: str
    value: object


@dataclass
class BusinessPortraitUpdateEntity(DocumentEntity):
    entity_id: str
    updates: List[BusinessPortraitUpdateItemEntity]
