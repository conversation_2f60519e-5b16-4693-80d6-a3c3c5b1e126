from dataclasses import dataclass
from typing import List, Dict

from caasm_persistence.entity.base import DocumentEntity, BaseEntity


@dataclass
class MaintenanceValidatorEntity(BaseEntity):
    type: str
    settings: Dict


@dataclass
class FieldValidatorEntity(MaintenanceValidatorEntity):
    field_name: str
    item_id: str


@dataclass
class EntityValidatorEntity(MaintenanceValidatorEntity):
    field_names: str
    error: str


@dataclass
class EntityValidationEntity(DocumentEntity):
    category: str
    entity_type: str
    field_validators: List[FieldValidatorEntity]
    entity_validators: List[EntityValidatorEntity]
