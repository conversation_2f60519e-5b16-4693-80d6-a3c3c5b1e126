from dataclasses import dataclass
from typing import List, Dict

from caasm_persistence.entity.base import DocumentEntity, BaseEntity
from caasm_tool.constants import StrEnum


class ActionCode(StrEnum):
    DELETE = "delete"
    UPDATE = "update"
    READ = "read"
    CREATE = "create"


@dataclass
class Action(BaseEntity):
    name: str
    icon: str
    code: str
    permission_codes: List[str]


@dataclass
class Menu(DocumentEntity):
    name: str
    code: str
    icon: str
    path: str
    redirect_path: str
    level: int
    parent_code: str
    children_codes: List[str]
    actions: List[Action]
    priority: int
    is_show: bool
    setting: Dict
