import datetime
from dataclasses import dataclass
from typing import List

from caasm_persistence.entity.base import DocumentEntity, ObjectId
from caasm_service.constants.snapshot import SnapshotRecordStatus
from caasm_service.entity.meta_model import Meta<PERSON>ield


@dataclass
class SnapshotRecord(DocumentEntity):
    size: int
    date: str
    status: SnapshotRecordStatus
    start_time: datetime.datetime
    finish_time: datetime.datetime
    latest: bool
    finished: bool
    deleted: bool


@dataclass
class MetaFieldSnapshotRecord(DocumentEntity):
    model_id: ObjectId
    date: str
    meta_fields: List[MetaField]


@dataclass
class MetaModelSnapshotRecord(DocumentEntity):
    category: str
    date: str
    meta_model_ids: List[ObjectId]
