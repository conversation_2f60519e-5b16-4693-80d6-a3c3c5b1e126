import datetime
from dataclasses import dataclass, field
from typing import List, Dict

from bson import ObjectId

from caasm_persistence.entity.base import BaseEntity
from caasm_service.constants.adapter import AdapterInnerType, AdapterFetchMode, AdapterFetchType
from caasm_service.constants.trigger import TriggerType


@dataclass
class Adapter(BaseEntity):
    name: str
    display_name: str
    model_id: ObjectId
    version: str
    type: str
    is_biz_useful: bool
    priority: int
    properties: List[str]
    connection: List["Connection"]
    fetch_setting: "FetchSetting"
    merge_setting: "MergeSetting"
    convert_setting: "ConvertSetting"
    fabric_setting: "FabricSetting"
    description: str
    adapter_inner_type: AdapterInnerType
    logo_id: ObjectId
    create_time: datetime.datetime
    update_time: datetime.datetime
    manufacturer_id: ObjectId
    id: ObjectId


@dataclass
class ValidateRule(BaseEntity):
    name: str
    error_hint: str
    setting: Dict


@dataclass
class Connection(BaseEntity):
    name: str
    display_name: str
    description: str
    type: str
    required: bool
    default: any
    validate_rules: List[ValidateRule]
    hidden: bool


@dataclass
class FetchOther(BaseEntity):
    point: str
    size: int
    fetch_field: str
    field: str
    property_name: str


@dataclass
class FetchSetting(BaseEntity):
    type: AdapterFetchType
    mode: AdapterFetchMode
    fetch_type_mapper: Dict[str, List[str]]
    condition_point: str
    point: str
    count_point: str
    size: int
    other_fetch_mapper: Dict[str, Dict[str, FetchOther]]
    cleaner_mapper: Dict[str, Dict[str, List[str]]]
    finish_point: str
    is_need_test_service: bool
    test_connection_point: str
    test_auth_point: str
    worker: int


@dataclass
class MergeSetting(BaseEntity):
    policy: str
    setting: Dict
    size: int


@dataclass
class ConvertSetting(BaseEntity):
    size: int
    before_executor_mapper: Dict
    executor_mapper: Dict
    after_executor_mapper: Dict


@dataclass
class FabricSetting(BaseEntity):
    choose_point_mapper: Dict[str, str]


@dataclass
class Trigger(BaseEntity):
    type: TriggerType
    value: Dict = field(default_factory=dict)
