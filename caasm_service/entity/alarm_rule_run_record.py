from dataclasses import dataclass

from bson import ObjectId

from caasm_persistence.entity.base import DocumentEntity
from caasm_service.constants.alarm import AlarmRuleInstanceRunStatus, AlarmSeverityLevelEnum
import datetime


@dataclass
class AlarmRuleRunRecordEntity(DocumentEntity):

    rule_name: str
    rule_instance_id: ObjectId
    status: AlarmRuleInstanceRunStatus
    finished: bool
    rule_level: AlarmSeverityLevelEnum
    check_count: int
    hit_count: int
    alarm_count: int
    err_info: str
    start_time: datetime.datetime
    finish_time: datetime.datetime
