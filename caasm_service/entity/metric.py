from dataclasses import dataclass
from datetime import datetime, date
from typing import List, Dict

from bson import ObjectId
from caasm_persistence.entity.base import BaseEntity, DocumentEntity

from caasm_service.constants.overview import OverviewCategoryEnum, DashboardSpaceDisplayEnum


@dataclass
class ChartEntity(BaseEntity):
    chart: str
    chart_instance_name: str
    horizontal_index: int
    vertical_index: int
    width: int
    height: int


@dataclass
class OverviewEntity(DocumentEntity):
    name: str
    icon: str
    index: int
    charts: List[ChartEntity]
    is_default: bool
    user_id: str
    role_codes: List[ObjectId]
    ownership: DashboardSpaceDisplayEnum
    category: OverviewCategoryEnum
    file_id: str
    frontend_validate_name: str
