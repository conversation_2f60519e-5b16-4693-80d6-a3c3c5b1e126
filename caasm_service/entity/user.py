import datetime
from dataclasses import dataclass
from typing import List

from caasm_persistence.entity.base import DocumentEntity


@dataclass
class User(DocumentEntity):
    username: str
    enabled: bool
    mobile: str
    email: str
    password: str
    is_super: bool
    role_codes: List[str]
    description: str
    sso_keys: List[str]
    try_login_count: int
    lock_expired: datetime.datetime
