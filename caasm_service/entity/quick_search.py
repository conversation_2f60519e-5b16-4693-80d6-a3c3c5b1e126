from dataclasses import dataclass
from typing import List

from caasm_persistence.entity.base import DocumentEntity, BaseEntity
from caasm_service.constants.scene import AsqlType, ParamsType


@dataclass
class QuickSearchParamEntity(BaseEntity):
    name: str
    description: str
    display_name: str
    type: str
    required: bool


@dataclass
class QuickSearchAqlEntity(DocumentEntity):
    asql: str
    category: str
    display_name: str
    params_type: ParamsType
    inner: bool
    asql_type: AsqlType
    is_support: bool
    params: List[QuickSearchParamEntity]
