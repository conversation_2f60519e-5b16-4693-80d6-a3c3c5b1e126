from dataclasses import dataclass
from datetime import datetime

from bson import ObjectId

from caasm_persistence.entity.base import DocumentEntity
from caasm_service.constants.export import ExportRecordStatus


@dataclass
class ExportRecordEntity(DocumentEntity):
    name: str
    expire_datetime: datetime
    create_user: str
    status: ExportRecordStatus
    category: str
    asql: str
    field_list: str
    date: str
    total: int
    completed: int
    workflow_id: ObjectId
    file_id: ObjectId
    ext: str
