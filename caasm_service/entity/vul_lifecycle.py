from dataclasses import dataclass
from datetime import datetime


from caasm_persistence.entity.base import DocumentEntity
from caasm_service.constants.vul_cycle import VulnLevelEnum, VulnStatusEnum, VulnOriginEnum


@dataclass
class VulLifecycleEntity(DocumentEntity):
    name: str  # 漏洞名称
    level: VulnLevelEnum  # 问题等级
    score: float  # 威胁分值
    host: str  # 受影响主机
    description: str  # 详细描述
    solution: str  # 修复建议
    cve_id: str  # CVE编号
    seen_time: str  # 发现日期
    status: VulnStatusEnum  # 状态
    cnnvd_id: str  # cnnvd编号
    cnvd_id: str  # cnvd编号
    order_ids: list  # 工单编号
    business_name: str  # 业务系统名称
    port: int  # 端口
    protocal: str  # 协议
    file_ids: list  # 附件列表
    internal: bool  # 是否为内部漏洞
    origin: VulnOriginEnum
