from dataclasses import dataclass

from bson import ObjectId

from caasm_persistence.entity.base import DocumentEntity


@dataclass
class OperationLog(DocumentEntity):
    """
    审计日志
    """

    user_name: str
    action: str
    url: str
    ip: str
    time: str
    timestamp: str
    method: str
    code: int
    response_code: int
    response_code_text: str
    role: list


@dataclass
class OperationLogConfigEntity(DocumentEntity):
    days: int
