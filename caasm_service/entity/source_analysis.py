from dataclasses import dataclass
from typing import List

from bson import ObjectId

from caasm_persistence.entity.base import DocumentEntity


@dataclass
class SourceAnalysisEntity(DocumentEntity):
    total_field_count: int
    contributed_count: int
    contribution_rate: float
    depended_on_count: int
    dependency_rate: float
    valid_count: int
    validity_rate: float


@dataclass
class AdapterInstanceSourceAnalysisEntity(SourceAnalysisEntity):
    adapter_instance_name: str
    adapter_instance_id: ObjectId


@dataclass
class AdapterSourceAnalysisEntity(SourceAnalysisEntity):
    adapter_name: str
    adapter_id: str


@dataclass
class FieldAdapterInstanceSourceEntity(DocumentEntity):
    adapter_instance_name: str
    adapter_instance_id: ObjectId
    value: object


@dataclass
class FieldSourceEntity(DocumentEntity):
    entity_id: str
    field_name: str
    sources: List[FieldAdapterInstanceSourceEntity]


@dataclass
class EntityAdapterInstanceSourceEntity(SourceAnalysisEntity):
    entity_id: str
    adapter_instance_name: str
    adapter_instance_id: ObjectId
