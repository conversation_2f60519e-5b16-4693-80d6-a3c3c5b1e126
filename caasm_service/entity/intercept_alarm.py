from dataclasses import dataclass

from caasm_persistence.entity.base import DocumentEntity


@dataclass
class InterceptAlarmEntity(DocumentEntity):
    """拦截告警实体类"""

    # 保留的字段
    src_ip: str
    src_group_relationship_name: str
    dst_ip: str
    dst_port: int
    dst_group_relationship_name: str
    count: int
    reason: str

    # 数据中心和安全域字段
    src_data_center: str = ""
    src_realm: str = ""
    dst_data_center: str = ""
    dst_realm: str = ""
    src_province: str = ""
    dst_province: str = ""
