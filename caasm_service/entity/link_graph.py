from dataclasses import dataclass
from typing import List

from bson import ObjectId

from caasm_persistence.entity.base import DocumentEntity, BaseEntity


@dataclass
class EntityLinkGraph(DocumentEntity):
    category: str
    asset_type: str
    entity_id: str
    display_values: List[str]
    graph_id: ObjectId
    finished: bool


@dataclass
class MissingAsset(BaseEntity):
    ip: str
    ports: List[int]


@dataclass
class Business(BaseEntity):
    business_id: str
    name: str


class SuspectedBusiness(Business):
    category: str
    entity_id: str


@dataclass
class Owner(BaseEntity):
    owner_id: str
    nickname: str
    category: str
    entity_id: str
    entity_display_name: str


@dataclass
class HostEntry(BaseEntity):
    entity_id: str
    ips: List[str]
    ports: List[int]


@dataclass
class LinkGraph(DocumentEntity):
    graph: bytes
    finished: bool
    missing_assets: List[MissingAsset]
    business: Business
    suspected_businesses: List[str]
    properties: List[str]
    owners: List[Owner]
    host_entries: List[HostEntry]
    adapters: List[str]
    domains: List[str]
    internet_ips: List[str]
