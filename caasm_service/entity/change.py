from dataclasses import dataclass
from datetime import date, datetime
from enum import Enum

from bson import ObjectId

from caasm_persistence.entity.base import DocumentEntity


class ChangeStatus(Enum):
    APPEARED = 1
    EXIST = 2
    DISAPPEARED = 3


CHANGE_STATUS_MAPPING = {ChangeStatus.APPEARED: "新增", ChangeStatus.EXIST: "存在", ChangeStatus.DISAPPEARED: "消亡"}


class ChangeType(Enum):
    APPEARED = 1
    DISAPPEARED = 2
    CHANGED = 3


CHANGE_TYPE_MAPPING = {
    ChangeType.APPEARED: "新增",
    ChangeType.DISAPPEARED: "消亡",
    ChangeType.CHANGED: "属性变化",
}


@dataclass
class ChangeDetail(DocumentEntity):
    category: str
    asset_type: str
    entity_id: str
    field_name: str
    change_type: ChangeType
    child_change_type: ChangeType
    new_date: date
    new_value: object
    new_display: str
    original_date: date
    original_value: object
    original_display: str
    changed_datetime: datetime
    user_id: ObjectId


@dataclass
class ChangeRecord(DocumentEntity):
    category: str
    original_date: date
    new_date: date
    finished: bool
