from dataclasses import dataclass
from typing import List

from bson import ObjectId

from caasm_persistence.entity.base import DocumentEntity
from caasm_tool.constants import AqlParamsType


@dataclass
class RetrieveScenes(DocumentEntity):
    """
    场景
    """

    name: str
    main_scene: str
    sub_scene: str
    category: str
    enabled: bool
    priority: int
    retrieve_statement_id: ObjectId
    params: List
    params_type: AqlParamsType
    aql_type: str
