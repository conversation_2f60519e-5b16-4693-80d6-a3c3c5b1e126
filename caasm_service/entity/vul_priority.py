from dataclasses import dataclass
from enum import Enum

from bson import ObjectId

from caasm_persistence.entity.base import DocumentEntity


class VulPrioritySeverity(Enum):
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    NONE = "none"


VUL_PRIORITY_SEVERITY_MAPPING = {
    VulPrioritySeverity.CRITICAL: "严重",
    VulPrioritySeverity.HIGH: "高",
    VulPrioritySeverity.MEDIUM: "中",
    VulPrioritySeverity.LOW: "低",
    VulPrioritySeverity.NONE: "无",
}


@dataclass
class VulPrioritySetting(DocumentEntity):
    name: str
    priority: ObjectId
    asset_condition: str
    vul_condition: str
    vul_unique_condition: str


@dataclass
class VulPriority(DocumentEntity):
    name: str
    priority: int
