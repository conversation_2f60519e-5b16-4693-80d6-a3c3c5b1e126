import datetime
from dataclasses import dataclass
from typing import List

from caasm_persistence.entity.base import DocumentEntity
from caasm_service.constants.adapter import AdapterMergeStatus


@dataclass
class MergeRecord(DocumentEntity):
    adapter_name: str
    index: int
    status: AdapterMergeStatus
    fetch_type: str
    start_time: datetime.datetime
    latest: bool
    data_deleted: bool
    finish_time: datetime.datetime
    fetch_tables: List[str]
