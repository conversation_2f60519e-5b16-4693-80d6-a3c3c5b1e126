from dataclasses import dataclass
from typing import List, Dict

from bson import ObjectId

from caasm_persistence.entity.base import BaseEntity, DocumentEntity
from caasm_service.constants.maintenance import (
    ImportOption,
    FormComponentType,
    FormItemUIType,
)
from caasm_service.entity.validation import FieldValidatorEntity


@dataclass
class FormComponentEntity(BaseEntity):
    title: str
    index: int
    description: str
    type: FormComponentType


@dataclass
class FormItemEntity(FormComponentEntity):
    id: str
    field_name: str
    required: bool
    settings: Dict
    ui_type: FormItemUIType
    validators: List[FieldValidatorEntity]
    status: Dict[str, bool]
    placeholder: str


@dataclass
class FormGroupEntity(FormComponentEntity):
    items: List[FormItemEntity]


@dataclass
class FormEntity(DocumentEntity):
    name: str
    description: str
    category: str
    entity_type: str
    operation_types: List[str]
    title: str
    titles: Dict[str, str]
    components: List[FormComponentEntity]
    cleaner: str


@dataclass
class BatchFieldEntity(BaseEntity):
    field_name: str
    index: int
    required: bool
    settings: Dict
    validators: List[FieldValidatorEntity]


@dataclass
class BatchOperationEntity(BaseEntity):
    operation_types: List[str]
    fields: List[BatchFieldEntity]
    fields_by_index: Dict[int, BatchFieldEntity]
    import_option: ImportOption
    export_template: str
    cleaner: str


@dataclass
class MaintenanceEntity(DocumentEntity):
    category: str
    entity_type: str
    forms: Dict[str, ObjectId]
    batch_operations: List[BatchOperationEntity]
    batch_operations_by_operation: Dict[str, BatchOperationEntity]
