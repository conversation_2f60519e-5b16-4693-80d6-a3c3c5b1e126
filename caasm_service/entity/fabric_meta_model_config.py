import datetime
from dataclasses import dataclass
from typing import List, Dict

from bson import ObjectId

from caasm_persistence.entity.base import DocumentEntity, BaseEntity


@dataclass
class UniqueIdentificationPolicy(BaseEntity):
    policy_label: str
    policy_value: str
    policy: list


@dataclass
class FabricPolicy(BaseEntity):
    name: str
    src_field: str
    dst_field: str
    setting: Dict


@dataclass
class AdapterConfidence(BaseEntity):
    adapter_name: str
    confidence: int


@dataclass
class FieldGlobalPolicy(BaseEntity):
    value: str
    label: str
    policy_description: str


@dataclass
class FieldPolicy(BaseEntity):
    field_id: ObjectId
    field_adapter_confidence: List["AdapterConfidence"]
    field_policy: List["FieldGlobalPolicy"]


@dataclass
class FabricModelConfig(DocumentEntity):
    asset_type_id: ObjectId
    is_modify: bool
    modify_time: datetime.datetime
    modify_username: str
    adapter_confidence: List[AdapterConfidence]
    field_global_policy: List[FieldGlobalPolicy]
    fabric_policy: FabricPolicy
