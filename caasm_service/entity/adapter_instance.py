import datetime
from dataclasses import dataclass
from typing import Dict

from caasm_persistence.entity.base import DocumentEntity
from caasm_service.constants.adapter import AdapterInstanceRunStatus, AdapterInstanceConnectionStatus
from caasm_service.entity.adapter import Trigger


@dataclass
class AdapterInstance(DocumentEntity):
    name: str
    adapter_name: str
    ancestor_adapter_name: str
    properties: list
    enabled: bool
    debug: bool
    connection: Dict
    trigger_type: str
    proxy: str
    trigger: Trigger
    description: str
    run_status: AdapterInstanceRunStatus
    connect_status: AdapterInstanceConnectionStatus
    last_sync_time: datetime
    is_complex: bool
