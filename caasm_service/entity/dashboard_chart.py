from dataclasses import dataclass
from typing import List

from bson import ObjectId
from caasm_persistence.entity.base import DocumentEntity, BaseEntity


@dataclass
class FieldGroupEntity(BaseEntity):
    field: str
    limit: int


@dataclass
class FieldCountEntity(BaseEntity):
    field: str
    count_method: str
    file: str
    aql_filter: str
    order: str
    percentage: bool


@dataclass
class TableEntity(BaseEntity):
    field_list: list


@dataclass
class QueryComparisonEntity(BaseEntity):
    querys: list


@dataclass
class ChartEntity(DocumentEntity):
    category_tree_id: str
    chart_name: str
    description: str
    creator_id: ObjectId

    internal: bool

    chart_type: str
    metric_type: str

    category: str

    f_category: str
    s_category: str

    base_query: str

    count_info: FieldCountEntity

    group_fields: List[FieldGroupEntity]

    front_end_setting: dict

    time: dict

    table: TableEntity

    asql: QueryComparisonEntity

    time_comparison: dict

    result: list
