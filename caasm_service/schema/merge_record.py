import datetime

from caasm_persistence.schema.base import fields, EnumField, DateTimeField, DocumentSchema
from caasm_service.constants.adapter import AdapterMergeStatus
from caasm_service.entity.merge_record import MergeRecord


class MergeRecordSchema(DocumentSchema):
    entity_define = MergeRecord

    adapter_name = fields.String(description="适配器名称")
    index = fields.Int(description="索引")
    fetch_type = fields.String(load_default=None, description="适配器名称")
    status = EnumField(
        AdapterMergeStatus,
        load_default=AdapterMergeStatus.WAIT,
        by_value=True,
        description="采集状态",
    )
    start_time = DateTimeField(load_default=datetime.datetime.now, allow_none=True, description="开始时间")
    latest = fields.Bool(load_default=None, description="最近标志位")
    finish_time = DateTimeField(load_default=None, allow_none=True, description="结束时间")
    data_deleted = fields.Bool(load_default=None)
    fetch_tables = fields.List(fields.String(), load_default=list)
