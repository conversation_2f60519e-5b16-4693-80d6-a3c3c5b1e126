from caasm_persistence.schema.base import (
    fields,
    DocumentSchema,
    ObjectIdField,
)
from caasm_service.entity.vul_priority import VulPrioritySetting, VulPriority


class VulPrioritySettingSchema(DocumentSchema):
    entity_define = VulPrioritySetting

    name = fields.String(required=True)
    priority = ObjectIdField(required=False, load_default=None)
    asset_condition = fields.String(allow_none=True, load_default=None)
    vul_condition = fields.String(allow_none=True, load_default=None)
    vul_unique_condition = fields.String(allow_none=True, load_default=None)


class VulPrioritySchema(DocumentSchema):
    entity_define = VulPriority

    name = fields.String()
    priority = fields.Integer()
