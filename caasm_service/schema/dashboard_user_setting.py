from caasm_persistence.schema.base import DocumentSchema
from caasm_persistence.schema.base import (
    BaseSchema,
    fields,
    EnumField,
    DocumentSchema,
    ObjectIdField,
)
from caasm_service.constants.overview import OverviewCategoryEnum
from caasm_service.entity.dashboard_user_setting import DashboardUserSetting


class DashboardUserSettingSchema(DocumentSchema):
    entity_define = DashboardUserSetting

    user_id = ObjectIdField(required=True)
    category = EnumField(
        OverviewCategoryEnum,
        default=OverviewCategoryEnum.DASHBOARD,
        by_value=True,
        required=False,
        load_default=OverviewCategoryEnum.DASHBOARD.value,
    )
    spaces_sequence = fields.List(ObjectIdField(), load_default=[])
