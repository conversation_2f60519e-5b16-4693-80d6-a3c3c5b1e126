from caasm_persistence.schema.base import (
    fields,
    DocumentSchema,
)
from caasm_service.entity.unique import Unique, UniqueIndex


class UniqueSchema(DocumentSchema):
    entity_define = Unique

    category = fields.String()
    date = fields.Date(load_default=None)


class UniqueIndexSchema(DocumentSchema):
    entity_define = UniqueIndex

    category = fields.String()
    index = fields.String()
    finished = fields.Boolean()
