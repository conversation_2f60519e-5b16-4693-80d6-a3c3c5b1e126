from caasm_persistence.schema.base import DocumentSchema, fields, ObjectIdField, EnumField
from caasm_service.entity.retrieve_scenes import RetrieveScenes
from caasm_tool.constants import AqlParamsType


class RetrieveScenesSchema(DocumentSchema):
    """
    检索场景
    """

    entity_define = RetrieveScenes

    name = fields.Str(description="名称")
    main_scene = fields.Str(description="主场景")
    sub_scene = fields.Str(description="子场景")
    category = fields.Str(description="分类")
    enabled = fields.Bool(description="是否启用", load_default=True)
    priority = fields.Int(description="优先级", load_default=0)
    params_type = EnumField(AqlParamsType, by_value=True, load_default=AqlParamsType.FIXED)
    params = fields.List(fields.Dict(), load_default=dict)
    retrieve_statement_id = ObjectIdField(description="检索语句ID", allow_none=True, load_default=None)
    aql_type = fields.Str(default=None, load_default=None, description="检索方式")
