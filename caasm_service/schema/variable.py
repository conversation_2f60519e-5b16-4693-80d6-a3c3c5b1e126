from marshmallow import fields
from marshmallow_enum import EnumField

from caasm_persistence.schema.base import DocumentSchema, ObjectIdField
from caasm_service.entity.variable import Variable


class VariableSchema(DocumentSchema):
    entity_define = Variable
    name = fields.Str(required=True, description="变量名称")
    description = fields.Str(required=False, description="变量描述信息")
    data_type = fields.Str(description="更新类型", load_default=None)
    data_value = fields.Raw(required=True, description="变量值")
