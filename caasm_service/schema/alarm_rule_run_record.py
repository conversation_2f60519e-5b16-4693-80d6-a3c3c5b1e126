import datetime

from caasm_persistence.schema.base import DocumentSchema, ObjectIdField, DateTimeField
from caasm_service.constants.alarm import AlarmRuleInstanceRunStatus, AlarmSeverityLevelEnum
from caasm_service.entity.alarm_rule_run_record import AlarmRuleRunRecordEntity
from caasm_persistence.schema.base import EnumField, fields, DocumentSchema


class AlarmRuleRunRecordSchema(DocumentSchema):
    entity_define = AlarmRuleRunRecordEntity

    rule_name = fields.String(description="规则名称", load_default="")
    rule_instance_id = ObjectIdField(description="规则实例ID", load_default=None)
    status = EnumField(AlarmRuleInstanceRunStatus, load_default=AlarmRuleInstanceRunStatus.WAIT, by_value=True)
    rule_level = EnumField(
        AlarmSeverityLevelEnum,
        by_value=True,
        load_default=AlarmSeverityLevelEnum.LOW,
        description="规则等级",
    )
    finished = fields.Boolean(description="规则实例完成状态", load_default=None)

    check_count = fields.Int(description="检查总数", load_default=0)
    hit_count = fields.Int(description="命中资产总数", load_default=0)
    alarm_count = fields.Int(description="生成告警总数", load_default=0)

    err_info = fields.String(description="错误信息", load_default="", allow_none=True)
    start_time = DateTimeField(load_default=datetime.datetime.now, allow_none=True, description="开始时间")
    finish_time = DateTimeField(load_default=None, allow_none=True, description="结束时间")
