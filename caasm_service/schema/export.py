from marshmallow import fields

from caasm_persistence.schema.base import DocumentSchema, EnumField, ObjectIdField
from caasm_service.constants.export import ExportRecordStatus
from caasm_service.entity.export import ExportRecordEntity


class ExportRecordSchema(DocumentSchema):
    entity_define = ExportRecordEntity

    name = fields.String(required=True)
    expire_datetime = fields.DateTime(required=True)
    create_user = fields.String(required=True)
    status = EnumField(ExportRecordStatus, by_value=True)
    category = fields.String(required=True)
    asql = fields.String(required=False, default="")
    field_list = fields.List(fields.String(required=True), required=False)
    date = fields.String(load_default=None, allow_none=True)
    total = fields.Integer(required=False, load_default=None, allow_none=True)
    completed = fields.Integer(required=False, load_default=None, allow_none=True)
    file_id = ObjectIdField(required=False, load_default=None)
    workflow_id = ObjectIdField(required=False, load_default=None)
    ext = fields.String(load_default="xlsx")
