from marshmallow import fields

from caasm_persistence.schema.base import DocumentNoneSchema, ObjectIdField
from caasm_service.entity.file_manage import FileManage


class FileManageSchema(DocumentNoneSchema):
    entity_define = FileManage

    file_id = ObjectIdField(description="file.files中的id", load_default=None)
    file_full_name = fields.Str(description="文件全名称", load_default=str, default=str)
    tag = fields.Str(description="文件标签", load_default=str)
    file_type = fields.Str(description="文件类型", load_default=str)
    description = fields.Str(description="描述信息", load_default=None)
    internal = fields.Boolean(description="是否内置", load_default=None)
