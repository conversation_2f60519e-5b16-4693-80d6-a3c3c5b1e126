from marshmallow.fields import String, Raw, List, Nested

from caasm_persistence.schema.base import BaseSchema, DocumentSchema
from caasm_service.entity.validation import FieldValidatorEntity, EntityValidatorEntity, EntityValidationEntity


class EntityValidatorBaseSchema(BaseSchema):
    type = String()
    settings = Raw(load_default=dict)


class FieldValidatorSchema(EntityValidatorBaseSchema):
    entity_define = FieldValidatorEntity

    field_name = String(load_default=None)
    item_id = String(load_default=None, load_only=True)


class EntityValidatorSchema(EntityValidatorBaseSchema):
    entity_define = EntityValidatorEntity

    field_names = List(String(required=True), required=True)
    error = String(load_default=None)


class EntityValidationSchema(DocumentSchema):
    entity_define = EntityValidationEntity

    category = String()
    entity_type = String(load_default=None, allow_none=True)
    field_validators = List(Nested(FieldValidatorSchema), load_default=list, dump_default=None)
    entity_validators = List(Nested(EntityValidatorSchema), load_default=list, dump_default=None, allow_none=True)
