from caasm_persistence.schema.base import EnumField, fields, DocumentSchema
from caasm_service.constants.trigger import TriggerType
from caasm_service.entity.job import Job


class JobSchema(DocumentSchema):
    entity_define = Job

    name = fields.Str(description="任务名称")
    enabled = fields.Bool(load_default=True, description="状态")
    trigger_info = fields.Dict(load_default=dict, description="触发器信息")
    depend_flag = fields.Bool(load_default=None)
    trigger_type = EnumField(TriggerType, by_value=True, load_default=TriggerType.CRON, description="触发器类型")
    callback_name = fields.Str(description="调用任务名称")
    runtime_job_id = fields.Str(load_default=None, description="运行时任务ID", allow_none=True)
    params = fields.Dict(description="调用任务参数", load_default=dict, allow_none=True)
