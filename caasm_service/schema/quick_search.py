from marshmallow import fields

from caasm_persistence.schema.base import DocumentNoneSchema, EnumField, BaseSchema
from caasm_service.constants.scene import AsqlType, ParamsType
from caasm_service.entity.quick_search import QuickSearchAqlEntity, QuickSearchParamEntity


class QuickSearchAqlParamEntity(BaseSchema):
    entity_define = QuickSearchParamEntity

    name = fields.String(required=True)
    description = fields.String(load_default="")
    display_name = fields.String(required=True)
    type = fields.String(required=True)
    required = fields.Boolean(load_default=False)


class QuickSearchAqlSchema(DocumentNoneSchema):
    entity_define = QuickSearchAqlEntity

    asql = fields.Str()
    display_name = fields.String()
    category = fields.Str(load_default=None)
    asql_type = EnumField(AsqlType, by_value=True, load_default=AsqlType.ASQL)
    params_type = EnumField(ParamsType, by_value=True, load_default=ParamsType.FIXED)
    inner = fields.Boolean(load_default=False)
    is_support = fields.Boolean(load_default=True)
    params = fields.List(fields.Nested(QuickSearchAqlParamEntity), load_default=list)
