from marshmallow import fields
from marshmallow_enum import EnumField

from caasm_persistence.schema.base import (
    DocumentNoneSchema,
    BaseSchema,
    ObjectIdField,
    DateTimeField,
)
from caasm_service.entity.vul_lifecycle import VulLifecycleEntity
from caasm_service.constants.vul_cycle import VulnStatusEnum, VulnLevelEnum, VulnOriginEnum


class VulnLifecycleSchema(DocumentNoneSchema):
    """漏洞生命周期Schema"""

    entity_define = VulLifecycleEntity

    name = fields.Str(required=True, metadata={"description": "漏洞名称"})

    level = EnumField(
        VulnLevelEnum,
        by_value=True,
        required=False,
        load_default=VulnLevelEnum.LOW.value,
        metadata={"description": "漏洞等级"},
    )

    score = fields.Float(required=False, load_default=None, metadata={"description": "威胁分值(0-10)"})

    host = fields.Str(required=False, load_default=None, metadata={"description": "受影响主机"})

    description = fields.Str(required=False, load_default=None, metadata={"description": "详细描述"})

    solution = fields.Str(required=False, load_default=None, metadata={"description": "修复建议"})

    cve_id = fields.Str(required=False, load_default=None, metadata={"description": "CVE编号"})

    seen_time = fields.Str(required=False, load_default=None, metadata={"description": "发现日期"})

    status = EnumField(
        VulnStatusEnum,
        by_value=True,
        required=False,
        load_default=VulnStatusEnum.UNFIXED.value,
        metadata={"description": "漏洞状态"},
    )

    cnnvd_id = fields.Str(
        required=False,
        load_default=None,
        metadata={"description": "CNNVD编号"},
    )

    cnvd_id = fields.Str(
        required=False,
        load_default=None,
        metadata={"description": "CNVD编号"},
    )

    order_ids = fields.List(fields.Str(), required=False, load_default=list, metadata={"description": "工单编号列表"})
    business_name = fields.Str(required=False, load_default=None, metadata={"description": "业务系统名称"})
    port = fields.Integer(required=False, load_default=None, metadata={"description": "端口"})
    protocal = fields.Str(required=False, load_default=None, metadata={"description": "协议"})
    file_ids = fields.List(fields.Dict(), required=False, load_default=list, metadata={"description": "附件列表"})
    internal = fields.Boolean(required=False, load_default=False, metadata={"description": "是否为内部漏洞"})
    origin = EnumField(
        VulnOriginEnum, by_value=True, required=False, load_default=None, metadata={"description": "漏洞来源"}
    )
