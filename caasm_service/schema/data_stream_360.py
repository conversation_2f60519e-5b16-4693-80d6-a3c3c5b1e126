from marshmallow import fields

from caasm_persistence.schema.base import DocumentSchema, BaseSchema
from caasm_service.entity.data_stream_360 import (
    DataStream360Entity,
    DataStream360RecordEntity,
    DataStream360IpSegmentEntity,
    DataStream360RealmEntity,
)


class DataStream360RecordSchema(DocumentSchema):
    entity_define = DataStream360RecordEntity

    successful = fields.Boolean(load_default=True)
    result = fields.String(load_default=None)
    error = fields.String(load_default=None)


class DataStream360Schema(DocumentSchema):
    entity_define = DataStream360Entity

    src_data_center = fields.String(required=True)
    src_realm = fields.String(load_default=None)
    dst_data_center = fields.String(required=True)
    dst_realm = fields.String(load_default=None)
    count = fields.Integer(required=True)
    src_province = fields.String(required=True)
    dst_province = fields.String(required=True)
    finished = fields.Boolean(required=True)


class DataStream360IpSegmentSchema(BaseSchema):
    entity_define = DataStream360IpSegmentEntity

    start = fields.String(required=False, load_default=None)
    end = fields.String(required=False, load_default=None)
    cidr = fields.String(required=False, load_default=None)


class DataStream360RealmSchema(DocumentSchema):
    entity_define = DataStream360RealmEntity

    data_center = fields.String(required=True)
    realm = fields.String(load_default=None)
    longitude = fields.Float(required=True)
    latitude = fields.Float(required=True)
    segments = fields.List(fields.Nested(DataStream360IpSegmentSchema), required=False, load_default=list)
    province = fields.String(required=True)
