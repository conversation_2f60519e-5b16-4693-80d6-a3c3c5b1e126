from marshmallow import fields

from caasm_persistence.schema.base import DocumentSchema
from caasm_service.entity.ip_segment_enrichment import IPSegmentEnrichmentEntity


class IPSegmentEnrichmentSchema(DocumentSchema):
    entity_define = IPSegmentEnrichmentEntity

    entity_id = fields.String(required=True)
    field_names = fields.List(fields.String(), load_default=[])
    date = fields.String(required=False, load_default=None)
