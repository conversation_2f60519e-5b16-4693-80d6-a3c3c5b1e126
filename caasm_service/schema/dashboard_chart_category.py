from caasm_persistence.schema.base import DocumentSchema
from caasm_persistence.schema.base import (
    fields,
    EnumField,
    DocumentSchema,
    ObjectIdField,
)
from caasm_service.entity.dashboard_chart_category import OverviewChartCategory


class OverviewChartCategorySchema(DocumentSchema):
    entity_define = OverviewChartCategory

    parent_id = fields.String()
    category_name = fields.String(required=True)
