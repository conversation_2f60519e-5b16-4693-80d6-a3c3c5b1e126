from marshmallow import fields

from caasm_persistence.schema.base import DocumentSchema, ObjectIdField
from caasm_service.entity.realm import Realm


class RealmSchema(DocumentSchema):
    entity_define = Realm

    name = fields.Str(required=True)
    realm_id = ObjectIdField(required=False, load_default=None)
    parent = ObjectIdField(required=False, allow_none=True, load_default=None)
    children = fields.List(ObjectIdField(), required=False, load_default=None)
