from caasm_persistence.schema.base import fields, DocumentSchema
from caasm_service.entity.internet_relation import InternetRelation


class InternetRelationSchema(DocumentSchema):
    entity_define = InternetRelation

    internet_ip = fields.Str(description="公网IP", load_default=None)
    domain = fields.Str(description="域名", load_default=None)
    internet_port = fields.Int(description="端口", load_default=None)
    protocol = fields.Str(description="协议", load_default=None)
    route = fields.Str(description="路由", load_default=None)
    internal_ip = fields.Str(description="内网IP", load_default=None)
    internal_port = fields.Int(description="内网端口", load_default=None)
