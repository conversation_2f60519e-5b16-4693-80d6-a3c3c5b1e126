import datetime

from caasm_persistence.schema.base import DocumentSchema, ObjectIdField, DateTimeField
from caasm_service.constants.alarm import (
    AlarmRuleInstanceRunStatus,
    AlarmSeverityLevelEnum,
    AlarmDisposalStatus,
    ALARMStatus,
)
from caasm_service.entity.alarm_record import AlarmRecordEntity
from caasm_service.entity.alarm_rule_run_record import AlarmRuleRunRecordEntity
from caasm_persistence.schema.base import EnumField, fields, DocumentSchema


class AlarmRecordSchema(DocumentSchema):
    entity_define = AlarmRecordEntity

    alarm_name = fields.String(description="规则名称", load_default="")
    rule_instance_id = ObjectIdField(description="规则实例ID", load_default=None)
    alarm_level = EnumField(AlarmSeverityLevelEnum, load_default=AlarmSeverityLevelEnum.UNKNOWN, by_value=True)

    alarm_category = fields.String(description="告警分类", load_default="")

    check_count = fields.Int(description="检查总数", load_default=0)
    hit_count = fields.Int(description="命中资产总数", load_default=0)

    alarm_timeline = fields.List(fields.Dict(), description="告警时间线", load_default=[])

    disposal_status = EnumField(AlarmDisposalStatus, load_default=AlarmDisposalStatus.UNDISPOSED, by_value=True)

    alarm_description = fields.String(description="告警描述", load_default="")

    asql = fields.String(description="asql语句，用于给前端返回", load_default="")

    unique_key = fields.String(description="唯一键")

    snapshot_date = fields.String(description="快照日期")

    alarm_status = EnumField(ALARMStatus, load_default=ALARMStatus.ACTIVE, by_value=True)
