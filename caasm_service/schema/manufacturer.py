from caasm_persistence.schema.base import (
    ObjectIdField,
    fields,
    DocumentSchema,
)
from caasm_service.entity.manufacturer import Manufacturer


class ManufacturerSchema(DocumentSchema):
    entity_define = Manufacturer

    name = fields.String(description="名称")
    description = fields.String(description="描述信息", required=False, load_default="")
    logo_id = ObjectIdField(description="logo文件ID", required=False, load_default=None)
