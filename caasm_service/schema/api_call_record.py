from marshmallow import fields

from caasm_persistence.schema.base import DocumentNoneSchema
from caasm_service.entity.api_call_record import ApiCallRecord


class ApiCallRecordSchema(DocumentNoneSchema):
    entity_define = ApiCallRecord

    ip = fields.Str(load_default=None)
    url = fields.Str(load_default=None)
    enabled = fields.Boolean(load_default=None)
    status_code = fields.Int(load_default=None)
    request_params = fields.Dict(load_default=None)
    error = fields.Str(load_default=None)
