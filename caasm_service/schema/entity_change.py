from caasm_persistence.schema.base import (
    fields,
    EnumField,
    DocumentSchema,
    ObjectIdField,
)
from caasm_service.entity.change import ChangeType
from caasm_service.entity.entity_change import EntityChangeDetail, EntityChangeRecord


class EntityChangeDetailSchema(DocumentSchema):
    entity_define = EntityChangeDetail

    category = fields.String()
    entity_id = fields.String()
    field_name = fields.String(load_default=None)
    change_type = EnumField(ChangeType, by_value=True)
    child_change_type = EnumField(ChangeType, by_value=True, load_default=None)
    new_value = fields.Raw(load_default=None)
    new_display = fields.String(load_default=None)
    original_value = fields.Raw(load_default=None)
    original_display = fields.String(load_default=None)
    changed_datetime = fields.DateTime()
    record_id = ObjectIdField()
    finished = fields.Boolean(load_default=False)
    app_name = fields.String(load_default=None)
    user_id = ObjectIdField(load_default=None)
    change_id = ObjectIdField(load_default=None)


class EntityChangeRecordSchema(DocumentSchema):
    entity_define = EntityChangeRecord

    category = fields.String()
    finished = fields.Boolean(load_default=False)
