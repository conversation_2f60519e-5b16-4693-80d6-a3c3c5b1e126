from marshmallow import fields

from caasm_persistence.schema.base import DocumentSchema
from caasm_service.entity.user import User


class UserSchema(DocumentSchema):
    entity_define = User

    username = fields.Str(description=" 用户名称", load_default="")
    enabled = fields.Bool(load_default=True, description="状态")
    is_super = fields.Bool(load_default=False, description="是否超管")
    mobile = fields.Str(load_default=None, allow_none=True, description="手机号")
    email = fields.Str(load_default=None, allow_none=True, description="邮箱")
    password = fields.Str(allow_none=True, load_default=None, description="密码信息")
    role_codes = fields.List(fields.Str(), description="权限code", load_default=list)
    description = fields.Str(description="描述信息", load_default="")
    sso_keys = fields.List(fields.Str(), description="cas 唯一索引", load_default=list, required=False)
    try_login_count = fields.Int(load_default=0, allow_none=True, description="尝试登录次数")
    lock_expired = fields.DateTime(load_default=None, allow_none=True, description="锁定时间")
