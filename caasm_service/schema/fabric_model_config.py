import datetime

from caasm_persistence.schema.base import DocumentSchema, fields, ObjectIdField, BaseSchema, DateTimeField
from caasm_service.entity.fabric_meta_model_config import (
    UniqueIdentificationPolicy,
    AdapterConfidence,
    FieldGlobalPolicy,
    FieldPolicy,
    FabricModelConfig,
    FabricPolicy,
)


class FabricPolicySchema(BaseSchema):
    entity_define = FabricPolicy

    name = fields.String(description="策略名称", load_default=None)
    src_field = fields.String(description="源字段", load_default=None)
    dst_field = fields.String(description="目的字段", load_default=None)
    setting = fields.Dict(description="配置信息", load_default=None)


class UniqueIdentificationPolicySchema(BaseSchema):
    entity_define = UniqueIdentificationPolicy

    policy_label = fields.String(description="策略名称", load_default="")
    policy_value = fields.String(description="策略值", load_default="")
    policy = fields.List(fields.String(description="策略方法或字段ID"), load_default=list)


class AdapterConfidenceSchema(BaseSchema):
    entity_define = AdapterConfidence

    adapter_name = fields.String(description="适配器名称", load_default="")
    confidence = fields.Int(description="确信度", load_default=0)


class FieldGlobalPolicySchema(BaseSchema):
    entity_define = FieldGlobalPolicy

    label = fields.String(description="策略名称,用于展示", load_default="")
    value = fields.String(description="策略名称", load_default="")
    policy_description = fields.String(description="策略描述", load_default="")


class FieldPolicySchema(BaseSchema):
    entity_define = FieldPolicy

    field_id = fields.String(description="字段ID", load_default=None)
    field_adapter_confidence = fields.List(fields.Nested(AdapterConfidenceSchema), load_default=list)
    field_policy = fields.List(fields.Nested(FieldGlobalPolicySchema), load_default=list)


class FabricModelConfigSchema(DocumentSchema):
    entity_define = FabricModelConfig

    asset_type_id = ObjectIdField(description="资产类型ID")
    is_modify = fields.Bool(description="是否修改", load_default=False)
    modify_time = DateTimeField(load_default=None, description="修改时间")
    modify_username = fields.String(description="修改用户名", load_default=None)
    fabric_policy = fields.Nested(FabricPolicySchema, load_default=None, allow_none=True)
    adapter_confidence = fields.List(fields.Nested(AdapterConfidenceSchema), load_default=None, allow_none=True)
    field_global_policy = fields.List(fields.Nested(FieldGlobalPolicySchema), load_default=None, allow_none=True)
