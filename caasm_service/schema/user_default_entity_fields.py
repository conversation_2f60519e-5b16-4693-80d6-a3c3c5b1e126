from marshmallow import fields

from caasm_persistence.schema.base import ObjectIdField, DocumentSchema
from caasm_service.entity.user_default_entity_fields import UserEntityDefaultFields


class UserDefaultEntityFieldsSchema(DocumentSchema):
    entity_define = UserEntityDefaultFields

    user_id = ObjectIdField(description="用户ID", load_default=None, allow_none=True)
    category = fields.Str(description="实体类型", load_default=None)
    field_names = fields.List(fields.Str(), load_default=None)
