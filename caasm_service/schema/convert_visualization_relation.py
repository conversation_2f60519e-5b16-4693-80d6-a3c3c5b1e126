from caasm_persistence.schema.base import fields, DocumentNoneSchema, BaseSchema, ObjectIdField
from caasm_service.entity.convert_visualization_relation import ConvertVisualizationRelation, ConvertRule


class ConvertRuleSchema(BaseSchema):
    entity_define = ConvertRule

    id = fields.Str(description="规则ID", load_default=None)
    name = fields.Str(description="规则名称", load_default=None)
    setting = fields.Dict(load_default=None)
    sub_rules = fields.Nested("self", load_default=None, many=True)


class ConvertVisualizationRelationSchema(DocumentNoneSchema):
    entity_define = ConvertVisualizationRelation

    adapter_name = fields.String(description="适配器名称", load_default=None)
    init = fields.Boolean(description="初始化", load_default=None)
    fetch_type = fields.String(description="采集类型", load_default=None)
    model_id = ObjectIdField(description="模型名称", load_default=None)
    internal = fields.Boolean(description="是否内置", load_default=None)
    asset_type_id = ObjectIdField(description="资产类型ID", load_default=None)
    rules = fields.Nested(ConvertRuleSchema, many=True, load_default=None)
    canvas = fields.Dict(load_default=None, description="画布信息")
    modify_flag = fields.Boolean(load_default=None, description="修改标志位")
