from caasm_persistence.schema.base import DocumentSchema, BaseSchema, fields
from caasm_service.entity.menu import Menu, Action


class ActionSchema(BaseSchema):
    entity_define = Action

    name = fields.Str(description="动作名称")
    icon = fields.Str(description="icon")
    code = fields.Str(description="唯一码")
    permission_codes = fields.List(fields.Str(), load_default=list)


class MenuSchema(DocumentSchema):
    entity_define = Menu

    name = fields.Str(description="菜单名称")
    icon = fields.Str(description="icon", load_default="")
    path = fields.Str(description="路径", load_default="")
    redirect_path = fields.Str(description="跳转地址", load_default="")
    level = fields.Int(description="级别", load_default=1)
    priority = fields.Int(description="优先级", load_default=1)
    code = fields.Str(description="菜单唯一码")
    setting = fields.Dict(description="", load_default=dict)
    is_show = fields.Bool(description="是否展示", load_default=True)
    parent_code = fields.Str(description="上级菜单code", load_default=None)
    children_codes = fields.List(fields.Str(), description="子菜单codes", load_default=list)
    actions = fields.List(fields.Nested(ActionSchema()), load_default=list)
