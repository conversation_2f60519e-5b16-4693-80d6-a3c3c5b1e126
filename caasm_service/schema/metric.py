from caasm_persistence.schema.base import (
    BaseSchema,
    fields,
    EnumField,
    DocumentSchema,
)

from caasm_service.constants.overview import OverviewCategoryEnum, DashboardSpaceDisplayEnum
from caasm_service.entity.metric import OverviewEntity, ChartEntity


class ChartSchema(BaseSchema):
    entity_define = ChartEntity

    chart = fields.String(required=True)
    chart_instance_name = fields.String(required=False, load_default=str)
    horizontal_index = fields.Int(required=True, allow_none=False)
    vertical_index = fields.Int(required=True, allow_none=False)
    width = fields.Int(required=True, allow_none=False)
    height = fields.Int(required=True, allow_none=False)


class OverviewSchema(DocumentSchema):
    entity_define = OverviewEntity

    name = fields.String(required=True, allow_none=False, max_length=16)
    icon = fields.String(required=False, allow_none=True, max_length=16, load_default=None)
    index = fields.Int(required=False, allow_none=False, min_value=0, load_default=0)
    charts = fields.List(fields.Nested(ChartSchema, required=False, allow_none=True), load_default=list)
    is_default = fields.Boolean(required=True, allow_none=False, default=False)
    user_id = fields.String(required=False, load_default=None)
    role_codes = fields.List(fields.String(), required=False, load_default=list)
    ownership = EnumField(
        DashboardSpaceDisplayEnum, default=DashboardSpaceDisplayEnum.PUBLIC, by_value=True, required=True
    )
    category = EnumField(
        OverviewCategoryEnum,
        default=OverviewCategoryEnum.DASHBOARD,
        by_value=True,
        required=False,
        load_default=OverviewCategoryEnum.DASHBOARD.value,
    )
    file_id = fields.String(required=False, load_default=str, default=str, allow_none=True)
    frontend_validate_name = fields.String(required=False, load_default=None)
