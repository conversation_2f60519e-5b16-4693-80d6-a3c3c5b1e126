from caasm_persistence.schema.base import DocumentSchema, fields, BaseSchema
from caasm_service.entity.role import Role, RoleMenu


class MenuRelationSchema(BaseSchema):
    entity_define = RoleMenu

    code = fields.Str(description="菜单代码")
    action_codes = fields.List(fields.Str(), description="动作code")
    children = fields.Nested("MenuRelationSchema", many=True)


class RoleSchema(DocumentSchema):
    entity_define = Role

    code = fields.Str(description="角色代码", load_default=None)
    name = fields.Str(description="角色名称", load_default="")
    description = fields.Str(description="描述信息", load_default="")
    permission_codes = fields.List(fields.Str(), description="权限代码，用于控制接口的权限", load_default=list)
    menus = fields.List(fields.Nested(MenuRelationSchema()), description="权限关系", load_default=list)
