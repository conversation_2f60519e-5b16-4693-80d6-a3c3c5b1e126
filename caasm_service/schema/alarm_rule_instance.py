from caasm_service.constants.alarm import (
    AlarmRuleTypeEnum,
    AlarmSeverityLevelEnum,
    AlarmRuleResultTypeEnum,
    AlarmRuleStatementModelEnum,
    AlarmRuleInstanceRunStatus,
)
from caasm_service.constants.trigger import TriggerType
from caasm_service.entity.alarm_rule_instance import AlarmRuleInstanceEntity, Trigger, AlarmRuleStatementModel
from caasm_persistence.schema.base import (
    EnumField,
    fields,
    DocumentSchema,
    BaseSchema,
    ObjectIdField,
)


class TriggerSchema(BaseSchema):
    entity_define = Trigger

    type = EnumField(TriggerType, load_default=TriggerType.CRON, by_value=True)
    value = fields.Dict()


class AlarmRuleStatementModelSchema(BaseSchema):
    entity_define = AlarmRuleStatementModel

    model = EnumField(AlarmRuleStatementModelEnum, load_default=AlarmRuleStatementModelEnum.ASQL, by_value=True)
    statement = fields.String(description="语句名称", load_default="")
    template_name = fields.String(description="模版名称", load_default="", allow_none=True)
    template_id = ObjectIdField(description="模版ID", load_default=None, default=None)


class AlarmRuleInstanceSchema(DocumentSchema):
    entity_define = AlarmRuleInstanceEntity

    rule_name = fields.String(description="规则名称", load_default="")
    alarm_name = fields.String(description="告警名称", load_default="")
    enabled = fields.Boolean(description="规则实例状态", load_default=None)
    rule_category = fields.String(description="规则分类", load_default="")

    rule_level = EnumField(
        AlarmSeverityLevelEnum,
        by_value=True,
        load_default=AlarmSeverityLevelEnum.LOW,
        description="规则等级",
    )

    rule_type = EnumField(
        AlarmRuleTypeEnum,
        by_value=True,
        load_default=AlarmRuleTypeEnum.CUSTOMIZE,
        description="规则类型",
    )

    result_type = EnumField(
        AlarmRuleResultTypeEnum,
        by_value=True,
        load_default=AlarmRuleResultTypeEnum.SINGLE_INSTANCE,
        description="规则类型",
    )

    rule_scope_statement_model = fields.Nested(
        AlarmRuleStatementModelSchema, description="规则限制范围", load_default=dict, allow_none=True
    )
    rule_condition_statement_model = fields.Nested(
        AlarmRuleStatementModelSchema, description="规则检查范围", load_default=dict
    )

    run_status = EnumField(
        AlarmRuleInstanceRunStatus,
        load_default=AlarmRuleInstanceRunStatus.WAIT,
        by_value=True,
        description="运行状态",
    )

    trigger_type = fields.String(description="trigger类型", load_default="")
    trigger = fields.Nested(TriggerSchema, description="触发器", load_default=dict, allow_none=True)

    rule_description = fields.String(description="规则描述", load_default="")
