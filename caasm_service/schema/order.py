from marshmallow import fields
from marshmallow_enum import EnumField

from caasm_persistence.schema.base import (
    DocumentNoneSchema,
    DateTimeField,
)
from caasm_service.entity.order import OrderEntity
from caasm_service.constants.order import OrderPriorityEnum, OrderStatusEnum


class OrderSchema(DocumentNoneSchema):
    """工单Schema"""

    entity_define = OrderEntity

    order_id = fields.Str(required=True, metadata={"description": "工单ID"})
    title = fields.Str(required=False, load_default=None, metadata={"description": "工单标题"})
    description = fields.Str(required=False, load_default=None, metadata={"description": "工单描述"})
    status = EnumField(
        OrderStatusEnum,
        by_value=True,
        required=False,
        load_default=OrderStatusEnum.PENDING,
        metadata={"description": "工单状态"},
    )
    priority = EnumField(
        OrderPriorityEnum,
        by_value=True,
        required=False,
        load_default=OrderPriorityEnum.MEDIUM,
        metadata={"description": "优先级"},
    )
    creator = fields.Str(required=False, load_default=None, metadata={"description": "创建人"})
    assignee = fields.Str(required=False, load_default=None, metadata={"description": "负责人"})
    business_system = fields.Str(required=False, load_default=None, metadata={"description": "所属业务系统"})
    vul_ids = fields.List(fields.Str(), required=False, load_default=list, metadata={"description": "关联的漏洞ID列表"})
    finish_time = DateTimeField(required=False, load_default=None, metadata={"description": "完成时间"})
    operation_logs = fields.List(fields.Dict(), required=False, load_default=list, metadata={"description": "操作日志列表"})
    creator_mobile = fields.Str(required=False, load_default=None, metadata={"description": "创建人手机号"})
    creator_phone = fields.Str(required=False, load_default=None, metadata={"description": "创建人电话号"})
    creator_email = fields.Str(required=False, load_default=None, metadata={"description": "创建人邮箱"})
    creator_id = fields.Str(required=False, load_default=None, metadata={"description": "创建人ID"})
    assignee_mobile = fields.Str(required=False, load_default=None, metadata={"description": "负责人手机号"})
    assignee_phone = fields.Str(required=False, load_default=None, metadata={"description": "负责人电话号"})
    assignee_email = fields.Str(required=False, load_default=None, metadata={"description": "负责人邮箱"})
