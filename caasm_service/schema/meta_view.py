from caasm_persistence.schema.base import DocumentNoneSchema, fields, BaseSchema
from caasm_service.entity.meta_view import MetaAssetMenuTreeEntity, MetaAssetTypeViewEntity, MetaCardConfig


class MetaAssetMenuTreeSchema(BaseSchema):
    entity_define = MetaAssetMenuTreeEntity

    name = fields.String(load_default=None)
    display_name = fields.String(load_default=None)
    children = fields.Nested("self", load_default=None, many=True)


class MetaCardConfigSchema(BaseSchema):
    entity_define = MetaCardConfig

    card_type = fields.String()
    card_name = fields.String(load_default=None)
    display_name = fields.String(load_default=None)
    config = fields.Raw(load_default=dict)


class MetaAssetTypeViewSchema(DocumentNoneSchema):
    entity_define = MetaAssetTypeViewEntity

    category = fields.String(load_default=None)
    asset_type = fields.String(load_default=None)
    description = fields.String(load_default=None)
    menu_tree = fields.Nested(MetaAssetMenuTreeSchema, many=True, load_default=None)
    multi_title_expr = fields.List(fields.String, load_default=None)
    support_cards = fields.List(fields.Nested(MetaCardConfigSchema), load_default=None)
