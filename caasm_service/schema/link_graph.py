from caasm_persistence.schema.base import DocumentSchema, fields, ObjectIdField, BaseSchema
from caasm_service.entity.link_graph import (
    EntityLinkGraph,
    LinkGraph,
    MissingAsset,
    Business,
    SuspectedBusiness,
    Owner,
    HostEntry,
)


class EntityLinkGraphSchema(DocumentSchema):
    entity_define = EntityLinkGraph

    category = fields.String()
    asset_type = fields.String(load_default=None)
    entity_id = fields.String()
    display_values = fields.List(fields.String(), load_default=list)
    graph_id = ObjectIdField()
    finished = fields.Boolean(load_default=False)


class MissingAssetSchema(BaseSchema):
    entity_define = MissingAsset

    ip = fields.String()
    ports = fields.List(fields.Integer(), load_default=list)


class BusinessSchema(BaseSchema):
    entity_define = Business

    business_id = fields.String(load_default=None)
    name = fields.String()


class SuspectedBusinessSchema(BusinessSchema):
    entity_define = SuspectedBusiness

    category = fields.String()
    entity_id = fields.String()


class OwnerSchema(BaseSchema):
    entity_define = Owner

    owner_id = fields.String(load_default=None)
    nickname = fields.String()
    category = fields.String()
    entity_id = fields.String()
    #   责任人所属实体显示名称
    entity_display_name = fields.String()


class HostEntrySchema(BaseSchema):
    entity_define = HostEntry

    entity_id = fields.String()
    ips = fields.List(fields.String(), load_default=list)
    ports = fields.List(fields.Integer(), load_default=list)


class LinkGraphSchema(DocumentSchema):
    entity_define = LinkGraph

    graph = fields.Raw(load_default=bytes)
    finished = fields.Boolean(load_default=False)
    missing_assets = fields.List(fields.Nested(MissingAssetSchema()), load_default=list)
    business = fields.Nested(BusinessSchema(), load_default=None)
    suspected_businesses = fields.List(fields.String(), load_default=list)
    properties = fields.List(fields.String(), load_default=list)
    owners = fields.List(fields.Nested(OwnerSchema()), load_default=list)
    host_entries = fields.List(fields.Nested(HostEntrySchema()), load_default=list)
    adapters = fields.List(fields.String(), load_default=list)
    domains = fields.List(fields.String(), load_default=list)
    internet_ips = fields.List(fields.String(), load_default=list)
