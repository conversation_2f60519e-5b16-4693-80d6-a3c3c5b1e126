from caasm_service.schema.adapter import AdapterSchema, ValidateRuleSchema
from caasm_service.schema.adapter_instance import AdapterInstanceSchema
from caasm_service.schema.adapter_instance_fetch_poc_log import AdapterInstanceFetchPocLogSchema
from caasm_service.schema.alarm_record import AlarmRecordSchema
from caasm_service.schema.alarm_rule_instance import AlarmRuleInstanceSchema
from caasm_service.schema.alarm_rule_run_record import AlarmRuleRunRecordSchema
from caasm_service.schema.alarm_rule_template import AlarmRuleTemplateSchema
from caasm_service.schema.api_call_record import ApiCallRecordSchema
from caasm_service.schema.asset_type import AssetTypeSchema
from caasm_service.schema.business_poraiter import (
    BusinessPortraitNetworkSchema,
    BusinessPortraitSchema,
    BusinessPortraitUpdateSchema,
    BusinessPortraitUpdateItemSchema,
)
from caasm_service.schema.category import CategorySchema
from caasm_service.schema.change import ChangeDetailSchema, ChangeRecordSchema
from caasm_service.schema.convert_func import ConvertFuncSchema
from caasm_service.schema.convert_func_type import ConvertFuncTypeSchema
from caasm_service.schema.convert_record import ConvertRecordSchema
from caasm_service.schema.convert_visualization import ConvertVisualizationSchema
from caasm_service.schema.convert_visualization_relation import ConvertVisualizationRelationSchema, ConvertRuleSchema
from caasm_service.schema.dashboard_chart import OverviewChartSchema
from caasm_service.schema.dashboard_chart_category import OverviewChartCategorySchema
from caasm_service.schema.dashboard_user_setting import DashboardUserSettingSchema
from caasm_service.schema.data_stream_360 import (
    DataStream360RecordSchema,
    DataStream360Schema,
    DataStream360IpSegmentSchema,
    DataStream360RealmSchema,
)
from caasm_service.schema.entity_change import EntityChangeDetailSchema, EntityChangeRecordSchema
from caasm_service.schema.export import ExportRecordSchema
from caasm_service.schema.fabric_model_config import FabricModelConfigSchema
from caasm_service.schema.fetch_record import FetchRecordSchema
from caasm_service.schema.field_fabric_policy import FieldFabricPolicySchema
from caasm_service.schema.file_manage import FileManageSchema
from caasm_service.schema.intercept_alarm import InterceptAlarmSchema
from caasm_service.schema.internet_relation import InternetRelationSchema
from caasm_service.schema.ip_segment_enrichment import IPSegmentEnrichmentSchema
from caasm_service.schema.job import JobSchema
from caasm_service.schema.join_export_fields import JoinExportFieldsSchema
from caasm_service.schema.lineage import (
    EntityLineageMetaSchema,
    EntityFetchMetaSchema,
    EntityMergeMetaSchema,
    EntityConvertMetaSchema,
    EntityFabricMetaSchema,
    EntityLineageStageSchema,
    EntityTypeLineageMetaSchema,
    EntityTypeFetchMetaSchema,
    EntityTypeMergeMetaSchema,
    EntityTypeConvertMetaSchema,
    EntityTypeFabricMetaSchema,
    EntityTypeLineageStageSchema,
    EntityUpstreamSchema,
    ValueLineageMetaSchema,
    ValueFetchMetaSchema,
    ValueMergeMetaSchema,
    ValueConvertMetaSchema,
    ValueFabricMetaSchema,
    ValueRowLineageSchema,
    ValueLineageSchema,
    ValueLineageStageSchema,
)
from caasm_service.schema.link_graph import (
    EntityLinkGraphSchema,
    LinkGraphSchema,
    MissingAssetSchema,
    BusinessSchema,
    SuspectedBusinessSchema,
    OwnerSchema,
    HostEntrySchema,
)
from caasm_service.schema.maintenance import (
    FormItemSchema,
    FormGroupSchema,
    FormSchema,
    MaintenanceSchema,
    BatchFieldSchema,
    BatchOperationSchema,
)
from caasm_service.schema.manufacturer import ManufacturerSchema
from caasm_service.schema.menu import MenuSchema
from caasm_service.schema.merge_record import MergeRecordSchema
from caasm_service.schema.meta_entity_fields import MetaEntityFieldsSchema
from caasm_service.schema.meta_entity_type_view import MetaEntityTypeViewSchema
from caasm_service.schema.meta_model import MetaModelSchema, MetaFieldSchema, MetaViewSchema
from caasm_service.schema.meta_view import MetaAssetTypeViewSchema, MetaAssetMenuTreeSchema
from caasm_service.schema.metric import OverviewSchema
from caasm_service.schema.network_mapping_cluster import NetworkMappingClusterSchema
from caasm_service.schema.operation_log import OperationLogSchema, OperationLogConfigSchema
from caasm_service.schema.quick_search import QuickSearchAqlSchema
from caasm_service.schema.realm import RealmSchema
from caasm_service.schema.retrieve_scenes import RetrieveScenesSchema
from caasm_service.schema.retrieve_statement import RetrieveStatementSchema
from caasm_service.schema.role import RoleSchema
from caasm_service.schema.sequence import SequenceSchema
from caasm_service.schema.setting import SettingSchema
from caasm_service.schema.snapshot import (
    SnapshotRecordSchema,
    MetaModelSnapshotRecordSchema,
    MetaFieldSnapshotRecordSchema,
)
from caasm_service.schema.sso import TempLoginKeySchema, SSOConfigSchema
from caasm_service.schema.system_status import SystemStatusSchema
from caasm_service.schema.unique import UniqueSchema, UniqueIndexSchema
from caasm_service.schema.user import UserSchema
from caasm_service.schema.user_asset_aql_collect import UserAssetAqlCollectSchema
from caasm_service.schema.user_asset_aql_history import UserAssetAqlHistorySchema
from caasm_service.schema.user_default_entity_fields import UserDefaultEntityFieldsSchema
from caasm_service.schema.validation import FieldValidatorSchema, EntityValidatorSchema, EntityValidationSchema
from caasm_service.schema.variable import VariableSchema
from caasm_service.schema.vul_cycle import VulnLifecycleSchema
from caasm_service.schema.vul_file import VulFileSchema
from caasm_service.schema.vul_priority import VulPrioritySettingSchema, VulPrioritySchema
from caasm_service.schema.vul_timeline import VulTimelineSchema
from caasm_service.schema.order import OrderSchema
from caasm_service.schema.whitelist import WhitelistSchema

retrieve_scenes_schema = RetrieveScenesSchema()
retrieve_statement_schema = RetrieveStatementSchema()
operation_log_schema = OperationLogSchema()
operation_log_config_schema = OperationLogConfigSchema()

manufacturer_schema = ManufacturerSchema()
adapter_schema = AdapterSchema()
adapter_instance_fetch_poc_log_schema = AdapterInstanceFetchPocLogSchema()
adapter_instance_schema = AdapterInstanceSchema()
fetch_record_schema = FetchRecordSchema()
merge_record_schema = MergeRecordSchema()
convert_record_schema = ConvertRecordSchema()
sequence_schema = SequenceSchema()
setting_schema = SettingSchema()
validate_rule_schema = ValidateRuleSchema()
convert_func_schema = ConvertFuncSchema()
convert_func_type_schema = ConvertFuncTypeSchema()

fabric_model_config_schema = FabricModelConfigSchema()
field_fabric_policy_schema = FieldFabricPolicySchema()

user_schema = UserSchema()
user_asset_aql_history_schema = UserAssetAqlHistorySchema()
user_asset_aql_collect_schema = UserAssetAqlCollectSchema()
user_default_entity_fields_schema = UserDefaultEntityFieldsSchema()
meta_entity_fields_schema = MetaEntityFieldsSchema()
menu_schema = MenuSchema()
role_schema = RoleSchema()

variable_schema = VariableSchema()

meta_model_schema = MetaModelSchema()
meta_field_schema = MetaFieldSchema()
meta_view_schema = MetaViewSchema()
meta_asset_type_view_schema = MetaAssetTypeViewSchema()
meta_asset_menu_tree_schema = MetaAssetMenuTreeSchema()
meta_entity_type_view_schema = MetaEntityTypeViewSchema()

convert_rule_schema = ConvertRuleSchema()
convert_visualization_schema = ConvertVisualizationSchema()
convert_visualization_relation_schema = ConvertVisualizationRelationSchema()

asset_type_schema = AssetTypeSchema()

realm_schema = RealmSchema()

business_portrait_schema = BusinessPortraitSchema()
business_portrait_network_schema = BusinessPortraitNetworkSchema()
business_portrait_update_item_scheme = BusinessPortraitUpdateItemSchema()
business_portrait_update_stream = BusinessPortraitUpdateSchema()

job_schema = JobSchema()

dashboard_user_setting_schema = DashboardUserSettingSchema()
overview_chart_schema = OverviewChartSchema()
overview_schema = OverviewSchema()
overview_chart_category_schema = OverviewChartCategorySchema()

file_manage_schema = FileManageSchema()

internet_relation_schema = InternetRelationSchema()

sso_config_schema = SSOConfigSchema()
temp_login_key = TempLoginKeySchema()

whitelist_schema = WhitelistSchema()

api_call_record_schema = ApiCallRecordSchema()

snapshot_schema = SnapshotRecordSchema()

meta_model_snapshot_record_schema = MetaModelSnapshotRecordSchema()
meta_field_snapshot_record_schema = MetaFieldSnapshotRecordSchema()

change_detail_schema = ChangeDetailSchema()
change_record_schema = ChangeRecordSchema()

entity_change_detail_schema = EntityChangeDetailSchema()
entity_change_record_schema = EntityChangeRecordSchema()

unique_schema = UniqueSchema()
unique_index_schema = UniqueIndexSchema()

vul_priority_setting_schema = VulPrioritySettingSchema()
vul_priority_schema = VulPrioritySchema()
vul_file_schema = VulFileSchema()
vul_timeline_schema = VulTimelineSchema()
join_export_fields_schema = JoinExportFieldsSchema()
quick_search_schema = QuickSearchAqlSchema()

entity_link_graph_schema = EntityLinkGraphSchema()
missing_asset_schema = MissingAssetSchema()
business_schema = BusinessSchema()
suspected_business_schema = SuspectedBusinessSchema()
owner_schema = OwnerSchema()
link_graph_schema = LinkGraphSchema()
host_entry_schema = HostEntrySchema()

network_mapping_cluster_schema = NetworkMappingClusterSchema()

alarm_rule_instance_schema = AlarmRuleInstanceSchema()
alarm_rule_run_record_schema = AlarmRuleRunRecordSchema()
alarm_rule_template_schema = AlarmRuleTemplateSchema()
alarm_record_schema = AlarmRecordSchema()
intercept_alarm_schema = InterceptAlarmSchema()

system_status_schema = SystemStatusSchema()

category_schema = CategorySchema()

entity_type_lineage_meta_schema = EntityTypeLineageMetaSchema()
entity_type_fetch_meta_schema = EntityTypeFetchMetaSchema()
entity_type_merge_meta_schema = EntityTypeMergeMetaSchema()
entity_type_convert_meta_schema = EntityTypeConvertMetaSchema()
entity_type_fabric_meta_schema = EntityTypeFabricMetaSchema()
entity_type_lineage_stage_schema = EntityTypeLineageStageSchema(unknown="INCLUDE")

entity_lineage_meta_schema = EntityLineageMetaSchema()
entity_fetch_meta_schema = EntityFetchMetaSchema()
entity_merge_meta_schema = EntityMergeMetaSchema()
entity_convert_meta_schema = EntityConvertMetaSchema()
entity_fabric_meta_schema = EntityFabricMetaSchema()
entity_upstream_schema = EntityUpstreamSchema()
entity_lineage_stage_schema = EntityLineageStageSchema()

value_lineage_meta_schema = ValueLineageMetaSchema()
value_fetch_meta_schema = ValueFetchMetaSchema()
value_merge_meta_schema = ValueMergeMetaSchema()
value_convert_meta_schema = ValueConvertMetaSchema()
value_fabric_meta_schema = ValueFabricMetaSchema()
value_row_lineage_schema = ValueRowLineageSchema()
value_lineage_schema = ValueLineageSchema()
value_lineage_stage_schema = ValueLineageStageSchema()

form_item_schema = FormItemSchema()
form_group_schema = FormGroupSchema()
form_schema = FormSchema()
import_field_schema = BatchFieldSchema()
import_operation_schema = BatchOperationSchema()
maintenance_schema = MaintenanceSchema()

field_validator_schema = FieldValidatorSchema()
entity_validator_schema = EntityValidatorSchema()
entity_validation_schema = EntityValidationSchema()

ip_segment_enrichment_schema = IPSegmentEnrichmentSchema()

#   导出
export_record_schema = ExportRecordSchema()

data_stream_360_record_schema = DataStream360RecordSchema()
data_stream_360_schema = DataStream360Schema()
data_stream_360_ip_segment_schema = DataStream360IpSegmentSchema()
data_stream_360_realm_schema = DataStream360RealmSchema()
vul_lifecycle_schema = VulnLifecycleSchema()
order_schema = OrderSchema()
