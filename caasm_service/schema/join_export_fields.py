import datetime

from caasm_persistence.schema.base import (
    fields,
    DocumentSchema,
    ObjectIdField,
    DateTimeField,
)
from caasm_service.entity.join_export_fields import ExportFieldsConfig


class JoinExportFieldsSchema(DocumentSchema):
    entity_define = ExportFieldsConfig

    vul_unique_field_names = fields.Dict(load_default=dict)
    asset_field_names = fields.Dict(load_default=dict)
    vul_field_names = fields.Dict(load_default=dict)
    main_category = fields.String(allow_none=False)
    user_id = ObjectIdField(required=True)
