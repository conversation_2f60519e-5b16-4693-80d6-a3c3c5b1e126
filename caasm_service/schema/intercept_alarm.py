from caasm_persistence.schema.base import DocumentSchema, fields
from caasm_service.entity.intercept_alarm import InterceptAlarmEntity


class InterceptAlarmSchema(DocumentSchema):
    """拦截告警模式类"""

    entity_define = InterceptAlarmEntity

    # 保留的字段
    src_ip = fields.String(description="源IP地址", load_default="")
    src_group_relationship_name = fields.String(description="源IP所属分组名称", load_default="")
    dst_ip = fields.String(description="目标IP地址", load_default="")
    dst_port = fields.Integer(description="目标端口", load_default=0)
    dst_group_relationship_name = fields.String(description="目标IP所属分组名称", load_default="")
    count = fields.Integer(description="拦截次数", load_default=0)
    reason = fields.String(description="拦截原因", load_default="")

    # 数据中心和安全域字段
    src_data_center = fields.String(description="源IP所属数据中心", load_default=None)
    src_realm = fields.String(description="源IP所属安全域", load_default=None)
    dst_data_center = fields.String(description="目标IP所属数据中心", load_default=None)
    dst_realm = fields.String(description="目标IP所属安全域", load_default=None)
    src_province = fields.String(description="源IP所属省份", load_default=None)
    dst_province = fields.String(description="目标IP所属省份", load_default=None)
