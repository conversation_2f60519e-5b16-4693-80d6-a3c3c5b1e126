from marshmallow import fields

from caasm_persistence.schema.base import DocumentNoneSchema, ObjectIdField
from caasm_service.entity.user_assql_aql_collect import UserAssetAqlCollectEntity


class UserAssetAqlCollectSchema(DocumentNoneSchema):
    entity_define = UserAssetAqlCollectEntity

    user_id = ObjectIdField(description="用户ID", load_default=None, allow_none=True)
    name = fields.Str(description="收藏名称", load_default=None)
    tag = fields.Str(description="标签", load_default=None)
    retrieve_statement_id = ObjectIdField(description="检索语句ID", load_default=None, allow_none=True)
    aql_type = fields.Str(description="查询类型", load_default="asql")
