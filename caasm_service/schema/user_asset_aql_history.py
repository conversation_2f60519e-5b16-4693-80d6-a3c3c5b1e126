from marshmallow import fields

from caasm_persistence.schema.base import DocumentNoneSchema, ObjectIdField
from caasm_service.entity.user_asset_aql_history import UserAssetAqlHistoryEntity


class UserAssetAqlHistorySchema(DocumentNoneSchema):
    entity_define = UserAssetAqlHistoryEntity

    aql = fields.Str(load_default=None)
    user_id = ObjectIdField(load_default=None)
    digest = fields.Str(load_default=None)
    adapter_names = fields.List(fields.Str(), load_default=None)
    aql_type = fields.Str(load_default=None)
