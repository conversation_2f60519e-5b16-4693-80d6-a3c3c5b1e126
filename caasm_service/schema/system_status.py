from marshmallow import fields

from caasm_persistence.schema.base import DocumentSchema, DateTimeField
from caasm_service.entity.system_status import SystemStatusEntity


class SystemStatusSchema(DocumentSchema):
    entity_define = SystemStatusEntity

    cpu_usage = fields.Float(required=True)
    mem_total = fields.Integer(required=True)
    mem_used = fields.Integer(required=True)
    mem_usage = fields.Float(required=True)
    disk_total = fields.Integer(required=True)
    disk_used = fields.Integer(required=True)
    disk_usage = fields.Float(required=True)
    tick = DateTimeField(required=True)
