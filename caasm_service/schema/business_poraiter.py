from caasm_persistence.schema.base import (
    BaseSchema,
    fields,
    DocumentNoneSchema,
    EnumField,
    DocumentSchema,
)
from caasm_service.entity.business_poraiter import (
    BusinessPortraitNetwork,
    BusinessPortrait,
    CriticalInfrastructure,
    GradeProtectionLevel,
    Magnitude,
    Status,
    IsInternetAccessible,
    BusinessPortraitUpdateEntity,
    BusinessPortraitUpdateItemEntity,
)


class BusinessPortraitNetworkSchema(BaseSchema):
    entity_define = BusinessPortraitNetwork

    ips = fields.List(fields.Str(), description="IP集合")
    ports = fields.List(fields.Int(), description="端口集合")


class BusinessPortraitSchema(DocumentNoneSchema):
    entity_define = BusinessPortrait

    business_id = fields.Str(description="业务ID")
    name = fields.Str(description="名称")
    owner_names = fields.List(fields.Str(), load_default=list)
    operator_names = fields.List(fields.Str(), load_default=list)
    magnitude = EnumField(Magnitude, by_value=True, description="", load_default=Magnitude.UNKNOWN)
    status = EnumField(Status, by_value=True, description="", load_default=Status.UNKNOWN)
    is_internet_accessible = EnumField(IsInternetAccessible, by_value=True, load_default=IsInternetAccessible.UNKNOWN)
    grade_protection_level = EnumField(
        GradeProtectionLevel,
        by_value=True,
        description="",
        load_default=GradeProtectionLevel.UNKNOWN,
    )
    critical_infrastructure = EnumField(
        CriticalInfrastructure, by_value=True, load_default=CriticalInfrastructure.UNKNOWN
    )
    adapters = fields.List(fields.Str(), description="适配器列表", load_default=list)

    host_count = fields.Int(load_default=0)
    itai_host_count = fields.Int(load_default=0)
    db_count = fields.Int(load_default=0)
    middleware_count = fields.Int(load_default=0)
    asset_group = fields.Dict(load_default=dict)
    asset_count = fields.Int(load_default=0)

    admin_accounts = fields.List(fields.Str(), load_default=list)
    data = fields.List(fields.Str(), load_default=list)
    software = fields.List(fields.Str(), load_default=list)
    network = fields.Nested(BusinessPortraitNetworkSchema(), allow_none=None, missing=None)
    system = fields.List(fields.Str(), load_default=list)
    physical = fields.List(fields.Str(), load_default=list)

    external_domain_count = fields.Int(load_default=0)
    external_ip_count = fields.Int(load_default=0)
    external_url_count = fields.Int(load_default=0)
    external_port_count = fields.Int(load_default=0)

    internal_ip_count = fields.Int(load_default=0)
    vulnerability_count = fields.Int(load_default=0)
    mid_vulnerability_count = fields.Int(load_default=0)
    low_vulnerability_count = fields.Int(load_default=0)
    high_vulnerability_count = fields.Int(load_default=0)
    software_count = fields.Int(load_default=0)
    website_count = fields.Int(load_default=0)
    internal_port_count = fields.Int(load_default=0)
    account_count = fields.Int(load_default=0)
    jar_count = fields.Int(load_default=0)
    web_frame_count = fields.Int(load_default=0)

    without_cmdb_count = fields.Int(load_default=0)
    without_hids_count = fields.Int(load_default=0)
    without_osm_count = fields.Int(load_default=0)
    without_va_count = fields.Int(load_default=0)
    without_av_count = fields.Int(load_default=0)

    coverage_charts = fields.List(fields.Raw(), load_default=list)

    data_centers = fields.List(fields.String(), load_default=list)
    realms = fields.List(fields.String(), load_default=list)


class BusinessPortraitUpdateItemSchema(BaseSchema):
    entity_define = BusinessPortraitUpdateItemEntity

    field = fields.String(required=True)
    value = fields.Raw(required=True)


class BusinessPortraitUpdateSchema(DocumentSchema):
    entity_define = BusinessPortraitUpdateEntity

    entity_id = fields.String(required=True)
    updates = fields.List(
        fields.Nested(BusinessPortraitUpdateItemSchema),
        required=True,
    )
