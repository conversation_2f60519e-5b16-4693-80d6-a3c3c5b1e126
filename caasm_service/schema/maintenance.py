from marshmallow import post_load, post_dump
from marshmallow.fields import String, Integer, List, Nested, Dict, Boolean, Raw

from caasm_persistence.schema.base import BaseSchema, DocumentSchema, EnumField, ObjectIdField
from caasm_service.constants.maintenance import (
    FormComponentType,
    ImportOption,
    FormItemUIType,
)
from caasm_service.entity.maintenance import (
    FormItemEntity,
    FormGroupEntity,
    FormEntity,
    MaintenanceEntity,
    BatchFieldEntity,
    BatchOperationEntity,
)
from caasm_service.schema.validation import FieldValidatorSchema


class FormComponentSchema(BaseSchema):
    title = String(required=False, load_default=None)
    index = Integer(load_default=0)
    description = String(required=False, load_default=None)


class FormItemSchema(FormComponentSchema):
    entity_define = FormItemEntity

    id = String(required=False, load_default=None)
    field_name = String(required=False, load_default=None)
    required = Boolean(load_default=False)
    type = EnumField(FormComponentType, by_value=True, load_default=FormComponentType.ITEM)
    settings = Dict(keys=String, values=Raw, load_default=dict)
    ui_type = EnumField(FormItemUIType, by_value=True, required=False, load_default=None)
    validators = List(Nested(FieldValidatorSchema), load_default=list)
    placeholder = String(required=False, load_default=None)
    status = Dict(
        keys=String(),
        values=Boolean(load_default=True),
        load_default=dict,
        dump_default=None,
    )


class FormGroupSchema(FormComponentSchema):
    entity_define = FormGroupEntity

    items = List(Nested(FormItemSchema), load_default=list, dump_default=None)
    type = EnumField(FormComponentType, by_value=True, load_default=FormComponentType.GROUP)


class FormSchema(DocumentSchema):
    entity_define = FormEntity

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        form_item_schema = FormItemSchema()
        form_group_schema = FormGroupSchema()

        self._schema_mapper = {FormComponentType.ITEM: form_item_schema, FormComponentType.GROUP: form_group_schema}

    name = String(required=True)
    description = String(required=False, load_default=None)
    category = String()
    entity_type = String(required=False, load_default=None)
    operation_types = List(String(), load_default=list, dump_default=None)
    title = String(required=False, load_default=None)
    titles = Dict(keys=String, values=String, load_default=dict)
    components = List(Raw(), load_default=list, dump_default=None)
    cleaner = String(required=False, load_default=None)

    @post_load
    def make_form(self, data, many=None, **kwargs):
        components_data = data.get("components", [])
        components = []
        for component_data in components_data:
            schema = self._schema_mapper.get(component_data["type"])
            components.append(schema.load(component_data))
        data["components"] = components
        return data

    @post_dump
    def dump_form(self, data, many=None, **kwargs):
        components = data.get("components", [])
        components_data = []
        for component in components:
            schema = self._schema_mapper.get(component.type.value)
            if schema is None:
                return None
            components_data.append(schema.dump(component))
        data["components"] = components_data
        return data


class BatchFieldSchema(BaseSchema):
    entity_define = BatchFieldEntity

    field_name = String(required=False)
    index = Integer(required=True)
    required = Boolean(load_default=False)
    settings = Raw(load_default=dict)
    validators = List(Nested(FieldValidatorSchema), load_default=list)


class BatchOperationSchema(BaseSchema):
    entity_define = BatchOperationEntity

    operation_types = List(String(), load_default=list, dump_default=None)
    fields = List(Nested(BatchFieldSchema), load_default=list)
    fields_by_index = Raw(load_default=dict, load_only=True)
    import_option = EnumField(ImportOption, by_value=True, required=False, load_default=ImportOption.REFRESH)
    export_template = String(required=False, load_default=None)
    cleaner = String(required=False, load_default=None)


class MaintenanceSchema(DocumentSchema):
    entity_define = MaintenanceEntity

    category = String()
    entity_type = String(allow_none=True, load_default=None)
    forms = Dict(
        keys=String(),
        values=ObjectIdField,
        load_default=dict,
        dump_default=None,
    )
    batch_operations = List(Nested(BatchOperationSchema), load_default=list)
    batch_operations_by_operation = Raw(load_default=dict, load_only=True)
