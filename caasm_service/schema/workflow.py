from caasm_persistence.schema.base import (
    DocumentNoneSchema,
    BaseSchema,
    EnumField,
    fields,
    ObjectIdField,
    DateTimeField,
)
from caasm_service.constants.workflow import NodeType, WorkflowStatus, CheckType
from caasm_service.entity.workflow import Playbook, Workflow, Task, Node, Param


class ParamSchema(BaseSchema):
    entity_define = Param

    name = fields.Str(load_default=None)
    value = fields.Raw(load_default=None)
    type = fields.Str(load_default=None)
    required = fields.Bool(load_default=None)


class NodeSchema(BaseSchema):
    entity_define = Node

    name = fields.Str(load_default=None)
    entry = fields.Str(load_default=None)
    display_name = fields.Str(load_default=None)
    type = EnumField(NodeType, by_value=True, load_default=None)
    check_type = EnumField(
        CheckType,
        by_value=True,
        load_default=CheckType.SINGLE_SUCCESS,
        dump_default=CheckType.SINGLE_SUCCESS,
    )
    callback_entry = fields.Str(load_default=None)
    param_template = fields.Dict(keys=fields.Str, values=fields.Nested(ParamSchema), load_default=dict)
    output_template = fields.Dict(keys=fields.Str, values=fields.Nested(ParamSchema), load_default=dict)
    next_nodes = fields.List(fields.Str, load_default=list)


class PlaybookSchema(DocumentNoneSchema):
    entity_define = Playbook

    name = fields.Str(load_default=None)
    enabled = fields.Bool(load_default=None)
    description = fields.Str(load_default=None)
    callback_entry = fields.Str(load_default=None)
    display_name = fields.Str(load_default=None)
    param_template = fields.Dict(keys=fields.Str, values=fields.Nested(ParamSchema), load_default=dict)
    output_template = fields.Dict(keys=fields.Str, values=fields.Nested(ParamSchema), load_default=dict)
    node_mapper = fields.Dict(keys=fields.Str, values=fields.Nested(NodeSchema), load_default=dict)
    sign = fields.Str(load_default=None)


class WorkflowSchema(DocumentNoneSchema):
    entity_define = Workflow

    playbook_id = ObjectIdField(load_default=None)
    param_template = fields.Dict(keys=fields.Str, values=fields.Nested(ParamSchema), load_default=dict)
    output_template = fields.Dict(keys=fields.Str, values=fields.Nested(ParamSchema), load_default=dict)
    callback_entry = fields.Str(load_default=None)

    name = fields.Str(load_default=None)
    display_name = fields.Str(load_default=None)
    status = EnumField(WorkflowStatus, by_value=True, load_default=None)
    finished = fields.Bool(load_default=None)
    start_time = DateTimeField(load_default=None)
    finish_time = DateTimeField(load_default=None)
    result = fields.Raw(load_default=None)
    params = fields.Dict(load_default=dict)
    runtime_id = fields.Str(load_default=None)
    top_workflow_id = ObjectIdField(load_default=None)
    superior_task_id = ObjectIdField(load_default=None)


class TaskSchema(DocumentNoneSchema):
    entity_define = Task

    entry = fields.Str(load_default=None)
    type = EnumField(NodeType, by_value=True, load_default=None)
    check_type = EnumField(
        CheckType,
        by_value=True,
        load_default=CheckType.SINGLE_SUCCESS,
        dump_default=CheckType.SINGLE_SUCCESS,
    )
    display_name = fields.Str(load_default=None)
    param_template = fields.Dict(keys=fields.Str, values=fields.Nested(ParamSchema), load_default=dict)
    output_template = fields.Dict(keys=fields.Str, values=fields.Nested(ParamSchema), load_default=dict)
    next_nodes = fields.List(fields.Str, load_default=list)
    callback_entry = fields.Str(load_default=None)
    top_workflow_id = ObjectIdField(load_default=None)
    name = fields.Str(load_default=None)
    workflow_id = ObjectIdField(load_default=None)
    runtime_id = fields.Str(load_default=None)
    params = fields.Raw(load_default=None)
    result = fields.Raw(load_default=None)
    error = fields.Str(load_default=None)
    status = EnumField(WorkflowStatus, by_value=True, load_default=None)
    finished = fields.Bool(load_default=None)
    heartbeat_time = DateTimeField(load_default=None)
    start_time = DateTimeField(load_default=None)
    finish_time = DateTimeField(load_default=None)
