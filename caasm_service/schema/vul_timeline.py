from enum import IntEnum

from caasm_persistence.schema.base import (
    fields,
    DocumentSchema,
    EnumField,
)
from caasm_service.entity.vul_timeline import VulTimeLine
from caasm_vul.enums import VulResponseStatus


class VulTimelineSchema(DocumentSchema):
    entity_define = VulTimeLine

    vul_id = fields.Str(required=True)
    status = EnumField(
        VulResponseStatus,
        by_value=True,
        description="",
        load_default=VulResponseStatus.TOLERABLE,
    )
    date = fields.String(required=False)
    uploader = fields.String(required=False, default="")
