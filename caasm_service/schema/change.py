from caasm_persistence.schema.base import (
    fields,
    EnumField,
    DocumentSchema,
    ObjectIdField,
)
from caasm_service.entity.change import ChangeDetail, ChangeType, ChangeRecord


class ChangeDetailSchema(DocumentSchema):
    entity_define = ChangeDetail

    category = fields.String()
    asset_type = fields.String(load_default=None, allow_none=True)
    entity_id = fields.String()
    field_name = fields.String(load_default=None, allow_none=True)
    change_type = EnumField(enum=ChangeType, by_value=True, allow_none=True, load_default=None)
    child_change_type = EnumField(enum=ChangeType, by_value=True, allow_none=True, load_default=None)
    new_date = fields.Date(load_default=None)
    new_value = fields.Raw(load_default=None, required=False)
    new_display = fields.String(load_default=None, required=False)
    original_date = fields.Date(load_default=None)
    original_value = fields.Raw(load_default=None, required=False)
    original_display = fields.String(load_default=None, required=False)
    changed_datetime = fields.DateTime(load_default=None, dump_default=None)
    user_id = ObjectIdField(required=False, load_default=None)


class ChangeRecordSchema(DocumentSchema):
    entity_define = ChangeRecord

    category = fields.String()
    original_date = fields.Date(allow_none=None, load_default=None)
    new_date = fields.Date(allow_none=None, load_default=None)
    finished = fields.Boolean(load_default=False, dump_default=False)
