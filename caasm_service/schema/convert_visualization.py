from caasm_persistence.schema.base import fields, DocumentNoneSchema
from caasm_service.entity.convert_visualization import ConvertVisualization


class ConvertVisualizationSchema(DocumentNoneSchema):
    entity_define = ConvertVisualization

    adapter_name = fields.Str(description="适配器名称", load_default=None)
    fetch_type = fields.Str(load_default=None, description="采集类型")
    demo = fields.Dict(load_default=None, description="样例数据")
