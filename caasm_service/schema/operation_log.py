from caasm_persistence.schema.base import DocumentSchema, fields
from caasm_service.entity.operation_log import OperationLog, OperationLogConfigEntity


class OperationLogSchema(DocumentSchema):
    """
    检索场景
    """

    entity_define = OperationLog

    user_name = fields.Str(description="名称", load_default=None)
    action = fields.Str(description="具体动作", default="未知", load_default=None)
    url = fields.Str(description="url", load_default=None)
    ip = fields.Str(description="ip", load_default=None)
    role = fields.List(fields.Str(), description="权限code", load_default=None)
    time = fields.Str(description="时间", load_default=None)
    method = fields.Str(description="方法", load_default=None)
    response_code = fields.Int(description="response响应码", load_default=None)
    response_code_text = fields.Str(description="response响应码翻译", load_default=None)
    code = fields.Int(description="响应码", load_default=None)
    timestamp = fields.Str(description="时间戳", load_default=None)


class OperationLogConfigSchema(DocumentSchema):
    entity_define = OperationLogConfigEntity

    days = fields.Integer(load_default=180)
