from caasm_service.constants.alarm import AlarmRuleTypeEnum
from caasm_service.entity.alarm_rule_template import AlarmRuleTemplateEntity
from caasm_persistence.schema.base import (
    EnumField,
    fields,
    DocumentSchema,
)


class AlarmRuleTemplateSchema(DocumentSchema):
    entity_define = AlarmRuleTemplateEntity

    template_name = fields.String(description="模版名称", load_default="")
    template_category = fields.String(description="模版分类", load_default="")
    template_statement = fields.String(description="模版语言", load_default="")
    template_description = fields.String(description="模版描述", load_default="")
    template_type = EnumField(
        AlarmRuleTypeEnum,
        by_value=True,
        load_default=AlarmRuleTypeEnum.CUSTOMIZE,
        description="模版类型",
    )
