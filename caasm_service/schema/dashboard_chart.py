from caasm_charts_manage.charts_manage.util.enum import MetricType, ChartPresentation
from caasm_persistence.schema.base import Enum<PERSON>ield, BaseSchema
from caasm_persistence.schema.base import (
    fields,
    DocumentSchema,
    ObjectIdField,
)
from caasm_service.entity.dashboard_chart import (
    ChartEntity,
    QueryComparisonEntity,
    FieldCountEntity,
    FieldGroupEntity,
    TableEntity,
)
from caasm_meta_data.constants import Category


class FieldGroup(BaseSchema):
    entity_define = FieldGroupEntity
    field = fields.String(required=False, allow_none=True, default=None)
    limit = fields.Integer(required=True)


class QueryComparison(BaseSchema):
    entity_define = QueryComparisonEntity

    querys = fields.List(fields.Dict(), required=False, load_default=None)


class FieldCount(BaseSchema):
    entity_define = FieldCountEntity

    field = fields.String(required=True)
    count_method = fields.String(required=False, load_default=str, default=str, allow_none=True)
    aql_filter = fields.String(required=False, load_default=str, default=str, allow_none=True)
    order = fields.String(required=False, load_default=str, default=str, allow_none=True)
    file = fields.String(required=False, load_default=str, default=str, allow_none=True)
    percentage = fields.Boolean(required=False, load_default=False, default=False, allow_none=True)


class TableSchema(BaseSchema):
    entity_define = TableEntity

    field_list = fields.List(fields.Dict(), required=False, load_default=None)


class OverviewChartSchema(DocumentSchema):
    entity_define = ChartEntity

    category_tree_id = fields.String(required=True)
    chart_name = fields.String(required=True)
    description = fields.String(required=False, load_default=str)
    creator_id = ObjectIdField(required=True)

    internal = fields.Boolean(load_default=False, default=False)

    chart_type = EnumField(ChartPresentation, required=True, allow_none=False, by_value=True)
    metric_type = EnumField(MetricType, required=True, allow_none=False, by_value=True)

    category = fields.String(required=False, load_default=Category.ASSET, dump_default=Category.ASSET)

    f_category = fields.String(required=False, load_default=str)
    s_category = fields.String(required=False, load_default=str)

    base_query = fields.String(required=False, allow_none=True, default=str)

    count_info = fields.Nested(
        FieldCount, required=False, allow_none=True, dump_default=None, load_default=None, default=None
    )

    group_fields = fields.List(fields.Nested(FieldGroup()), required=False, load_default=None)

    front_end_setting = fields.Dict(required=False, load_default=dict)

    asql = fields.Nested(
        QueryComparison,
        required=False,
        allow_none=True,
        dump_default=None,
        load_default=None,
        default=None,
    )

    time = fields.Dict(required=False, load_default=None)

    table = fields.Nested(
        TableSchema, required=False, allow_none=True, dump_default=None, load_default=None, default=None
    )
    time_comparison = fields.Dict(required=False, load_default=None)

    result = fields.Raw(required=False, load_default=list)
