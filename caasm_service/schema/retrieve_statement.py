from caasm_persistence.schema.base import DocumentSchema, fields
from caasm_service.entity.retrieve_statement import RetrieveStatement


class RetrieveStatementSchema(DocumentSchema):
    """
    检索语句
    """

    entity_define = RetrieveStatement

    aql = fields.Str(description="aql语句", allow_none=True, load_default=None)
    digest = fields.Str(description="语句摘要", allow_none=True, load_default=None)
    adapter_names = fields.List(fields.Str(), load_default=list, description="适配器列表")
