from marshmallow import fields

from caasm_persistence.schema.base import DocumentSchema, ObjectIdField
from caasm_service.entity.adapter_instance_fetch_poc_log import AdapterInstanceFetchPocLog


class AdapterInstanceFetchPocLogSchema(DocumentSchema):
    entity_define = AdapterInstanceFetchPocLog

    adapter_instance_id = ObjectIdField(description="实例ID", load_default=None)
    adapter_name = fields.String(description="适配器名称", load_default=None)
    fetch_type = fields.String(description="采集类型", load_default=None)
    content = fields.String(description="日志内容", load_default=None)
