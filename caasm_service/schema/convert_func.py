from caasm_persistence.schema.base import BaseSchema, fields, DocumentSchema
from caasm_service.entity.convert_func import BaseData, Input, Output, ConvertFunc


class BaseDataSchema(BaseSchema):
    entity_define = BaseData

    name = fields.String(description="名称", load_default="")
    en_display_name = fields.String(description="英文名称", load_default="")
    display_name = fields.String(description="展示名称", load_default="")
    description = fields.String(description="描述信息", load_default="")
    type = fields.String(load_default="string")
    headers = fields.List(fields.String(max_length=256, allow_none=False, required=True), load_default=None)
    src = fields.List(fields.String(max_length=256, allow_none=False, required=True), load_default=[])
    must = fields.Bool(description="是否必传", load_default=False)
    default = fields.Raw(load_default=None, description="默认值")
    show = fields.Bool(description="是否必传", load_default=True)
    direct = fields.Bool(description="是否直接参数，在解析规则时需要", load_default=False, required=False)


class InputSchema(BaseSchema):
    entity_define = Input

    count = fields.Int(description="数量", load_default=0)
    complex_type = fields.Dict(keys=fields.Str(), values=fields.Raw(), description="复杂字段", load_default=dict)
    data = fields.List(fields.Nested(BaseDataSchema(), load_default=dict), load_default=[], required=False)


class OutputSchema(BaseSchema):
    entity_define = Output

    count = fields.Int(description="数量", required=False, load_default=0)
    data = fields.List(fields.Nested(BaseDataSchema(), load_default=dict), required=False, load_default=[])


class ConvertFuncSchema(DocumentSchema):
    entity_define = ConvertFunc

    name = fields.String(description="名称", load_default="")
    en_display_name = fields.String(description="英文名称", load_default="")
    display_name = fields.String(description="显示名称", load_default="")
    classify = fields.List(
        fields.String(max_length=256, allow_none=False, required=True), description="分类", load_default=[]
    )
    description = fields.String(description="描述信息", load_default="")
    input = fields.Nested(InputSchema, load_default={})
    output = fields.Nested(OutputSchema, load_default={}, required=False)
