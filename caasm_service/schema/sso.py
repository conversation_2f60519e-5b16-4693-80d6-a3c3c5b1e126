from marshmallow import fields

from caasm_persistence.schema.base import DocumentSchema, EnumField
from caasm_service.constants.auth import AuthenticationType, AuthenticationStatus
from caasm_service.entity.sso import TempLoginKey, SSOConfig


class SSOConfigSchema(DocumentSchema):
    entity_define = SSOConfig

    authentication_type = EnumField(
        AuthenticationType, by_value=True, load_default=AuthenticationType.DEFAULT, description="权限模型"
    )
    status = EnumField(
        AuthenticationStatus, by_value=True, load_default=AuthenticationStatus.ENABLE, description="权限模型"
    )
    config = fields.Dict()


class TempLoginKeySchema(DocumentSchema):
    """
    在发现没有用户信息的时候 login_key 只能使用一次
    """

    entity_define = TempLoginKey

    login_key = fields.String(required=True, description="用户登录keymd5加密")
    sso_key = fields.String(required=True, description="用户凭证")
    status = EnumField(
        AuthenticationStatus, by_value=True, load_default=AuthenticationStatus.ENABLE, description="权限模型"
    )
    authentication_type = EnumField(
        AuthenticationType, by_value=True, load_default=AuthenticationType.DEFAULT, description="权限模型"
    )
